package id.co.bri.brimo.presenters.pulsarevamp

import com.google.gson.Gson
import id.co.bri.brimo.R
import id.co.bri.brimo.contract.IView.pulsarevamp.IFormPulsaDataView
import id.co.bri.brimo.data.api.ApiSource
import id.co.bri.brimo.data.preference.BRImoPrefSource
import id.co.bri.brimo.data.repository.anggaran.AnggaranPfmSource
import id.co.bri.brimo.data.repository.category.CategoryPfmSource
import id.co.bri.brimo.data.repository.transaksi.TransaksiPfmSource
import id.co.bri.brimo.domain.converter.MapperHelper
import id.co.bri.brimo.domain.helpers.GeneralHelper
import id.co.bri.brimo.models.apimodel.response.RestResponse
import id.co.bri.brimo.models.apimodel.response.pulsarevamp.FormPulsaDataResponse
import id.co.bri.brimo.presenters.domain.helper.rx.TestSchedulerProvider
import io.reactivex.Observable
import io.reactivex.disposables.CompositeDisposable
import io.reactivex.schedulers.TestScheduler
import org.junit.Assert.*
import org.junit.Before
import org.junit.Test
import org.junit.runner.RunWith
import org.mockito.ArgumentMatchers
import org.mockito.Mock
import org.mockito.Mockito.*
import org.mockito.junit.MockitoJUnitRunner


@RunWith(MockitoJUnitRunner::class)
class FormPulsaDataRevampPresenterTest {

    @Mock
    var iFormPulsaDataView : IFormPulsaDataView? = null

    @Mock
    var mBRImoPrefRepository: BRImoPrefSource? = null

    @Mock
    var mApiSource: ApiSource? = null

    @Mock
    var categoryPfmSource: CategoryPfmSource? = null

    @Mock
    var transaksiPfmSource: TransaksiPfmSource? = null

    @Mock
    var anggaranPfmSource: AnggaranPfmSource? = null

    @Mock
    var compositeDisposable: CompositeDisposable? = null

    @Mock
    var response : FormPulsaDataResponse? = null

    private var mFormPulsaDataRevampPresenter: FormPulsaDataRevampPresenter<IFormPulsaDataView>? = null
    private var mTestScheduler = TestScheduler()
    private val seqNum = "40"
    private val responseRawEncoded = "ac740a16f65302d1440dcbb1fc6"
    private val responseIdDummy = "12314324324"
    private val urlForm ="/eX8/CIBvtNeAAKq3ZmlkAXOF8uSRcqNFbWkYWXGGFs="
    private val urlFromFastMenu ="X82IoFwvIUdHfBahFx4/bqgo5wkZMUXACPGDQKAXHKM="
    private val urlInquiry = "68gOueiCRjkP1hYQn6H4kIR5lwenBuybpJE2cLtWZ6o="
    private val urlKonfirmasi ="+ysxwPK+HwTbuZ6SBTaTFsjqBq9da40ONWzpT31Y97g="

    @Before
    @Throws(Exception::class)
    fun setUp() {
        mTestScheduler = TestScheduler()
        val testSchedulerProvider = TestSchedulerProvider(mTestScheduler)
        mFormPulsaDataRevampPresenter = FormPulsaDataRevampPresenter(
                testSchedulerProvider,
                compositeDisposable,
                mBRImoPrefRepository,
                mApiSource,
                categoryPfmSource,
                transaksiPfmSource,
                anggaranPfmSource
        )
        mFormPulsaDataRevampPresenter!!.setFormUrl(urlForm)
        mFormPulsaDataRevampPresenter!!.setInquiryUrl(urlInquiry)
        mFormPulsaDataRevampPresenter!!.setKonfirmasiUrl(urlKonfirmasi)
        mFormPulsaDataRevampPresenter!!.view = iFormPulsaDataView
        `when`(mBRImoPrefRepository!!.seqNumber).thenReturn(seqNum)
    }

    @Test
    fun setFormUrl() {
        mFormPulsaDataRevampPresenter?.setFormUrl(urlForm)
        assertEquals(urlForm, mFormPulsaDataRevampPresenter?.urlFormPulsa)
    }

    @Test
    fun setInquiryUrl() {
        mFormPulsaDataRevampPresenter?.setInquiryUrl(urlInquiry)
        assertEquals(urlInquiry, mFormPulsaDataRevampPresenter?.urlInquiryPulsa)
    }

    @Test
    fun setKonfirmasiUrl() {
        mFormPulsaDataRevampPresenter?.setKonfirmasiUrl(urlKonfirmasi)
        assertEquals(urlKonfirmasi, mFormPulsaDataRevampPresenter?.urlKonfirmasiPulsa)
    }

    @Test
    fun getDataFormOnFailure() {
        val jsonRaw = ""

        val restResponse = Gson().fromJson(jsonRaw, RestResponse::class.java)
        mockStatic(MapperHelper::class.java).use { mapperHelperMockedStatic ->
            mapperHelperMockedStatic.`when`<Any> { MapperHelper.stringToRestResponse(responseRawEncoded, seqNum) }.thenReturn(restResponse)
            mapperHelperMockedStatic.`when`<Any> { MapperHelper.getIdResponse(responseRawEncoded) }.thenReturn(jsonRaw)

            `when`<Observable<String>>(mApiSource!!.getDataForm(mFormPulsaDataRevampPresenter?.urlFormPulsa, seqNum)).thenReturn(Observable.just(responseRawEncoded))

            mFormPulsaDataRevampPresenter?.getDataForm()

            mTestScheduler.triggerActions()
            verify(iFormPulsaDataView)?.onException(any())
        }
    }

    @Test
    fun getDataForm00() {
        val jsonRaw = """{ "code": "00", "description": "", "data": { "reference_number": "refnum" } }"""
        val restResponse = Gson().fromJson(jsonRaw, RestResponse::class.java)
        mockStatic(GeneralHelper::class.java)
            .use { generalHelperMockedStatic ->
                generalHelperMockedStatic.`when`<Any> {
                    GeneralHelper.isContains(
                        R.array.response_code_success,
                        restResponse.code
                    )
                }.thenReturn(true)
                mockStatic(MapperHelper::class.java)
                    .use { mapperHelperMockedStatic ->
                        mapperHelperMockedStatic.`when`<Any> { MapperHelper.stringToRestResponse(responseRawEncoded, seqNum) }.thenReturn(restResponse)
                        mapperHelperMockedStatic.`when`<Any> { MapperHelper.getIdResponse(responseRawEncoded) }.thenReturn(responseIdDummy)

                        `when`<Observable<String>>(mApiSource!!.getDataForm(mFormPulsaDataRevampPresenter?.urlFormPulsa, seqNum)).thenReturn(Observable.just(responseRawEncoded))

                        mFormPulsaDataRevampPresenter?.getDataForm()

                        mTestScheduler.triggerActions()

                        verify(iFormPulsaDataView)?.onSuccessGetData(ArgumentMatchers.any(FormPulsaDataResponse::class.java))
                    }
            }
    }

    @Test
    fun getDataFormOnApiCallError() {
        val jsonRaw = """{ "code": "05", "description": "", "data": { "reference_number": "refnum" } }"""
        val restResponse = Gson().fromJson(jsonRaw, RestResponse::class.java)
        mockStatic(GeneralHelper::class.java)
            .use { generalHelperMockedStatic ->
                generalHelperMockedStatic.`when`<Any> {
                    GeneralHelper.isContains(
                        R.array.response_code_error_specific,
                        restResponse.code
                    )
                }.thenReturn(false)
                mockStatic(MapperHelper::class.java)
                    .use { mapperHelperMockedStatic ->
                        mapperHelperMockedStatic.`when`<Any> { MapperHelper.stringToRestResponse(responseRawEncoded, seqNum) }.thenReturn(restResponse)
                        mapperHelperMockedStatic.`when`<Any> { MapperHelper.getIdResponse(responseRawEncoded) }.thenReturn(responseIdDummy)

                        `when`<Observable<String>>(mApiSource!!.getDataForm(mFormPulsaDataRevampPresenter?.urlFormPulsa, seqNum)).thenReturn(Observable.just(responseRawEncoded))

                        mFormPulsaDataRevampPresenter?.getDataForm()

                        mTestScheduler.triggerActions()

                        verify(iFormPulsaDataView)?.onSessionEnd(any())

                    }
            }
    }


    /*getDataFromFastMenu*/
    @Test
    fun getDataFromFastMenuOnFailure() {
        val jsonRaw = ""

        val restResponse = Gson().fromJson(jsonRaw, RestResponse::class.java)
        mockStatic(MapperHelper::class.java).use { mapperHelperMockedStatic ->
            mapperHelperMockedStatic.`when`<Any> { MapperHelper.stringToRestResponse(responseRawEncoded, seqNum) }.thenReturn(restResponse)
            mapperHelperMockedStatic.`when`<Any> { MapperHelper.getIdResponse(responseRawEncoded) }.thenReturn(jsonRaw)

            `when`<Observable<String>>(mApiSource!!.getData(mFormPulsaDataRevampPresenter?.urlFormPulsa, mFormPulsaDataRevampPresenter?.fastMenuRequest, seqNum)).thenReturn(Observable.just(responseRawEncoded))

            mFormPulsaDataRevampPresenter?.getDataFormFastMenu()

            mTestScheduler.triggerActions()
            verify(iFormPulsaDataView)?.onException(any())
        }
    }

    @Test
    fun getDataFromFastMenu00() {
        val jsonRaw = """{ "code": "00", "description": "", "data": { "reference_number": "refnum" } }"""
        val restResponse = Gson().fromJson(jsonRaw, RestResponse::class.java)
        mockStatic(GeneralHelper::class.java)
            .use { generalHelperMockedStatic ->
                generalHelperMockedStatic.`when`<Any> {
                    GeneralHelper.isContains(
                        R.array.response_code_success,
                        restResponse.code
                    )
                }.thenReturn(true)
                mockStatic(MapperHelper::class.java)
                    .use { mapperHelperMockedStatic ->
                        mapperHelperMockedStatic.`when`<Any> { MapperHelper.stringToRestResponse(responseRawEncoded, seqNum) }.thenReturn(restResponse)
                        mapperHelperMockedStatic.`when`<Any> { MapperHelper.getIdResponse(responseRawEncoded) }.thenReturn(responseIdDummy)

                        `when`<Observable<String>>(mApiSource!!.getData(mFormPulsaDataRevampPresenter?.urlFormPulsa, mFormPulsaDataRevampPresenter?.fastMenuRequest, seqNum)).thenReturn(Observable.just(responseRawEncoded))

                        mFormPulsaDataRevampPresenter?.getDataFormFastMenu()

                        mTestScheduler.triggerActions()

                        verify(iFormPulsaDataView)?.onSuccessGetData(ArgumentMatchers.any(FormPulsaDataResponse::class.java))
                    }
            }
    }

    @Test
    fun getDataFromFastMenuOnApiCallError() {
        mFormPulsaDataRevampPresenter?.setFormUrl(urlFromFastMenu)
        val jsonRaw = """{ "code": "05", "description": "", "data": { "reference_number": "refnum" } }"""
        val restResponse = Gson().fromJson(jsonRaw, RestResponse::class.java)
        mockStatic(GeneralHelper::class.java)
            .use { generalHelperMockedStatic ->
                generalHelperMockedStatic.`when`<Any> {
                    GeneralHelper.isContains(
                        R.array.response_code_error_specific,
                        restResponse.code
                    )
                }.thenReturn(false)
                mockStatic(MapperHelper::class.java)
                    .use { mapperHelperMockedStatic ->
                        mapperHelperMockedStatic.`when`<Any> { MapperHelper.stringToRestResponse(responseRawEncoded, seqNum) }.thenReturn(restResponse)
                        mapperHelperMockedStatic.`when`<Any> { MapperHelper.getIdResponse(responseRawEncoded) }.thenReturn(responseIdDummy)

                        `when`<Observable<String>>(mApiSource!!.getData(mFormPulsaDataRevampPresenter?.urlFormPulsa, mFormPulsaDataRevampPresenter?.fastMenuRequest, seqNum)).thenReturn(Observable.just(responseRawEncoded))

                        mFormPulsaDataRevampPresenter?.getDataFormFastMenu()

                        mTestScheduler.triggerActions()

                        verify(iFormPulsaDataView)?.onSessionEnd(any())

                    }
            }
    }
}