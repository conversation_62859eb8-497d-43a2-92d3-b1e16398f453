package id.co.bri.brimo.presenters.ccqrismpm

import com.google.gson.Gson
import id.co.bri.brimo.R
import id.co.bri.brimo.contract.IView.ICcQrisMpmView
import id.co.bri.brimo.data.api.ApiSource
import id.co.bri.brimo.data.preference.BRImoPrefSource
import id.co.bri.brimo.data.repository.anggaran.AnggaranPfmSource
import id.co.bri.brimo.data.repository.category.CategoryPfmSource
import id.co.bri.brimo.data.repository.transaksi.TransaksiPfmSource
import id.co.bri.brimo.domain.converter.MapperHelper
import id.co.bri.brimo.domain.helpers.GeneralHelper
import id.co.bri.brimo.models.apimodel.response.GeneralConfirmationResponse
import id.co.bri.brimo.models.apimodel.response.RestResponse
import id.co.bri.brimo.models.apimodel.response.ccqrismpm.CcQrisMpmResponse
import id.co.bri.brimo.presenters.domain.helper.rx.TestSchedulerProvider
import io.reactivex.Observable
import io.reactivex.disposables.CompositeDisposable
import io.reactivex.schedulers.TestScheduler
import org.junit.Assert.*
import org.junit.Before
import org.junit.Test
import org.junit.runner.RunWith
import org.mockito.ArgumentMatchers
import org.mockito.Mock
import org.mockito.Mockito
import org.mockito.junit.MockitoJUnitRunner

@RunWith(MockitoJUnitRunner::class)
class CcQrisMpmPresenterTest {

    @Mock
    var iCcQrisMpmView : ICcQrisMpmView? = null

    @Mock
    var mBRImoPrefRepository: BRImoPrefSource? = null

    @Mock
    var mApiSource: ApiSource? = null

    @Mock
    var categoryPfmSource: CategoryPfmSource? = null

    @Mock
    var transaksiPfmSource: TransaksiPfmSource? = null

    @Mock
    var anggaranPfmSource: AnggaranPfmSource? = null

    @Mock
    var compositeDisposable: CompositeDisposable? = null

    @Mock
    lateinit var response : GeneralConfirmationResponse

    private var mCcQrisMpmPresenter: CcQrisMpmPresenter<ICcQrisMpmView>? = null
    private var mTestScheduler = TestScheduler()
    private val seqNum = "40"
    private val responseRawEncoded = "ac740a16f65302d1440dcbb1fc671af1z8Wx8DB"
    private val responseIdDummy = "12314324324"
    private val mProviderId ="mProviderId"
    private val mPhoneNumber ="mPhoneNumber"
    private val mSaveAs ="mSaveAs"
    private val mIsFromFast = false
    private val mPaymentCode = "mPaymentCode"
    private val mUrlGetListSof = "mUrlGetListSof"


    @Before
    @Throws(Exception::class)
    fun setUp() {
        mTestScheduler = TestScheduler()
        val testSchedulerProvider = TestSchedulerProvider(mTestScheduler)
        mCcQrisMpmPresenter = compositeDisposable?.let {
            mBRImoPrefRepository?.let { it1 ->
                mApiSource?.let { it2 ->
                    categoryPfmSource?.let { it3 ->
                        transaksiPfmSource?.let { it4 ->
                            anggaranPfmSource?.let { it5 ->
                                CcQrisMpmPresenter(
                                    testSchedulerProvider,
                                    it,
                                    it1,
                                    it2,
                                    it3,
                                    it4,
                                    it5
                                )
                            }
                        }
                    }
                }
            }
        }
        mCcQrisMpmPresenter?.view = iCcQrisMpmView
        mCcQrisMpmPresenter?.setUrlListSof(mUrlGetListSof)
        Mockito.`when`(mBRImoPrefRepository!!.seqNumber).thenReturn(seqNum)
    }


    @Test
    fun getDataListSofOnFailure() {
        val jsonRaw = ""

        val restResponse = Gson().fromJson(jsonRaw, RestResponse::class.java)
        Mockito.mockStatic(MapperHelper::class.java).use { mapperHelperMockedStatic ->
            mapperHelperMockedStatic.`when`<Any> { MapperHelper.stringToRestResponse(responseRawEncoded, seqNum) }.thenReturn(restResponse)
            mapperHelperMockedStatic.`when`<Any> { MapperHelper.getIdResponse(responseRawEncoded) }.thenReturn(jsonRaw)

            Mockito.`when`<Observable<String>>(mApiSource!!.getDataTanpaRequest(ArgumentMatchers.any(), ArgumentMatchers.any())).thenReturn(Observable.just(responseRawEncoded))

            mCcQrisMpmPresenter?.getDataListSof()

            mTestScheduler.triggerActions()
            Mockito.verify(iCcQrisMpmView)?.onException(Mockito.anyString())
        }
    }

    @Test
    fun getDataListSof00() {
        val jsonRaw = """{ "code": "00", "description": "", "data": { "reference_number": "refnum" } }"""
        val restResponse = Gson().fromJson(jsonRaw, RestResponse::class.java)
        Mockito.mockStatic(GeneralHelper::class.java)
            .use { generalHelperMockedStatic ->
                generalHelperMockedStatic.`when`<Any> {
                    GeneralHelper.isContains(
                        R.array.response_code_success,
                        restResponse.code
                    )
                }.thenReturn(true)
                Mockito.mockStatic(MapperHelper::class.java)
                    .use { mapperHelperMockedStatic ->
                        mapperHelperMockedStatic.`when`<Any> { MapperHelper.stringToRestResponse(responseRawEncoded, seqNum) }.thenReturn(restResponse)
                        mapperHelperMockedStatic.`when`<Any> { MapperHelper.getIdResponse(responseRawEncoded) }.thenReturn(responseIdDummy)

                        Mockito.`when`<Observable<String>>(mApiSource!!
                            .getDataTanpaRequest(ArgumentMatchers.any(), ArgumentMatchers.any()))
                            .thenReturn(Observable.just(responseRawEncoded))

                        mCcQrisMpmPresenter?.getDataListSof()

                        mTestScheduler.triggerActions()
                        Mockito.verify(iCcQrisMpmView)?.onSuccess(
                            ArgumentMatchers.any(CcQrisMpmResponse::class.java))
                    }
            }
    }

    @Test
    fun getDataListSofOnApiCallError() {
        val jsonRaw = """{ "code": "05", "description": "", "data": { "reference_number": "refnum" } }"""
        val restResponse = Gson().fromJson(jsonRaw, RestResponse::class.java)
        Mockito.mockStatic(GeneralHelper::class.java)
            .use { generalHelperMockedStatic ->
                generalHelperMockedStatic.`when`<Any> {
                    GeneralHelper.isContains(
                        R.array.response_code_error_specific,
                        restResponse.code
                    )
                }.thenReturn(false)
                Mockito.mockStatic(MapperHelper::class.java)
                    .use { mapperHelperMockedStatic ->
                        mapperHelperMockedStatic.`when`<Any> { MapperHelper.stringToRestResponse(responseRawEncoded, seqNum) }.thenReturn(restResponse)
                        mapperHelperMockedStatic.`when`<Any> { MapperHelper.getIdResponse(responseRawEncoded) }.thenReturn(responseIdDummy)

                        Mockito.`when`<Observable<String>>(mApiSource!!.getDataTanpaRequest(
                            ArgumentMatchers.any(), ArgumentMatchers.any())).thenReturn(Observable.just(responseRawEncoded))

                        mCcQrisMpmPresenter?.getDataListSof()

                        mTestScheduler.triggerActions()

                        Mockito.verify(iCcQrisMpmView)?.onSessionEnd(Mockito.any())

                    }
            }
    }
}