package id.co.bri.brimo.presenters.nfcpayment

import android.content.Context
import android.content.res.Resources
import id.co.bri.brimo.R
import id.co.bri.brimo.contract.IView.nfcpayment.INFCPaymentView
import id.co.bri.brimo.data.api.ApiSource
import id.co.bri.brimo.data.preference.BRImoPrefSource
import id.co.bri.brimo.data.repository.anggaran.AnggaranPfmSource
import id.co.bri.brimo.data.repository.category.CategoryPfmSource
import id.co.bri.brimo.data.repository.transaksi.TransaksiPfmSource
import id.co.bri.brimo.domain.helpers.GeneralHelper
import id.co.bri.brimo.models.apimodel.request.nfcpayment.GeneratePayloadNfcRequest
import id.co.bri.brimo.presenters.domain.helper.rx.TestSchedulerProvider
import id.co.bri.brimo.security.AES
import io.reactivex.Observable
import io.reactivex.disposables.CompositeDisposable
import io.reactivex.schedulers.TestScheduler
import org.junit.Assert
import org.junit.Before
import org.junit.Test
import org.mockito.Mock
import org.mockito.Mockito.any
import org.mockito.Mockito.mock
import org.mockito.Mockito.mockStatic
import org.mockito.Mockito.verify
import org.mockito.Mockito.`when`
import org.mockito.MockitoAnnotations
import kotlin.reflect.full.declaredMemberProperties
import kotlin.reflect.jvm.isAccessible

class NFCPaymentPresenterTest {

    @Mock
    private var presenterView: INFCPaymentView? = null

    @Mock
    private var preference: BRImoPrefSource? = null

    @Mock
    private var apiSource: ApiSource? = null

    @Mock
    private var transactionPFMSource: TransaksiPfmSource? = null

    @Mock
    private var categoryPfmSource: CategoryPfmSource? = null

    @Mock
    private var compositeDisposable: CompositeDisposable? = null

    @Mock
    private var anggaranPfmSource: AnggaranPfmSource? = null

    private val testScheduler: TestScheduler = TestScheduler()
    private val scheduler:TestSchedulerProvider = TestSchedulerProvider(testScheduler)

    private lateinit var presenter: NFCPaymentPresenter<INFCPaymentView>

    private companion object {
        const val DUMMY_SEQNUM = "12"

        val SUCCESS_RESPONSE = """
            {
                "code": "00",
                "description": "oke",
                "data": null
            }
        """.trimIndent()

        val FAILED_RESPONSE = """
            {
                "code": "05",
                "description": "oke",
                "data": null
            }
        """.trimIndent()

        val NFC_WAITING_RESPONSE = """
            {
                "code": "00",
                "description": "Inquired",
                "data": null
            }
        """.trimIndent()

        val NFC_INQUIRED_RESPONSE = """
            {
                "code": "01",
                "description": "Inquired",
                "data": null
            }
        """.trimIndent()

        val NFC_SUCCESS_RESPONSE = """
            {
                "code": "02",
                "description": "Inquired",
                "data": null
            }
        """.trimIndent()

        val NFC_FAILED_RESPONSE = """
            {
                "code": "03",
                "description": "Inquired",
                "data": null
            }
        """.trimIndent()

        val NFC_EXPIRED_RESPONSE = """
            {
                "code": "03",
                "description": "Inquired",
                "data": null
            }
        """.trimIndent()
    }

    @Before
    fun setup() {
        MockitoAnnotations.initMocks(this)
        val context = mock(Context::class.java)
        GeneralHelper.setHelperContext(context)

        presenter = NFCPaymentPresenter(
            schedulerProvider = scheduler,
            compositeDisposable = compositeDisposable!!,
            brimoPreference = preference!!,
            apiSource = apiSource!!,
            categoryPfmSource = categoryPfmSource!!,
            transactionPfmSource = transactionPFMSource!!,
            budgetPfmSource = anggaranPfmSource!!
        )

        presenter.view = presenterView
        `when`(preference?.seqNumber).thenReturn(DUMMY_SEQNUM)
        `when`(context.resources).thenReturn(mock(Resources::class.java))
    }

    @Test
    fun `set url fast menu should be get valid url`() {
        presenter.init(isFastMenu = true)
        presenter.setUrlCheckPayment("ikqrtshHLBm3ZDmfO6ifLVLm18AQtRGpioQgBOHv62k=")
        presenter.setUrlGeneratePayload("ziM9XOo3ZgCnFZy7basZtsr7tU8flcN3EeY5QThzPHc=")

        Assert.assertTrue(getPrivateField(presenter, "isFastMenu")!!)
        Assert.assertEquals("ikqrtshHLBm3ZDmfO6ifLVLm18AQtRGpioQgBOHv62k=", getPrivateField(presenter, "urlCheckPayment"))
        Assert.assertEquals("ziM9XOo3ZgCnFZy7basZtsr7tU8flcN3EeY5QThzPHc=", getPrivateField(presenter, "urlGeneratePayload"))
    }

    @Test
    fun `set url non-fastmenu should be get valid url`() {
        presenter.init(isFastMenu = false)
        presenter.setUrlCheckPayment("ikqrtshHLBm3ZDmfO6ifLVLm18AQtRGpioQgBOHv62k=")
        presenter.setUrlGeneratePayload("ziM9XOo3ZgCnFZy7basZtsr7tU8flcN3EeY5QThzPHc=")

        Assert.assertFalse(getPrivateField(presenter, "isFastMenu")!!)
        Assert.assertEquals("ikqrtshHLBm3ZDmfO6ifLVLm18AQtRGpioQgBOHv62k=", getPrivateField(presenter, "urlCheckPayment"))
        Assert.assertEquals("ziM9XOo3ZgCnFZy7basZtsr7tU8flcN3EeY5QThzPHc=", getPrivateField(presenter, "urlGeneratePayload"))
    }

    @Test
    fun `getDataPayloadNfc from fast-menu should be success`() {
        presenter.init(isFastMenu = true)
        presenter.setUrlCheckPayment("ikqrtshHLBm3ZDmfO6ifLVLm18AQtRGpioQgBOHv62k=")
        presenter.setUrlGeneratePayload("ziM9XOo3ZgCnFZy7basZtsr7tU8flcN3EeY5QThzPHc=")

        `when`(preference?.username).thenReturn("testusername")
        `when`(preference?.tokenKey).thenReturn("randomTokenKey")

        mockStatic(GeneralHelper::class.java).use {
            it.`when`<Any> { GeneralHelper.isContains(R.array.response_code_success, "00") }.thenReturn(true)
        }

        `when`(apiSource?.getData(any(), any(GeneratePayloadNfcRequest::class.java), any()))
            .thenReturn(Observable.just(getRawResponse(SUCCESS_RESPONSE)))

        presenter.getDataPayloadNfc("", "", "", "")

        testScheduler.triggerActions()

        verify(presenterView)?.showProgress()
        verify(presenterView)?.hideProgress()
    }

    @Test
    fun `getDataPayloadNfc from fast-menu should be failed`() {
        presenter.init(isFastMenu = true)
        presenter.setUrlCheckPayment("ikqrtshHLBm3ZDmfO6ifLVLm18AQtRGpioQgBOHv62k=")
        presenter.setUrlGeneratePayload("ziM9XOo3ZgCnFZy7basZtsr7tU8flcN3EeY5QThzPHc=")

        `when`(preference?.username).thenReturn("testusername")
        `when`(preference?.tokenKey).thenReturn("randomTokenKey")

        mockStatic(GeneralHelper::class.java).use {
            it.`when`<Any> { GeneralHelper.isContains(R.array.response_code_success, "05") }.thenReturn(false)
        }

        `when`(apiSource?.getData(any(), any(GeneratePayloadNfcRequest::class.java), any()))
            .thenReturn(Observable.just(getRawResponse(FAILED_RESPONSE)))

        presenter.getDataPayloadNfc("", "", "", "")

        testScheduler.triggerActions()

        verify(presenterView)?.showProgress()
        verify(presenterView)?.hideProgress()
    }

    @Test
    fun `getDataPayloadNfc from no fast-menu should be success`() {
        presenter.init(isFastMenu = false)
        presenter.setUrlCheckPayment("ikqrtshHLBm3ZDmfO6ifLVLm18AQtRGpioQgBOHv62k=")
        presenter.setUrlGeneratePayload("ziM9XOo3ZgCnFZy7basZtsr7tU8flcN3EeY5QThzPHc=")

        `when`(preference?.username).thenReturn("testusername")
        `when`(preference?.tokenKey).thenReturn("randomTokenKey")

        mockStatic(GeneralHelper::class.java).use {
            it.`when`<Any> { GeneralHelper.isContains(R.array.response_code_success, "00") }.thenReturn(true)
        }

        `when`(apiSource?.getData(any(), any(GeneratePayloadNfcRequest::class.java), any()))
            .thenReturn(Observable.just(getRawResponse(SUCCESS_RESPONSE)))

        presenter.getDataPayloadNfc("", "", "", "")

        testScheduler.triggerActions()

        verify(presenterView)?.showProgress()
        verify(presenterView)?.hideProgress()
    }

    @Test
    fun `getDataPayloadNfc from no fast-menu should be failed`() {
        presenter.init(isFastMenu = false)
        presenter.setUrlCheckPayment("ikqrtshHLBm3ZDmfO6ifLVLm18AQtRGpioQgBOHv62k=")
        presenter.setUrlGeneratePayload("ziM9XOo3ZgCnFZy7basZtsr7tU8flcN3EeY5QThzPHc=")

        `when`(preference?.username).thenReturn("testusername")
        `when`(preference?.tokenKey).thenReturn("randomTokenKey")

        mockStatic(GeneralHelper::class.java).use {
            it.`when`<Any> { GeneralHelper.isContains(R.array.response_code_success, "05") }.thenReturn(false)
        }

        `when`(apiSource?.getData(any(), any(GeneratePayloadNfcRequest::class.java), any()))
            .thenReturn(Observable.just(getRawResponse(FAILED_RESPONSE)))

        presenter.getDataPayloadNfc("", "", "", "")

        testScheduler.triggerActions()

        verify(presenterView)?.showProgress()
        verify(presenterView)?.hideProgress()
    }

    @Test
    fun `check nfc payment from fast-menu should be success`() {
        presenter.init(isFastMenu = true)
        presenter.setUrlCheckPayment("ikqrtshHLBm3ZDmfO6ifLVLm18AQtRGpioQgBOHv62k=")
        presenter.setUrlGeneratePayload("ziM9XOo3ZgCnFZy7basZtsr7tU8flcN3EeY5QThzPHc=")

        `when`(preference?.username).thenReturn("testusername")
        `when`(preference?.tokenKey).thenReturn("randomTokenKey")

        `when`(apiSource?.getData(any(), any(GeneratePayloadNfcRequest::class.java), any()))
            .thenReturn(Observable.just(getRawResponse(NFC_SUCCESS_RESPONSE)))

        presenter.getDataPayloadNfc("", "", "", "")

        testScheduler.triggerActions()

        verify(presenterView)?.showProgress()
        verify(presenterView)?.hideProgress()
    }

    @Test
    fun `check nfc payment from fast-menu should be failed`() {
        presenter.init(isFastMenu = true)
        presenter.setUrlCheckPayment("ikqrtshHLBm3ZDmfO6ifLVLm18AQtRGpioQgBOHv62k=")
        presenter.setUrlGeneratePayload("ziM9XOo3ZgCnFZy7basZtsr7tU8flcN3EeY5QThzPHc=")

        `when`(preference?.username).thenReturn("testusername")
        `when`(preference?.tokenKey).thenReturn("randomTokenKey")

        `when`(apiSource?.getData(any(), any(GeneratePayloadNfcRequest::class.java), any()))
            .thenReturn(Observable.just(getRawResponse(NFC_FAILED_RESPONSE)))

        presenter.getDataPayloadNfc("", "", "", "")

        testScheduler.triggerActions()

        verify(presenterView)?.showProgress()
        verify(presenterView)?.hideProgress()
    }

    @Test
    fun `check nfc payment from fast-menu should be get waiting status`() {
        presenter.init(isFastMenu = true)
        presenter.setUrlCheckPayment("ikqrtshHLBm3ZDmfO6ifLVLm18AQtRGpioQgBOHv62k=")
        presenter.setUrlGeneratePayload("ziM9XOo3ZgCnFZy7basZtsr7tU8flcN3EeY5QThzPHc=")

        `when`(preference?.username).thenReturn("testusername")
        `when`(preference?.tokenKey).thenReturn("randomTokenKey")

        `when`(apiSource?.getData(any(), any(GeneratePayloadNfcRequest::class.java), any()))
            .thenReturn(Observable.just(getRawResponse(NFC_WAITING_RESPONSE)))

        presenter.getDataPayloadNfc("", "", "", "")

        testScheduler.triggerActions()

        verify(presenterView)?.showProgress()
        verify(presenterView)?.hideProgress()
    }

    @Test
    fun `check nfc payment from fast-menu should be get waiting inquired status`() {
        presenter.init(isFastMenu = true)
        presenter.setUrlCheckPayment("ikqrtshHLBm3ZDmfO6ifLVLm18AQtRGpioQgBOHv62k=")
        presenter.setUrlGeneratePayload("ziM9XOo3ZgCnFZy7basZtsr7tU8flcN3EeY5QThzPHc=")

        `when`(preference?.username).thenReturn("testusername")
        `when`(preference?.tokenKey).thenReturn("randomTokenKey")

        `when`(apiSource?.getData(any(), any(GeneratePayloadNfcRequest::class.java), any()))
            .thenReturn(Observable.just(getRawResponse(NFC_INQUIRED_RESPONSE)))

        presenter.getDataPayloadNfc("", "", "", "")

        testScheduler.triggerActions()

        verify(presenterView)?.showProgress()
        verify(presenterView)?.hideProgress()
    }

    @Test
    fun `check nfc payment from fast-menu should be get waiting expired status`() {
        presenter.init(isFastMenu = true)
        presenter.setUrlCheckPayment("ikqrtshHLBm3ZDmfO6ifLVLm18AQtRGpioQgBOHv62k=")
        presenter.setUrlGeneratePayload("ziM9XOo3ZgCnFZy7basZtsr7tU8flcN3EeY5QThzPHc=")

        `when`(preference?.username).thenReturn("testusername")
        `when`(preference?.tokenKey).thenReturn("randomTokenKey")

        `when`(apiSource?.getData(any(), any(GeneratePayloadNfcRequest::class.java), any()))
            .thenReturn(Observable.just(getRawResponse(NFC_EXPIRED_RESPONSE)))

        presenter.getDataPayloadNfc("", "", "", "")

        testScheduler.triggerActions()

        verify(presenterView)?.showProgress()
        verify(presenterView)?.hideProgress()
    }

    private fun <T> getPrivateField(instance: Any, fieldName: String): T? {
        val property = instance::class.declaredMemberProperties.find { it.name == fieldName }
        property?.isAccessible = true
        return property?.getter?.call(instance) as? T
    }

    private fun getRawResponse(response: String): String {
        val responseMd5 = GeneralHelper.md5(response)
        val key = responseMd5.substring(0, 8) + GeneralHelper.getZeroPaddedSeqnum(DUMMY_SEQNUM)
        val result = AES.encryptGcm(response, key)
        return responseMd5 + result
    }

}