package id.co.bri.brimo.presenters.pulsarevamp


import com.google.gson.Gson
import id.co.bri.brimo.R
import id.co.bri.brimo.contract.IView.pulsarevamp.IInquiryPulsaRevampView
import id.co.bri.brimo.data.api.ApiSource
import id.co.bri.brimo.data.preference.BRImoPrefSource
import id.co.bri.brimo.data.repository.anggaran.AnggaranPfmSource
import id.co.bri.brimo.data.repository.category.CategoryPfmSource
import id.co.bri.brimo.data.repository.transaksi.TransaksiPfmSource
import id.co.bri.brimo.domain.converter.MapperHelper
import id.co.bri.brimo.domain.helpers.GeneralHelper
import id.co.bri.brimo.models.apimodel.request.KonfirmasiPulsaRequest
import id.co.bri.brimo.models.apimodel.response.GeneralConfirmationResponse
import id.co.bri.brimo.models.apimodel.response.RestResponse
import id.co.bri.brimo.presenters.domain.helper.rx.TestSchedulerProvider
import io.reactivex.Observable
import io.reactivex.disposables.CompositeDisposable
import io.reactivex.schedulers.TestScheduler
import org.junit.Assert.*
import org.junit.Before
import org.junit.Test
import org.junit.runner.RunWith
import org.mockito.ArgumentMatchers
import org.mockito.Mock
import org.mockito.Mockito
import org.mockito.junit.MockitoJUnitRunner

@RunWith(MockitoJUnitRunner::class)
class InquiryPulsaDataRevampPresenterTest {

    @Mock
    var iInquiryPulsaRevampView : IInquiryPulsaRevampView? = null

    @Mock
    var mBRImoPrefRepository: BRImoPrefSource? = null

    @Mock
    var mApiSource: ApiSource? = null

    @Mock
    var categoryPfmSource: CategoryPfmSource? = null

    @Mock
    var transaksiPfmSource: TransaksiPfmSource? = null

    @Mock
    var anggaranPfmSource: AnggaranPfmSource? = null

    @Mock
    var compositeDisposable: CompositeDisposable? = null

    @Mock
    var response : GeneralConfirmationResponse? = null

    var mInquiryPulsaDataRevampPresenter: InquiryPulsaDataRevampPresenter<IInquiryPulsaRevampView>? = null
    private var mTestScheduler = TestScheduler()
    private val seqNum = "40"
    private val responseRawEncoded = "ac740a16f65302d1440dcbb1fc671af1z8Wx8DB"
    private val responseIdDummy = "12314324324"
    private val urlKonfirmasi ="urlKonfirmasi"
    private val mRefNum ="mRefNum"
    private val mProviderId ="mProviderId"
    private val mPhoneNumber ="mPhoneNumber"
    private val mAmount ="mAmount"
    private val mSaveAs ="mSaveAs"
    private val mItem ="mItem"
    private val mType ="mType"
    private val mNote ="mNote"
    private val mIsFromFast = false


    @Before
    @Throws(Exception::class)
    fun setUp() {
        mTestScheduler = TestScheduler()
        val testSchedulerProvider = TestSchedulerProvider(mTestScheduler)
        mInquiryPulsaDataRevampPresenter = InquiryPulsaDataRevampPresenter(
                testSchedulerProvider,
                compositeDisposable,
                mBRImoPrefRepository,
                mApiSource,
                categoryPfmSource,
                transaksiPfmSource,
                anggaranPfmSource
        )
        mInquiryPulsaDataRevampPresenter!!.setUrlConfirmation(urlKonfirmasi)
        mInquiryPulsaDataRevampPresenter!!.view = iInquiryPulsaRevampView
        Mockito.`when`(mBRImoPrefRepository!!.seqNumber).thenReturn(seqNum)
    }


    @Test
    fun getDataConfirmationPulsaRevampOnFailure() {
        val jsonRaw = ""

        val restResponse = Gson().fromJson(jsonRaw, RestResponse::class.java)
        Mockito.mockStatic(MapperHelper::class.java).use { mapperHelperMockedStatic ->
            mapperHelperMockedStatic.`when`<Any> { MapperHelper.stringToRestResponse(responseRawEncoded, seqNum) }.thenReturn(restResponse)
            mapperHelperMockedStatic.`when`<Any> { MapperHelper.getIdResponse(responseRawEncoded) }.thenReturn(jsonRaw)

            Mockito.`when`<Observable<String>>(mApiSource!!.getData(ArgumentMatchers.any(), ArgumentMatchers.any(KonfirmasiPulsaRequest::class.java), ArgumentMatchers.any())).thenReturn(Observable.just(responseRawEncoded))

            mInquiryPulsaDataRevampPresenter?.getDataConfirmationPulsaRevamp(mRefNum, mProviderId, mPhoneNumber, mAmount, mSaveAs, mItem, mType, mNote, mIsFromFast)

            mTestScheduler.triggerActions()
            Mockito.verify(iInquiryPulsaRevampView)?.onException(Mockito.any())
        }
    }

    @Test
    fun getDataConfirmationPulsaRevamp00() {
        val jsonRaw = """{ "code": "00", "description": "", "data": { "reference_number": "refnum" } }"""
        val restResponse = Gson().fromJson(jsonRaw, RestResponse::class.java)
        Mockito.mockStatic(GeneralHelper::class.java)
            .use { generalHelperMockedStatic ->
                generalHelperMockedStatic.`when`<Any> {
                    GeneralHelper.isContains(
                        R.array.response_code_success,
                        restResponse.code
                    )
                }.thenReturn(true)
                Mockito.mockStatic(MapperHelper::class.java)
                    .use { mapperHelperMockedStatic ->
                        mapperHelperMockedStatic.`when`<Any> { MapperHelper.stringToRestResponse(responseRawEncoded, seqNum) }.thenReturn(restResponse)
                        mapperHelperMockedStatic.`when`<Any> { MapperHelper.getIdResponse(responseRawEncoded) }.thenReturn(responseIdDummy)

                        Mockito.`when`<Observable<String>>(mApiSource!!.getData(ArgumentMatchers.any(), ArgumentMatchers.any(KonfirmasiPulsaRequest::class.java), ArgumentMatchers.any())).thenReturn(Observable.just(responseRawEncoded))

                        mInquiryPulsaDataRevampPresenter?.getDataConfirmationPulsaRevamp(mRefNum, mProviderId, mPhoneNumber, mAmount, mSaveAs, mItem, mType, mNote, mIsFromFast)

                        mTestScheduler.triggerActions()

                        Mockito.verify(iInquiryPulsaRevampView)?.onSuccessGetConfirmation(ArgumentMatchers.any(GeneralConfirmationResponse::class.java))
                    }
            }
    }

    @Test
    fun getDataConfirmationPulsaRevampOnApiCallError() {
        val jsonRaw = """{ "code": "05", "description": "", "data": { "reference_number": "refnum" } }"""
        val restResponse = Gson().fromJson(jsonRaw, RestResponse::class.java)
        Mockito.mockStatic(GeneralHelper::class.java)
            .use { generalHelperMockedStatic ->
                generalHelperMockedStatic.`when`<Any> {
                    GeneralHelper.isContains(
                        R.array.response_code_error_specific,
                        restResponse.code
                    )
                }.thenReturn(false)
                Mockito.mockStatic(MapperHelper::class.java)
                    .use { mapperHelperMockedStatic ->
                        mapperHelperMockedStatic.`when`<Any> { MapperHelper.stringToRestResponse(responseRawEncoded, seqNum) }.thenReturn(restResponse)
                        mapperHelperMockedStatic.`when`<Any> { MapperHelper.getIdResponse(responseRawEncoded) }.thenReturn(responseIdDummy)

                        Mockito.`when`<Observable<String>>(mApiSource!!.getData(ArgumentMatchers.any(), ArgumentMatchers.any(KonfirmasiPulsaRequest::class.java), ArgumentMatchers.any())).thenReturn(Observable.just(responseRawEncoded))

                        mInquiryPulsaDataRevampPresenter?.getDataConfirmationPulsaRevamp(mRefNum, mProviderId, mPhoneNumber, mAmount, mSaveAs, mItem, mType, mNote, mIsFromFast)

                        mTestScheduler.triggerActions()

                        Mockito.verify(iInquiryPulsaRevampView)?.onSessionEnd(Mockito.any())

                    }
            }
    }

    @Test
    fun setUrlConfirmation() {
        mInquiryPulsaDataRevampPresenter?.setUrlConfirmation(urlKonfirmasi)
        assertEquals(urlKonfirmasi, mInquiryPulsaDataRevampPresenter?.url)
    }
}