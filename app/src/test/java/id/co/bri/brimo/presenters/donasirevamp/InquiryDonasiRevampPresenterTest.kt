package id.co.bri.brimo.presenters.donasirevamp

import com.google.gson.Gson
import id.co.bri.brimo.contract.IPresenter.donasirevamp.IInquiryDonasiRevampPresenter
import id.co.bri.brimo.contract.IView.donasirevamp.IFormDonasiRevampView
import id.co.bri.brimo.contract.IView.donasirevamp.IInquiryDonasiRevampView
import id.co.bri.brimo.data.api.ApiSource
import id.co.bri.brimo.data.preference.BRImoPrefSource
import id.co.bri.brimo.data.repository.anggaran.AnggaranPfmSource
import id.co.bri.brimo.data.repository.category.CategoryPfmSource
import id.co.bri.brimo.data.repository.transaksi.TransaksiPfmSource
import id.co.bri.brimo.domain.converter.MapperHelper
import id.co.bri.brimo.models.apimodel.request.donasirevamp.ConfirmationDonasiRevampRequest
import id.co.bri.brimo.models.apimodel.response.RestResponse
import id.co.bri.brimo.presenters.domain.helper.rx.TestSchedulerProvider
import io.reactivex.Observable
import io.reactivex.disposables.CompositeDisposable
import io.reactivex.schedulers.TestScheduler
import org.junit.Before
import org.junit.Test
import org.junit.runner.RunWith
import org.mockito.ArgumentMatchers.any
import org.mockito.Mock
import org.mockito.Mockito
import org.mockito.junit.MockitoJUnitRunner


@RunWith(MockitoJUnitRunner::class)
class InquiryDonasiRevampPresenterTest {

    @Mock
    var iInquiryDonasiRevampView: IInquiryDonasiRevampView? = null

    @Mock
    lateinit var mBRImoPrefRepository: BRImoPrefSource

    @Mock
    lateinit var mApiSource: ApiSource

    @Mock
    lateinit var categoryPfmSource: CategoryPfmSource

    @Mock
    lateinit var transaksiPfmSource: TransaksiPfmSource

    @Mock
    lateinit var anggaranPfmSource: AnggaranPfmSource

    @Mock
    lateinit var compositeDisposable: CompositeDisposable

    private var mInquiryDonasiRevampPresenter: InquiryDonasiRevampPresenter<IInquiryDonasiRevampView>? = null

    private var mTestScheduler = TestScheduler()

    private val seqNum = "40"
    private val responseRawEncoded =
        "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\""
    private val responseIdDummy = "12314324324"
    private val mAccNum = "123"
    private val mPin = "123456"
    private val mType = "T"
    private val mConfirmationUrl: String =""
    private var mUrl:String=""
    private var mUrlListDeposito:String=""



    @Before
    @Throws(Exception::class)
    fun setUp() {
        mTestScheduler = TestScheduler()
        val testSchedulerProvider = TestSchedulerProvider(mTestScheduler)
        mInquiryDonasiRevampPresenter = InquiryDonasiRevampPresenter(
            testSchedulerProvider,
            compositeDisposable,
            mBRImoPrefRepository,
            mApiSource,
            categoryPfmSource,
            transaksiPfmSource,
            anggaranPfmSource
        )
        mInquiryDonasiRevampPresenter?.view = iInquiryDonasiRevampView
        Mockito.`when`(mBRImoPrefRepository.seqNumber).thenReturn(seqNum)
    }


    @Test
    fun getDataConfirmation() {
        val jsonRaw = """{ "code": "00", "description": "", "data": { "reference_number": "refnum" } }"""

        mInquiryDonasiRevampPresenter?.setUrlConfirmation(mConfirmationUrl)
        val restResponse = Gson().fromJson(jsonRaw, RestResponse::class.java)
        Mockito.mockStatic(MapperHelper::class.java).use { mapperHelperMockedStatic ->
            mapperHelperMockedStatic.`when`<Any> {
                MapperHelper.stringToRestResponse(
                    responseRawEncoded,
                    seqNum
                )
            }.thenReturn(restResponse)
            mapperHelperMockedStatic.`when`<Any> { MapperHelper
                .getIdResponse(responseRawEncoded) }.thenReturn("")
            Mockito.`when`<Observable<String>>(
                mApiSource.getData(mConfirmationUrl, ConfirmationDonasiRevampRequest(), seqNum)
            ).thenReturn(Observable.just(responseRawEncoded))
            mInquiryDonasiRevampPresenter?.getDataConfirmation(ConfirmationDonasiRevampRequest() )

            mTestScheduler.triggerActions()
            Mockito.verify(iInquiryDonasiRevampView)?.onException(any())

        }
    }
}