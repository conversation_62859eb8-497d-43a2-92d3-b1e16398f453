package id.co.bri.brimo.presenters.lupausername

import com.google.gson.Gson
import id.co.bri.brimo.R
import id.co.bri.brimo.contract.IView.lupausername.IVerifikasiOtpEmailView
import id.co.bri.brimo.data.api.ApiSource
import id.co.bri.brimo.data.preference.BRImoPrefSource
import id.co.bri.brimo.data.repository.anggaran.AnggaranPfmSource
import id.co.bri.brimo.data.repository.category.CategoryPfmSource
import id.co.bri.brimo.data.repository.transaksi.TransaksiPfmSource
import id.co.bri.brimo.domain.converter.MapperHelper
import id.co.bri.brimo.domain.helpers.GeneralHelper
import id.co.bri.brimo.models.apimodel.request.GeneralResendOtpRequest
import id.co.bri.brimo.models.apimodel.request.forgetuserpass.ValidateOtpUserPassReq
import id.co.bri.brimo.models.apimodel.response.OtpForgotUsernameRes
import id.co.bri.brimo.models.apimodel.response.RestResponse
import id.co.bri.brimo.models.apimodel.response.forgetuserpass.OtpEmailRes
import id.co.bri.brimo.presenters.domain.helper.rx.TestSchedulerProvider
import io.reactivex.Observable
import io.reactivex.disposables.CompositeDisposable
import io.reactivex.schedulers.TestScheduler
import org.junit.Assert
import org.junit.Before
import org.junit.Test
import org.junit.runner.RunWith
import org.mockito.Mock
import org.mockito.Mockito
import org.mockito.junit.MockitoJUnitRunner

@RunWith(MockitoJUnitRunner::class)
class VerifikasiOtpEmailPresenterTest {
    @Mock
    lateinit var mockView: IVerifikasiOtpEmailView

    @Mock
    lateinit var mockPrefRepository: BRImoPrefSource

    @Mock
    lateinit var mockApiSource: ApiSource

    @Mock
    lateinit var mockCompositeDisposable: CompositeDisposable

    @Mock
    lateinit var mockCategoryPfmSource: CategoryPfmSource

    @Mock
    lateinit var mockTransactionPfmSource: TransaksiPfmSource

    @Mock
    lateinit var mockAnggaranPfmSource: AnggaranPfmSource

    private var mockTestScheduler = TestScheduler()
    private lateinit var presenter: VerifikasiOtpEmailPresenter<IVerifikasiOtpEmailView>
    private val seqNum = "40"
    private val mUrlValidate = "KTneckqZsuOBXjEbRF+wCjYzxZZEse8MfOV/heV+6i8="
    private val mUrlResend = "dBCYs2EJcHHIuzHwoAKANEvKNlrQxwoRTe4zY/8GYxY="
    private val responseIdDummy = "12314324324"
    private val responseRawEncoded = "ac740a16f65302d1440dcbb1fc"
    private val resendReq = GeneralResendOtpRequest("12345")
    private val validateReq = ValidateOtpUserPassReq("123", "123", "123")

    @Before
    fun setup() {
        mockTestScheduler = TestScheduler()
        val testSchedulerProvider = TestSchedulerProvider(mockTestScheduler)
        presenter = VerifikasiOtpEmailPresenter(
            testSchedulerProvider,
            mockCompositeDisposable,
            mockPrefRepository,
            mockApiSource,
            mockCategoryPfmSource,
            mockTransactionPfmSource,
            mockAnggaranPfmSource
        )
        presenter.setUrlValidate(mUrlValidate)
        presenter.setUrlResend(mUrlResend)
        presenter.view = mockView
        Mockito.`when`(mockPrefRepository.seqNumber).thenReturn(seqNum)
    }

    @Test
    @Throws(Exception::class)
    fun checkViewTest() {
        Assert.assertEquals(mockView, presenter.view)
    }

    @Test
    fun getResendOnFailure() {
        val jsonRaw = ""

        val restResponse = Gson().fromJson(jsonRaw, RestResponse::class.java)
        Mockito.mockStatic<MapperHelper>(MapperHelper::class.java).use { mapperHelperMockedStatic ->
            mapperHelperMockedStatic.`when`<Any> {
                MapperHelper.stringToRestResponse(
                    responseRawEncoded,
                    seqNum
                )
            }.thenReturn(restResponse)
            mapperHelperMockedStatic.`when`<Any> { MapperHelper.getIdResponse(responseRawEncoded) }
                .thenReturn("")
            Mockito.`when`<Observable<String>>(
                mockApiSource.getData(
                    presenter.urlResend,
                    resendReq,
                    seqNum
                )
            ).thenReturn(Observable.just<String>(responseRawEncoded))
            presenter.resendOtp(resendReq)
            mockTestScheduler.triggerActions()
            Mockito.verify(mockView)?.hideProgress()
        }
    }

    @Test
    fun getResendOnApiCallError() {
        val jsonRaw =
            """{ "code": "05", "description": "", "data": { "reference_number": "refnum" } }"""
        val restResponse = Gson().fromJson(jsonRaw, RestResponse::class.java)
        Mockito.mockStatic(GeneralHelper::class.java)
            .use { generalHelperMockedStatic ->
                generalHelperMockedStatic.`when`<Any> {
                    GeneralHelper.isContains(
                        R.array.response_code_error_specific,
                        restResponse.code
                    )
                }.thenReturn(false)
                Mockito.mockStatic(MapperHelper::class.java)
                    .use { mapperHelperMockedStatic ->
                        mapperHelperMockedStatic.`when`<Any> {
                            MapperHelper.stringToRestResponse(
                                responseRawEncoded,
                                seqNum
                            )
                        }.thenReturn(restResponse)
                        mapperHelperMockedStatic.`when`<Any> {
                            MapperHelper.getIdResponse(
                                responseRawEncoded
                            )
                        }.thenReturn(responseIdDummy)

                        Mockito.`when`<Observable<String>>(
                            mockApiSource.getData(
                                presenter.urlResend,
                                resendReq,
                                seqNum
                            )
                        ).thenReturn(Observable.just<String>(responseRawEncoded))

                        presenter.resendOtp(resendReq)
                        mockTestScheduler.triggerActions()
                    }
            }
    }


    @Test
    fun getResendOnApiCallSuccess00() {
        val jsonRaw =
            """{ "code": "00", "description": "", "data": {} }"""
        val restResponse = Gson().fromJson(jsonRaw, RestResponse::class.java)
        Mockito.mockStatic<GeneralHelper>(GeneralHelper::class.java)
            .use { generalHelperMockedStatic ->
                generalHelperMockedStatic.`when`<Any> {
                    GeneralHelper.isContains(
                        R.array.response_code_success,
                        restResponse.code
                    )
                }.thenReturn(true)
                Mockito.mockStatic<MapperHelper>(MapperHelper::class.java)
                    .use { mapperHelperMockedStatic ->
                        mapperHelperMockedStatic.`when`<Any> {
                            MapperHelper.stringToRestResponse(
                                responseRawEncoded,
                                seqNum
                            )
                        }.thenReturn(restResponse)
                        mapperHelperMockedStatic.`when`<Any> {
                            MapperHelper.getIdResponse(
                                responseRawEncoded
                            )
                        }
                            .thenReturn(responseIdDummy)
                        Mockito.`when`<Observable<String>>(
                            mockApiSource.getData(
                                presenter.urlResend,
                                resendReq,
                                seqNum
                            )
                        ).thenReturn(Observable.just<String>(responseRawEncoded))
                        presenter.resendOtp(resendReq)
                        mockTestScheduler.triggerActions()
                        Mockito.verify(mockView)?.hideProgress()
                        Mockito.verify(mockView)?.onSuccessResendOtp(
                            Mockito.any<OtpEmailRes>(
                                OtpEmailRes::class.java
                            )
                        )
                    }
            }
    }

    @Test
    fun getValidateOnFailure() {
        val jsonRaw = ""

        val restResponse = Gson().fromJson(jsonRaw, RestResponse::class.java)
        Mockito.mockStatic<MapperHelper>(MapperHelper::class.java).use { mapperHelperMockedStatic ->
            mapperHelperMockedStatic.`when`<Any> {
                MapperHelper.stringToRestResponse(
                    responseRawEncoded,
                    seqNum
                )
            }.thenReturn(restResponse)
            mapperHelperMockedStatic.`when`<Any> { MapperHelper.getIdResponse(responseRawEncoded) }
                .thenReturn("")
            Mockito.`when`<Observable<String>>(
                mockApiSource.getData(
                    presenter.urlValidate,
                    validateReq,
                    seqNum
                )
            ).thenReturn(Observable.just<String>(responseRawEncoded))
            presenter.validateOtp(validateReq, true)
            mockTestScheduler.triggerActions()
            Mockito.verify(mockView)?.hideProgress()
        }
    }

    @Test
    fun getValidateOnApiCallError() {
        val jsonRaw =
            """{ "code": "05", "description": "", "data": { "reference_number": "refnum" } }"""
        val restResponse = Gson().fromJson(jsonRaw, RestResponse::class.java)
        Mockito.mockStatic(GeneralHelper::class.java)
            .use { generalHelperMockedStatic ->
                generalHelperMockedStatic.`when`<Any> {
                    GeneralHelper.isContains(
                        R.array.response_code_error_specific,
                        restResponse.code
                    )
                }.thenReturn(false)
                Mockito.mockStatic(MapperHelper::class.java)
                    .use { mapperHelperMockedStatic ->
                        mapperHelperMockedStatic.`when`<Any> {
                            MapperHelper.stringToRestResponse(
                                responseRawEncoded,
                                seqNum
                            )
                        }.thenReturn(restResponse)
                        mapperHelperMockedStatic.`when`<Any> {
                            MapperHelper.getIdResponse(
                                responseRawEncoded
                            )
                        }.thenReturn(responseIdDummy)

                        Mockito.`when`<Observable<String>>(
                            mockApiSource.getData(
                                presenter.urlValidate,
                                validateReq,
                                seqNum
                            )
                        ).thenReturn(Observable.just<String>(responseRawEncoded))

                        presenter.validateOtp(validateReq, true)
                        mockTestScheduler.triggerActions()
                    }
            }
    }


    @Test
    fun getValidateOnApiCallSuccess00() {
        val jsonRaw =
            """{ "code": "00", "description": "", "data": {} }"""
        val restResponse = Gson().fromJson(jsonRaw, RestResponse::class.java)
        Mockito.mockStatic<GeneralHelper>(GeneralHelper::class.java)
            .use { generalHelperMockedStatic ->
                generalHelperMockedStatic.`when`<Any> {
                    GeneralHelper.isContains(
                        R.array.response_code_success,
                        restResponse.code
                    )
                }.thenReturn(true)
                Mockito.mockStatic<MapperHelper>(MapperHelper::class.java)
                    .use { mapperHelperMockedStatic ->
                        mapperHelperMockedStatic.`when`<Any> {
                            MapperHelper.stringToRestResponse(
                                responseRawEncoded,
                                seqNum
                            )
                        }.thenReturn(restResponse)
                        mapperHelperMockedStatic.`when`<Any> {
                            MapperHelper.getIdResponse(
                                responseRawEncoded
                            )
                        }
                            .thenReturn(responseIdDummy)
                        Mockito.`when`<Observable<String>>(
                            mockApiSource.getData(
                                presenter.urlValidate,
                                validateReq,
                                seqNum
                            )
                        ).thenReturn(Observable.just<String>(responseRawEncoded))
                        presenter.validateOtp(validateReq, true)
                        mockTestScheduler.triggerActions()
                        Mockito.verify(mockView)?.hideProgress()
                        Mockito.verify(mockView)?.onSuccessValidateOtp(
                            Mockito.any<OtpForgotUsernameRes>(
                                OtpForgotUsernameRes::class.java
                            )
                        )
                    }
            }
    }
}