package id.co.bri.brimo.presenters.pajakhoreka


import com.google.gson.Gson
import id.co.bri.brimo.R
import id.co.bri.brimo.contract.IView.pajakhoreka.IFormPajakHorekaView
import id.co.bri.brimo.contract.IView.signal.IFormSignalView
import id.co.bri.brimo.data.api.ApiSource
import id.co.bri.brimo.data.preference.BRImoPrefSource
import id.co.bri.brimo.data.repository.anggaran.AnggaranPfmSource
import id.co.bri.brimo.data.repository.category.CategoryPfmSource
import id.co.bri.brimo.data.repository.transaksi.TransaksiPfmSource
import id.co.bri.brimo.domain.converter.MapperHelper
import id.co.bri.brimo.domain.helpers.GeneralHelper
import id.co.bri.brimo.models.apimodel.request.signal.InquirySignalRequest
import id.co.bri.brimo.models.apimodel.response.*
import id.co.bri.brimo.models.apimodel.response.pajakhoreka.FormPajakHorekaResponse
import id.co.bri.brimo.models.apimodel.response.signal.FormSignalResponse
import id.co.bri.brimo.presenters.domain.helper.rx.TestSchedulerProvider
import io.reactivex.Observable
import io.reactivex.disposables.CompositeDisposable
import io.reactivex.schedulers.TestScheduler
import org.junit.Assert
import org.junit.Before
import org.junit.Test
import org.junit.runner.RunWith
import org.mockito.ArgumentMatchers
import org.mockito.Mock
import org.mockito.Mockito.*
import org.mockito.junit.MockitoJUnitRunner


@RunWith(MockitoJUnitRunner::class)
class FormPajakHorekaPresenterTest {
    @Mock
    var iIFormPajakHorekaView: IFormPajakHorekaView? = null

    @Mock
    var mBRImoPrefRepository: BRImoPrefSource? = null

    @Mock
    var mApiSource: ApiSource? = null

    @Mock
    var categoryPfmSource: CategoryPfmSource? = null

    @Mock
    var transaksiPfmSource: TransaksiPfmSource? = null

    @Mock
    var anggaranPfmSource: AnggaranPfmSource? = null

    @Mock
    var compositeDisposable: CompositeDisposable? = null

    private var mFormPajakHorekaPresenter: FormPajakHorekaPresenter<IFormPajakHorekaView>? = null
    private var mTestScheduler = TestScheduler()

    private val seqNum = "40"
    private val responseRawEncoded = "ac740a16f65302d1440dcbb1fc671af1z8Wx8DB"
    private val responseIdDummy = "12314324324"
    private val mUrl = "mUrl"
    private var mUrlInquiry: String = "mUrlInquiry"
    private var mPaymentNum: String = "123456"


    @Before
    @Throws(Exception::class)
    fun setUp() {
        mTestScheduler = TestScheduler()
        val testSchedulerProvider = TestSchedulerProvider(mTestScheduler)
        mFormPajakHorekaPresenter = FormPajakHorekaPresenter(
            testSchedulerProvider,
            compositeDisposable,
            mBRImoPrefRepository,
            mApiSource,
            categoryPfmSource,
            transaksiPfmSource,
            anggaranPfmSource
        )
        mFormPajakHorekaPresenter!!.view = iIFormPajakHorekaView
        `when`(mBRImoPrefRepository!!.seqNumber).thenReturn(seqNum)
        mFormPajakHorekaPresenter?.setUrlForm(mUrl)
        mFormPajakHorekaPresenter?.setUrlInquiry(mUrlInquiry)
    }

    @Test
    fun checkView(){
        Assert.assertEquals(mFormPajakHorekaPresenter?.view, iIFormPajakHorekaView)
    }

    @Test
    fun setUrlFormTrue(){
        mFormPajakHorekaPresenter?.setUrlForm(mUrl)
        Assert.assertEquals(mFormPajakHorekaPresenter?.mUrlForm, mUrl)
    }

    @Test
    fun setUrlInquiryTrue(){
        mFormPajakHorekaPresenter?.setUrlInquiry(mUrlInquiry)
        Assert.assertEquals(mFormPajakHorekaPresenter?.mUrlInquiry, mUrlInquiry)
    }

    @Test
    fun getDataFormOnFailure() {
        val jsonRaw = ""

        val restResponse = Gson().fromJson(jsonRaw, RestResponse::class.java)
        mockStatic(MapperHelper::class.java).use { mapperHelperMockedStatic ->
            mapperHelperMockedStatic.`when`<Any> { MapperHelper.stringToRestResponse(responseRawEncoded, seqNum) }.thenReturn(restResponse)
            mapperHelperMockedStatic.`when`<Any> { MapperHelper.getIdResponse(responseRawEncoded) }.thenReturn("")

            `when`<Observable<String>>(mApiSource!!.getDataTanpaRequest(mUrl, seqNum)).thenReturn(
                Observable.just(responseRawEncoded))

            mFormPajakHorekaPresenter?.getDataForm()
            mTestScheduler.triggerActions()
            verify(iIFormPajakHorekaView)?.onException(any())
        }
    }

    @Test
    fun getDataFormOnSuccess() {
        val jsonRaw = """{ "code": "00", "description": "", "data": { "reference_number": "refnum" } }"""
        val restResponse = Gson().fromJson(jsonRaw, RestResponse::class.java)
        mockStatic(GeneralHelper::class.java)
            .use { generalHelperMockedStatic ->
                generalHelperMockedStatic.`when`<Any> { GeneralHelper.isContains(R.array.response_code_success, restResponse.code) }.thenReturn(true)
                mockStatic(MapperHelper::class.java)
                    .use { mapperHelperMockedStatic ->
                        mapperHelperMockedStatic.`when`<Any> { MapperHelper.stringToRestResponse(responseRawEncoded, seqNum) }.thenReturn(restResponse)
                        mapperHelperMockedStatic.`when`<Any> { MapperHelper.getIdResponse(responseRawEncoded) }.thenReturn(responseIdDummy)

                        `when`<Observable<String>>(mApiSource!!.getDataTanpaRequest(mUrl, seqNum)).thenReturn(
                            Observable.just(responseRawEncoded))

                        mFormPajakHorekaPresenter?.getDataForm()

                        mTestScheduler.triggerActions()

                        verify(iIFormPajakHorekaView)?.onSuccessGetForm(ArgumentMatchers.any(FormPajakHorekaResponse::class.java))
                    }
            }
    }

    @Test
    fun getDataFormOnApiCallError() {
        val jsonRaw = """{ "code": "05", "description": "", "data": { "reference_number": "refnum" } }"""
        val restResponse = Gson().fromJson(jsonRaw, RestResponse::class.java)
        mockStatic(GeneralHelper::class.java)
            .use { generalHelperMockedStatic ->
                generalHelperMockedStatic.`when`<Any> { GeneralHelper.isContains(R.array.response_code_error_specific, restResponse.code) }.thenReturn(false)
                mockStatic(MapperHelper::class.java)
                    .use { mapperHelperMockedStatic ->
                        mapperHelperMockedStatic.`when`<Any> { MapperHelper.stringToRestResponse(responseRawEncoded, seqNum) }.thenReturn(restResponse)
                        mapperHelperMockedStatic.`when`<Any> { MapperHelper.getIdResponse(responseRawEncoded) }.thenReturn(responseIdDummy)

                        `when`<Observable<String>>(mApiSource!!.getDataTanpaRequest(mUrl, seqNum)).thenReturn(
                            Observable.just(responseRawEncoded))

                        mFormPajakHorekaPresenter?.getDataForm()

                        mTestScheduler.triggerActions()

                        verify(iIFormPajakHorekaView)?.onException(any())

                    }
            }
    }

    /*getInquiry*/

    @Test
    fun getInquiryOnFailure() {
        val jsonRaw = ""

        val restResponse = Gson().fromJson(jsonRaw, RestResponse::class.java)
        mockStatic(MapperHelper::class.java).use { mapperHelperMockedStatic ->
            mapperHelperMockedStatic.`when`<Any> { MapperHelper.stringToRestResponse(responseRawEncoded, seqNum) }.thenReturn(restResponse)
            mapperHelperMockedStatic.`when`<Any> { MapperHelper.getIdResponse(responseRawEncoded) }.thenReturn("")

            `when`<Observable<String>>(mApiSource!!.getData(any(), ArgumentMatchers.any(InquirySignalRequest::class.java), any())).thenReturn(
                Observable.just(responseRawEncoded))

            mFormPajakHorekaPresenter?.getInquiry(mPaymentNum)
            mTestScheduler.triggerActions()
            verify(iIFormPajakHorekaView)?.onException(any())
        }
    }

    @Test
    fun getInquiryOnSuccess() {
        val jsonRaw = """{ "code": "00", "description": "", "data": { "reference_number": "refnum" } }"""
        val restResponse = Gson().fromJson(jsonRaw, RestResponse::class.java)
        mockStatic(GeneralHelper::class.java)
            .use { generalHelperMockedStatic ->
                generalHelperMockedStatic.`when`<Any> { GeneralHelper.isContains(R.array.response_code_success, restResponse.code) }.thenReturn(true)
                mockStatic(MapperHelper::class.java)
                    .use { mapperHelperMockedStatic ->
                        mapperHelperMockedStatic.`when`<Any> { MapperHelper.stringToRestResponse(responseRawEncoded, seqNum) }.thenReturn(restResponse)
                        mapperHelperMockedStatic.`when`<Any> { MapperHelper.getIdResponse(responseRawEncoded) }.thenReturn(responseIdDummy)

                        `when`<Observable<String>>(mApiSource!!.getData(any(), ArgumentMatchers.any(InquirySignalRequest::class.java), any())).thenReturn(
                            Observable.just(responseRawEncoded))

                        mFormPajakHorekaPresenter?.getInquiry(mPaymentNum)

                        mTestScheduler.triggerActions()
                        verify(iIFormPajakHorekaView)?.hideProgress()
                        verify(iIFormPajakHorekaView)?.onSuccessInquiry(ArgumentMatchers.any(InquiryBrivaRevampResponse::class.java))
                    }
            }
    }

    @Test
    fun getInquiryOnApiCallError() {
        val jsonRaw = """{ "code": "05", "description": "", "data": { "reference_number": "refnum" } }"""
        val restResponse = Gson().fromJson(jsonRaw, RestResponse::class.java)
        mockStatic(GeneralHelper::class.java)
            .use { generalHelperMockedStatic ->
                generalHelperMockedStatic.`when`<Any> { GeneralHelper.isContains(R.array.response_code_error_specific, restResponse.code) }.thenReturn(false)
                mockStatic(MapperHelper::class.java)
                    .use { mapperHelperMockedStatic ->
                        mapperHelperMockedStatic.`when`<Any> { MapperHelper.stringToRestResponse(responseRawEncoded, seqNum) }.thenReturn(restResponse)
                        mapperHelperMockedStatic.`when`<Any> { MapperHelper.getIdResponse(responseRawEncoded) }.thenReturn(responseIdDummy)

                        `when`<Observable<String>>(mApiSource!!.getData(any(), ArgumentMatchers.any(InquirySignalRequest::class.java), any())).thenReturn(
                            Observable.just(responseRawEncoded))

                        mFormPajakHorekaPresenter?.getInquiry(mPaymentNum)

                        mTestScheduler.triggerActions()

                        verify(iIFormPajakHorekaView)?.onSessionEnd(any())

                    }
            }
    }
}