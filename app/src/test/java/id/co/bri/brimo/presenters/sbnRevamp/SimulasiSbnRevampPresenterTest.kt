package id.co.bri.brimo.presenters.sbnRevamp

import com.google.gson.Gson
import id.co.bri.brimo.R
import id.co.bri.brimo.contract.IView.sbnrevamp.ISimulasiSbnView
import id.co.bri.brimo.data.api.ApiSource
import id.co.bri.brimo.data.preference.BRImoPrefSource
import id.co.bri.brimo.data.repository.anggaran.AnggaranPfmSource
import id.co.bri.brimo.data.repository.category.CategoryPfmSource
import id.co.bri.brimo.data.repository.transaksi.TransaksiPfmSource
import id.co.bri.brimo.domain.converter.MapperHelper
import id.co.bri.brimo.domain.helpers.GeneralHelper
import id.co.bri.brimo.models.apimodel.request.sbnrevamp.SbnSimulasiRequest
import id.co.bri.brimo.models.apimodel.response.RestResponse
import id.co.bri.brimo.presenters.domain.helper.rx.TestSchedulerProvider
import id.co.bri.brimo.presenters.sbnrevamp.SimulasiSbnPresenter
import io.reactivex.Observable
import io.reactivex.disposables.CompositeDisposable
import io.reactivex.schedulers.TestScheduler
import org.junit.After
import org.junit.Assert
import org.junit.Before
import org.junit.Test
import org.junit.runner.RunWith
import org.mockito.ArgumentMatchers
import org.mockito.Mock
import org.mockito.Mockito
import org.mockito.junit.MockitoJUnitRunner

@RunWith(MockitoJUnitRunner::class)
class SimulasiSbnRevampPresenterTest {

    @Mock
    lateinit var ISimulasiSbnView: ISimulasiSbnView

    @Mock
    lateinit var mBRImoPrefRepository: BRImoPrefSource

    @Mock
    lateinit var mApiSource: ApiSource

    @Mock
    lateinit var categoryPfmSource: CategoryPfmSource

    @Mock
    lateinit var transaksiPfmSource: TransaksiPfmSource

    @Mock
    lateinit var anggaranPfmSource: AnggaranPfmSource

    @Mock
    lateinit var compositeDisposable: CompositeDisposable



    private var mSimulasiSbnPresenter: SimulasiSbnPresenter<ISimulasiSbnView>? = null
    private var mTestScheduler = TestScheduler()
    private val seqNum = "40"
    private val responseRawEncoded =
            "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"
    private val responseIdDummy = "12314324324"
    private val mUrlSimulasiSbn = "mUrlSimulasiSbn"
    private val mSeri = "mSeri"
    private val mAmount = "mAmount"
    private val mIdSeri = 0


    @Before
    @Throws(Exception::class)
    fun setUp() {
        mTestScheduler = TestScheduler()
        val testSchedulerProvider = TestSchedulerProvider(mTestScheduler)
        mSimulasiSbnPresenter = SimulasiSbnPresenter(
                testSchedulerProvider,
                compositeDisposable,
                mBRImoPrefRepository,
                mApiSource,
                categoryPfmSource,
                transaksiPfmSource,
                anggaranPfmSource
        )


        mSimulasiSbnPresenter?.mUrlSimulasiSbn = mUrlSimulasiSbn
        mSimulasiSbnPresenter!!.view = ISimulasiSbnView
        Mockito.`when`(mBRImoPrefRepository.seqNumber).thenReturn(seqNum)

    }

    @After
    fun setupAfter() {

    }

    @Test
    fun setUrlSimulasi(){
        mSimulasiSbnPresenter!!.mUrlSimulasiSbn = mUrlSimulasiSbn
        Assert.assertEquals( mSimulasiSbnPresenter!!.mUrlSimulasiSbn, mUrlSimulasiSbn)
    }


    @Test
    fun getDataSimulasiSbnFailure() {
        val jsonRaw = ""

        val restResponse = Gson().fromJson(jsonRaw, RestResponse::class.java)
        Mockito.mockStatic(MapperHelper::class.java).use { mapperHelperMockedStatic ->
            mapperHelperMockedStatic.`when`<Any> {
                MapperHelper.stringToRestResponse(
                        responseRawEncoded,
                        seqNum
                )
            }.thenReturn(restResponse)
            mapperHelperMockedStatic.`when`<Any> { MapperHelper.getIdResponse(responseRawEncoded) }.thenReturn("")
            Mockito.`when`<Observable<String>>(mApiSource.getData(ArgumentMatchers.any(), ArgumentMatchers.any(SbnSimulasiRequest::class.java), ArgumentMatchers.any())).thenReturn(Observable.just(responseRawEncoded))
            val simulasiRequest = SbnSimulasiRequest(mSeri, mAmount)
            mSimulasiSbnPresenter?.getDataSimulasiSbn(simulasiRequest)
            mTestScheduler.triggerActions()
            Mockito.verify(ISimulasiSbnView)?.onException(Mockito.any())
        }
    }

    @Test
    fun getDataSimulasiSbn00() {
        val jsonRaw = """{ "code": "00", "description": "", "data": { "reference_number": "refnum" } }"""
        val restResponse = Gson().fromJson(jsonRaw, RestResponse::class.java)
        Mockito.mockStatic(GeneralHelper::class.java)
                .use { generalHelperMockedStatic ->
                    generalHelperMockedStatic.`when`<Any> { GeneralHelper.isContains(R.array.response_code_success, restResponse.code) }.thenReturn(true)
                    Mockito.mockStatic(MapperHelper::class.java)
                            .use { mapperHelperMockedStatic ->
                                mapperHelperMockedStatic.`when`<Any> { MapperHelper.stringToRestResponse(responseRawEncoded, seqNum) }.thenReturn(restResponse)
                                mapperHelperMockedStatic.`when`<Any> { MapperHelper.getIdResponse(responseRawEncoded) }.thenReturn(responseIdDummy)

                                Mockito.`when`<Observable<String>>(mApiSource.getData(ArgumentMatchers.any(), ArgumentMatchers.any(SbnSimulasiRequest::class.java), ArgumentMatchers.any())).thenReturn(Observable.just(responseRawEncoded))

                                val simulasiRequest = SbnSimulasiRequest(mSeri, mAmount)
                                mSimulasiSbnPresenter?.getDataSimulasiSbn(simulasiRequest)

                                mTestScheduler.triggerActions()
                            }
                }
    }

}