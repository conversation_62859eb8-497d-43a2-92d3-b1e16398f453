package id.co.bri.brimo.presenters.qrtransfer

import com.google.gson.Gson
import id.co.bri.brimo.R
import id.co.bri.brimo.contract.IView.qrtransfer.IQrTransferCodeView
import id.co.bri.brimo.data.api.ApiSource
import id.co.bri.brimo.data.preference.BRImoPrefSource
import id.co.bri.brimo.data.repository.anggaran.AnggaranPfmSource
import id.co.bri.brimo.data.repository.category.CategoryPfmSource
import id.co.bri.brimo.data.repository.transaksi.TransaksiPfmSource
import id.co.bri.brimo.domain.converter.MapperHelper
import id.co.bri.brimo.domain.helpers.GeneralHelper
import id.co.bri.brimo.models.apimodel.request.BaseFormRequest
import id.co.bri.brimo.models.apimodel.response.*
import id.co.bri.brimo.presenters.domain.helper.rx.TestSchedulerProvider
import io.reactivex.Observable
import io.reactivex.disposables.CompositeDisposable
import io.reactivex.schedulers.TestScheduler
import org.junit.After
import org.junit.Assert
import org.junit.Before
import org.junit.Test
import org.junit.runner.RunWith
import org.mockito.ArgumentMatchers
import org.mockito.Mock
import org.mockito.Mockito.*
import org.mockito.junit.MockitoJUnitRunner


@RunWith(MockitoJUnitRunner::class)
class QrTransferCodePresenterTest {
    @Mock
    var iQrTransferCodeView: IQrTransferCodeView? = null

    @Mock
    var mBRImoPrefRepository: BRImoPrefSource? = null

    @Mock
    var mApiSource: ApiSource? = null

    @Mock
    var categoryPfmSource: CategoryPfmSource? = null

    @Mock
    var transaksiPfmSource: TransaksiPfmSource? = null

    @Mock
    var anggaranPfmSource: AnggaranPfmSource? = null

    @Mock
    var compositeDisposable: CompositeDisposable? = null


    private var mQrTransferCodePresenter: QrTransferCodePresenter<IQrTransferCodeView>? = null
    private var mTestScheduler = TestScheduler()
    private val seqNum = "40"
    private val responseRawEncoded = "ac740a16f65302d1440dcbb1fc"
    private val responseIdDummy = "12314324324"
    private val mUrlDelete = "mUrlDelete"
    

    @Before
    @Throws(Exception::class)
    fun setUp() {
        mTestScheduler = TestScheduler()
        val testSchedulerProvider = TestSchedulerProvider(mTestScheduler)
        mQrTransferCodePresenter = QrTransferCodePresenter(
            testSchedulerProvider,
            compositeDisposable,
            mBRImoPrefRepository,
            mApiSource,
            categoryPfmSource,
            transaksiPfmSource,
            anggaranPfmSource
        )

        mQrTransferCodePresenter?.onUrlDeleteQr(mUrlDelete)
        mQrTransferCodePresenter!!.view = iQrTransferCodeView
        `when`(mBRImoPrefRepository!!.seqNumber).thenReturn(seqNum)
    }

    @After
    fun setupAfter() {

    }

    @Test
    fun checkNullUrlInquiryTrue(){
        mQrTransferCodePresenter?.onUrlDeleteQr(null)
        Assert.assertEquals(null, mQrTransferCodePresenter?.urlDelete)
    }

    @Test
    fun setInquiryUrlTrue(){
        mQrTransferCodePresenter?.onUrlDeleteQr(mUrlDelete)
        Assert.assertEquals(mUrlDelete, mQrTransferCodePresenter?.urlDelete)
    }

    @Test
    fun getDeleteQrOnFailure() {
        val jsonRaw = ""

        val restResponse = Gson().fromJson(jsonRaw, RestResponse::class.java)
        mockStatic(MapperHelper::class.java).use { mapperHelperMockedStatic ->
            mapperHelperMockedStatic.`when`<Any> { MapperHelper.stringToRestResponse(responseRawEncoded, seqNum) }.thenReturn(restResponse)
            mapperHelperMockedStatic.`when`<Any> { MapperHelper.getIdResponse(responseRawEncoded) }.thenReturn("")

            `when`<Observable<String>>(mApiSource!!.getData(ArgumentMatchers.any(), ArgumentMatchers.any(BaseFormRequest::class.java), ArgumentMatchers.any())).thenReturn(Observable.just(responseRawEncoded))

            mQrTransferCodePresenter?.getDeleteQr()
            mTestScheduler.triggerActions()
            verify(iQrTransferCodeView)?.onException(any())
        }
    }

    @Test
    fun getDataInquiry00() {
        val jsonRaw = """{ "code": "00", "description": "", "data": { "reference_number": "refnum" } }"""
        val restResponse = Gson().fromJson(jsonRaw, RestResponse::class.java)
        mockStatic(GeneralHelper::class.java)
            .use { generalHelperMockedStatic ->
                generalHelperMockedStatic.`when`<Any> { GeneralHelper.isContains(R.array.response_code_success, restResponse.code) }.thenReturn(true)
                mockStatic(MapperHelper::class.java)
                    .use { mapperHelperMockedStatic ->
                        mapperHelperMockedStatic.`when`<Any> { MapperHelper.stringToRestResponse(responseRawEncoded, seqNum) }.thenReturn(restResponse)
                        mapperHelperMockedStatic.`when`<Any> { MapperHelper.getIdResponse(responseRawEncoded) }.thenReturn(responseIdDummy)

                        `when`<Observable<String>>(mApiSource!!.getData(ArgumentMatchers.any(), ArgumentMatchers.any(BaseFormRequest::class.java), ArgumentMatchers.any())).thenReturn(Observable.just(responseRawEncoded))

                        mQrTransferCodePresenter?.getDeleteQr()

                        mTestScheduler.triggerActions()

                        verify(iQrTransferCodeView)?.showProgress()
                    }
            }
    }

    @Test
    fun getDataInquiryOnApiCallError() {
        val jsonRaw = """{ "code": "05", "description": "", "data": { "reference_number": "refnum" } }"""
        val restResponse = Gson().fromJson(jsonRaw, RestResponse::class.java)
        mockStatic(GeneralHelper::class.java)
            .use { generalHelperMockedStatic ->
                generalHelperMockedStatic.`when`<Any> { GeneralHelper.isContains(R.array.response_code_error_specific, restResponse.code) }.thenReturn(false)
                mockStatic(MapperHelper::class.java)
                    .use { mapperHelperMockedStatic ->
                        mapperHelperMockedStatic.`when`<Any> { MapperHelper.stringToRestResponse(responseRawEncoded, seqNum) }.thenReturn(restResponse)
                        mapperHelperMockedStatic.`when`<Any> { MapperHelper.getIdResponse(responseRawEncoded) }.thenReturn(responseIdDummy)

                        `when`<Observable<String>>(mApiSource!!.getData(ArgumentMatchers.any(), ArgumentMatchers.any(BaseFormRequest::class.java), ArgumentMatchers.any())).thenReturn(Observable.just(responseRawEncoded))

                        mQrTransferCodePresenter?.getDeleteQr()

                        mTestScheduler.triggerActions()

                        verify(iQrTransferCodeView)?.onSessionEnd(any())

                    }
            }
    }
}