package id.co.bri.brimo.presenters.dplkrevamp

import com.google.gson.Gson
import id.co.bri.brimo.R
import id.co.bri.brimo.contract.IView.dplkrevamp.IInquiryAutoPaymentView
import id.co.bri.brimo.contract.IView.dplkrevamp.IOnboardingAutoPaymentRevampView
import id.co.bri.brimo.data.api.ApiSource
import id.co.bri.brimo.data.preference.BRImoPrefSource
import id.co.bri.brimo.data.repository.anggaran.AnggaranPfmSource
import id.co.bri.brimo.data.repository.category.CategoryPfmSource
import id.co.bri.brimo.data.repository.transaksi.TransaksiPfmSource
import id.co.bri.brimo.domain.converter.MapperHelper
import id.co.bri.brimo.domain.helpers.GeneralHelper
import id.co.bri.brimo.models.apimodel.request.dplkrevamp.InquiryAutoPaymentRequest
import id.co.bri.brimo.models.apimodel.request.dplkrevamp.SubmitAutoPaymentRequest
import id.co.bri.brimo.models.apimodel.response.RestResponse
import id.co.bri.brimo.models.apimodel.response.dplkrevamp.InquiryAutoPaymentResponse
import id.co.bri.brimo.models.apimodel.response.dplkrevamp.SubmitAutoPaymentResponse
import id.co.bri.brimo.presenters.domain.helper.rx.TestSchedulerProvider
import io.reactivex.Observable
import io.reactivex.disposables.CompositeDisposable
import io.reactivex.schedulers.TestScheduler
import org.junit.After
import org.junit.Before
import org.junit.Test
import org.junit.runner.RunWith
import org.mockito.ArgumentMatchers
import org.mockito.Mock
import org.mockito.Mockito
import org.mockito.Mockito.mock
import org.mockito.Mockito.verify
import org.mockito.junit.MockitoJUnitRunner

@RunWith(MockitoJUnitRunner::class)
class InquiryAutoPaymentPresenterTest {

    @Mock
    lateinit var iInquiryAutopaymentView : IInquiryAutoPaymentView

    @Mock
    lateinit var mBRImoPrefRepository: BRImoPrefSource

    @Mock
    lateinit var mApiSource: ApiSource

    @Mock
    lateinit var categoryPfmSource: CategoryPfmSource

    @Mock
    lateinit var transaksiPfmSource: TransaksiPfmSource

    @Mock
    lateinit var anggaranPfmSource: AnggaranPfmSource

    @Mock
    lateinit var compositeDisposable: CompositeDisposable

    private var mInquiryAutopaymentPresenter: InquiryAutoPaymentPresenter<IInquiryAutoPaymentView>? = null
    private var mTestScheduler = TestScheduler()
    private val seqNum = "40"
    private val responseRawEncoded = ""
    private val responseIdDummy = "12314324324"
    private val murlSubmitAutoPayment = "urlInquiryAutoPayment"
    private val murlEditAutoPayment = "murlEditAutoPayment"
    private val mSubmitRequest = SubmitAutoPaymentRequest(
        "12",
        "12345",
        20000.00,
        "12",
        "1232123123123"
    )

    @Before
    @Throws(Exception::class)
    fun setUp() {
        mTestScheduler = TestScheduler()
        val testSchedulerProvider = TestSchedulerProvider(mTestScheduler)
        mInquiryAutopaymentPresenter = InquiryAutoPaymentPresenter(
            testSchedulerProvider,
            compositeDisposable,
            mBRImoPrefRepository,
            mApiSource,
            categoryPfmSource,
            transaksiPfmSource,
            anggaranPfmSource
        )

        mInquiryAutopaymentPresenter!!.view = iInquiryAutopaymentView
        Mockito.`when`(mBRImoPrefRepository.seqNumber).thenReturn(seqNum)
        mInquiryAutopaymentPresenter?.setUrlSubmitAutoPayment(murlSubmitAutoPayment)
    }

    @After
    fun setupAfter() {

    }

    @Test
    fun submitAutoPaymentOnFailure() {
        val jsonRaw = ""

        val restResponse = Gson().fromJson(jsonRaw, RestResponse::class.java)
        Mockito.mockStatic(MapperHelper::class.java).use { mapperHelperMockedStatic ->
            mapperHelperMockedStatic.`when`<Any> {
                MapperHelper.stringToRestResponse(
                    responseRawEncoded,
                    seqNum
                )
            }.thenReturn(restResponse)
            mapperHelperMockedStatic.`when`<Any> { MapperHelper.getIdResponse(responseRawEncoded) }.thenReturn("")
            Mockito.`when`<Observable<String>>(
                mApiSource.getData(
                    murlSubmitAutoPayment,
                    mSubmitRequest,
                    seqNum,
                )
            ).thenReturn(Observable.just(responseRawEncoded))
            mInquiryAutopaymentPresenter?.submitAutoPayment(mSubmitRequest)
            mTestScheduler.triggerActions()
            Mockito.verify(iInquiryAutopaymentView)?.onException(ArgumentMatchers.any())
        }
    }

    @Test
    fun submitAutoPayment00() {
        val jsonRawInquiry = "{\n" +
                "    \"message\": \"Jadwal Setoran Rutin berhasil dibuat\"\n" +
                "  }\n".trimIndent()
        val jsonRawResponse = """{ "code": "00", "description": "Success", "data": ${jsonRawInquiry} }"""

        val restResponse = Gson().fromJson(jsonRawResponse, RestResponse::class.java)
        val inquiryAutoPaymentResponse = Gson().fromJson(jsonRawInquiry, SubmitAutoPaymentResponse::class.java)

        Mockito.mockStatic(GeneralHelper::class.java)
            .use { generalHelperMockedStatic ->
                generalHelperMockedStatic.`when`<Any> { GeneralHelper.isContains(R.array.response_code_success, restResponse.code) }.thenReturn(true)
                Mockito.mockStatic(MapperHelper::class.java)
                    .use { mapperHelperMockedStatic ->
                        mapperHelperMockedStatic.`when`<Any> { MapperHelper.stringToRestResponse(responseRawEncoded, seqNum) }.thenReturn(restResponse)
                        mapperHelperMockedStatic.`when`<Any> { MapperHelper.getIdResponse(responseRawEncoded) }.thenReturn(responseIdDummy)

                        Mockito.`when`<Observable<String>>(
                            mApiSource.getData(
                                murlSubmitAutoPayment,
                                mSubmitRequest,
                                seqNum
                            )
                        ).thenReturn(Observable.just(responseRawEncoded))

                        mInquiryAutopaymentPresenter?.submitAutoPayment(mSubmitRequest)

                        mTestScheduler.triggerActions()
                        if (restResponse.code.equals("00", ignoreCase = true)){
                            Mockito.verify(iInquiryAutopaymentView).onSuccessInquirySubmitAutoPayment(inquiryAutoPaymentResponse)
                        }
                    }
            }
    }

    @Test
    fun getInquiryAddPaymentOnApiCallError() {
        // Given
        val jsonRaw = """{ "code": "69", "description": "Session Expired", "data": { "reference_number": "refnum" } }"""
        val restResponse = Gson().fromJson(jsonRaw, RestResponse::class.java)

        Mockito.mockStatic(GeneralHelper::class.java)
            .use { generalHelperMockedStatic ->
                // Mocking the condition to return false
                generalHelperMockedStatic.`when`<Any> { GeneralHelper.isContains(R.array.response_code_error_specific, restResponse.code) }.thenReturn(false)
                Mockito.mockStatic(MapperHelper::class.java)
                    .use { mapperHelperMockedStatic ->
                        mapperHelperMockedStatic.`when`<Any> { MapperHelper.stringToRestResponse(responseRawEncoded, seqNum) }.thenReturn(restResponse)
                        mapperHelperMockedStatic.`when`<Any> { MapperHelper.getIdResponse(responseRawEncoded) }.thenReturn(responseIdDummy)

                        // Mocking the getDataTanpaRequest call to return the responseRawEncoded
                        Mockito.`when`<Observable<String>>(
                            mApiSource.getData(
                                murlSubmitAutoPayment,
                                mSubmitRequest,
                                seqNum,
                            )
                        ).thenReturn(Observable.just(responseRawEncoded))

                        // When
                        mInquiryAutopaymentPresenter?.submitAutoPayment(mSubmitRequest)

                        // Triggering the actions on the test scheduler
                        mTestScheduler.triggerActions()
                    }
            }
        when(restResponse.code){
            "05" -> Mockito.verify(iInquiryAutopaymentView).onSessionEnd(restResponse.desc)
            "93" -> Mockito.verify(iInquiryAutopaymentView).onExceptionTrxExpired(restResponse.desc)
            else -> Mockito.verify(iInquiryAutopaymentView).onException(restResponse.desc)
        }
    }

    @Test
    fun `submitAutoPayment returns early when URL is null`() {
        // Given
        mInquiryAutopaymentPresenter!!.urlInquirySubmitAutoPayment = null

        // When
        mInquiryAutopaymentPresenter!!.submitAutoPayment(mSubmitRequest)

        // Then
        // Verify that no interaction with the view occurred
        Mockito.verifyZeroInteractions(iInquiryAutopaymentView)
    }

    @Test
    fun getDefaultSaldo() {
        mInquiryAutopaymentPresenter?.getDefaultSaldo()
    }



    @Test
    fun start() {
        mInquiryAutopaymentPresenter?.start()
        mInquiryAutopaymentPresenter?.getDefaultSaldo()
        mTestScheduler.triggerActions()
    }


}