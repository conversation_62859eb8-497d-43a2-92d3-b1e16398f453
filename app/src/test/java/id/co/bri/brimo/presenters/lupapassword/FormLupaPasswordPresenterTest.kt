package id.co.bri.brimo.presenters.lupapassword

import com.google.gson.Gson
import id.co.bri.brimo.R
import id.co.bri.brimo.contract.IView.lupapassword.IFormLupaPasswordView
import id.co.bri.brimo.data.api.ApiSource
import id.co.bri.brimo.data.preference.BRImoPrefSource
import id.co.bri.brimo.data.repository.anggaran.AnggaranPfmSource
import id.co.bri.brimo.data.repository.category.CategoryPfmSource
import id.co.bri.brimo.data.repository.transaksi.TransaksiPfmSource
import id.co.bri.brimo.domain.converter.MapperHelper
import id.co.bri.brimo.domain.helpers.GeneralHelper
import id.co.bri.brimo.models.apimodel.request.ForgetPassInquReq
import id.co.bri.brimo.models.apimodel.request.QrTransferInquiryRequest
import id.co.bri.brimo.models.apimodel.response.DetailAkunResponse
import id.co.bri.brimo.models.apimodel.response.GeneralInquiryResponse
import id.co.bri.brimo.models.apimodel.response.RestResponse
import id.co.bri.brimo.presenters.domain.helper.rx.TestSchedulerProvider
import io.reactivex.Observable
import io.reactivex.disposables.CompositeDisposable
import io.reactivex.schedulers.TestScheduler
import org.junit.After
import org.junit.Assert
import org.junit.Before
import org.junit.Test
import org.junit.runner.RunWith
import org.mockito.ArgumentMatchers
import org.mockito.Mock
import org.mockito.Mockito
import org.mockito.Mockito.any
import org.mockito.Mockito.mockStatic
import org.mockito.Mockito.verify
import org.mockito.Mockito.`when`
import org.mockito.junit.MockitoJUnitRunner

@RunWith(MockitoJUnitRunner::class)
class FormLupaPasswordPresenterTest {
    @Mock
    var iFormLupaPasswordView: IFormLupaPasswordView? = null

    @Mock
    var mBRImoPrefRepository: BRImoPrefSource? = null

    @Mock
    var mApiSource: ApiSource? = null

    @Mock
    var categoryPfmSource: CategoryPfmSource? = null

    @Mock
    var transaksiPfmSource: TransaksiPfmSource? = null

    @Mock
    var anggaranPfmSource: AnggaranPfmSource? = null

    @Mock
    var compositeDisposable: CompositeDisposable? = null

    @Mock
    lateinit var response: DetailAkunResponse

    private var mFormLupaPasswordPresenter: FormLupaPasswordPresenter<IFormLupaPasswordView>? = null
    private var mTestScheduler = TestScheduler()
    private val seqNum = "40"
    private val responseRawEncoded = "ac740a16f65302d1440dcbb1fc"
    private val responseIdDummy = "12314324324"
    private val mUrl = "mUrl"
    private val mForgotPassInquReq = ForgetPassInquReq("mUsername", "mBornDate", "mPin")

    @Before
    @Throws(Exception::class)
    fun setUp() {
        mTestScheduler = TestScheduler()
        val testSchedulerProvider = TestSchedulerProvider(mTestScheduler)
        mFormLupaPasswordPresenter = FormLupaPasswordPresenter(
                testSchedulerProvider,
                compositeDisposable,
                mBRImoPrefRepository,
                mApiSource,
                categoryPfmSource,
                transaksiPfmSource,
                anggaranPfmSource
        )

        mFormLupaPasswordPresenter?.setUrl(mUrl)
        mFormLupaPasswordPresenter!!.view = iFormLupaPasswordView
        `when`(mBRImoPrefRepository!!.seqNumber).thenReturn(seqNum)
    }

    @After
    fun setupAfter() {

    }

    @Test
    fun checkNullUrl() {
        mFormLupaPasswordPresenter?.setUrl(null)
        Assert.assertEquals(null, mFormLupaPasswordPresenter?.url)
    }

    @Test
    fun onSendDataInquiryFailure() {
        val jsonRaw = ""

        val restResponse = Gson().fromJson(jsonRaw, RestResponse::class.java)
        mockStatic(MapperHelper::class.java).use { mapperHelperMockedStatic ->
            mapperHelperMockedStatic.`when`<Any> { MapperHelper.stringToRestResponse(responseRawEncoded, seqNum) }.thenReturn(restResponse)
            mapperHelperMockedStatic.`when`<Any> { MapperHelper.getIdResponse(responseRawEncoded) }.thenReturn("")

            `when`<Observable<String>>(mApiSource!!.getData(ArgumentMatchers.any(), ArgumentMatchers.any(ForgetPassInquReq::class.java),
                    ArgumentMatchers.any())).thenReturn(Observable.just(responseRawEncoded))

            mFormLupaPasswordPresenter?.onSendDataInquiry(mForgotPassInquReq)
            mTestScheduler.triggerActions()
            verify(iFormLupaPasswordView)?.onException(any())
        }
    }

    @Test
    fun onSendDataInquiry00() {
        val jsonRaw = """{ "code": "00", "description": "", "data": { "reference_number": "refnum" } }"""
        val restResponse = Gson().fromJson(jsonRaw, RestResponse::class.java)
        mockStatic(GeneralHelper::class.java)
                .use { generalHelperMockedStatic ->
                    generalHelperMockedStatic.`when`<Any> { GeneralHelper.isContains(R.array.response_code_success, restResponse.code) }.thenReturn(true)
                    mockStatic(MapperHelper::class.java)
                            .use { mapperHelperMockedStatic ->
                                mapperHelperMockedStatic.`when`<Any> { MapperHelper.stringToRestResponse(responseRawEncoded, seqNum) }.thenReturn(restResponse)
                                mapperHelperMockedStatic.`when`<Any> { MapperHelper.getIdResponse(responseRawEncoded) }.thenReturn(responseIdDummy)

                                `when`<Observable<String>>(mApiSource!!.getData(ArgumentMatchers.any(), ArgumentMatchers.any(ForgetPassInquReq::class.java), ArgumentMatchers.any())).thenReturn(Observable.just(responseRawEncoded))

                                mFormLupaPasswordPresenter?.onSendDataInquiry(mForgotPassInquReq)

                                mTestScheduler.triggerActions()

                                verify(iFormLupaPasswordView)?.showProgress()
                                verify(iFormLupaPasswordView)?.onDataSuccess(ArgumentMatchers.any(DetailAkunResponse::class.java))
                            }
                }
    }

    @Test
    fun getDataInquiryOnApiCallError() {
        val jsonRaw = """{ "code": "12", "description": "", "data": { "reference_number": "refnum" } }"""
        val restResponse = Gson().fromJson(jsonRaw, RestResponse::class.java)
        mockStatic(GeneralHelper::class.java)
                .use { generalHelperMockedStatic ->
                    generalHelperMockedStatic.`when`<Any> { GeneralHelper.isContains(R.array.response_code_error_specific, restResponse.code) }.thenReturn(false)
                    mockStatic(MapperHelper::class.java)
                            .use { mapperHelperMockedStatic ->
                                mapperHelperMockedStatic.`when`<Any> { MapperHelper.stringToRestResponse(responseRawEncoded, seqNum) }.thenReturn(restResponse)
                                mapperHelperMockedStatic.`when`<Any> { MapperHelper.getIdResponse(responseRawEncoded) }.thenReturn(responseIdDummy)

                                `when`<Observable<String>>(mApiSource!!.getData(ArgumentMatchers.any(), ArgumentMatchers.any(ForgetPassInquReq::class.java), ArgumentMatchers.any())).thenReturn(Observable.just(responseRawEncoded))

                                mFormLupaPasswordPresenter?.onSendDataInquiry(mForgotPassInquReq)

                                mTestScheduler.triggerActions()

                                verify(iFormLupaPasswordView)?.onException(any())

                            }
                }
    }
}
