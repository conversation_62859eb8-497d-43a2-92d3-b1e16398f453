<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/white"
    android:fitsSystemWindows="true"
    tools:context=".ui.activities.voucher.DetailVoucherActivity">

    <androidx.appcompat.widget.Toolbar
        android:id="@+id/toolbar"
        android:layout_width="match_parent"
        android:layout_height="?attr/actionBarSize"
        android:background="@color/primary_blue80"
        android:elevation="@dimen/_4sdp"
        android:outlineProvider="bounds"
        android:textAlignment="center"
        app:theme="@style/ThemeOverlay.AppCompat.Dark.ActionBar">

        <TextView
            android:id="@+id/textTitle"
            style="@style/SubTitleText.Bold.White"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center"
            android:ellipsize="marquee"
            android:marqueeRepeatLimit="marquee_forever"
            android:scrollHorizontally="true"
            android:singleLine="true"
            android:textAlignment="center" />

        <ImageView
            android:id="@+id/iv_history"
            style="@style/ToolbarTitleStyleRight"
            android:layout_width="@dimen/space_x3"
            android:layout_height="@dimen/space_x3"
            android:layout_marginEnd="@dimen/space_x2"
            android:backgroundTint="@color/white"
            android:backgroundTintMode="src_in"
            android:contentDescription="@null"
            android:src="@drawable/ic_onboarding_info"
            android:visibility="gone" />

    </androidx.appcompat.widget.Toolbar>

    <FrameLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_below="@+id/toolbar">

        <androidx.coordinatorlayout.widget.CoordinatorLayout
            android:id="@+id/content"
            android:layout_width="match_parent"
            android:layout_height="match_parent">

            <androidx.core.widget.NestedScrollView
                android:layout_width="match_parent"
                android:layout_height="match_parent">

                <RelativeLayout
                    android:layout_width="match_parent"
                    android:layout_height="match_parent">

                    <ImageView
                        android:id="@+id/img_game"
                        android:layout_width="wrap_content"
                        android:layout_height="100dp"
                        android:layout_centerHorizontal="true"
                        android:layout_marginTop="@dimen/space_x2_half"
                        android:contentDescription="@null"
                        android:scaleType="fitCenter" />

                    <LinearLayout
                        android:id="@+id/ll_top_source"
                        android:layout_width="match_parent"
                        android:layout_height="match_parent"
                        android:layout_below="@id/img_game"
                        android:layout_marginHorizontal="@dimen/space_x2"
                        android:orientation="vertical">

                        <TextView
                            android:id="@+id/tv_title_voucher"
                            style="@style/Body2MediumText.Bold.NeutralDark40"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:layout_marginTop="22dp"
                            tools:text="Title" />

                        <LinearLayout
                            android:id="@+id/ll_deskripsi"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:orientation="vertical">

                            <TextView
                                android:id="@+id/tv_desc_voucher_less"
                                style="@style/Body3SmallText.Medium.NeutralLight80"
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:layout_marginTop="@dimen/space_half"
                                android:ellipsize="end"
                                android:lineSpacingExtra="4dp"
                                android:maxLines="2"
                                android:visibility="gone"
                                tools:text="@string/no_favorit_desc" />

                            <TextView
                                android:id="@+id/tv_desc_voucher_more"
                                style="@style/Body3SmallText.Medium.NeutralLight80"
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:layout_marginTop="@dimen/space_half"
                                android:lineSpacingExtra="4dp"
                                android:visibility="gone"
                                tools:text="@string/no_favorit_desc" />

                            <LinearLayout
                                android:id="@+id/ll_lht_lebih"
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:layout_marginHorizontal="@dimen/space_x2"
                                android:orientation="vertical">

                                <LinearLayout
                                    android:id="@+id/lihat_lebih"
                                    android:layout_width="wrap_content"
                                    android:layout_height="wrap_content"
                                    android:layout_gravity="center"
                                    android:layout_marginTop="@dimen/space_x2"
                                    android:layout_marginBottom="@dimen/space_x2"
                                    android:orientation="horizontal"
                                    android:visibility="visible">

                                    <TextView
                                        android:id="@+id/tv_lihat_detail"
                                        style="@style/Body2MediumText.Bold.PrimaryBlue80"
                                        android:layout_width="wrap_content"
                                        android:layout_height="wrap_content"
                                        android:layout_gravity="center"
                                        android:text="@string/lihat_lebih_banyak" />

                                    <ImageView
                                        android:id="@+id/iv_arrow"
                                        android:layout_width="wrap_content"
                                        android:layout_height="wrap_content"
                                        android:layout_gravity="center_vertical"
                                        android:contentDescription="@string/information"
                                        android:src="@drawable/dropdown" />
                                </LinearLayout>

                            </LinearLayout>

                            <RelativeLayout
                                android:id="@+id/rl_cara_redeem"
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:background="@drawable/bg_nl10_border_nl30"
                                android:paddingHorizontal="@dimen/space_x2"
                                android:paddingVertical="@dimen/space_x1_half">

                                <ImageView
                                    android:id="@+id/iv_voucher"
                                    android:layout_width="wrap_content"
                                    android:layout_height="wrap_content"
                                    android:layout_centerVertical="true"
                                    android:contentDescription="@null"
                                    android:src="@drawable/ic_voucher_redeem" />

                                <TextView
                                    android:id="@+id/tv_redeem"
                                    style="@style/Body3SmallText.SemiBold.PrimaryBlue80"
                                    android:layout_width="match_parent"
                                    android:layout_height="wrap_content"
                                    android:layout_centerVertical="true"
                                    android:layout_marginHorizontal="@dimen/space_half"
                                    android:layout_toStartOf="@id/iv_chevron"
                                    android:layout_toEndOf="@id/iv_voucher"
                                    android:text="@string/lihat_cara_redeem" />

                                <ImageView
                                    android:id="@+id/iv_chevron"
                                    android:layout_width="wrap_content"
                                    android:layout_height="wrap_content"
                                    android:layout_alignParentEnd="true"
                                    android:layout_centerVertical="true"
                                    android:contentDescription="@null"
                                    android:src="@drawable/ic_chevron_right_blue80" />

                            </RelativeLayout>

                        </LinearLayout>

                        <LinearLayout
                            android:id="@+id/ll_info_tambahan"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginTop="@dimen/space_x2"
                            android:orientation="vertical"
                            android:visibility="gone">

                            <TextView
                                android:id="@+id/tv_info"
                                style="@style/Body2MediumText.Bold.NeutralDark40"
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:text="@string/informasi_tambahan"
                                android:visibility="gone" />

                            <androidx.recyclerview.widget.RecyclerView
                                android:id="@+id/rv_snk"
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:layout_marginTop="@dimen/space_x1"
                                android:visibility="gone"
                                tools:itemCount="1"
                                tools:listitem="@layout/item_list_snk_voucher" />

                        </LinearLayout>

                    </LinearLayout>

                    <View
                        android:id="@+id/divider"
                        android:layout_width="match_parent"
                        android:layout_height="8dp"
                        android:layout_below="@id/ll_top_source"
                        android:layout_marginVertical="@dimen/space_x2"
                        android:background="@color/neutral_light10" />

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_below="@+id/divider"
                        android:orientation="vertical">

                        <TextView
                            style="@style/Body2MediumText.Bold.NeutralDark40"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:layout_marginHorizontal="@dimen/space_x2"
                            android:text="@string/pilih_pembelian" />

                        <androidx.recyclerview.widget.RecyclerView
                            android:id="@+id/rv_voucher"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:layout_marginHorizontal="@dimen/space_x1"
                            android:layout_marginTop="@dimen/space_x1"
                            android:layout_marginBottom="@dimen/space_x3"
                            android:nestedScrollingEnabled="true"
                            app:spanCount="2"
                            tools:itemCount="1"
                            tools:listitem="@layout/item_option_voucher_revamp" />

                    </LinearLayout>

                </RelativeLayout>

            </androidx.core.widget.NestedScrollView>

        </androidx.coordinatorlayout.widget.CoordinatorLayout>

        <include
            android:id="@+id/ly_total_detail"
            layout="@layout/item_total_detail" />

    </FrameLayout>

</RelativeLayout>