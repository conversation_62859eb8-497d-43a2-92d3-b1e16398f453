package id.co.bri.brimo.ui.activities.asuransi;

import android.app.Activity;
import android.content.Context;
import android.content.Intent;
import android.os.Bundle;
import android.util.Log;
import android.view.View;
import android.view.inputmethod.InputMethodManager;

import androidx.recyclerview.widget.GridLayoutManager;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.google.gson.Gson;

import java.util.ArrayList;
import java.util.List;

import javax.inject.Inject;

import id.co.bri.brimo.R;
import id.co.bri.brimo.adapters.BillingAmountAdapter;
import id.co.bri.brimo.adapters.BillingDetailAdapter;
import id.co.bri.brimo.adapters.BillingInfoAdapter;
import id.co.bri.brimo.contract.IPresenter.base.IBaseInquiryPresenter;
import id.co.bri.brimo.contract.IView.base.IBaseInquiryView;
import id.co.bri.brimo.databinding.ActivityInquiryBrivaCloseBinding;
import id.co.bri.brimo.domain.config.Constant;
import id.co.bri.brimo.domain.helpers.GeneralHelper;
import id.co.bri.brimo.domain.helpers.textwatcher.AmountFormatWatcher;
import id.co.bri.brimo.models.AccountModel;
import id.co.bri.brimo.models.BillingDetail;
import id.co.bri.brimo.models.ParameterKonfirmasiModel;
import id.co.bri.brimo.models.ParameterModel;
import id.co.bri.brimo.models.apimodel.response.GeneralInquiryResponse;
import id.co.bri.brimo.ui.activities.InquiryGeneralCloseActivity;
import id.co.bri.brimo.ui.activities.base.BaseInquiryActivity;

public class InquiryMicrositeBrivaActivity extends BaseInquiryActivity implements AmountFormatWatcher.onAmountChange, IBaseInquiryView {

    private static final String TAG = "InquiryGeneralCloseActi";
    boolean isCardShow, isLihatLebih = false;
    BillingDetailAdapter detailAdapter;
    BillingInfoAdapter infoAdapter;
    BillingInfoAdapter infoMinimumAdapter;
    BillingAmountAdapter amountAdapter;
    List<BillingDetail> listAllData = new ArrayList<>();
    List<BillingDetail> listMinimumData = new ArrayList<>();
    int rowMinimumData, rowAllData;
    @Inject
    IBaseInquiryPresenter<IBaseInquiryView> brivaPresenter;
    private ActivityInquiryBrivaCloseBinding binding;

    public static void launchIntent(Activity caller, GeneralInquiryResponse inquiryResponse, String urlKonfirmasi, String urlPayment, String title, ParameterModel parameterModel, boolean fromFastMenu) {
        Intent intent = new Intent(caller, InquiryGeneralCloseActivity.class);

        isFromFastMenu = fromFastMenu;

        //put data to Intent
        if (inquiryResponse != null) {
            try {
                intent.putExtra(TAG_INQUIRY_DATA, new Gson().toJson(inquiryResponse));
            } catch (Exception e) {
                if (!GeneralHelper.isProd())
                    Log.e(TAG_INQUIRY_DATA, "launchIntent: ", e);
            }
        }

        if (parameterModel != null) {
            try {
                intent.putExtra(TAG_PARAMS_DATA, new Gson().toJson(parameterModel));
            } catch (Exception e) {
                if (!GeneralHelper.isProd())
                    Log.e(TAG_PARAMS_DATA, "launchIntent: ", e);
            }
        }

        //set URL service
        try {
            intent.putExtra(TAG_URL_KONFIRM, urlKonfirmasi);
            intent.putExtra(TAG_URL_PAYMENT, urlPayment);
            intent.putExtra(TAG_TITLE, title);
        } catch (Exception e) {
            if (!GeneralHelper.isProd())
                Log.e(TAG, "launchIntent: ", e);
        }

        caller.startActivityForResult(intent, Constant.REQ_PAYMENT);
    }

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        binding = ActivityInquiryBrivaCloseBinding.inflate(getLayoutInflater());
        setContentView(binding.getRoot());

        //inject presenter dari ActivityModule
        injectDependency();

        setLihatLebih();

        selectInputAmount();

        setupTextWatcher();

        binding.llEditText.setOnClickListener(view -> {
            edNominal.requestFocus();
            edNominal.setFocusableInTouchMode(true);
            InputMethodManager imm = (InputMethodManager) getSystemService(Context.INPUT_METHOD_SERVICE);
            imm.showSoftInput(edNominal, InputMethodManager.SHOW_FORCED);
        });

    }

    @Override
    protected int getLayoutResource() {
        return R.layout.activity_inquiry_briva_close;
    }

    @Override
    protected String getTitleBar() {
        return "Tagihan";
    }


    public void setLihatLebih() {
        if (mInquiryResponse.getBillingDetail() == null)
            return;

        listAllData = mInquiryResponse.getBillingDetail();
        rowAllData = mInquiryResponse.getBillingDetail().size() / 2;
        rowMinimumData = mInquiryResponse.getRowDataShow();

        if (rowAllData <= rowMinimumData || rowMinimumData == 0) {
            //jika jumlah row sama
            if (rowAllData == rowMinimumData) {
                //apakah ada sisa total item modulus 2?
                int rowSisa = mInquiryResponse.getBillingDetail().size() % 2;
                //jika ada sisa tampilkan lebih sedikit
                if (rowSisa > 0) {
                    lihatSedikit();
                    binding.rvBillingInfoMinimum.setVisibility(View.VISIBLE);
                    binding.rvBillingInfo.setVisibility(View.GONE);
                    binding.lihatLebih.setVisibility(View.VISIBLE);
                    binding.lihatSedikit.setVisibility(View.GONE);
                } else {
                    lihatSemua();
                }
            } else {
                lihatSemua();
            }
        } else {
            lihatSedikit();
            binding.rvBillingInfoMinimum.setVisibility(View.VISIBLE);
            binding.rvBillingInfo.setVisibility(View.GONE);
            binding.lihatLebih.setVisibility(View.VISIBLE);
            binding.lihatSedikit.setVisibility(View.GONE);
        }

        binding.lihatSedikit.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View view) {
                toggleLihat();
            }
        });

        binding.lihatLebih.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View view) {
                toggleLihat();
            }
        });

    }

    protected void toggleLihat() {
        if (!isLihatLebih) {
            lihatLebih();
        } else {
            lihatSedikit();
        }
    }

    private void lihatSemua() {
        lihatLebih();

        binding.lihatLebih.setVisibility(View.GONE);
        binding.lihatSedikit.setVisibility(View.GONE);
    }

    private void lihatLebih() {
        // Info Transaksi
        binding.rvBillingInfo.setHasFixedSize(true);
        binding.rvBillingInfo.setLayoutManager(new GridLayoutManager(this, 2));
        infoAdapter = new BillingInfoAdapter(this, listAllData);
        binding.rvBillingInfo.setAdapter(infoAdapter);

        onAnimatorFade(binding.rvBillingInfoMinimum, false, Constant.REQUEST_CLOSE_INQUIRY);
        onAnimatorShow(binding.rvBillingInfo, true, Constant.REQUEST_CLOSE_INQUIRY);

        binding.lihatLebih.setVisibility(View.GONE);
        binding.lihatSedikit.setVisibility(View.VISIBLE);

        isLihatLebih = true;
    }

    private void lihatSedikit() {
        listMinimumData = mInquiryResponse.getBillingDetailMinimum();
        binding.rvBillingInfoMinimum.setHasFixedSize(true);
        binding.rvBillingInfoMinimum.setLayoutManager(new GridLayoutManager(this, 2));
        infoMinimumAdapter = new BillingInfoAdapter(this, listMinimumData);
        binding.rvBillingInfoMinimum.setAdapter(infoMinimumAdapter);

        onAnimatorFade(binding.rvBillingInfo, true, Constant.REQUEST_CLOSE_INQUIRY);
        onAnimatorShow(binding.rvBillingInfoMinimum, false, Constant.REQUEST_CLOSE_INQUIRY);

        binding.lihatLebih.setVisibility(View.VISIBLE);
        binding.lihatSedikit.setVisibility(View.GONE);

        isLihatLebih = false;
    }

    // select input amount untuk CC
    protected void selectInputAmount() {
        binding.btnBayarPenuh.setOnClickListener(view -> {
            amountPay.setText(mInquiryResponse.getAmount().toString());
            showNominalField(false);
        });
        binding.btnBayarMinimal.setOnClickListener(view -> {
            amountPay.setText(mInquiryResponse.getMinimumAmount().toString());
            showNominalField(false);
        });
        binding.btnInputManual.setOnClickListener(view -> {
            edNominal.getText().clear();
            showNominalField(true);
        });
    }


    // set default amount
    protected void showNominalField(boolean isEnable) {
        //menampilkan field
        showCardNominal();
        edNominal.setText(amountPay.getText());
        edNominal.setEnabled(isEnable);
        setupInputError(edNominal);
    }


    private void setupTextWatcher() {
        edNominal.addTextChangedListener(new AmountFormatWatcher(edNominal, this, false));
    }


    @Override
    protected void onStop() {
        super.onStop();

    }

    @Override
    protected void injectDependency() {
        getActivityComponent().inject(this);

        if (brivaPresenter != null) {
            brivaPresenter.setView(this);
            brivaPresenter.setUrlConfirmation(mUrlKonfirmasi);
            brivaPresenter.start();
        }
    }

    @Override
    protected void setupView() {
        if (mInquiryResponse == null)
            return;

        //set view untuk CC
        binding.tvMinimal.setText(mInquiryResponse.getMinimumAmountString());
        if (mInquiryResponse.getOpenPayment().equals(true) && mInquiryResponse.getIsBilling().equals(true) && mInquiryResponse.getMinimumPayment().equals(true)) {
            binding.llInputBayar.setVisibility(View.VISIBLE);
            binding.layoutMinimum.setVisibility(View.VISIBLE);
            binding.btnInputManual.setSelected(true);
            binding.segmented2.check(R.id.btn_input_manual);
            showNominalField(true);
        } else if (mInquiryResponse.getOpenPayment().equals(true) && mInquiryResponse.getIsBilling().equals(true) && mInquiryResponse.getMinimumPayment().equals(false)) {
            binding.llInputBayar.setVisibility(View.VISIBLE);
            binding.layoutMinimum.setVisibility(View.VISIBLE);
            binding.llButtonSegment.setVisibility(View.GONE);
            binding.segmented2.setVisibility(View.GONE);
            showNominalField(true);
        }
        //amount untuk inquiry close
        else {
            amountPay.setText(mInquiryResponse.getPayAmount().toString());
            edNominal.setText(amountPay.getText());
            setupInputError(edNominal);
        }

        // Detail Amount
        binding.rvBillingAmount.setHasFixedSize(true);
        binding.rvBillingAmount.setLayoutManager(new LinearLayoutManager(this, RecyclerView.VERTICAL, false));
        amountAdapter = new BillingAmountAdapter(this, mInquiryResponse.getBillingAmount());
        binding.rvBillingAmount.setAdapter(amountAdapter);

        // Detail Transaksi
        binding.rvBillingDetail.setHasFixedSize(true);
        binding.rvBillingDetail.setLayoutManager(new LinearLayoutManager(this, RecyclerView.VERTICAL, false));
        detailAdapter = new BillingDetailAdapter(this, mInquiryResponse.getBillingAmountDetail());
        binding.rvBillingDetail.setAdapter(detailAdapter);

        //Set View from Parameter
        btnSubmit.setText(mParameterModel.getStringButtonSubmit());
        binding.lblNominal.setText(mParameterModel.getStringLabelNominal());

        //set minimum
        if (mInquiryResponse.getMinimumTransaction() != null && mInquiryResponse.getMinimumTransactionString() != null) {
            minTrx = mInquiryResponse.getMinimumTransaction();
            minTrxString = mInquiryResponse.getMinimumTransactionString();
        }

        tvError.setText(String.format("Minimal %s %s", mParameterModel.getStringLabelMinimum(), minTrxString));
    }

    @Override
    public int getAmount() {
        return amountPay.getValue();
    }


    @Override
    public void onSubmit() {
        if (!isLoading) {
            brivaPresenter.getDataConfirmation(mInquiryResponse.getReferenceNumber(), model.getAcoount(), amountPay.getText(), etSaved.getText().toString(), isFromFastMenu);
            isLoading = true;
        }
    }


    @Override
    public ParameterKonfirmasiModel setParameterKonfirmasi() {
        ParameterKonfirmasiModel parameterKonfirmasiModel = new ParameterKonfirmasiModel();

        parameterKonfirmasiModel.setStringLabelTujuan(mParameterModel.getStringLabelTujuan());
        parameterKonfirmasiModel.setStringButtonSubmit(mParameterModel.getStringButtonSubmit());
        parameterKonfirmasiModel.setDefaultIcon(mParameterModel.getDefaultIcon());

        return parameterKonfirmasiModel;
    }


    @Override
    public void onAmountChange(String amount) {
        amountPay.setText(amount);
        setupInputError(edNominal);
    }


    @Override
    public void setAmountListener() {

    }

    public void showCardNominal() {
        if (!isCardShow) {
            onAnimatorShow(binding.llEditText, true, Constant.REQUEST_CLOSE_INQUIRY);
            isCardShow = true;
        }
    }

    @Override
    public void onSelectSumberDana(AccountModel bankModel) {
        super.onSelectSumberDana(bankModel);
        setupInputError(edNominal);
    }

    @Override
    protected void onDestroy() {
        super.onDestroy();
        binding = null;
    }
}