package id.co.bri.brimo.ui.activities.onboardingrevamp

import android.annotation.SuppressLint
import android.app.Activity
import android.content.Intent
import android.os.Bundle
import android.os.CountDownTimer
import android.view.KeyEvent
import android.view.View
import android.view.ViewGroup
import android.view.ViewTreeObserver
import androidx.activity.OnBackPressedCallback
import androidx.activity.result.ActivityResultLauncher
import androidx.activity.result.contract.ActivityResultContracts
import androidx.core.content.ContextCompat
import androidx.core.text.HtmlCompat
import androidx.recyclerview.widget.GridLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.google.gson.Gson
import id.co.bri.brimo.R
import id.co.bri.brimo.adapters.PinNumberBlueAdapter
import id.co.bri.brimo.adapters.WatermarkAdapter
import id.co.bri.brimo.adapters.baseadapter.base.BasePinAdapter
import id.co.bri.brimo.adapters.pinadapter.OtpRevampAdapter
import id.co.bri.brimo.contract.IPresenter.onboardingrevamp.IOnboardingOtpPrivyPresenter
import id.co.bri.brimo.contract.IView.onboardingrevamp.IOnboardingOtpPrivyView
import id.co.bri.brimo.databinding.ActivityRegistrasiOtpPrivyBinding
import id.co.bri.brimo.domain.config.Constant
import id.co.bri.brimo.domain.helpers.GeneralHelper
import id.co.bri.brimo.models.InfoModel
import id.co.bri.brimo.models.apimodel.request.onboardingrevamp.ValidateOtpPrivyReq
import id.co.bri.brimo.models.apimodel.response.onboardingrevamp.OnboardingOtpRes
import id.co.bri.brimo.models.apimodel.response.onboardingrevamp.OnboardingReceiptResponse
import id.co.bri.brimo.ui.activities.AskActivity
import id.co.bri.brimo.ui.activities.base.BaseActivity
import id.co.bri.brimo.ui.customviews.pin.InsertPinNumbers
import id.co.bri.brimo.ui.fragments.BottomDialog2ButtonRevampFragment
import java.util.HashMap
import javax.inject.Inject

class OnboardingOtpPrivyActivity : BaseActivity(),
    IOnboardingOtpPrivyView,
    View.OnKeyListener,
    PinNumberBlueAdapter.OnPinNumberListener,
    BasePinAdapter.PinAdapterListener,
    BottomDialog2ButtonRevampFragment.OnClickDialog {

    @Inject
    lateinit var presenter: IOnboardingOtpPrivyPresenter<IOnboardingOtpPrivyView>

    private lateinit var binding: ActivityRegistrasiOtpPrivyBinding

    private var onboardingOtpRes: OnboardingOtpRes? = null
    private var countDownTimer: CountDownTimer? = null
    private val second = 1000
    private var onboardingId = ""
    private var isC1 = false

    private val watermarkList: ArrayList<Int> = ArrayList()

    private lateinit var otpRevampAdapter: OtpRevampAdapter
    private lateinit var pinNumberAdapter: PinNumberBlueAdapter
    private lateinit var pinOtpLayoutManager: GridLayoutManager
    private lateinit var pinPadLayoutManager: GridLayoutManager

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)

        binding = ActivityRegistrasiOtpPrivyBinding.inflate(layoutInflater)
        setContentView(binding.root)

        onBackPressedDispatcher.addCallback(this, onBackPressedCallback)

        injectDependency()
        intentExtra()
        setupView()
        onSetLayoutHeight()
        onWatermark()
        setEventAppsFlyer("openaccount_otp_pengesahan")
    }

    private fun setEventAppsFlyer(eventName: String) {
        val eventValue: MutableMap<String, Any> = HashMap()
        eventValue[Constant.CUSTOMER_ID] = presenter.persistenceId
        trackAppsFlyerAnalyticEvent(eventName, eventValue)
    }

    private fun intentExtra() {
        onboardingOtpRes =
            Gson().fromJson(intent.getStringExtra(Constant.GENRES), OnboardingOtpRes::class.java)
    }

    private fun injectDependency() {
        activityComponent.inject(this)
        presenter.view = this
        presenter.start()
        presenter.setUrlSendOtp(GeneralHelper.getString(R.string.url_onboarding_validate_otp_privy_v3))
        presenter.setUrlResendOtp(GeneralHelper.getString(R.string.url_onboarding_resend_otp_privy_v3))
        onboardingId = presenter.getDeviceId()
    }

    private fun setupView() {
        GeneralHelper.setToolbarRevamp(
            this,
            binding.toolbarRevamp.toolbar,
            GeneralHelper.getString(R.string.kode_pengesahan)
        )

        binding.tvDescOtp.text = HtmlCompat.fromHtml(
            String.format(
                GeneralHelper.getString(R.string.txt_otp_privy_onboarding),
                onboardingOtpRes?.phone, onboardingOtpRes?.email
            ), HtmlCompat.FROM_HTML_MODE_LEGACY
        )
        binding.tvDescSyarat.text = GeneralHelper.getString(R.string.txt_desc_otp_privy_onboarding)

        otpRevampAdapter = OtpRevampAdapter(this, 2)
        pinNumberAdapter =
            PinNumberBlueAdapter(InsertPinNumbers.getPinNumberList(this))
        pinOtpLayoutManager = GridLayoutManager(this, 6)
        pinPadLayoutManager = GridLayoutManager(this, 3)

        pinNumberAdapter.onPinNumberListener = this
        otpRevampAdapter.setListener(this)
        binding.rvBox.layoutManager = pinOtpLayoutManager
        binding.rvBox.adapter = otpRevampAdapter
        binding.rvInputOtp.layoutManager = pinPadLayoutManager
        binding.rvInputOtp.adapter = pinNumberAdapter

        setTextTimer(onboardingOtpRes!!.expiredIinSecond)

        binding.tvKrmUlang.setOnClickListener {
            presenter.resendOtp()
        }
    }

    private fun onSetLayoutHeight() {
        val viewTreeObserver: ViewTreeObserver = binding.layoutPin.viewTreeObserver
        viewTreeObserver.addOnGlobalLayoutListener(object :
            ViewTreeObserver.OnGlobalLayoutListener {
            override fun onGlobalLayout() {
                binding.layoutPin.viewTreeObserver.removeGlobalOnLayoutListener(this)
                val height: Int = binding.layoutPin.measuredHeight
                val params: ViewGroup.LayoutParams = binding.rvWatermark.layoutParams
                params.height = height
                binding.rvWatermark.layoutParams = params
            }
        })
    }

    private fun onWatermark() {
        for (i in 0..199) {
            watermarkList.add(i)
        }
        binding.rvWatermark.setHasFixedSize(true)
        binding.rvWatermark.layoutManager = object :
            GridLayoutManager(this, 4, RecyclerView.VERTICAL, false) {
            override fun canScrollVertically(): Boolean {
                return false
            }
        }
        val watermarkAdapter = WatermarkAdapter(this, watermarkList)
        binding.rvWatermark.adapter = watermarkAdapter
    }

    private fun setTextTimer(timer: Int) {
        val countDown: Int = second * timer
        countDownTimer = object : CountDownTimer(countDown.toLong(), second.toLong()) {
            override fun onTick(millisUntilFinished: Long) {
                val seconds: Int = millisUntilFinished.toInt() / second
                val timeFormat = GeneralHelper.getTimeFormat(seconds)
                binding.tvTimer.text = String.format(
                    resources.getString(R.string.countdown_otp00_00),
                    timeFormat[1], timeFormat[2]
                )


                binding.tvKrmUlang.setTextColor(
                    ContextCompat.getColor(
                        applicationContext,
                        R.color.neutral_dark40
                    )
                )
                binding.tvKrmUlang.alpha = 0.3f
                binding.tvKrmUlang.isEnabled = false
            }

            override fun onFinish() {
                binding.tvTimer.text = GeneralHelper.getString(R.string.time00_00)

                binding.tvKrmUlang.setTextColor(
                    ContextCompat.getColor(
                        applicationContext,
                        R.color.primary_blue80
                    )
                )
                binding.tvKrmUlang.alpha = 1f
                binding.tvKrmUlang.isEnabled = true
            }
        }.start()
    }

    override fun onKey(p0: View?, p1: Int, p2: KeyEvent?): Boolean {
        return false
    }

    override fun onPinClicked(pinNumber: Int) {
        otpRevampAdapter.addPin(pinNumber.toString())
    }

    override fun onDeleteClicked() {
        otpRevampAdapter.deletePin()
    }

    @SuppressLint("NotifyDataSetChanged")
    override fun notifyChanges() {
        otpRevampAdapter.notifyDataSetChanged()
    }

    override fun onComplete(string: String) {
        presenter.sendOtp(ValidateOtpPrivyReq(onboardingId, string))
    }

    override fun deleteAllPin() {
        otpRevampAdapter.deleteAllPin()
    }

    override fun onSuccessConfirm(receiptResponse: OnboardingReceiptResponse) {
        setEventAppsFlyer("openaccount_otp_pengesahan_success")
        val intent = Intent(this, OnboardingInformationActivity::class.java)
        val infoModel = InfoModel(
            receiptResponse.status,
            R.drawable.ic_akun_terbentuk,
            String.format(
                GeneralHelper.getString(R.string.selamat_nama), receiptResponse.accountName
            ),
            GeneralHelper.getString(R.string.desc_sukses_onboarding),
            GeneralHelper.getString(R.string.lanjutkan)
        )
        intent.putExtra(Constant.TAG_ADDITIONAL, infoModel)
        intent.putExtra(Constant.GENRES, Gson().toJson(receiptResponse))
        intent.putExtra(Constant.USERNAMEDS, receiptResponse.accountName)
        startActivityIntent.launch(intent)
    }

    override fun onResend(onboardingOtpRes: OnboardingOtpRes) {
        this.onboardingOtpRes = onboardingOtpRes
        setTextTimer(onboardingOtpRes.expiredIinSecond)
    }

    override fun onGenerateAccount() {
        setEventAppsFlyer("openaccount_otp_pengesahan_success")
        val intent = Intent(this, OnboardingInformationActivity::class.java)
        val infoModel = InfoModel(
            12,
            R.drawable.ic_onboarding_user,
            GeneralHelper.getString(R.string.title_proses_tabungan),
            GeneralHelper.getString(R.string.desc_proses_tabungan),
            GeneralHelper.getString(R.string.check_status)
        )
        intent.putExtra(Constant.TAG_ADDITIONAL, infoModel)
        intent.putExtra(Constant.CHECK_POINT_DS, 12)
        startActivityIntent.launch(intent)
    }

    override fun onGenerateUser() {
        setEventAppsFlyer("openaccount_otp_pengesahan_success")
        val intent = Intent(this, OnboardingInformationActivity::class.java)
        val infoModel = InfoModel(
            13,
            R.drawable.ic_onboarding_user,
            GeneralHelper.getString(R.string.title_proses_tabungan),
            GeneralHelper.getString(R.string.desc_proses_tabungan),
            GeneralHelper.getString(R.string.check_status)
        )
        intent.putExtra(Constant.TAG_ADDITIONAL, infoModel)
        intent.putExtra(Constant.CHECK_POINT_DS, 13)
        startActivityIntent.launch(intent)
    }

    override fun onExceptionLogin() {
        isC1 = true
        val dialogRevampFragment = BottomDialog2ButtonRevampFragment(
            this,
            GeneralHelper.getString(R.string.registrasi_akun_brimo_belum_berhasil),
            GeneralHelper.getString(R.string.desc_registrasi_akun_brimo_belum_berhasil),
            GeneralHelper.getString(R.string.login_sekarang),
            GeneralHelper.getString(R.string.btn_tutup),
        )

        dialogRevampFragment.isCancelable = false
        dialogRevampFragment.show(supportFragmentManager, "")
    }

    override fun onExceptionRegistration() {
        isC1 = false
        val dialogRevampFragment = BottomDialog2ButtonRevampFragment(
            this,
            GeneralHelper.getString(R.string.title_email_hp_terpakai),
            GeneralHelper.getString(R.string.desc_email_hp_terpakai),
            GeneralHelper.getString(R.string.registrasi_brimo_sekarang),
            GeneralHelper.getString(R.string.btn_tutup),
        )

        dialogRevampFragment.isCancelable = false
        dialogRevampFragment.show(supportFragmentManager, "")
    }

    override fun clickButton(typeButton: String) {
        if (BottomDialog2ButtonRevampFragment.ButtonType.BLUE == typeButton) {
            if (isC1) {
                val intent = Intent(this, AskActivity::class.java)
                intent.flags = Intent.FLAG_ACTIVITY_NEW_TASK or Intent.FLAG_ACTIVITY_CLEAR_TASK
                intent.putExtra(Constant.ONBOARDING_BRIMO, true)
                startActivityIntent.launch(intent)
                finish()
            } else {
                val intent = Intent(this, AskActivity::class.java)
                intent.flags = Intent.FLAG_ACTIVITY_NEW_TASK or Intent.FLAG_ACTIVITY_CLEAR_TASK
                intent.putExtra("registrasi", "registrasi_sekarang")
                startActivityIntent.launch(intent)
                finish()
            }
        } else if (BottomDialog2ButtonRevampFragment.ButtonType.WHITE == typeButton) {
            val intent = Intent()
            intent.putExtra(Constant.BACK_GENERAL, Constant.ONBOARDING_BRIMO)
            setResult(Activity.RESULT_CANCELED, intent)
            finish()
        }
    }

    private var startActivityIntent: ActivityResultLauncher<Intent> = registerForActivityResult(
        ActivityResultContracts.StartActivityForResult()
    ) { result ->
        if (result.resultCode == Activity.RESULT_CANCELED && result.data != null) {
            setResult(RESULT_CANCELED, result.data)
            finish()
        }
    }

    private val onBackPressedCallback: OnBackPressedCallback =
        object : OnBackPressedCallback(true) {
            override fun handleOnBackPressed() {
                val intent = Intent()
                intent.putExtra(Constant.CHECK_POINT, 11)
                setResult(Activity.RESULT_CANCELED, intent)
                finish()
            }
        }

    override fun onDestroy() {
        presenter.stop()
        super.onDestroy()
    }
}
