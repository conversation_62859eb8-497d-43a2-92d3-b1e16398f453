package id.co.bri.brimo.di.modules.fragment;

import android.content.Context;

import androidx.recyclerview.widget.GridLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import id.co.bri.brimo.adapters.PinNumberAdapter;
import id.co.bri.brimo.adapters.PinNumberAdapterNewSkin;
import id.co.bri.brimo.adapters.PinNumberVoipAdapter;
import id.co.bri.brimo.adapters.pinadapter.OtpInputAdapter;
import id.co.bri.brimo.adapters.pinadapter.PinHiddenAdapter;
import id.co.bri.brimo.di.scopes.PerForm;
import id.co.bri.brimo.domain.helpers.SizeHelper;
import id.co.bri.brimo.ui.customviews.GridSpacingItemDecoration;
import id.co.bri.brimo.ui.customviews.pin.InsertPinNumbers;

import javax.inject.Named;

import dagger.Module;
import dagger.Provides;

@Module
public class PinAllModule {


    @Provides
    @PerForm
    public PinNumberAdapter providePinNumberAdapter(Context sontek) {
        return new PinNumberAdapter(InsertPinNumbers.Companion.getPinNumberList(sontek));
    }

    @Provides
    @PerForm
    public PinHiddenAdapter providePinHiddenAdapter(Context sontek) {
        return new PinHiddenAdapter(sontek);
    }

    @Provides
    @PerForm
    public PinNumberAdapterNewSkin providePinNumberAdapterNewSkin(Context sontek) {
        return new PinNumberAdapterNewSkin(InsertPinNumbers.Companion.getPinNumberList(sontek));
    }

    @Named("Pindot")
    @Provides
    @PerForm
    public GridLayoutManager providePinDotLayoutManager(Context sontek) {
        return new GridLayoutManager(sontek, 6);
    }

    @Named("Pinotp")
    @Provides
    @PerForm
    public GridLayoutManager provideOtpLayoutManager(Context context) {
        return new GridLayoutManager(context, 6);
    }

    @Named("Pinpad")
    @Provides
    @PerForm
    public GridLayoutManager providePinPadLayoutManager(Context context) {
        return new GridLayoutManager(context, 3);
    }

    @Provides
    @PerForm
    public RecyclerView.ItemDecoration provideItemDecoration(Context context) {
        return new GridSpacingItemDecoration(3, SizeHelper.dpToPx(context, 25), true);
    }

    @Provides
    @PerForm
    public OtpInputAdapter provideOtpAdapter(Context context) {
        return new OtpInputAdapter(context);
    }

    @Provides
    @PerForm
    public PinNumberVoipAdapter providePinNumberVoipAdapter(Context context) {
        return new PinNumberVoipAdapter(InsertPinNumbers.Companion.getPinNumberList(context));
    }


}
