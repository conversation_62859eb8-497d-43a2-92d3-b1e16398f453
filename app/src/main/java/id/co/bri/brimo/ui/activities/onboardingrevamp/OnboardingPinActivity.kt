package id.co.bri.brimo.ui.activities.onboardingrevamp

import android.annotation.SuppressLint
import android.app.Activity
import android.content.Intent
import android.os.Bundle
import android.view.KeyEvent
import android.view.View
import androidx.activity.OnBackPressedCallback
import androidx.activity.result.ActivityResultLauncher
import androidx.activity.result.contract.ActivityResultContracts
import androidx.recyclerview.widget.GridLayoutManager
import id.co.bri.brimo.R
import id.co.bri.brimo.adapters.PinNumberAdapter
import id.co.bri.brimo.adapters.baseadapter.base.BasePinAdapter
import id.co.bri.brimo.adapters.pinadapter.OtpInputAdapter
import id.co.bri.brimo.databinding.ActivityRegistrasiPinBinding
import id.co.bri.brimo.domain.config.Constant
import id.co.bri.brimo.domain.helpers.GeneralHelper
import id.co.bri.brimo.ui.activities.base.BaseActivity
import id.co.bri.brimo.ui.customviews.pin.InsertPinNumbers

class OnboardingPinActivity : BaseActivity(),
    View.OnKeyListener,
    PinNumberAdapter.OnPinNumberListener,
    BasePinAdapter.PinAdapterListener {

    private lateinit var binding: ActivityRegistrasiPinBinding

    private lateinit var otpRevampAdapter: OtpInputAdapter
    private lateinit var pinNumberAdapter: PinNumberAdapter
    private lateinit var pinOtpLayoutManager: GridLayoutManager
    private lateinit var pinPadLayoutManager: GridLayoutManager

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)

        binding = ActivityRegistrasiPinBinding.inflate(layoutInflater)
        setContentView(binding.root)

        onBackPressedDispatcher.addCallback(this, onBackPressedCallback)
        setupView()
    }

    private fun setupView() {
        GeneralHelper.setToolbarRevamp(
            this,
            binding.toolbarRevamp.toolbar,
            GeneralHelper.getString(R.string.buat_pin_brimo)
        )

        otpRevampAdapter = OtpInputAdapter(this)
        pinNumberAdapter =
            PinNumberAdapter(InsertPinNumbers.getPinNumberList(this))
        pinOtpLayoutManager = GridLayoutManager(this, 6)
        pinPadLayoutManager = GridLayoutManager(this, 3)

        pinNumberAdapter.onPinNumberListener = this
        otpRevampAdapter.setListener(this)
        binding.rvBox.layoutManager = pinOtpLayoutManager
        binding.rvBox.adapter = otpRevampAdapter
        binding.rvInputOtp.layoutManager = pinPadLayoutManager
        binding.rvInputOtp.adapter = pinNumberAdapter
    }

    override fun onKey(p0: View?, p1: Int, p2: KeyEvent?): Boolean {
        return false
    }

    override fun onPinClicked(pinNumber: Int) {
        otpRevampAdapter.addPin(pinNumber.toString())
    }

    override fun onDeleteClicked() {
        otpRevampAdapter.deletePin()
    }

    @SuppressLint("NotifyDataSetChanged")
    override fun notifyChanges() {
        otpRevampAdapter.notifyDataSetChanged()
    }

    override fun onComplete(string: String) {
        val intent = Intent(this, OnboardingConfirmPinActivity::class.java)
        intent.putExtra(Constant.PIN, string)
        startActivityIntent.launch(intent)
        otpRevampAdapter.deleteAllPin()
    }

    private var startActivityIntent: ActivityResultLauncher<Intent> = registerForActivityResult(
        ActivityResultContracts.StartActivityForResult()
    ) { result ->
        if (result.resultCode == Activity.RESULT_CANCELED && result.data != null) {
            if (result.data!!.hasExtra(Constant.TAG_ERROR_MESSAGE)) {
                GeneralHelper.showSnackBarRevamp(
                    this.findViewById(R.id.content),
                    result.data!!.getStringExtra(Constant.TAG_ERROR_MESSAGE)
                )
            } else {
                setResult(RESULT_CANCELED, result.data)
                finish()
            }
        }
    }

    private val onBackPressedCallback: OnBackPressedCallback =
        object : OnBackPressedCallback(true) {
            override fun handleOnBackPressed() {
                val intent = Intent()
                intent.putExtra(Constant.CHECK_POINT, 10)
                setResult(Activity.RESULT_CANCELED, intent)
                finish()
            }
        }
}