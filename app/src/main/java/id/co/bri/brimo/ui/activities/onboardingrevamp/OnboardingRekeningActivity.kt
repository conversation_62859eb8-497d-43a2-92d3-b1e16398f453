package id.co.bri.brimo.ui.activities.onboardingrevamp

import android.Manifest
import android.app.Activity
import android.content.ActivityNotFoundException
import android.content.Intent
import android.content.pm.PackageManager
import android.net.Uri
import android.os.Build
import android.os.Bundle
import android.util.Log
import androidx.activity.result.ActivityResultLauncher
import androidx.activity.result.contract.ActivityResultContracts
import androidx.annotation.RequiresApi
import androidx.core.app.ActivityCompat
import com.google.gson.Gson
import id.co.bri.brimo.R
import id.co.bri.brimo.contract.IPresenter.onboardingrevamp.IOnboardingRekeningPresenter
import id.co.bri.brimo.contract.IView.onboardingrevamp.IOnboardingRekeningView
import id.co.bri.brimo.databinding.ActivityOnboardingRekeningBinding
import id.co.bri.brimo.domain.config.Constant
import id.co.bri.brimo.domain.helpers.DeviceIDHelper
import id.co.bri.brimo.domain.helpers.GeneralHelper
import id.co.bri.brimo.models.NotifikasiModel
import id.co.bri.brimo.models.apimodel.request.ReferralRequest
import id.co.bri.brimo.models.apimodel.response.onboardingrevamp.ForceUpdateResponse
import id.co.bri.brimo.models.apimodel.response.onboardingrevamp.TabunganResponse
import id.co.bri.brimo.ui.activities.FastMenuActivity
import id.co.bri.brimo.ui.activities.base.BaseActivity
import id.co.bri.brimo.ui.customviews.dialog.DialogInformation
import id.co.bri.brimo.ui.fragments.bottomsheet.OpenBottomSheetGeneralFragment
import javax.inject.Inject

class OnboardingRekeningActivity : BaseActivity(),
    IOnboardingRekeningView,
    DialogInformation.OnActionClick {

    @Inject
    lateinit var presenter: IOnboardingRekeningPresenter<IOnboardingRekeningView>

    private lateinit var binding: ActivityOnboardingRekeningBinding

    private val permissionsBukRek = arrayOf(
        Manifest.permission.WRITE_EXTERNAL_STORAGE,
        Manifest.permission.READ_EXTERNAL_STORAGE,
        Manifest.permission.CAMERA,
        Manifest.permission.RECORD_AUDIO,
        Manifest.permission.ACCESS_FINE_LOCATION,
        Manifest.permission.ACCESS_COARSE_LOCATION
    )

    @RequiresApi(Build.VERSION_CODES.TIRAMISU)
    private val permissionsBukRek33 = arrayOf(
        Manifest.permission.READ_MEDIA_IMAGES,
        Manifest.permission.READ_MEDIA_AUDIO,
        Manifest.permission.READ_MEDIA_VIDEO,
        Manifest.permission.CAMERA,
        Manifest.permission.RECORD_AUDIO,
        Manifest.permission.POST_NOTIFICATIONS,
        Manifest.permission.ACCESS_FINE_LOCATION,
        Manifest.permission.ACCESS_COARSE_LOCATION
    )

    private fun permissionsCheck(): Array<String> {
        val permission: Array<String> = if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU) {
            permissionsBukRek33
        } else {
            permissionsBukRek
        }
        return permission
    }

    private var permissionsAccess = permissionsCheck()

    private val permissionsAll = 1

    private var isActivity = false

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        binding = ActivityOnboardingRekeningBinding.inflate(layoutInflater)
        setContentView(binding.root)

        GeneralHelper.setHelperContext(this)
        DeviceIDHelper.setHelperContext(this)

        injectDependency()
        intentExtra()
        setupViews()
        checkPermission()
    }

    override fun onNewIntent(intent: Intent) {
        super.onNewIntent(intent)

        presenter.checkUsername(intent)
    }

    override fun onCheckReferral(intent: Intent) {
        val data = intent.data
        if (data != null) {
            isActivity = false
            val code: String? = data.getQueryParameter("code")

            if (!isActivity) {
                presenter.checkDevice()
                presenter.setUrlReferral(GeneralHelper.getString(R.string.url_onboarding_check_referral_v3))
                presenter.sendReferral(ReferralRequest(code))
            }
        }
    }

    override fun onLoginExist() {
        val dialogInformation = DialogInformation(
            this,
            "maaf_maaf",
            GeneralHelper.getString(R.string.anda_sudah_login_brimo),
            GeneralHelper.getString(R.string.desc_sudah_login_brimo),
            GeneralHelper.getString(R.string.ok),
            this,
            true,
            true
        )
        val ft = supportFragmentManager.beginTransaction()
        ft.add(dialogInformation, null)
        ft.commitAllowingStateLoss()
    }

    override fun onClickAction() {
        val intent = Intent(this, FastMenuActivity::class.java)
        intent.addFlags(Intent.FLAG_ACTIVITY_CLEAR_TOP or Intent.FLAG_ACTIVITY_NEW_TASK)
        startActivity(intent)
        finish()
    }

    private fun intentExtra() {
        if (intent.hasExtra(Constant.TAG_ACTIVITY))
            isActivity = intent.getBooleanExtra(Constant.TAG_ACTIVITY, false)

        if (intent.hasExtra(Constant.TAG_NOTIF))
            parseDataNotifForeground(intent)
    }

    private fun checkPermission() {
        if (!hasPermissions(this, *permissionsAccess)) {
            ActivityCompat.requestPermissions(this, permissionsAccess, permissionsAll)
        } else {
            presenter.checkUsername(intent)
        }
    }

    private fun setupViews() {
        GeneralHelper.setToolbar(
            this,
            binding.toolbar.toolbar,
            GeneralHelper.getString(R.string.pembukaan_rekening)
        )

        binding.btnSubmit.setOnClickListener { presenter.getProgressOnboarding() }
    }

    private fun injectDependency() {
        activityComponent.inject(this)
        presenter.view = this
        presenter.start()
        presenter.setUrlProgress(GeneralHelper.getString(R.string.url_onboarding_check_progress_v3))
    }

    override fun onSavingsDataView(tabunganResponse: TabunganResponse) {
        isActivity = true
        val intent = Intent(this, OnboardingTabunganActivity::class.java)
        intent.putExtra(Constant.GENRES, tabunganResponse)
        intent.putExtra(Constant.PERSISTENCE_ID, presenter.persistenceId)
        startActivityIntent.launch(intent)
    }

    override fun onCheckPointView(status: Int, stringResponse: String) {
        isActivity = true
        val intent = Intent(this, OnboardingCheckPointActivity::class.java)
        intent.putExtra(Constant.CHECK_POINT, status)
        intent.putExtra(Constant.GENRES, stringResponse)
        startActivityIntent.launch(intent)
    }

    override fun onSkipProduct() {
        isActivity = true
        val intent = Intent(this, OnboardingPilihKantorActivity::class.java)
        intent.putExtra(Constant.SKIP, true)
        startActivityIntent.launch(intent)
    }

    override fun onSkipOffice(tabunganResponse: TabunganResponse) {
        isActivity = true
        val intent = Intent(this, OnboardingTabunganActivity::class.java)
        intent.putExtra(Constant.GENRES, tabunganResponse)
        intent.putExtra(Constant.SKIP, true)
        startActivityIntent.launch(intent)
    }

    override fun onPhotoKtp() {
        isActivity = true
        val intent = Intent(this, OnboardingCameraActivity::class.java)
        intent.putExtra(Constant.CHECK_POINT, 1)
        intent.putExtra(Constant.SKIP, true)
        startActivityIntent.launch(intent)
    }

    override fun onUpdateVersion(forceUpdate: ForceUpdateResponse) {
        OpenBottomSheetGeneralFragment.showDialogInformation(
            supportFragmentManager,
            "",
            "ic_forced_update",
            forceUpdate.title,
            forceUpdate.description,
            { openPlaystore() },
            false,
            forceUpdate.button
        )
    }

    private fun openPlaystore() {
        try {
            startActivity(
                Intent(
                    Intent.ACTION_VIEW,
                    Uri.parse("market://details?id=${Constant.APP_PACKAGE_NAME}")
                )
            )
        } catch (e: ActivityNotFoundException) {
            startActivity(
                Intent(
                    Intent.ACTION_VIEW,
                    Uri.parse("https://play.google.com/store/apps/details?id=${Constant.APP_PACKAGE_NAME}")
                )
            )
        }
    }

    override fun parseDataNotifForeground(intent: Intent?) {
        if (intent != null) {
            try {
                val extras = intent.extras
                if (extras != null) {
                    try {
                        val notifikasiString = extras.getString(Constant.TAG_NOTIF)
                        val notifikasiModel =
                            Gson().fromJson(notifikasiString, NotifikasiModel::class.java)
                        if (notifikasiModel != null) {
                            if (notifikasiModel.type == Constant.ONBOARDING_BRIMO && intent.hasExtra(
                                    Constant.TAG_ACTIVITY
                                )
                            ) {
                                presenter.getProgressOnboarding()
                            }
                        }
                    } catch (e: Exception) {
                        if (!GeneralHelper.isProd()) Log.e(
                            "TestNotif", "parseDataNotif: ", e
                        )
                    }
                }
            } catch (e: Exception) {
                if (!GeneralHelper.isProd()) Log.e(
                    "TestNotif", "parseDataNotif: ", e
                )
            }
        }
    }

    private var startActivityIntent: ActivityResultLauncher<Intent> = registerForActivityResult(
        ActivityResultContracts.StartActivityForResult()
    ) { result ->
        if (result.resultCode == Activity.RESULT_CANCELED && result.data != null) {
            setResult(RESULT_CANCELED, result.data)
            finish()
        }
    }

    override fun onDestroy() {
        presenter.stop()
        super.onDestroy()
    }
}