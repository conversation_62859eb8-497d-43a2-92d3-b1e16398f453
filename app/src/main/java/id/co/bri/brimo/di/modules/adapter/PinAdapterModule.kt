package id.co.bri.brimo.di.modules.adapter

import id.co.bri.brimo.adapters.baseadapter.base.BasePinAdapter
import dagger.Module
import dagger.Provides
import java.util.*

@Module
class PinAdapterModule(private val basePinAdapter: BasePinAdapter) {

    @Provides
    fun provideAdapter(): BasePinAdapter {
        return basePinAdapter
    }

    @Provides
    fun provideStack(): Stack<String> {
        return Stack()
    }

    @Provides
    fun provideStringBuilder(): StringBuilder {
        return StringBuilder("")
    }

}