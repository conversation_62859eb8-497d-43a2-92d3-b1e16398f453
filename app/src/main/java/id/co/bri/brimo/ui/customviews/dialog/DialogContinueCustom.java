package id.co.bri.brimo.ui.customviews.dialog;

import android.annotation.SuppressLint;
import android.app.Dialog;
import android.graphics.drawable.ColorDrawable;
import android.os.Bundle;
import android.view.View;
import android.view.ViewGroup;
import android.view.Window;
import android.widget.Button;
import android.widget.LinearLayout;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.fragment.app.DialogFragment;
import id.co.bri.brimo.R;

public class DialogContinueCustom extends DialogFragment {
    private Dialog alertDialog;
    private Button btnYes;
    private Button btnNo;
    private TextView tvTitle;
    private TextView tvSubtitle;
    private String mTitle;
    private String mSubtitle;
    private String mButtonYes = "";
    private String mButtonNo = "";
    private boolean isCancel = false;
    private boolean isOneButton = false;
    private DialogContinueDefaultListener dialogDefaultListener;

    public DialogContinueCustom(DialogContinueDefaultListener clickListener, String title, String subTitle) {
        this.dialogDefaultListener = clickListener;
        this.mTitle = title;
        this.mSubtitle = subTitle;
    }

    public DialogContinueCustom(DialogContinueDefaultListener clickListener, String title, String subTitle,
                                String sButtonYes, String sButtonNo, boolean isCancel) {
        this.dialogDefaultListener = clickListener;
        this.mTitle = title;
        this.mSubtitle = subTitle;
        this.mButtonYes = sButtonYes;
        this.mButtonNo = sButtonNo;
        this.isCancel = isCancel;
    }

    public DialogContinueCustom(DialogContinueDefaultListener clickListener, String title, String subTitle,
                                String sButtonYes, String sButtonNo, boolean isCancel, boolean isOneButton) {
        this.dialogDefaultListener = clickListener;
        this.mTitle = title;
        this.mSubtitle = subTitle;
        this.mButtonYes = sButtonYes;
        this.mButtonNo = sButtonNo;
        this.isCancel = isCancel;
        this.isOneButton = isOneButton;
    }

    @SuppressLint("ValidFragment")
    public DialogContinueCustom() {
    }

    @NonNull
    @Override
    public Dialog onCreateDialog(@Nullable Bundle savedInstanceState) {
        alertDialog = new Dialog(getActivity()) {
            @Override
            public void onBackPressed() {
                super.onBackPressed();
                getActivity().onBackPressed();
            }
        };
        alertDialog.setCanceledOnTouchOutside(isCancel);
        alertDialog.getWindow().requestFeature(Window.FEATURE_NO_TITLE);
        alertDialog.getWindow().setBackgroundDrawable(new ColorDrawable(0));
        alertDialog.setContentView(R.layout.dialog_continue_custom);
        alertDialog.setOnKeyListener((dialogInterface, i, keyEvent) -> false);
        alertDialog.show();

        initView();

        btnNo.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View view) {
                if (dialogDefaultListener != null) {
                    dialogDefaultListener.onClickContinueNo();
                }
                dismiss();
            }
        });

        btnYes.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View view) {
                if (dialogDefaultListener != null) {
                    dialogDefaultListener.onClickContinueYes();
                }
                dismiss();
            }
        });
        return alertDialog;
    }

    private void initView() {

        btnYes = alertDialog.findViewById(R.id.btn_yes);
        btnNo = alertDialog.findViewById(R.id.btn_no);
        tvTitle = alertDialog.findViewById(R.id.tv_title);
        tvSubtitle = alertDialog.findViewById(R.id.tv_subtitle);

        tvTitle.setText(mTitle);
        tvSubtitle.setText(mSubtitle);

        if (isOneButton){
            btnNo.setVisibility(View.GONE);
            // Create a LinearLayout with horizontal orientation
            LinearLayout linearLayout = new LinearLayout(requireContext());
            linearLayout.setOrientation(LinearLayout.HORIZONTAL);
            linearLayout.setLayoutParams(new LinearLayout.LayoutParams(
                    LinearLayout.LayoutParams.MATCH_PARENT,
                    LinearLayout.LayoutParams.MATCH_PARENT
            ));
            LinearLayout.LayoutParams params1 = new LinearLayout.LayoutParams(
                    ViewGroup.LayoutParams.MATCH_PARENT, // Width is set to 0 so that weight can be applied
                    LinearLayout.LayoutParams.WRAP_CONTENT,
                    1f // Weight is set to 1
            );
            btnYes.setLayoutParams(params1);
            if (!mButtonYes.isEmpty())
                btnYes.setText(mButtonYes);

        }else {
            btnNo.setVisibility(View.VISIBLE);
            if (!mButtonYes.isEmpty())
                btnYes.setText(mButtonYes);

            if (!mButtonNo.isEmpty())
                btnNo.setText(mButtonNo);
        }

    }

    public interface DialogContinueDefaultListener {
        void onClickContinueYes();

        void onClickContinueNo();
    }

}
