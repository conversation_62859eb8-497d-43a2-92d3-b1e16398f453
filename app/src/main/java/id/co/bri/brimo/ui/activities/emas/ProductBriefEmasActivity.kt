package id.co.bri.brimo.ui.activities.emas

import android.app.Activity
import android.content.Intent
import android.os.Bundle
import android.os.SystemClock
import android.view.View
import android.widget.LinearLayout
import androidx.fragment.app.Fragment
import androidx.viewpager.widget.ViewPager
import id.co.bri.brimo.R
import id.co.bri.brimo.adapters.CatatanKeuanganAdapter
import id.co.bri.brimo.contract.IPresenter.emas.IProductBriefEmasPresenter
import id.co.bri.brimo.contract.IView.emas.IProductBriefEmasView
import id.co.bri.brimo.databinding.ActivityProductBriefEmasBinding
import id.co.bri.brimo.domain.config.Constant
import id.co.bri.brimo.domain.helpers.GeneralHelper
import id.co.bri.brimo.models.apimodel.response.onExceptionWH
import id.co.bri.brimo.models.apimodel.response.QuestionResponse
import id.co.bri.brimo.models.apimodel.response.emas.PersonalDataResponse
import id.co.bri.brimo.models.apimodel.response.emas.ProductBrief
import id.co.bri.brimo.models.apimodel.response.emas.SafetyModeDrawerResponse
import id.co.bri.brimo.ui.activities.DetailPusatBantuanActivity
import id.co.bri.brimo.ui.activities.base.BaseActivity
import id.co.bri.brimo.ui.fragments.BottomFragmentSafteyMode
import id.co.bri.brimo.ui.fragments.DetailTabTabunganFragment
import id.co.bri.brimo.ui.fragments.DialogFragmentRc02
import javax.inject.Inject

class ProductBriefEmasActivity : BaseActivity(), ViewPager.OnPageChangeListener,
    View.OnClickListener, IProductBriefEmasView, BottomFragmentSafteyMode.DialogDefaulListener,
    DialogFragmentRc02.DialogDefaultListener {
    lateinit var binding: ActivityProductBriefEmasBinding
    lateinit var linearLayout: LinearLayout
    private var counter = 1
    private var responseSafety: SafetyModeDrawerResponse? = null
    private var dialog: DialogFragmentRc02? = null

    companion object {
        private var titleTab: MutableList<String> = mutableListOf()
        private var webTab: MutableList<String> = mutableListOf()
        private var descTabungan: String? = null
        private var imageTabungan: String? = null

        @JvmStatic
        fun launchIntent(caller: Activity, productBrief: ProductBrief) {
            val intent = Intent(caller, ProductBriefEmasActivity::class.java)
            descTabungan = productBrief.description
            imageTabungan = productBrief.imageUrl
            titleTab.clear()
            webTab.clear()
            for (i in productBrief.tab.indices) {
                titleTab.add(productBrief.tab[i].title)
                webTab.add(productBrief.tab[i].content)
            }
            caller.startActivityForResult(intent, Constant.REQ_BUKA_REKENING)
        }
    }

    @Inject
    lateinit var presenter: IProductBriefEmasPresenter<IProductBriefEmasView>

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        binding = ActivityProductBriefEmasBinding.inflate(layoutInflater)
        setContentView(binding.root)
        injectDependency()

        setupView()
    }

    private fun setupView() {
        GeneralHelper.setToolbar(this, binding.tbRekening.toolbar, "Detail Tabungan")
        GeneralHelper.loadImageUrlClearCache(
            this,
            imageTabungan,
            binding.imageRekening,
            R.drawable.bri,
            0
        )
        binding.textRekening.text = descTabungan
        val fragmentList: MutableList<Fragment> = java.util.ArrayList()
        for (i in webTab.indices) {
            fragmentList.add(DetailTabTabunganFragment(webTab[i]))
        }
        val viewAdapter = CatatanKeuanganAdapter(
            supportFragmentManager,
            this,
            fragmentList,
            titleTab
        )
        binding.viewpager.adapter = viewAdapter
        binding.tabDetailRekening.setViewPager(binding.viewpager)
        binding.tabDetailRekening.setOnPageChangeListener(this)
        linearLayout = binding.tabDetailRekening.getChildAt(0) as LinearLayout
        GeneralHelper.changeTabsFontBoldRevamp(this, linearLayout, 0)

        binding.btnRekeningBaru.setOnClickListener(this)
    }

    fun injectDependency() {
        activityComponent.inject(this)
        //get prresenter from dagger inject
        presenter.view = this
        presenter.setUrl(GeneralHelper.getString(R.string.url_onboard_form))
        presenter.setUrlPusatBantuan(GeneralHelper.getString(R.string.url_safety_pusat_bantuan))
        presenter.start()
    }

    override fun onPageScrolled(position: Int, positionOffset: Float, positionOffsetPixels: Int) {
    }

    override fun onPageSelected(position: Int) {
        GeneralHelper.changeTabsFontBoldRevamp(this, linearLayout, position)
    }

    override fun onPageScrollStateChanged(state: Int) {
    }

    override fun onClick(p0: View?) {
        if (SystemClock.elapsedRealtime() - mLastClickTime < 1000) {
            return
        }
        mLastClickTime = SystemClock.elapsedRealtime()

        when (p0!!.id) {
            R.id.btn_rekening_baru -> {
                setEventAppsFlyerClickedOpenSavings()
                if (counter == 1) {
                    presenter.getDataProductBriefEmas()
                } else {
                    exceptionSM(responseSafety!!)
                }
            }
        }
    }

    private fun setEventAppsFlyerClickedOpenSavings() {
        val eventValue: MutableMap<String, Any> = HashMap()
        eventValue[Constant.CUSTOMER_ID] = presenter.persistenceId
        trackAppsFlyerAnalyticEvent("emas_start_buka_tabungan", eventValue)
    }

    override fun onSuccessGetPersonalData(response: PersonalDataResponse) {
        InformasiPribadiActivity.launchIntent(this, response)
    }

    override fun exceptionSM(drawer: SafetyModeDrawerResponse) {
        responseSafety = drawer
        counter += 1

        var bottomFragmentSafteyMode = BottomFragmentSafteyMode(
            this,
            drawer.drawerContent!!.title,
            drawer.drawerContent!!.description,
            "OK",
            "Pelajari Lebih Lanjut",
            Constant.OPEN_ACCOUNT_S3f
        )
        bottomFragmentSafteyMode!!.show(supportFragmentManager, "")
    }

    override fun exceptionEODEOM(onOnExceptionWH: onExceptionWH) {
        dialog = DialogFragmentRc02.newInstance(
            this,
            onOnExceptionWH
        )
        dialog!!.show(supportFragmentManager, "")
    }

    override fun onSuccessGetPusatBantuan(response: QuestionResponse) {
        DetailPusatBantuanActivity.launchIntent(this, response, response.getTopicName());
    }

    override fun onClickToSafety() {
        presenter.getPusatBantuanSafety(Constant.ID_PUSAT_BANTUAN_SAFETY_MODE)
    }


    override fun onClickDialogRc02() {
        dialog!!.dismiss()
    }

    override fun onActivityResult(requestCode: Int, resultCode: Int, data: Intent?) {
        super.onActivityResult(requestCode, resultCode, data)
        if (requestCode == Constant.REQ_BUKA_REKENING && resultCode == Constant.REQ_UPDATE && data != null) {
            this.setResult(Constant.REQ_UPDATE, data)
            finish()
        } else if (requestCode == Constant.REQ_BUKA_REKENING && resultCode == RESULT_OK) {
            setResult(RESULT_OK, data)
            finish()
        } else if (requestCode == Constant.REQ_UPDATE && resultCode == RESULT_OK && data != null) {
            this.setResult(Constant.REQ_UPDATE, data)
            finish()
        } else if (resultCode == RESULT_OK && data != null) {
            this.setResult(Constant.REQ_UPDATE, data)
            finish()
        } else if (resultCode == RESULT_FIRST_USER && data != null) {
            this.setResult(RESULT_FIRST_USER, data)
            finish()
        }
    }
}