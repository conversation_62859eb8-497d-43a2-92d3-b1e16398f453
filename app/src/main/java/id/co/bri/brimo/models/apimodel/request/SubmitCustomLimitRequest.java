package id.co.bri.brimo.models.apimodel.request;

import com.google.gson.annotations.Expose;
import com.google.gson.annotations.SerializedName;

public class SubmitCustomLimitRequest {
    @SerializedName("pin")
    @Expose
    private String pin;
    @SerializedName("product_id")
    @Expose
    private String productId;
    @SerializedName("custom_limit")
    @Expose
    private String limitCustom;
    @SerializedName("account_number")
    @Expose
    private String accountNumber;

    public SubmitCustomLimitRequest(String pin, String productId, String limitCustom, String accountNumber) {
        this.pin = pin;
        this.productId = productId;
        this.limitCustom = limitCustom;
        this.accountNumber = accountNumber;
    }


    public String getPin() {
        return pin;
    }

    public void setPin(String pin) {
        this.pin = pin;
    }

    public String getProductId() {
        return productId;
    }

    public void setProductId(String productId) {
        this.productId = productId;
    }

    public String getLimitCustom() {
        return limitCustom;
    }

    public void setLimitCustom(String limitCustom) {
        this.limitCustom = limitCustom;
    }

    public String getAccountNumber() {
        return accountNumber;
    }

    public void setAccountNumber(String accountNumber) {
        this.accountNumber = accountNumber;
    }
}
