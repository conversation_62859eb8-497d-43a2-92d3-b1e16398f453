package id.co.bri.brimo.models.apimodel.request;

import com.google.gson.annotations.SerializedName;

public class UpdateSavedTitleTfRequest extends UpdateSavedRequestTfMethod {
    @SerializedName("save_as")
    private String saveAs;
    @SerializedName("subtitle")
    private String subtitle;

    public UpdateSavedTitleTfRequest(String savedId, String paymentType, String tfMethod, String saveAs) {
        super(savedId, paymentType, tfMethod);
        this.saveAs = saveAs;
    }

    public UpdateSavedTitleTfRequest(String savedId, String saveAs, String subtitle) {
        super(savedId);
        this.saveAs = saveAs;
        this.subtitle = subtitle;
    }

    public UpdateSavedTitleTfRequest(String savedId, String saveAs) {
        super(savedId);
        this.saveAs = saveAs;
    }

    public String getSaveAs() {
        return saveAs;
    }

    public void setSaveAs(String saveAs) {
        this.saveAs = saveAs;
    }

    public String getSubtitle() {
        return subtitle;
    }

    public void setSubtitle(String subtitle) {
        this.subtitle = subtitle;
    }
}
