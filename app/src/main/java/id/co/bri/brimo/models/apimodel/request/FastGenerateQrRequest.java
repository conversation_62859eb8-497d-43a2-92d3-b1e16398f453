package id.co.bri.brimo.models.apimodel.request;

import com.google.gson.annotations.Expose;
import com.google.gson.annotations.SerializedName;

public class FastGenerateQrRequest extends FastMenuRequest {
    @SerializedName("account_number")
    @Expose
    private String accountNumber;
    @SerializedName("pin")
    @Expose
    private String pin;

    public FastGenerateQrRequest( FastMenuRequest request, String pin, String accountNumberr) {
        super(request.getUsername(), request.getTokenKey());
        this.accountNumber = accountNumberr;
        this.pin = pin;

    }

    public String getAccountNumber() {
        return accountNumber;
    }

    public void setAccountNumber(String referenceNumber) {
        this.accountNumber = referenceNumber;
    }

    public String getPin() {
        return pin;
    }

    public void setPin(String pin) {
        this.pin = pin;
    }




}
