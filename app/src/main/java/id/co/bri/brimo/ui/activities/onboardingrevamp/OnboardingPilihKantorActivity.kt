package id.co.bri.brimo.ui.activities.onboardingrevamp

import android.Manifest
import android.annotation.SuppressLint
import android.app.Activity
import android.app.SearchManager
import android.content.Intent
import android.content.pm.PackageManager
import android.os.Bundle
import android.provider.Settings
import android.util.TypedValue
import android.view.View
import android.widget.EditText
import android.widget.ImageView
import androidx.activity.result.ActivityResultLauncher
import androidx.activity.result.contract.ActivityResultContracts
import androidx.appcompat.widget.SearchView
import androidx.core.content.res.ResourcesCompat
import androidx.recyclerview.widget.LinearLayoutManager
import com.ethanhua.skeleton.Skeleton
import com.ethanhua.skeleton.SkeletonScreen
import id.co.bri.brimo.R
import id.co.bri.brimo.adapters.onboarding.OnboardingListLokasiKantorAdapter
import id.co.bri.brimo.contract.IPresenter.onboardingrevamp.IOnboardingPilihKantorPresenter
import id.co.bri.brimo.contract.IView.onboardingrevamp.IOnboardingPilihKantorView
import id.co.bri.brimo.databinding.ActivityPilihanKantorBinding
import id.co.bri.brimo.domain.config.Constant
import id.co.bri.brimo.domain.helpers.GeneralHelper
import id.co.bri.brimo.domain.helpers.GpsTracker
import id.co.bri.brimo.models.apimodel.request.LocationNonCifRequest
import id.co.bri.brimo.models.apimodel.request.SelectProductRequest
import id.co.bri.brimo.models.apimodel.response.ListKantorResponse
import id.co.bri.brimo.ui.activities.base.BaseActivity
import javax.inject.Inject

class OnboardingPilihKantorActivity : BaseActivity(),
    IOnboardingPilihKantorView,
    OnboardingListLokasiKantorAdapter.OnClickItem {

    @Inject
    lateinit var presenter: IOnboardingPilihKantorPresenter<IOnboardingPilihKantorView>

    private lateinit var binding: ActivityPilihanKantorBinding

    private lateinit var lokasiKantorAdapter: OnboardingListLokasiKantorAdapter
    private var offices: List<ListKantorResponse.Office> = emptyList()

    private var skeletonScreen: SkeletonScreen? = null

    private var inLatitude: String = "0.0"
    private var inLongitude: String = "0.0"

    private var onboardingId = ""
    private var querySearch = ""
    private var address = ""
    private var productId = ""
    private var isSkipProduct = false

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        binding = ActivityPilihanKantorBinding.inflate(layoutInflater)
        setContentView(binding.root)

        injectDependency()
        intentExtra()
        setupViews()
        getLocation()
        searchLocation()
    }

    private fun intentExtra() {
        if (intent.hasExtra(Constant.KATEGORI_ID))
            productId = intent.getStringExtra(Constant.KATEGORI_ID).toString()

        if (intent.hasExtra(Constant.SKIP))
            isSkipProduct = intent.getBooleanExtra(Constant.SKIP, false)
    }

    private fun injectDependency() {
        activityComponent.inject(this)
        presenter.view = this
        presenter.start()
        presenter.setUrlLocation(GeneralHelper.getString(R.string.url_onboarding_get_branch_v3))
        presenter.setUrlProduct(GeneralHelper.getString(R.string.url_onboarding_select_product_v3))
        onboardingId = presenter.getDeviceId()
    }

    private fun setupViews() {
        GeneralHelper.setToolbarRevamp(
            this,
            binding.toolbar.toolbar,
            GeneralHelper.getString(R.string.choose_bri_office)
        )

        binding.lyTerpilih.visibility = View.GONE

        val imageView: ImageView =
            binding.searchviewBukarek.findViewById(androidx.appcompat.R.id.search_mag_icon)
        imageView.setColorFilter(GeneralHelper.getColor(R.color.neutral_dark40))
        val editText: EditText =
            binding.searchviewBukarek.findViewById(androidx.appcompat.R.id.search_src_text)
        editText.setHintTextColor(resources.getColor(R.color.neutral_light60, theme))
        editText.typeface = ResourcesCompat.getFont(this, R.font.bri_digital_text_medium)
        editText.setTextSize(TypedValue.COMPLEX_UNIT_SP, 14f)

        lokasiKantorAdapter = OnboardingListLokasiKantorAdapter(this, offices, this)
        val layoutManager = LinearLayoutManager(this)
        binding.recyclerView2.setHasFixedSize(true)
        binding.recyclerView2.layoutManager = layoutManager
        binding.recyclerView2.adapter = lokasiKantorAdapter

        skeletonScreen = Skeleton.bind(binding.recyclerView2)
            .adapter(lokasiKantorAdapter)
            .shimmer(true)
            .angle(10)
            .frozen(false)
            .duration(1200)
            .load(R.layout.item_skeleton_pilih_kantor)
            .show()
    }

    private fun getLocation() {
        val gpsTracker = GpsTracker(this)
        if (gpsTracker.canGetLocation()) {
            if (gpsTracker.latitude.toString().isNotEmpty())
                inLatitude = gpsTracker.latitude.toString()
            if (gpsTracker.longitude.toString().isNotEmpty())
                inLongitude = gpsTracker.longitude.toString()
            presenter.sendLocation(createLocationRequest())
        } else {
            showDialogEnableLocation()
        }
    }

    private fun searchLocation() {
        val searchManager = this.getSystemService(SEARCH_SERVICE) as SearchManager

        binding.searchviewBukarek.setSearchableInfo(searchManager.getSearchableInfo(this.componentName))

        binding.searchviewBukarek.maxWidth = Int.MAX_VALUE
        binding.searchviewBukarek.setOnQueryTextListener(object : SearchView.OnQueryTextListener {
            override fun onQueryTextSubmit(query: String): Boolean {
                querySearch = query
                binding.lyNotFound.visibility = View.GONE
                binding.llKantorTerdekat.visibility = View.VISIBLE
                presenter.sendLocation(createLocationRequest())
                skeletonScreen!!.show()
                return false
            }

            override fun onQueryTextChange(newText: String): Boolean {
                address = newText
                return false
            }
        })
    }

    override fun onClickKantor(office: ListKantorResponse.Office) {
        presenter.sendSelectProduct(
            SelectProductRequest(
                onboardingId,
                productId,
                office.branchCode
            )
        )
    }

    @SuppressLint("NotifyDataSetChanged")
    override fun onLocationDefault(kantorResponse: ListKantorResponse) {
        offices = kantorResponse.officeList
        skeletonScreen?.hide()
        lokasiKantorAdapter.setItems(offices)
        lokasiKantorAdapter.notifyDataSetChanged()
        binding.recyclerView2.visibility = View.VISIBLE
        binding.lyNotFound.visibility = View.GONE
        binding.llKantorTerdekat.visibility = View.VISIBLE
    }

    override fun onFailedLocation(msg: String) {
        dataNotFound()
    }

    private fun dataNotFound() {
        binding.lyNotFound.visibility = View.VISIBLE
        skeletonScreen!!.hide()
        binding.llKantorTerdekat.visibility = View.GONE

        if (checkSelfPermission(Manifest.permission.ACCESS_COARSE_LOCATION) != PackageManager.PERMISSION_GRANTED) {
            binding.lyTerpilih.visibility = View.GONE
            binding.tvDataTidakDitemukan.text =
                GeneralHelper.getString(R.string.title_internet_problem)
            binding.tvDescNotFound.text = GeneralHelper.getString(R.string.desc_internet_problem)
        } else {
            if (querySearch != "") {
                binding.lyTerpilih.visibility = View.GONE
                binding.tvDataTidakDitemukan.text =
                    GeneralHelper.getString(R.string.title_kantor_not_found)
                binding.tvDescNotFound.text = String.format(
                    GeneralHelper.getString(R.string.desc_kantor_not_found),
                    querySearch
                )
            } else {
                binding.lyTerpilih.visibility = View.GONE
                binding.tvDataTidakDitemukan.text =
                    GeneralHelper.getString(R.string.title_internet_problem)
                binding.tvDescNotFound.text =
                    GeneralHelper.getString(R.string.desc_internet_problem)
            }
        }
    }

    override fun createLocationRequest(): LocationNonCifRequest {
        return LocationNonCifRequest(onboardingId, inLatitude, inLongitude, address)
    }

    override fun onSuccessOffice() {
        val intent = Intent(this, OnboardingCameraActivity::class.java)
        intent.putExtra(Constant.CHECK_POINT, 1)
        startActivityIntent.launch(intent)
    }

    private var startActivityIntent: ActivityResultLauncher<Intent> = registerForActivityResult(
        ActivityResultContracts.StartActivityForResult()
    ) { result ->
        if (result.resultCode == Activity.RESULT_CANCELED && result.data != null) {
            if (isSkipProduct) {
                launchIntentCheckPointClass(result.data!!)
            } else {
                setResult(RESULT_CANCELED, result.data)
                finish()
            }
        }
    }

    private fun launchIntentCheckPointClass(data: Intent) {
        val intent = Intent(this, OnboardingCheckPointActivity::class.java)
        intent.putExtra(
            Constant.CHECK_POINT,
            data.getIntExtra(Constant.CHECK_POINT, 0)
        )
        intent.putExtra(Constant.NAME, Constant.ONBOARDING_BRIMO)
        startActivityIntent.launch(intent)
        finish()
    }

    override fun onDestroy() {
        presenter.stop()
        super.onDestroy()
    }
}