package id.co.bri.brimo.ui.activities.voiceassistant

import android.animation.Animator
import android.animation.Animator.AnimatorListener
import android.animation.ValueAnimator
import android.app.Activity
import android.content.ActivityNotFoundException
import android.content.Intent
import android.content.pm.PackageManager
import android.graphics.Bitmap
import android.graphics.Color
import android.net.Uri
import android.os.Bundle
import android.os.Environment
import android.os.Handler
import android.provider.Settings
import android.speech.RecognitionListener
import android.speech.RecognizerIntent
import android.speech.SpeechRecognizer
import android.speech.tts.TextToSpeech
import android.util.Log
import android.view.View
import android.view.animation.AccelerateDecelerateInterpolator
import android.view.animation.Animation
import android.widget.TextView
import android.widget.Toast
import androidx.core.app.ActivityCompat
import androidx.core.content.FileProvider
import androidx.core.view.ViewCompat
import androidx.core.view.WindowInsetsCompat
import androidx.core.view.isVisible
import androidx.recyclerview.widget.LinearLayoutManager
import id.co.bri.brimo.BuildConfig
import id.co.bri.brimo.R
import id.co.bri.brimo.adapters.DataTransaksiRevampAdapter
import id.co.bri.brimo.adapters.receipt.ReceiptAccountDataAdapter
import id.co.bri.brimo.adapters.receipt.ReceiptDataTotalAdapter
import id.co.bri.brimo.adapters.voiceassistant.SavedRecipientAdapter
import id.co.bri.brimo.adapters.voiceassistant.TransferMethodAdapter
import id.co.bri.brimo.contract.IPresenter.voiceassistant.IVoiceAssistantPresenter
import id.co.bri.brimo.contract.IView.voiceassistant.IVoiceAssistantView
import id.co.bri.brimo.databinding.ActivityVoiceAssistantBinding
import id.co.bri.brimo.domain.config.AppConfig
import id.co.bri.brimo.domain.config.Constant
import id.co.bri.brimo.domain.config.VoiceAssistantConfig
import id.co.bri.brimo.domain.helpers.GeneralHelper
import id.co.bri.brimo.domain.helpers.SizeHelper
import id.co.bri.brimo.domain.helpers.calendar.CalendarHelper
import id.co.bri.brimo.domain.helpers.image.ImageHelper
import id.co.bri.brimo.models.apimodel.request.voiceassistant.VoiceAssistantRequest
import id.co.bri.brimo.models.apimodel.response.DataView
import id.co.bri.brimo.models.apimodel.response.DetailListType
import id.co.bri.brimo.models.apimodel.response.GeneralResponse
import id.co.bri.brimo.models.apimodel.response.MetodeTransfer
import id.co.bri.brimo.models.apimodel.response.ReceiptRevampResponse
import id.co.bri.brimo.models.apimodel.response.voiceassistant.CheckBalanceVoiceAssistantResponse
import id.co.bri.brimo.models.apimodel.response.voiceassistant.InquiryTransferVoiceAssistantResponse
import id.co.bri.brimo.models.apimodel.response.voiceassistant.SavedRecipientVoiceAssistantResponse
import id.co.bri.brimo.models.apimodel.response.voiceassistant.TransferMethod
import id.co.bri.brimo.models.apimodel.response.voiceassistant.TransferMethodVoiceAssistantResponse
import id.co.bri.brimo.models.apimodel.response.voiceassistant.VoiceAssistantResponse
import id.co.bri.brimo.ui.activities.FastMenuActivity
import id.co.bri.brimo.ui.activities.LupaPinFastActivity
import id.co.bri.brimo.ui.activities.base.BaseActivity
import id.co.bri.brimo.ui.customviews.dialog.DialogExitCustom
import id.co.bri.brimo.ui.fragments.FragmentMetode
import id.co.bri.brimo.ui.fragments.FragmentMetode.SelectMetode
import id.co.bri.brimo.ui.fragments.PinFragment
import id.co.bri.brimo.ui.fragments.PinFragment.SendPin
import id.co.bri.brimo.ui.fragments.bottomsheet.OpenBottomSheetGeneralFragment.showDialogInformationWithAction
import id.co.bri.brimo.ui.fragments.voiceassistant.UpdateNominalVoiceAssistantFragment
import java.io.File
import java.io.FileOutputStream
import java.util.Locale
import javax.inject.Inject

class VoiceAssistantActivity : BaseActivity(), SendPin, UpdateNominalVoiceAssistantFragment.DialogUpdateNominalListener,
    IVoiceAssistantView, SavedRecipientAdapter.OnItemClickListener, TransferMethodAdapter.OnItemClickListener, SelectMetode, TextToSpeech.OnInitListener, DialogExitCustom.DialogClickYesNoListener {

    private val binding: ActivityVoiceAssistantBinding by lazy { ActivityVoiceAssistantBinding.inflate(layoutInflater) }
    private val transferMethodAdapter: TransferMethodAdapter by lazy { TransferMethodAdapter(this) }
    private val savedRecipientAdapter: SavedRecipientAdapter by lazy { SavedRecipientAdapter(this) }
    private val imageHelper: ImageHelper by lazy { ImageHelper(this) }

    private lateinit var receiptDataTotalAdapter: ReceiptDataTotalAdapter
    private lateinit var dataTransaksiAdapter: DataTransaksiRevampAdapter
    private lateinit var speechRecognizer: SpeechRecognizer
    private lateinit var recognizerIntent: Intent
    private lateinit var tts: TextToSpeech

    @Inject
    lateinit var voiceAssistantPresenter: IVoiceAssistantPresenter<IVoiceAssistantView>

    private var referenceNumber: String? = null
    private var idStateVoiceAssistant: String? = null

    private var audioPermission = arrayOf(android.Manifest.permission.RECORD_AUDIO)
    private var isActionFromVoice = false
    private var isNeededToResetView = false
    private var isOnTransferConfirmation = false
    private var isOnSavedRecipientList = false
    private var isShowAll = false
    private var isReadytoShare = false
    private var isShared = false
    private var isDialogForInstallLanguageTTS = true
    private var hasUsedFeature = false
    private var nextActionAfterInputPin = VoiceAssistantConfig.INPUT_PIN_TRANSFER
    private var animationMicHandler = Handler()

    /**
     * Listener for handle speech to text or voice recognition listener
     */
    private val mRecognitionListener = object : RecognitionListener {
        override fun onReadyForSpeech(params: Bundle?) {
            setListeningView()
        }

        override fun onBeginningOfSpeech() {}

        override fun onRmsChanged(rmsdB: Float) {
            val scale = 0.25f + (rmsdB / 10.0f)

            binding.imgAnimation1.animate()
                .scaleX(scale)
                .scaleY(scale)
                .setDuration(50) // Adjust the duration as needed
                .setInterpolator(AccelerateDecelerateInterpolator()) // Use an interpolator for smooth animation
                .start()
        }

        override fun onBufferReceived(buffer: ByteArray?) {}

        override fun onEndOfSpeech() {
            speechRecognizerStopListening()
            if (!GeneralHelper.isProd()) Log.d("Latency Count", "onEndOfSpeech")
        }

        override fun onError(error: Int) {
            setStopListeningView(false)
            speechRecognizerStopListening()
            resetSpeechRecognizer()
        }

        override fun onResults(results: Bundle?) {
            var textFromSpeech = ""
            val matches = results?.getStringArrayList(SpeechRecognizer.RESULTS_RECOGNITION)

            if (matches != null) {
                for (result in matches) {
                    textFromSpeech += """
                        $result
                    """.trimIndent()
                }
            }

            if (!GeneralHelper.isProd()) Log.d("Latency Count", "onResultsSTT")

            binding.tvUserTranscribedText.text = textFromSpeech

            voiceAssistantPresenter.sendTextToVoiceAssistant(
                VoiceAssistantRequest(
                    tokenKey = voiceAssistantPresenter.getTokenKey(),
                    username = voiceAssistantPresenter.getUsername(),
                    action = VoiceAssistantConfig.CHAT,
                    id = idStateVoiceAssistant,
                    text = textFromSpeech,
                    referenceNumber = referenceNumber,
                    pin = null,
                    nextAction = null,
                    codeTransferMethod = null,
                    amountTransfer = null,
                    accountName = null,
                    accountBank = null,
                    account = null,
                    bankCode = null
                )
            )

            resetSpeechRecognizer()
        }

        override fun onPartialResults(partialResults: Bundle?) {}

        override fun onEvent(eventType: Int, params: Bundle?) {}
    }


    /**
     * variable untuk menjalankan animasi ketika mic dalam state listen atau sedang mendengarkan
     */
    private var runnable = object : Runnable {
        override fun run() {
            with(binding) {
                imgAnimation3.animate().scaleX(1.3f). scaleY(1.3f).alpha(0.7f).setDuration(500)
                    .withEndAction {
                        imgAnimation3.scaleX = 1f
                        imgAnimation3.scaleY = 1f
                        imgAnimation3.alpha = 1f
                    }

                imgAnimation2.animate().scaleX(1.2f).scaleY(1.2f).alpha(1f).setDuration(1000)
                    .withEndAction {
                        imgAnimation2.scaleX = 1f
                        imgAnimation2.scaleY = 1f
                        imgAnimation2.alpha = 1f
                    }
            }

            animationMicHandler.postDelayed(this, 1500)
        }
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContentView(binding.root)
        
        ViewCompat.setOnApplyWindowInsetsListener(binding.root) { v, insets ->
            val systemBars = insets.getInsets(WindowInsetsCompat.Type.systemBars())
            v.setPadding(systemBars.left, systemBars.top, systemBars.right, systemBars.bottom)
            insets
        }

        injectDependency()
        initView()
        initRv()
        initSpeechRecognizer()
        initTts()
        setRecognizerIntent()
        requestAudioPermissions()
        setBtnMicrophoneClickListener()
        setBtnCloseClickListener()
    }

    override fun onRequestPermissionsResult(
        requestCode: Int,
        permissions: Array<out String>,
        grantResults: IntArray
    ) {
        super.onRequestPermissionsResult(requestCode, permissions, grantResults)

        if (requestCode == Constant.REQUEST_RECORD_AUDIO) {
            if (grantResults.isNotEmpty() && grantResults[0] == PackageManager.PERMISSION_GRANTED) {
                requestAudioPermissions()
            } else {
                showAlertFinish(getString(R.string.notes_need_permission))
            }
        }

        if (requestCode == PERMISSIONS_ALL) {
            var grantAll = true
            if (grantResults.isNotEmpty()) {
                for (i in grantResults.indices) {
                    if (grantResults[i] != PackageManager.PERMISSION_GRANTED) {
                        grantAll = false
                        break
                    }
                }
            }

            if (!grantAll) {
                showAlertFinish(getString(R.string.notes_need_permission_resi))
            } else {
                requestMediaPermission()
            }
        }
    }

    override fun onResume() {
        super.onResume()

        if (isShared) {
            alreadyShareBuktiTransfer()
            isShared = false
        }
    }

    override fun onDestroy() {
        super.onDestroy()

        if (::speechRecognizer.isInitialized) {
            speechRecognizer.destroy()
        }

        if (::voiceAssistantPresenter.isInitialized) {
            voiceAssistantPresenter.stop()
        }

        if (::tts.isInitialized) {
            tts.shutdown()
        }

        idStateVoiceAssistant = null
        referenceNumber = null
    }

    override fun onBackPressed() {
        super.onBackPressed()
        overridePendingTransition(R.anim.fade_in_trasition, R.anim.bottom_down)
    }

    /**
     * Function for initialization Text To Speech
     */
    override fun onInit(status: Int) {
        if (status == TextToSpeech.SUCCESS) {
            val localeId = Locale("id", "ID")
            val result = tts.setLanguage(localeId)

            if (result == TextToSpeech.LANG_MISSING_DATA || result == TextToSpeech.LANG_NOT_SUPPORTED) {
                val dialogExitCustom = DialogExitCustom(
                    this,
                    getString(R.string.informasi),
                    getString(R.string.tts_informasi_unduh_bahasa_indonesia),
                    getString(R.string.batal2),
                    getString(R.string.txt_pergi_ke_pengaturan)
                )
                val ft = this.supportFragmentManager.beginTransaction()
                ft.add(dialogExitCustom, null)
                ft.commitAllowingStateLoss()

                isDialogForInstallLanguageTTS = true
            } else {
                val voices = tts.voices
                for (voice in voices) {
                    if (voice.locale == localeId && voice.name.contains(VoiceAssistantConfig.TTS_VOICE)) {
                        tts.setVoice(voice)
                        break
                    }
                }
            }
        } else {
            showSnackbarErrorMessageRevamp(getString(R.string.tts_bermasalah), ALERT_ERROR, this, false)
        }
    }

    override fun onClickBtnYes() {
        if (isDialogForInstallLanguageTTS) {
            val installIntent = Intent()
            installIntent.action = TextToSpeech.Engine.ACTION_INSTALL_TTS_DATA
            startActivity(installIntent)
        } else {
            val intent = Intent(Settings.ACTION_APPLICATION_DETAILS_SETTINGS)
            intent.data = Uri.parse(VoiceAssistantConfig.PACKAGE_SPEECH_RECOGNIZER)
            startActivity(intent)
        }

        finish()
        overridePendingTransition(R.anim.fade_in_trasition, R.anim.bottom_down)
    }

    override fun onClickBtnNo() {
        onBackPressed()
    }

    override fun onSendPinComplete(pin: String?) {
        voiceAssistantPresenter.sendTextToVoiceAssistant(
            VoiceAssistantRequest(
                tokenKey = voiceAssistantPresenter.getTokenKey(),
                username = voiceAssistantPresenter.getUsername(),
                action = VoiceAssistantConfig.PIN_VALIDATION,
                id = idStateVoiceAssistant,
                text = null,
                referenceNumber = referenceNumber,
                pin = pin,
                nextAction = nextActionAfterInputPin,
                codeTransferMethod = null,
                amountTransfer = null,
                accountName = null,
                accountBank = null,
                account = null,
                bankCode = null
            )
        )
    }

    override fun onLupaPin() {
        LupaPinFastActivity.launchIntent(this)
    }

    override fun onDialogUpdateNominalConfirmationClick(newAmount: String) {
        voiceAssistantPresenter.sendTextToVoiceAssistant(
            VoiceAssistantRequest(
                tokenKey = voiceAssistantPresenter.getTokenKey(),
                username = voiceAssistantPresenter.getUsername(),
                action = VoiceAssistantConfig.EDIT_AMOUNT,
                id = idStateVoiceAssistant,
                text = null,
                referenceNumber = referenceNumber,
                pin = null,
                nextAction = null,
                codeTransferMethod = null,
                amountTransfer = newAmount,
                accountName = null,
                accountBank = null,
                account = null,
                bankCode = null
            )
        )
    }

    override fun onClick(metodeTransfer: MetodeTransfer?, selectedPos: Int) {
        voiceAssistantPresenter.sendTextToVoiceAssistant(
            VoiceAssistantRequest(
                tokenKey = voiceAssistantPresenter.getTokenKey(),
                username = voiceAssistantPresenter.getUsername(),
                action = VoiceAssistantConfig.CHOOSE_TRANSFER_METHOD,
                id = idStateVoiceAssistant,
                text = null,
                referenceNumber = referenceNumber,
                pin = null,
                nextAction = null,
                codeTransferMethod = metodeTransfer?.code,
                amountTransfer = null,
                accountName = null,
                accountBank = null,
                account = null,
                bankCode = null
            )
        )
    }

    override fun onItemRecipientClick(data: SavedRecipientVoiceAssistantResponse) {
        voiceAssistantPresenter.sendTextToVoiceAssistant(
            VoiceAssistantRequest(
                tokenKey = voiceAssistantPresenter.getTokenKey(),
                username = voiceAssistantPresenter.getUsername(),
                action = VoiceAssistantConfig.CHOOSE_RECIPIENT,
                id = idStateVoiceAssistant,
                text = null,
                referenceNumber = referenceNumber,
                pin = null,
                nextAction = null,
                codeTransferMethod = null,
                amountTransfer = null,
                accountName = data.accountName,
                accountBank = data.accountBank,
                account = data.accountNumber,
                bankCode = data.bankCode
            )
        )
    }

    override fun onSearchItemRecipient(isSearchIsEmpty: Boolean, querySearch: CharSequence) {
        //nothing to do
    }

    override fun onItemTransferMethodClick(data: TransferMethod) {
        voiceAssistantPresenter.sendTextToVoiceAssistant(
            VoiceAssistantRequest(
                tokenKey = voiceAssistantPresenter.getTokenKey(),
                username = voiceAssistantPresenter.getUsername(),
                action = VoiceAssistantConfig.CHOOSE_TRANSFER_METHOD,
                id = idStateVoiceAssistant,
                text = null,
                referenceNumber = referenceNumber,
                pin = null,
                nextAction = null,
                codeTransferMethod = data.code,
                amountTransfer = null,
                accountName = null,
                accountBank = null,
                account = null,
                bankCode = null
            )
        )
    }

    override fun onWaitingResponseFromVoiceAssistant(isWaiting: Boolean, isActionFromVoice: Boolean) {
        this.isActionFromVoice = isActionFromVoice

        if (isWaiting) {
            setProcessingView(isActionFromVoice)
        } else {
            setStopListeningView(isActionFromVoice)
        }
    }

    override fun onRequestPIN(response: VoiceAssistantResponse) {
        when (response.action) {
            VoiceAssistantConfig.INPUT_PIN_TRANSFER -> nextActionAfterInputPin = VoiceAssistantConfig.PROCESS_TRANSFER
            VoiceAssistantConfig.INPUT_PIN_BALANCE -> nextActionAfterInputPin = VoiceAssistantConfig.PROCESS_CHECK_BALANCE
        }

        val pinFragment = PinFragment(this, this, false)
        pinFragment.show()
    }

    override fun onCheckBalance(response: VoiceAssistantResponse) {
        val payload = response.getPayload(CheckBalanceVoiceAssistantResponse::class.java)

        resetView()
        startAudioTTS(response.text)

        idStateVoiceAssistant = null
        referenceNumber = null
        isNeededToResetView = true
        isOnTransferConfirmation = false
        isOnSavedRecipientList = false
        hasUsedFeature = true

        with(binding) {
            cvCheckBalance.isVisible = true
            cvCheckBalance.startAnimation(animationFadeIn)

            tvAssistantTranscribedText.text = response.text
            tvBalanceName.text = payload.name
            tvBalanceAccount.text = payload.account
            tvBalanceAmount.text = GeneralHelper.formatNominalIDR(payload.currency, payload.balanceString)
        }
    }

    override fun onShowingTextOnly(response: VoiceAssistantResponse) {
        startAudioTTS(response.text)

        if (isNeededToResetView) {
            resetView()
            isNeededToResetView = false
            isOnTransferConfirmation = false
            isOnSavedRecipientList = false
        }

        if (!isOnTransferConfirmation) {
            referenceNumber = null
        }

        idStateVoiceAssistant = response.id
        hasUsedFeature = true

        binding.tvAssistantTranscribedText.text = response.text
    }

    override fun onShowingInitView(response: VoiceAssistantResponse) {
        startAudioTTS(response.text)
        resetView()

        binding.tvAssistantTranscribedText.text = response.text

        idStateVoiceAssistant = null
        referenceNumber = null
        isNeededToResetView = false
        isOnTransferConfirmation = false
        isOnSavedRecipientList = false
        hasUsedFeature = false
    }

    override fun onShowingSavedRecipientList(response: VoiceAssistantResponse) {
        val payload = response.getPayloadAsList(SavedRecipientVoiceAssistantResponse::class.java)

        if (!isOnSavedRecipientList) {
            resetView()
        }

        startAudioTTS(response.text)

        idStateVoiceAssistant = response.id
        referenceNumber = null
        isNeededToResetView = false
        isOnTransferConfirmation = false
        isOnSavedRecipientList = true
        hasUsedFeature = true

        with(binding) {
            tvAssistantTranscribedText.text = response.text

            if (payload.isNotEmpty()) {
                cvListRecipient.isVisible = true
                cvListRecipient.startAnimation(animationFadeIn)
                savedRecipientAdapter.submitList(payload)
                savedRecipientAdapter.setItemClickable(true)
            }
        }
    }

    override fun onShowingTransferMethod(response: VoiceAssistantResponse) {
        val payload = response.getPayload(TransferMethodVoiceAssistantResponse::class.java)

        resetView()
        startAudioTTS(response.text)

        idStateVoiceAssistant = response.id
        referenceNumber = null
        isNeededToResetView = false
        isOnTransferConfirmation = false
        isOnSavedRecipientList = false
        hasUsedFeature = true

        with(binding) {
            tvAssistantTranscribedText.text = response.text

            if (payload.methods.isNotEmpty()) {
                rvMethodTransfer.apply {
                    isVisible = true
                    startAnimation(animationFadeIn)
                }
            }

            transferMethodAdapter.submitList(payload.methods)
        }
    }

    override fun onRequestAmountTransfer(response: VoiceAssistantResponse) {
        resetView()
        startAudioTTS(response.text)

        idStateVoiceAssistant = response.id
        referenceNumber = null
        isNeededToResetView = false
        isOnTransferConfirmation = false
        isOnSavedRecipientList = false
        hasUsedFeature = true

        binding.tvAssistantTranscribedText.text = response.text
    }

    override fun onShowingConfirmationTransfer(response: VoiceAssistantResponse) {
        val payload = response.getPayload(InquiryTransferVoiceAssistantResponse::class.java)

        resetView()
        startAudioTTS(response.text)

        idStateVoiceAssistant = response.id
        referenceNumber = response.referenceNumber
        isNeededToResetView = false
        isOnTransferConfirmation = true
        isOnSavedRecipientList = false
        hasUsedFeature = true

        if (payload.transferMethodList.isNotEmpty()) {
            setBtnEditTransferMethodClickListener(payload.transferMethodList, payload.getSelectedTransferMethod())
        }
        setBtnEditAmountClickListener(
            payload.minimumAmount.toDouble(),
            payload.maximumTransferLimit.toDouble(),
            payload.balanceData.balance,
            payload.balanceData.minBalance.toDouble()
        )
        setBtnEditRecipientClickListener()
        setBtnCancelClickListener()
        setBtnConfirmationClickListener()

        with(binding) {
            tvAssistantTranscribedText.text = response.text

            clConfirmationDetailDataTransaction.isVisible = true
            btnEditRecipient.isVisible = true
            cvConfirmationDetail.isVisible = true
            cvConfirmationDetail.startAnimation(animationFadeIn)

            tvAmount.text = getString(R.string.nominal_with_currency_rupiah, GeneralHelper.formatNominal(payload.transferAmount))
            tvTotal.text = getString(R.string.nominal_with_currency_rupiah, GeneralHelper.formatNominal(payload.totalAmount))
            tvCostAdmin.text = getString(R.string.nominal_with_currency_rupiah, GeneralHelper.formatNominal(payload.adminFee))

            if (!payload.isTransferMethodBRI() && payload.transferMethodList.isNotEmpty()) {
                tvMethodTransfer.text = payload.getTransferMethodTitle()
            }
            tvMethodTransferBri.text = getString(R.string.transfer_method_bri)

            tvSenderName.text = payload.sourceAccountData.accountName
            tvSenderAccount.text = payload.sourceAccountData.accountNumber
            tvSenderInitial.text = GeneralHelper.formatInitialName(payload.sourceAccountData.accountName)
            tvSenderBank.text = getString(R.string.bank_bri)

            tvRecipientName.text = payload.destinationName
            tvRecipientAccount.text = payload.destinationAccount
            tvRecipientInitial.text = GeneralHelper.formatInitialName(payload.destinationName)
            tvRecipientBank.text = payload.bankName

            tvCostAdminLabel.isVisible = !payload.isTransferMethodBRI()
            tvCostAdmin.isVisible = !payload.isTransferMethodBRI()
            tvMethodTransfer.isVisible = !payload.isTransferMethodBRI()
            tvMethodTransferBri.isVisible = payload.isTransferMethodBRI()
        }
    }

    override fun onCompleteTransfer(response: VoiceAssistantResponse) {
        val payload = response.getPayload(ReceiptRevampResponse::class.java)

        resetView()
        startAudioTTS(response.text)

        isShowAll = false
        idStateVoiceAssistant = null
        referenceNumber = null
        isNeededToResetView = true
        isOnTransferConfirmation = false
        isOnSavedRecipientList = false
        hasUsedFeature = true

        setBtnLihatLebihClickListener()
        setBtnLihatSedikitClickListener()
        setBtnShareClickListener()

        with(binding) {
            tvAssistantTranscribedText.text = response.text

            nsvSlipTransferComplete.scrollTo(0, 0)
            nsvSlipTransferComplete.isVisible = true
            nsvSlipTransferComplete.startAnimation(animationFadeIn)

            llShowMore.isVisible = false
            lihatSedikit.isVisible = false
            rvTransactionDataViewSmall.isVisible = false

            if (payload.isOnProcess) {
                lihatLebih.isVisible = false
                ivReceiptPending.isVisible = true
                ivWatermarkPending.isVisible = true
                ivReceiptSuccess.isVisible = false
                btnShare.isVisible = false
            } else {
                lihatLebih.isVisible = true
                ivReceiptPending.isVisible = false
                ivWatermarkPending.isVisible = false
                ivReceiptSuccess.isVisible = true
                btnShare.isVisible = true

                setTitleView(payload)
                setSubtitleHtml(payload)
                setReceiptAmountDataView(payload)
                setReceiptTransactionDataView(payload)
                setReceiptTransactionDataViewSmall(payload)
                setReceiptFooterView(payload)
            }

            setReceiptAdditionalView(payload)
            setReceiptSourceAccountView(payload)
            setReceiptBillingDetailView(payload)
            setReceiptHeaderView(payload)
            setReceiptTotalView(payload)
        }
    }

    override fun onExceptionVoiceAssistantAnswerPINInvalid(message: String) {}

    override fun onExceptionVoiceAssistantAnswerAccountBlock(message: String) {}

    override fun onExceptionVoiceAssistantAnswerError(message: String) {
        startAudioTTS(message)

        if (isNeededToResetView) {
            resetView()
            isNeededToResetView = false
            isOnTransferConfirmation = false
            isOnSavedRecipientList = false
        }

        if (!isOnTransferConfirmation) {
            referenceNumber = null
        }

        hasUsedFeature = true

        binding.tvAssistantTranscribedText.text = message
    }

    override fun onExceptionVoiceAssistant93(message: String) {
        if (hasUsedFeature) {
            resetView()
            binding.tvAssistantTranscribedText.text = getString(R.string.txt_voice_assistant_transaction_expired)
            startAudioTTS(binding.tvAssistantTranscribedText.text.toString())

            referenceNumber = null
            idStateVoiceAssistant = null
            isNeededToResetView = true
            isOnTransferConfirmation = false
            isOnSavedRecipientList = false
            hasUsedFeature = false
        } else {
            val returnIntent = Intent()

            returnIntent.putExtra(Constant.TAG_ERROR_MESSAGE, message)

            this.setResult(RESULT_CANCELED, returnIntent)
            finish()
            overridePendingTransition(R.anim.fade_in_trasition, R.anim.bottom_down)
        }
    }

    override fun onSessionEnd(message: String?) {
        super.onSessionEnd(message)
        overridePendingTransition(R.anim.fade_in_trasition, R.anim.bottom_down)
    }

    override fun onException99(message: String?) {
        setStopListeningView(isActionFromVoice)
        GeneralHelper.showDialogGagalBackDescBerubahRevamp(this, Constant.SERVER_UNDER_MAINTENANCE, message, false, false)
    }

    private fun requestAudioPermissions(){
        if (!hasPermissions(this, *audioPermission)) {
            ActivityCompat.requestPermissions(this, audioPermission, Constant.REQUEST_RECORD_AUDIO)
        } else {
            speechRecognizerStartListening()
        }
    }

    private fun requestMediaPermission() {
        if (!hasPermissions(this, *PERMISSIONS)) {
            ActivityCompat.requestPermissions(this, PERMISSIONS, PERMISSIONS_ALL)
        } else {
            shareImage(generateImage())
            isShared = true
            isReadytoShare = false
        }
    }

    private fun injectDependency() {
        activityComponent.inject(this)
        voiceAssistantPresenter.view = this
        voiceAssistantPresenter.setUrlVoiceAssistant(getString(R.string.url_llm))
    }

    private fun initView() {
        with(binding) {
            llInitialInstruction.isVisible = true

            ivMicrophone.isClickable = false
            ivMicrophone.setImageResource(R.drawable.ic_assistant_mic_think)

            imgAnimation3.isVisible = false
            imgAnimation2.isVisible = false
            imgAnimation1.isVisible = false

            tvAssistantTranscribedText.text = getString(R.string.assistant_initial_message, voiceAssistantPresenter.getNickname())
        }
    }

    private fun initSpeechRecognizer() {
        speechRecognizer = SpeechRecognizer.createSpeechRecognizer(this@VoiceAssistantActivity)
        if (SpeechRecognizer.isRecognitionAvailable(this@VoiceAssistantActivity)) {
            speechRecognizer.setRecognitionListener(mRecognitionListener)
        } else {
            showAlertFinish(getString(R.string.speech_recognizer_unavailable))
        }
    }

    private fun initTts() {
        if (isGoogleTTSEngineEnabled()) {
            tts = TextToSpeech(this, this, VoiceAssistantConfig.TTS_ENGINE)
            tts.setPitch(VoiceAssistantConfig.TTS_PITCH)
            tts.setSpeechRate(VoiceAssistantConfig.TTS_SPEECH_RATE)
        } else {
            val dialogExitCustom = DialogExitCustom(
                this,
                getString(R.string.informasi),
                getString(R.string.tts_informasi_aktifkan),
                getString(R.string.batal2),
                getString(R.string.txt_pergi_ke_pengaturan)
            )
            val ft = this.supportFragmentManager.beginTransaction()
            ft.add(dialogExitCustom, null)
            ft.commitAllowingStateLoss()

            isDialogForInstallLanguageTTS = false
        }
    }

    private fun isGoogleTTSEngineEnabled(): Boolean {
        val packageManager = packageManager
        return packageManager.getPackageInfo(VoiceAssistantConfig.TTS_ENGINE, PackageManager.GET_META_DATA).applicationInfo.enabled
    }

    private fun initRv() {
        with(binding.rvRecipient) {
            adapter = savedRecipientAdapter
            layoutManager = LinearLayoutManager(this@VoiceAssistantActivity, LinearLayoutManager.VERTICAL, false)
        }

        with(binding.rvMethodTransfer) {
            adapter = transferMethodAdapter
            layoutManager = LinearLayoutManager(this@VoiceAssistantActivity, LinearLayoutManager.VERTICAL, false)
        }
    }

    private fun speechRecognizerStartListening() {
        speechRecognizer.startListening(recognizerIntent)
    }

    private fun speechRecognizerStopListening() {
        speechRecognizer.stopListening()
    }

    private fun resetSpeechRecognizer() {
        speechRecognizer.destroy()
        initSpeechRecognizer()
    }

    private fun setRecognizerIntent() {
        recognizerIntent = Intent(RecognizerIntent.ACTION_RECOGNIZE_SPEECH)
        recognizerIntent.putExtra(
            RecognizerIntent.EXTRA_LANGUAGE_PREFERENCE,
            AppConfig.LANGUAGE_CONFIG
        )
        recognizerIntent.putExtra(
            RecognizerIntent.EXTRA_LANGUAGE,
            AppConfig.LANGUAGE_CONFIG
        )
        recognizerIntent.putExtra(
            RecognizerIntent.EXTRA_LANGUAGE_MODEL,
            RecognizerIntent.LANGUAGE_MODEL_FREE_FORM
        )
        recognizerIntent.putExtra(RecognizerIntent.EXTRA_MAX_RESULTS, 1)
        recognizerIntent.putExtra(RecognizerIntent.EXTRA_PARTIAL_RESULTS, true)
    }

    private fun setBtnMicrophoneClickListener() {
        binding.ivMicrophone.setOnClickListener {
            speechRecognizerStartListening()
        }
    }

    private fun setBtnCloseClickListener() {
        binding.btnClose.setOnClickListener {
            onBackPressed()
        }
    }

    private fun setBtnEditAmountClickListener(minimumTransaction: Double, maximumTransaction: Double, balance: Double, minimumBalance: Double) {
        binding.tvAmount.setOnClickListener {
            val updateNominalDialog = UpdateNominalVoiceAssistantFragment(
                minimumTransaction,
                maximumTransaction,
                balance,
                minimumBalance
            )
            updateNominalDialog.isCancelable = false
            updateNominalDialog.show(supportFragmentManager, "")
        }
    }

    private fun setBtnEditTransferMethodClickListener(listTransferMethod: List<MetodeTransfer>, selectedPos: Int) {
        binding.tvMethodTransfer.setOnClickListener {
            val fragmentMetode = FragmentMetode(
                listTransferMethod,
                this, selectedPos, false
            )
            fragmentMetode.show(supportFragmentManager, "")
        }
    }

    private fun setBtnEditRecipientClickListener() {
        binding.btnEditRecipient.setOnClickListener {
            voiceAssistantPresenter.sendTextToVoiceAssistant(
                VoiceAssistantRequest(
                    tokenKey = voiceAssistantPresenter.getTokenKey(),
                    username = voiceAssistantPresenter.getUsername(),
                    action = VoiceAssistantConfig.CHANGE_RECIPIENT,
                    id = idStateVoiceAssistant,
                    text = null,
                    referenceNumber = referenceNumber,
                    pin = null,
                    nextAction = null,
                    codeTransferMethod = null,
                    amountTransfer = null,
                    accountName = null,
                    accountBank = null,
                    account = null,
                    bankCode = null
                )
            )
        }
    }

    private fun setBtnLihatLebihClickListener() {
        binding.lihatLebih.setOnClickListener {
            lihatLebih()
        }
    }

    private fun setBtnLihatSedikitClickListener() {
        binding.lihatSedikit.setOnClickListener {
            lihatSedikit()
        }
    }

    private fun setBtnConfirmationClickListener() {
        binding.btnConfirmProceed.setOnClickListener {
            if (tts.isSpeaking) {
                tts.stop()
            }

            nextActionAfterInputPin = VoiceAssistantConfig.INPUT_PIN_TRANSFER
            val pinFragment = PinFragment(this, this, false)
            pinFragment.show()
        }
    }

    private fun setBtnCancelClickListener() {
        binding.btnConfirmCancel.setOnClickListener {
            voiceAssistantPresenter.sendTextToVoiceAssistant(
                VoiceAssistantRequest(
                    tokenKey = voiceAssistantPresenter.getTokenKey(),
                    username = voiceAssistantPresenter.getUsername(),
                    action = VoiceAssistantConfig.HOME_PAGE,
                    id = idStateVoiceAssistant,
                    text = null,
                    referenceNumber = referenceNumber,
                    pin = null,
                    nextAction = null,
                    codeTransferMethod = null,
                    amountTransfer = null,
                    accountName = null,
                    accountBank = null,
                    account = null,
                    bankCode = null
                )
            )
        }
    }

    private fun setBtnShareClickListener() {
        binding.btnShare.setOnClickListener {
            shareBuktiTransaksi()
        }
    }

    private fun setListeningView() {
        with(binding) {
            imgAnimation1.isVisible = true
            imgAnimation2.isVisible = true
            imgAnimation3.isVisible = true

            ivMicrophone.isClickable = false
            ivMicrophone.setImageResource(R.drawable.ic_assistant_mic_listen)

            tvAssistantState.isVisible = true
            tvAssistantState.text = getString(R.string.assistant_hearing_input)

            tvUserTranscribedText.text = ""
            tvUserTranscribedText.isVisible = false

            btnConfirmProceed.isEnabled = false
            btnConfirmCancel.isEnabled = false
            btnEditRecipient.isClickable = false
            tvAmount.isClickable = false
            tvMethodTransfer.isClickable = false

            savedRecipientAdapter.setItemClickable(false)
            transferMethodAdapter.setItemClickable(false)
        }

        startPulseMicrophone()

        if (tts.isSpeaking) {
            tts.stop()
        }
    }

    private fun setProcessingView(isActionFromVoice: Boolean) {
        val currentTranscribedTextUser = binding.tvUserTranscribedText.text

        with(binding) {
            ivMicrophone.isClickable = false
            ivMicrophone.setImageResource(R.drawable.ic_assistant_mic_think)

            if (isActionFromVoice) {
                setStartLoadingView()
            }

            imgAnimation3.isVisible = false
            imgAnimation2.isVisible = false
            imgAnimation1.isVisible = false

            tvAssistantState.isVisible = true
            tvAssistantState.text = getString(R.string.assistant_processing_input)

            btnConfirmProceed.isEnabled = false
            btnConfirmCancel.isEnabled = false
            btnEditRecipient.isClickable = false
            tvAmount.isClickable = false
            tvMethodTransfer.isClickable = false

            savedRecipientAdapter.setItemClickable(false)
            transferMethodAdapter.setItemClickable(false)

            if (isActionFromVoice) {
                tvUserTranscribedText.text = ""
                tvUserTranscribedText.isVisible = true
                tvUserTranscribedText.typingAnimationTextView(currentTranscribedTextUser.toString())
            }

            if (binding.llInitialInstruction.isVisible) {
                binding.llInitialInstruction.isVisible = false
                binding.llInitialInstruction.startAnimation(animationFadeOut)
            }
        }

        stopPulseMicrophone()

        if (tts.isSpeaking) {
            tts.stop()
        }
    }

    private fun setStopListeningView(isActionFromVoice: Boolean) {
        with(binding) {
            ivMicrophone.setImageResource(R.drawable.ic_assistant_mic_idle)

            if (isActionFromVoice) {
                setEndLoadingView()
            }

            ivMicrophone.isClickable = true
            tvAssistantState.isVisible = false
            imgAnimation3.isVisible = false
            imgAnimation2.isVisible = false
            imgAnimation1.isVisible = false

            btnConfirmProceed.isEnabled = true
            btnConfirmCancel.isEnabled = true
            btnEditRecipient.isClickable = true
            tvAmount.isClickable = true
            tvMethodTransfer.isClickable = true

            if (tvUserTranscribedText.isVisible) {
                tvUserTranscribedText.isVisible = false
                tvUserTranscribedText.startAnimation(animationFadeOut)
            }

            savedRecipientAdapter.setItemClickable(true)
            transferMethodAdapter.setItemClickable(true)
        }

        stopPulseMicrophone()
    }

    private fun resetView() {
        with(binding) {
            tvUserTranscribedText.text = ""
            cvConfirmationDetail.isVisible = false
            nsvSlipTransferComplete.isVisible = false
            cvListRecipient.isVisible = false
            cvCheckBalance.isVisible = false
            rvMethodTransfer.isVisible = false
        }
    }

    private fun setStartLoadingView() {
        with(binding) {
            ivMicrophone.visibility = View.INVISIBLE

            lottieStartLoadingVoiceAssistant.isVisible = true
            lottieStartLoadingVoiceAssistant.playAnimation()
            lottieStartLoadingVoiceAssistant.addAnimatorListener(object : AnimatorListener {
                override fun onAnimationStart(p0: Animator) {}

                override fun onAnimationEnd(p0: Animator) {
                    lottieStartLoadingVoiceAssistant.isVisible = false

                    lottieLoadingVoiceAssistant.progress = 0f
                    lottieLoadingVoiceAssistant.isVisible = true
                    lottieLoadingVoiceAssistant.playAnimation()
                }

                override fun onAnimationCancel(p0: Animator) {
                    lottieLoadingVoiceAssistant.isVisible = false
                }

                override fun onAnimationRepeat(p0: Animator) {}
            })
        }
    }

    private fun setEndLoadingView() {
        with(binding) {
            lottieStartLoadingVoiceAssistant.cancelAnimation()
            lottieLoadingVoiceAssistant.isVisible = false

            lottieEndLoadingVoiceAssistant.isVisible = true
            lottieEndLoadingVoiceAssistant.playAnimation()
            lottieEndLoadingVoiceAssistant.addAnimatorListener(object : AnimatorListener {
                override fun onAnimationStart(p0: Animator) {}

                override fun onAnimationEnd(p0: Animator) {
                    lottieEndLoadingVoiceAssistant.isVisible = false

                    ivMicrophone.visibility = View.VISIBLE
                }

                override fun onAnimationCancel(p0: Animator) {}

                override fun onAnimationRepeat(p0: Animator) {}
            })
        }
    }

    private fun setTitleView(payload: ReceiptRevampResponse) {
        if (payload.title != null) {
            binding.tvTitle.text = payload.title
        }

        val dateTransaction = if ((payload.dateTransaction != null)) payload.dateTransaction else ""
        binding.tvDate.text = dateTransaction

        val subtitle = if ((payload.subtitle != null)) payload.subtitle else ""
        if (subtitle.isNotEmpty()) {
            binding.tvDate.visibility = View.VISIBLE
            binding.tvDate.text = payload.subtitle
        }
    }

    private fun setSubtitleHtml(payload: ReceiptRevampResponse) {
        if (payload.subtitleHtml != null) {
            val subtitleHtml: String = payload.subtitleHtml
            if (payload.subtitleHtml != null && subtitleHtml.isNotEmpty()) {
                binding.wvSubtitleHtml.visibility = View.VISIBLE
                binding.wvSubtitleHtml.setBackgroundColor(Color.TRANSPARENT)
                SizeHelper.setMarginsView(this, binding.cvContentReceiptShare, 20, 8, 20, 0)
                GeneralHelper.setWebViewReceipt(binding.wvSubtitleHtml, "", subtitleHtml)
            } else {
                binding.wvSubtitleHtml.visibility = View.GONE
            }
        } else {
            binding.wvSubtitleHtml.visibility = View.GONE
        }
    }

    private fun setReceiptAdditionalView(payload: ReceiptRevampResponse) {
        if (payload.additionalInfo != null && payload.additionalInfo.isNotEmpty()) {
            with(binding) {
                cvAdditionalInfo.visibility = View.VISIBLE
                tvAdditionalInfo.text = payload.additionalInfo

                if (!payload.isOnProcess) {
                    cvAdditionalInfoReceiptShare.visibility = View.VISIBLE
                    tvAdditionalInfoReceiptShare.text = payload.additionalInfo
                }
            }
        }
    }

    private fun setReceiptSourceAccountView(payload: ReceiptRevampResponse) {
        if (payload.sourceAccountDataView != null) {
            val detailListTypes: MutableList<DetailListType> = ArrayList()
            if (payload.sourceAccountDataView != null) {
                detailListTypes.add(payload.sourceAccountDataView)
            }
            val receiptAccountDataAdapter = ReceiptAccountDataAdapter(detailListTypes, this@VoiceAssistantActivity, true, 0)

            binding.rvItemReceiptSourceAccountData.apply {
                setHasFixedSize(true)
                layoutManager = LinearLayoutManager(this@VoiceAssistantActivity, LinearLayoutManager.VERTICAL, false)
                adapter = receiptAccountDataAdapter
            }

            if (!payload.isOnProcess) {
                binding.rvItemReceiptSourceAccountDataReceiptShare.apply {
                    setHasFixedSize(true)
                    layoutManager = LinearLayoutManager(this@VoiceAssistantActivity, LinearLayoutManager.VERTICAL, false)
                    adapter = receiptAccountDataAdapter
                }
            }
        }
    }

    private fun setReceiptBillingDetailView(payload: ReceiptRevampResponse) {
        if (payload.billingDetail != null) {
            val detailListTypesBilling: MutableList<DetailListType> = ArrayList()
            if (payload.billingDetail != null) {
                detailListTypesBilling.add(payload.billingDetail)
            }
            val receiptAccountDataAdapter = ReceiptAccountDataAdapter(detailListTypesBilling, this@VoiceAssistantActivity, false, 0)

            binding.rvItemReceiptBillingDetail.apply {
                setHasFixedSize(true)
                layoutManager = LinearLayoutManager(this@VoiceAssistantActivity, LinearLayoutManager.VERTICAL, false)
                adapter = receiptAccountDataAdapter
            }

            if (!payload.isOnProcess) {
                binding.rvItemReceiptBillingDetailReceiptShare.apply {
                    setHasFixedSize(true)
                    layoutManager = LinearLayoutManager(this@VoiceAssistantActivity, LinearLayoutManager.VERTICAL, false)
                    adapter = receiptAccountDataAdapter
                }
            }
        }
    }

    private fun setReceiptHeaderView(payload: ReceiptRevampResponse) {
        if (payload.headerDataView != null) {
            dataTransaksiAdapter = DataTransaksiRevampAdapter(payload.headerDataView, this@VoiceAssistantActivity)

            binding.rvHeaderDataView.apply {
                setHasFixedSize(true)
                layoutManager = LinearLayoutManager(this@VoiceAssistantActivity, LinearLayoutManager.VERTICAL, false)
                adapter = dataTransaksiAdapter
            }

            if (!payload.isOnProcess) {
                binding.rvHeaderDataViewReceiptShare.apply {
                    setHasFixedSize(true)
                    layoutManager = LinearLayoutManager(this@VoiceAssistantActivity, LinearLayoutManager.VERTICAL, false)
                    adapter = dataTransaksiAdapter
                }
            }
        }
    }

    private fun setReceiptTotalView(payload: ReceiptRevampResponse) {
        if (payload.totalDataView != null) {
            receiptDataTotalAdapter = ReceiptDataTotalAdapter(payload.totalDataView, this@VoiceAssistantActivity)

            binding.rvTotalDataView.apply {
                setHasFixedSize(true)
                layoutManager = LinearLayoutManager(this@VoiceAssistantActivity, LinearLayoutManager.VERTICAL, false)
                adapter = receiptDataTotalAdapter
            }

            if (!payload.isOnProcess) {
                binding.rvTotalDataViewReceiptShare.apply {
                    setHasFixedSize(true)
                    layoutManager = LinearLayoutManager(this@VoiceAssistantActivity, LinearLayoutManager.VERTICAL, false)
                    adapter = receiptDataTotalAdapter
                }
            }
        }
    }

    private fun setReceiptAmountDataView(payload: ReceiptRevampResponse) {
        if (payload.amountDataView != null) {
            dataTransaksiAdapter = DataTransaksiRevampAdapter(payload.amountDataView, this@VoiceAssistantActivity)
            binding.rvAmountDataView.apply {
                setHasFixedSize(true)
                layoutManager = LinearLayoutManager(this@VoiceAssistantActivity, LinearLayoutManager.VERTICAL, false)
                adapter = dataTransaksiAdapter
            }

            if (!payload.isOnProcess) {
                binding.rvAmountDataViewReceiptShare.apply {
                    setHasFixedSize(true)
                    layoutManager = LinearLayoutManager(this@VoiceAssistantActivity, LinearLayoutManager.VERTICAL, false)
                    adapter = dataTransaksiAdapter
                }
            }
        }
    }

    private fun setReceiptTransactionDataView(payload: ReceiptRevampResponse) {
        if (payload.transactionDataView != null) {
            dataTransaksiAdapter = DataTransaksiRevampAdapter(payload.transactionDataView, this@VoiceAssistantActivity)
            binding.rvDataViewTransaction.apply {
                setHasFixedSize(true)
                layoutManager = LinearLayoutManager(this@VoiceAssistantActivity, LinearLayoutManager.VERTICAL, false)
                adapter = dataTransaksiAdapter
            }

            if (!payload.isOnProcess) {
                binding.rvDataViewTransactionReceiptShare.apply {
                    setHasFixedSize(true)
                    layoutManager = LinearLayoutManager(this@VoiceAssistantActivity, LinearLayoutManager.VERTICAL, false)
                    adapter = dataTransaksiAdapter
                }
            }
        }
    }

    private fun setReceiptTransactionDataViewSmall(payload: ReceiptRevampResponse) {
        val dataViews: MutableList<DataView> = mutableListOf()

        var i = 0
        while (((i < payload.rowDataShow) && (i < payload.transactionDataView.size))) {
            if (payload.transactionDataView[i] != null) {
                dataViews.add(payload.transactionDataView[i])
            }
            i++
        }

        if (dataViews.size > 0) {
            dataTransaksiAdapter = DataTransaksiRevampAdapter(dataViews, this@VoiceAssistantActivity)
            binding.rvTransactionDataViewSmall.apply {
                isVisible = true
                setHasFixedSize(true)
                layoutManager = LinearLayoutManager(this@VoiceAssistantActivity, LinearLayoutManager.VERTICAL, false)
                adapter = dataTransaksiAdapter
            }
        }
    }

    private fun setReceiptFooterView(payload: ReceiptRevampResponse) {
        if (payload.footer != null && payload.footer.isNotEmpty()) {
            binding.tvFooter.visibility = View.VISIBLE
            binding.tvFooter.text = payload.footer

            if (!payload.isOnProcess) {
                binding.tvFooterReceiptShare.visibility = View.VISIBLE
                binding.tvFooterReceiptShare.text = payload.footer
            }
        } else {
            binding.tvFooter.visibility = View.GONE

            if (!payload.isOnProcess) {
                binding.tvFooterReceiptShare.visibility = View.GONE
            }
        }

        val footerHtmlStr: String = payload.footerHtml
        if (payload.footerHtml != null && footerHtmlStr.isNotEmpty()) {
            binding.wvFooter.visibility = View.VISIBLE
            binding.wvFooter.setBackgroundColor(Color.TRANSPARENT)
            GeneralHelper.setWebViewReceipt(binding.wvFooter, "", footerHtmlStr)

            if (!payload.isOnProcess) {
                binding.wvFooterReceiptShare.visibility = View.VISIBLE
                binding.wvFooterReceiptShare.setBackgroundColor(Color.TRANSPARENT)
                GeneralHelper.setWebViewReceipt(binding.wvFooterReceiptShare, "", footerHtmlStr)
            }
        } else {
            binding.wvFooter.visibility = View.GONE

            if (!payload.isOnProcess) {
                binding.wvFooterReceiptShare.visibility = View.GONE
            }
        }
    }

    private fun shareBuktiTransaksi() {
        animationFadeIn.setAnimationListener(object : Animation.AnimationListener {
            override fun onAnimationStart(animation: Animation?) {}

            override fun onAnimationEnd(animation: Animation?) {
                animationFadeIn.setAnimationListener(null)

                if (!isReadytoShare) {
                    Handler().postDelayed({ requestMediaPermission() }, 500)
                    isReadytoShare = true
                }
            }

            override fun onAnimationRepeat(animation: Animation?) {}
        })

        binding.receiptShareParent.isVisible = true
        binding.receiptShareParent.startAnimation(animationFadeIn)
    }

    private fun alreadyShareBuktiTransfer() {
        animationFadeOut.setAnimationListener(object : Animation.AnimationListener {
            override fun onAnimationStart(animation: Animation?) {}

            override fun onAnimationEnd(animation: Animation?) {
                binding.receiptShareParent.isVisible = false
                animationFadeOut.setAnimationListener(null)
            }

            override fun onAnimationRepeat(animation: Animation?) {}
        })

        binding.receiptShareParent.startAnimation(animationFadeOut)
    }

    private fun lihatLebih() {
        animationFadeOut.setAnimationListener(object : Animation.AnimationListener {
            override fun onAnimationStart(animation: Animation) {
                // do nothing
            }

            override fun onAnimationEnd(animation: Animation) {
                with(binding) {
                    llShowMore.isVisible = true
                    lihatLebih.isVisible = false
                    lihatSedikit.isVisible = true
                    rvTransactionDataViewSmall.isVisible = false
                }

                animationFadeOut.setAnimationListener(null)
            }

            override fun onAnimationRepeat(animation: Animation) {
                // do nothing
            }
        })

        binding.lihatLebih.startAnimation(animationFadeOut)
        binding.flipLihatSedikit.startAnimation(animationFadeOut)

        isShowAll = true
    }

    private fun lihatSedikit() {
        animationFadeOut.setAnimationListener(object : Animation.AnimationListener {
            override fun onAnimationStart(animation: Animation) {
                // do nothing
            }

            override fun onAnimationEnd(animation: Animation) {
                with(binding) {
                    llShowMore.isVisible = false
                    lihatLebih.isVisible = true
                    lihatSedikit.isVisible = false
                    rvTransactionDataViewSmall.isVisible = false
                }

                animationFadeOut.setAnimationListener(null)
            }

            override fun onAnimationRepeat(animation: Animation) {
                // do nothing
            }
        })

        binding.llShowMore.startAnimation(animationFadeOut)
        binding.lihatSedikit.startAnimation(animationFadeOut)

        isShowAll = false
    }

    private fun generateImage(): File {
        val file: File
        val bm: Bitmap = imageHelper.getBitmapFromView(
            binding.receiptShare,
            binding.receiptShare.getChildAt(0).height,
            binding.receiptShare.getChildAt(0).width
        )

        file = saveBitmap(bm, generateNameReceipt())
        return file
    }

    /**
     * Method untuk digunakan untuk generate Nama File BRImo
     *
     * @return tag name FIle Receipt
     */
    private fun generateNameReceipt(): String {
        var tag = ""
        val dateTime = CalendarHelper.getCurrentTimeReceipt()
        try {
            tag = Constant.TAG_START_NAME + dateTime + Constant.TAG_END_NAME
        } catch (e: java.lang.Exception) {
            if (!GeneralHelper.isProd()) Log.e(
                TAG,
                "generateNameReceipt: ",
                e
            )

            tag = Constant.TAG_START_NAME + Constant.TAG_END_NAME
        }
        return tag
    }

    private fun saveBitmap(bm: Bitmap, fileName: String): File {
        val path = Environment.getExternalStorageDirectory().absolutePath + Constant.URI_DOWNLOAD
        val dir = File(path)
        if (!dir.exists()) dir.mkdirs()
        val file = File(dir, fileName)

        try {
            val fOut = FileOutputStream(file)
            bm.compress(Bitmap.CompressFormat.PNG, 90, fOut)
            fOut.flush()
            fOut.close()
        } catch (e: java.lang.Exception) {
            if (!GeneralHelper.isProd()) {
                Log.e(TAG, "saveBitmap: ", e)
            }
        }
        return file
    }

    /**
     * Generate Share INtent to Another APPS
     *
     * @param file file image receipt
     */
    private fun shareImage(file: File) {
        val uri = FileProvider.getUriForFile(
            this,
            BuildConfig.APPLICATION_ID + ".fileprovider",
            file
        )
        val intent = Intent()
        intent.setAction(Intent.ACTION_SEND)
        intent.setType("image/*")

        intent.putExtra(Intent.EXTRA_SUBJECT, "")
        intent.putExtra(Intent.EXTRA_TEXT, "")
        intent.putExtra(Intent.EXTRA_STREAM, uri)
        intent.addFlags(Intent.FLAG_GRANT_READ_URI_PERMISSION)

        val chooser = Intent.createChooser(intent, "Download and share receipt")

        val resInfoList =
            this.packageManager.queryIntentActivities(chooser, PackageManager.MATCH_DEFAULT_ONLY)

        for (resolveInfo in resInfoList) {
            val packageName = resolveInfo.activityInfo.packageName
            this.grantUriPermission(
                packageName,
                uri,
                Intent.FLAG_GRANT_WRITE_URI_PERMISSION or Intent.FLAG_GRANT_READ_URI_PERMISSION
            )
        }

        try {
            startActivity(chooser)
        } catch (e: ActivityNotFoundException) {
            Toast.makeText(this, getString(R.string.not_found_apps), Toast.LENGTH_SHORT).show()
        }
    }

    private fun startPulseMicrophone() {
        runnable.run()
    }

    private fun stopPulseMicrophone() {
        animationMicHandler.removeCallbacks(runnable)
    }

    private fun TextView.typingAnimationTextView(text: String, delay: Long = 75L) {
        if (text.isEmpty() || text == "") return

        val charAnimation = ValueAnimator.ofInt(0, text.length)
        charAnimation.apply {
            this.duration = delay * text.length.toLong()
            this.repeatCount = 0
            addUpdateListener {
                val charCount = it.animatedValue as Int
                val animatedText = text.substring(0, charCount)
                <EMAIL> = animatedText
            }
        }
        charAnimation.start()
    }

    private fun startAudioTTS(message: String) {
        tts.speak(message, TextToSpeech.QUEUE_FLUSH, null, null)
    }

    companion object {
        private const val TAG = "VoiceAssistantActivity"

        fun launchIntent(caller: Activity) {
            val intent = Intent(caller, VoiceAssistantActivity::class.java)
            caller.startActivityForResult(intent, Constant.REQ_PAYMENT)
            caller.overridePendingTransition(R.anim.bottom_up, R.anim.fade_out_trasition)
        }
    }

}