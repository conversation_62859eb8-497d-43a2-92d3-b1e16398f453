package id.co.bri.brimo.models.apimodel.request;

import com.google.gson.annotations.Expose;
import com.google.gson.annotations.SerializedName;

public class CreateNewPinFastRequest {
    @SerializedName("reference_number")
    @Expose
    private String referenceNumber;
    @SerializedName("username")
    @Expose
    private String username;
    @SerializedName("new_pin")
    @Expose
    private String newPin;
    @SerializedName("token_key")
    @Expose
    private String tokenKey;

    public CreateNewPinFastRequest(String referenceNumber, String username, String newPin, String tokenKey) {
        this.referenceNumber = referenceNumber;
        this.username = username;
        this.newPin = newPin;
        this.tokenKey = tokenKey;
    }

    public String getReferenceNumber() {
        return referenceNumber;
    }

    public void setReferenceNumber(String referenceNumber) {
        this.referenceNumber = referenceNumber;
    }

    public String getUsername() {
        return username;
    }

    public void setUsername(String username) {
        this.username = username;
    }

    public String getNewPin() {
        return newPin;
    }

    public void setNewPin(String newPin) {
        this.newPin = newPin;
    }

    public String getTokenKey() {
        return tokenKey;
    }

    public void setTokenKey(String tokenKey) {
        this.tokenKey = tokenKey;
    }

}
