package id.co.bri.brimo.ui.activities.sbn;

import android.annotation.SuppressLint;
import android.app.Activity;
import android.content.Intent;
import android.os.Bundle;
import android.view.View;

import androidx.annotation.Nullable;

import com.google.gson.Gson;

import id.co.bri.brimo.R;
import id.co.bri.brimo.databinding.ActivityTermAndConditionEsbnactivityBinding;
import id.co.bri.brimo.domain.config.Constant;
import id.co.bri.brimo.domain.helpers.GeneralHelper;
import id.co.bri.brimo.models.apimodel.request.esbn.ModelConfirmationEarlyRedeemRequest;
import id.co.bri.brimo.models.apimodel.request.esbn.ModelConfirmationRequest;
import id.co.bri.brimo.models.apimodel.request.esbn.SubmitOfferSbnRequest;
import id.co.bri.brimo.ui.activities.base.BaseActivity;
import id.co.bri.brimo.ui.activities.sbn.earlyredemption.ConfirmationErlyRedeemActivity;

public class TermAndConditionESBNActivity extends BaseActivity implements View.OnClickListener {

    private ActivityTermAndConditionEsbnactivityBinding binding;

    private static ModelConfirmationEarlyRedeemRequest modelConfirmationEarlyRedeemRequest;
    private boolean isChecked = false;
    private ModelConfirmationRequest modelConfirmationRequest;
    private SubmitOfferSbnRequest submitOfferSbnRequest;

    private static boolean isEarly = false;

    private static final String TAG_RESPONSE = "response";
    private static final String TAG_RESPONSE_TEXT = "response_text";
    private static final String TAG_RESPONSE_SUBMIT = "response_submit";
    private static final String TAG_RESPONSE_CONFIRMATION = "response_confirmation";

    private static String linkMemo = "";

    public static void launchIntent(Activity caller, String desc, String text, SubmitOfferSbnRequest submitOfferSbnRequest, ModelConfirmationRequest modelConfirmationRequest, String link) {
        Intent intent = new Intent(caller, TermAndConditionESBNActivity.class);
        intent.putExtra(TAG_RESPONSE, desc);
        intent.putExtra(TAG_RESPONSE_TEXT, text);
        intent.putExtra(TAG_RESPONSE_SUBMIT, new Gson().toJson(submitOfferSbnRequest));
        intent.putExtra(TAG_RESPONSE_CONFIRMATION, new Gson().toJson(modelConfirmationRequest));
        caller.startActivityForResult(intent, Constant.REQ_PAYMENT);
        isEarly = false;
        linkMemo = link;
    }

    public static void launchIntent(Activity caller, String desc, String text, boolean early, ModelConfirmationEarlyRedeemRequest model) {
        Intent intent = new Intent(caller, TermAndConditionESBNActivity.class);
        intent.putExtra(TAG_RESPONSE, desc);
        intent.putExtra(TAG_RESPONSE_TEXT, text);
        isEarly = early;
        modelConfirmationEarlyRedeemRequest = model;
        caller.startActivityForResult(intent, Constant.REQ_PAYMENT);
    }

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        binding = ActivityTermAndConditionEsbnactivityBinding.inflate(getLayoutInflater());
        setContentView(binding.getRoot());

        setupView();

        binding.ivCheckBox.setOnClickListener(this);
        binding.tvSyarat.setOnClickListener(this);
        binding.btnSubmit.setOnClickListener(this);
    }

    private void setupView() {
        if (isEarly) {
            GeneralHelper.setToolbar(this, binding.toolbar.toolbar, "Early Redemption SBN");
        } else {
            GeneralHelper.setToolbar(this, binding.toolbar.toolbar, "Beli SBN");
        }

        if (getIntent().getStringExtra(TAG_RESPONSE) != null) {
            GeneralHelper.setWebView(binding.wvDescription, "", getIntent().getStringExtra(TAG_RESPONSE));
        }

        if (getIntent().getStringExtra(TAG_RESPONSE_TEXT) != null) {
            binding.tvSyarat.setText(getIntent().getStringExtra(TAG_RESPONSE_TEXT));
        }

        if (!isEarly) {
            if (getIntent().getStringExtra(TAG_RESPONSE_SUBMIT) != null) {
                submitOfferSbnRequest = new Gson().fromJson(getIntent().getStringExtra(TAG_RESPONSE_SUBMIT), SubmitOfferSbnRequest.class);
            }
            if (getIntent().getStringExtra(TAG_RESPONSE_CONFIRMATION) != null) {
                modelConfirmationRequest = new Gson().fromJson(getIntent().getStringExtra(TAG_RESPONSE_CONFIRMATION), ModelConfirmationRequest.class);
            }
        }

    }

    @SuppressLint("NonConstantResourceId")
    @Override
    public void onClick(View v) {
        switch (v.getId()) {
            case R.id.iv_check_box:
            case R.id.tv_syarat:
                if (isEarly) {
                    isChecked = !isChecked;
                    checkButton();
                } else {
                    if (!isChecked) {
                        PreviewMemorandumActivity.launchIntent(this, linkMemo);
                    } else {
                        isChecked = !isChecked;
                        checkButton();
                    }
                }
                break;
            case R.id.btnSubmit:
                if (isEarly) {
                    ConfirmationErlyRedeemActivity.launchIntent(this, modelConfirmationEarlyRedeemRequest);
                } else {
                    ConfirmationESBNActivity.launchIntent(this, submitOfferSbnRequest, modelConfirmationRequest);
                }
                break;
        }
    }

    private void checkButton() {
        if (isChecked) {
            binding.ivCheckBox.setBackgroundResource(R.drawable.checkbox_on);
            binding.btnSubmit.setAlpha(1);
            binding.btnSubmit.setEnabled(true);
        } else {
            binding.ivCheckBox.setBackgroundResource(R.drawable.checkbox_off);
            binding.btnSubmit.setAlpha(0.3f);
            binding.btnSubmit.setEnabled(false);
        }
    }

    @Override
    protected void onActivityResult(int requestCode, int resultCode, @Nullable Intent data) {
        super.onActivityResult(requestCode, resultCode, data);
        if (requestCode == Constant.REQ_PAYMENT && data != null) {
            if (resultCode == Activity.RESULT_OK) {
                this.setResult(RESULT_OK, data);
                this.finish();
            } else if (resultCode == Constant.REQ_PETTUNJUK1) {
                isChecked = data.getBooleanExtra("checkbox", false);
                checkButton();
            } else if (resultCode == Constant.REQ_PETTUNJUK2) {
                isChecked = false;
                GeneralHelper.showSnackBar(this.findViewById(R.id.content), "Dokumen gagal dimuat. Silahkan coba lagi");
                checkButton();
            } else {
                this.setResult(RESULT_CANCELED, data);
                this.finish();
            }
        } else if (requestCode == Constant.REQ_PAYMENT) {
            this.setResult(RESULT_CANCELED);
            this.finish();
        } else if (requestCode == Constant.REQ_FORGET_PIN) {
            if (resultCode == RESULT_OK) {
                this.setResult(RESULT_OK);
                this.finish();
            }
        }
    }
}