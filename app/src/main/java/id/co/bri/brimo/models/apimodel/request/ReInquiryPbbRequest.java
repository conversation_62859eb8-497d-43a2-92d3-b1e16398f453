package id.co.bri.brimo.models.apimodel.request;

import com.google.gson.annotations.SerializedName;

public class ReInquiryPbbRequest {
    @SerializedName("code_wilayah")
    private String codeWilayah;

    @SerializedName("no_pembayaran")
    private String noPembayaran;

    public ReInquiryPbbRequest(String codeWilayah, String noPembayaran) {
        this.codeWilayah = codeWilayah;
        this.noPembayaran = noPembayaran;
    }

    public String getCodeWilayah() {
        return codeWilayah;
    }

    public void setCodeWilayah(String codeWilayah) {
        this.codeWilayah = codeWilayah;
    }

    public String getNoPembayaran() {
        return noPembayaran;
    }

    public void setNoPembayaran(String noPembayaran) {
        this.noPembayaran = noPembayaran;
    }
}
