package id.co.bri.brimo.ui.activities.britamarencana;

import android.annotation.SuppressLint;
import android.app.Activity;
import android.content.Context;
import android.content.Intent;
import android.os.Bundle;
import android.os.SystemClock;
import android.text.Editable;
import android.text.TextWatcher;
import android.view.View;
import android.view.inputmethod.InputMethodManager;
import androidx.annotation.Nullable;
import javax.inject.Inject;
import id.co.bri.brimo.R;
import id.co.bri.brimo.contract.IPresenter.britamarencana.IHitungRencanaPresenter;
import id.co.bri.brimo.contract.IView.britamarencana.IHitungRencanaView;
import id.co.bri.brimo.databinding.ActivityHitungRencanaBinding;
import id.co.bri.brimo.domain.config.Constant;
import id.co.bri.brimo.domain.helpers.GeneralHelper;
import id.co.bri.brimo.domain.helpers.calendar.CalendarHelper;
import id.co.bri.brimo.domain.helpers.textwatcher.AmountFormatWatcher;
import id.co.bri.brimo.models.apimodel.request.InquiryEdiRequest;
import id.co.bri.brimo.models.apimodel.request.InquiryOpenRequest;
import id.co.bri.brimo.models.apimodel.response.DetailRencanaResponse;
import id.co.bri.brimo.models.apimodel.response.FormBukaRencanaResponse;
import id.co.bri.brimo.models.apimodel.response.InquiryEditRencanaResponse;
import id.co.bri.brimo.models.apimodel.response.InquiryOpenRencanaResponse;
import id.co.bri.brimo.ui.activities.base.BaseActivity;

public class HitungRencanaActivity extends BaseActivity implements AmountFormatWatcher.onAmountChange, View.OnClickListener, IHitungRencanaView{

    private ActivityHitungRencanaBinding binding;

    protected Double nominalRencana, nominalSetoran;
    protected int month;
    protected boolean isValid = false;
    protected boolean isSimulasiShow = false;
    protected static boolean isFromUbah;
    protected Double lastSetoran;
    protected Double nominalSaldo;
    protected Long minMonth = Long.valueOf(0);
    protected Long maxMonth = Long.valueOf(0);
    protected Long minTarget = Long.valueOf(0);
    private static String productTypes;

    protected static DetailRencanaResponse mDetailRencanaResponse;

    public static void launchIntent(Activity caller, boolean fromUbah, String productType) {
        Intent intent = new Intent(caller, HitungRencanaActivity.class);
        isFromUbah = fromUbah;
        productTypes = productType;
        caller.startActivityForResult(intent, Constant.REQ_PAYMENT);
    }
    public static void launchIntent(Activity caller, boolean fromUbah, DetailRencanaResponse detailRencanaResponse) {
        Intent intent = new Intent(caller, HitungRencanaActivity.class);
        isFromUbah = fromUbah;
        mDetailRencanaResponse = detailRencanaResponse;
        caller.startActivityForResult(intent, Constant.REQ_PAYMENT);
    }

    @Inject
    IHitungRencanaPresenter<IHitungRencanaView> presenter;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        binding = ActivityHitungRencanaBinding.inflate(getLayoutInflater());
        setContentView(binding.getRoot());

        GeneralHelper.setToolbar(this, binding.toolbar.toolbar, "BritAma Rencana");
        binding.btnSubmit.setOnClickListener(this);
        binding.tvkonversi.setOnClickListener(this);
        injectDependency();
        setupView();
    }

    private void injectDependency() {
        getActivityComponent().inject(this);
        if (presenter != null) {
            presenter.setView(this);
            presenter.start();
            if(!isFromUbah){
                presenter.setUrl(GeneralHelper.getString(R.string.url_open_rencana));
                presenter.setUrlInquiry(GeneralHelper.getString(R.string.url_inquiry_open_rencana));
                presenter.getFormBukaRencana();
            }
            else {
                presenter.setUrl(GeneralHelper.getString(R.string.url_rencana_ubah_form));
                presenter.setUrlInquiry(GeneralHelper.getString(R.string.url_inquiry_ubah_rencana));
                presenter.getFormEditRencana();
            }
        }
    }

    //setup view dan listener untuk bulan + mapping route
    public void setupView() {

        if (isFromUbah) {
            binding.layoutrencana.setVisibility(View.VISIBLE);
            binding.layoutketerangan.setVisibility(View.VISIBLE);
            nominalRencana = mDetailRencanaResponse.getAccount().getTargetAmount();
            lastSetoran = mDetailRencanaResponse.getAccount().getDeposit();
            nominalSaldo = (double) (mDetailRencanaResponse.getAccount().getBalance());
        } else {
            binding.layoutrencana.setVisibility(View.GONE);
            binding.layoutketerangan.setVisibility(View.GONE);
        }

        binding.edNominal.addTextChangedListener(new AmountFormatWatcher(binding.edNominal, this, false));
        binding.etJangkaWaktu.addTextChangedListener(new TextWatcher() {
            @Override
            public void beforeTextChanged(CharSequence charSequence, int i, int i1, int i2) {

            }

            @Override
            public void onTextChanged(CharSequence charSequence, int i, int i1, int i2) {
                if (charSequence.length() == 0) {
                    setTvAlert2(R.color.colorErrorMinTrx);
                    hideSimulasi();
                    binding.tvkonversi.setVisibility(View.GONE);
                    isValid = false;
                    buttonEnable(false);
                } else {
                    if(isFromUbah) {
                        month = (int) (Integer.parseInt(charSequence.toString()) - mDetailRencanaResponse.getAccount().getMonthPayment());
                        if (Integer.parseInt(charSequence.toString()) < minMonth || Integer.parseInt(charSequence.toString()) > maxMonth) {
                            buttonEnable(false);
                            binding.tvkonversi.setVisibility(View.GONE);
                            setTvAlert2(R.color.colorErrorMinTrx);
                            isValid = false;
                        } else {
                            binding.tvAlert.setVisibility(View.GONE);
                            binding.tvkonversi.setVisibility(View.VISIBLE);
                            binding.tvkonversi.setText(CalendarHelper.konversiBulan(Integer.parseInt(charSequence.toString())));
                            if(nominalRencana>= nominalSaldo) {
                                isValid = true;
                            }
                            else isValid = false;
                        }
                    }
                    else {
                        month = Integer.parseInt(charSequence.toString());
                        if (month < minMonth || month > maxMonth) {
                            buttonEnable(false);
                            binding.tvkonversi.setVisibility(View.GONE);
                            setTvAlert2(R.color.colorErrorMinTrx);
                            isValid = false;
                        } else {
                            binding.tvAlert.setVisibility(View.GONE);
                            binding.tvkonversi.setVisibility(View.VISIBLE);
                            binding.tvkonversi.setText(CalendarHelper.konversiBulan(Integer.parseInt(charSequence.toString())));
                            if(nominalRencana!=null && nominalRencana>=minTarget) isValid = true;
                            else isValid = false;
                        }
                    }
                }
                if(!isFromUbah)
                    countSetoran(nominalRencana);
                else countSetoran((nominalRencana-nominalSaldo));
            }

            @Override
            public void afterTextChanged(Editable editable) {

            }
        });

        binding.llFrameInput.setOnClickListener((View.OnClickListener) view -> {
            binding.edNominal.requestFocus();
            binding.edNominal.setFocusableInTouchMode(true);
            InputMethodManager imm = (InputMethodManager) getSystemService(Context.INPUT_METHOD_SERVICE);
            imm.showSoftInput(binding.edNominal, InputMethodManager.SHOW_FORCED);
        });
    }

    //alert minimal bulan
    public void setTvAlert2(int color) {
        binding.tvAlert.setVisibility(View.VISIBLE);
        binding.tvAlert.setText(String.format(GeneralHelper.getString(R.string.minim_bulan), minMonth, maxMonth));
        binding.tvAlert.setTextColor(getResources().getColor(color));
    }


    //set text view setoran
    public void setTvSetoran(int color) {
        binding.tvError.setText(String.format(GeneralHelper.getString(R.string.minim_rencana), GeneralHelper.formatNominal(minTarget)));
        binding.tvError.setTextColor(getResources().getColor(color));
        binding.tvError.setVisibility(View.VISIBLE);
        isValid = false;
    }


    //membaca editext nominal
    @Override
    public void onAmountChange(String amount) {
        nominalRencana = (double) Long.parseLong(binding.edNominal.getText().toString().replace(".", "").replace("Rp", "").replace("-", ""));
        if (nominalRencana >= minTarget) {
            binding.tvError.setVisibility(View.GONE);
            if( Integer.parseInt(binding.etJangkaWaktu.getText().toString()) >= minMonth && Integer.parseInt(binding.etJangkaWaktu.getText().toString()) <= maxMonth) isValid = true;
        } else if (binding.edNominal.length() == 0) {
            isValid = false;
            hideSimulasi();
            setTvSetoran(R.color.colorErrorMinTrx);
        } else setTvSetoran(R.color.colorErrorMinTrx);
        if(!isFromUbah)
            countSetoran(nominalRencana);
        else countSetoran((nominalRencana-nominalSaldo));
    }

    @Override
    public void setAmountListener() {

    }


    //method untuk menghitung setoran bulanan nasabah
    public void countSetoran(Double nominalRencana) {
        if (isValid && binding.edNominal.getText().length() != 0 && binding.etJangkaWaktu.getText().length() != 0) {
            if(nominalRencana != null) nominalSetoran = Math.ceil(nominalRencana/month);
            binding.tvSetoran.setText(GeneralHelper.formatRupiah((nominalSetoran)).split(",")[0]);
            showSimulasi();
            if (isFromUbah)
                if (nominalSetoran < lastSetoran) {
                    binding.tvMinimal.setVisibility(View.VISIBLE);
                    binding.layoutketerangan.setVisibility(View.GONE);
                    buttonEnable(false);
                } else {
                    binding.tvMinimal.setVisibility(View.GONE);
                    binding.layoutketerangan.setVisibility(View.GONE);
                    buttonEnable(true);
                }
            else
                buttonEnable(true);
        } else {
            if (isFromUbah) binding.layoutketerangan.setVisibility(View.VISIBLE);
            hideSimulasi();
            buttonEnable(false);
        }
    }

    public void buttonEnable(boolean enabled) {
        if (enabled) {
            binding.btnSubmit.setAlpha(1);
            binding.btnSubmit.setEnabled(true);
        } else {
            binding.btnSubmit.setEnabled(false);
            binding.btnSubmit.setAlpha((float) 0.3);
        }
    }


    @Override
    public void onClick(View view) {
        if (SystemClock.elapsedRealtime() - mLastClickTime < 1000) {
            return;
        }
        mLastClickTime = SystemClock.elapsedRealtime();
        int id = view.getId();
        switch (id) {
            case R.id.btnSubmit:
                if(isFromUbah)
                    presenter.getInquiryEditRencana(new InquiryEdiRequest(mDetailRencanaResponse.getAccount().getAccountNumber(), binding.etJangkaWaktu.getText().toString(),binding.edNominal.getText().toString().replace(".", "").replace("Rp", "").replace("-", "")));
                else
                    presenter.getInquiryOpenRencana(new InquiryOpenRequest(productTypes, binding.etJangkaWaktu.getText().toString(), String.valueOf(nominalRencana)));
                break;
            case R.id.tvkonversi:
                binding.etJangkaWaktu.requestFocus();
                binding.etJangkaWaktu.setFocusableInTouchMode(true);
                InputMethodManager imm = (InputMethodManager) getSystemService(Context.INPUT_METHOD_SERVICE);
                imm.showSoftInput(binding.etJangkaWaktu, InputMethodManager.SHOW_FORCED);
        }
    }

    @Override
    public void onSuccessGetFormRencana(FormBukaRencanaResponse formBukaRencanaResponse) {
        minMonth = formBukaRencanaResponse.getParameter().getMinimalMonth();
        maxMonth = formBukaRencanaResponse.getParameter().getMaximalMonth();
        minTarget = formBukaRencanaResponse.getParameter().getMinimalTargetAmount();
        setTvAlert2(R.color.colorErrorMinTrx);
        setTvSetoran(R.color.colorErrorMinTrx);
    }

    @Override
    public void onSuccessInquiry(InquiryOpenRencanaResponse inquiryOpenRencanaResponse) {
        KonfirmasiRencanaActivity.launchIntent(this, inquiryOpenRencanaResponse, false, productTypes);
    }

    @SuppressLint("SetTextI18n")
    @Override
    public void onSuccessGetFormEditRencana(FormBukaRencanaResponse formBukaRencanaResponse) {
        binding.tvSetoranLama.setText(mDetailRencanaResponse.getAccount().getDepositString());
        binding.tvTargetBulan.setText(mDetailRencanaResponse.getAccount().getPeriod().toString() + " Bulan");
        binding.tvTargetLama.setText(mDetailRencanaResponse.getAccount().getTargetAmountString());
        minMonth = mDetailRencanaResponse.getAccount().getPeriod();
        maxMonth = formBukaRencanaResponse.getParameter().getMaximalMonth();
        minTarget = (long)(mDetailRencanaResponse.getAccount().getTargetAmount()+1);
        setTvAlert2(R.color.colorErrorMinTrx);
        setTvSetoran(R.color.colorErrorMinTrx);
    }

    @Override
    public void onSuccessInquiryEdit(InquiryEditRencanaResponse inquiryEditRencanaResponse) {
        KonfirmasiRencanaActivity.launchIntent(this,inquiryEditRencanaResponse, true, productTypes);
    }

    @Override
    public void onException12() {
        finish();
    }

    @Override
    protected void onActivityResult(int requestCode, int resultCode, @Nullable Intent data) {
        super.onActivityResult(requestCode, resultCode, data);
        if (requestCode == Constant.REQ_PAYMENT) {

            if (resultCode == RESULT_OK) {
                this.setResult(RESULT_OK, data);
                this.finish();
            }
//            else if(resultCode == RESULT_CANCELED ){
//                this.setResult(RESULT_CANCELED, data);
//                this.finish();
//            }
        }
    }

    /**
     * Animate simulasi show
     */
    public void showSimulasi() {
        if (!isSimulasiShow) {
            onAnimatorShow(binding.llSimulasi, true, Constant.REQUEST_RENCANA);
            isSimulasiShow = true;
        }

    }

    /**
     * Animate simulasi hide
     */
    public void hideSimulasi() {
        if (isSimulasiShow) {
            onAnimatorFade(binding.llSimulasi, true, Constant.REQUEST_RENCANA);
            isSimulasiShow = false;
        }
    }

    @Override
    protected void onDestroy() {
        super.onDestroy();
        binding = null;
    }
}