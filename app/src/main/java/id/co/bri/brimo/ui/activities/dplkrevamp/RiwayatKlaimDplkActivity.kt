package id.co.bri.brimo.ui.activities.dplkrevamp

import android.app.Activity
import android.content.Intent
import android.os.Bundle
import android.widget.LinearLayout
import androidx.fragment.app.Fragment
import androidx.viewpager.widget.ViewPager
import id.co.bri.brimo.R
import id.co.bri.brimo.adapters.CustomFragmentPagerAdapter
import id.co.bri.brimo.databinding.ActivityRiwayatKlaimDplkBinding
import id.co.bri.brimo.domain.config.Constant
import id.co.bri.brimo.domain.helpers.GeneralHelper
import id.co.bri.brimo.models.apimodel.response.dplkrevamp.RiwayatClaimDplkResponse
import id.co.bri.brimo.ui.activities.base.BaseActivity
import id.co.bri.brimo.ui.fragments.dplkrevamp.HistoryRiwayatClaimDplkFragment
import id.co.bri.brimo.ui.fragments.dplkrevamp.HistoryRiwayatProsesClaimDplkFragment

class RiwayatKlaimDplkActivity : BaseActivity(), ViewPager.OnPageChangeListener {

    private lateinit var binding: ActivityRiwayatKlaimDplkBinding

    private var fragmentList = mutableListOf<Fragment>()
    private var titleTab = mutableListOf<String>()
    private lateinit var viewAdapter: CustomFragmentPagerAdapter
    private lateinit var linearLayout: LinearLayout


    companion object {
        private lateinit var  mResponse : RiwayatClaimDplkResponse

        @JvmStatic
        fun launchIntent(
            caller: Activity,
            response : RiwayatClaimDplkResponse
        ) {
            val intent = Intent(caller, RiwayatKlaimDplkActivity::class.java)
            mResponse = response
            caller.startActivityForResult(intent, Constant.REQ_PAYMENT)
        }
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        binding = ActivityRiwayatKlaimDplkBinding.inflate(layoutInflater)
        setContentView(binding.root)
        setupView()
    }

    private fun setupView() {
        GeneralHelper.setToolbar(this, binding.tbCatatanKeuangan.toolbar,
            getString(R.string.tb_riwayat_klaim))
        with(binding){

            titleTab.add(getString(R.string.txt_dalam_proses))
            titleTab.add(getString(R.string.txt_riwayat))

            fragmentList.add(HistoryRiwayatProsesClaimDplkFragment(this@RiwayatKlaimDplkActivity,mResponse))
            fragmentList.add(HistoryRiwayatClaimDplkFragment(this@RiwayatKlaimDplkActivity,mResponse))

            viewAdapter = CustomFragmentPagerAdapter(supportFragmentManager, fragmentList, titleTab)
            binding.vpCatatanKeuangan.adapter = viewAdapter
            binding.tabCatatanKeuangan.setViewPager(binding.vpCatatanKeuangan)

            linearLayout = binding.tabCatatanKeuangan.getChildAt(0) as LinearLayout
            binding.tabCatatanKeuangan.setOnPageChangeListener(this@RiwayatKlaimDplkActivity)

            GeneralHelper.changeTabsFontBoldForMutation(this@RiwayatKlaimDplkActivity, linearLayout, 0)
        }


    }

    override fun onPageScrolled(position: Int, positionOffset: Float, positionOffsetPixels: Int) {

    }

    override fun onPageSelected(position: Int) {
        GeneralHelper.changeTabsFontBoldForMutation(this, linearLayout, position)
    }

    override fun onPageScrollStateChanged(state: Int) {
    }
}