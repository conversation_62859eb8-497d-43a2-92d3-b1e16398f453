package id.co.bri.brimo.models.apimodel.request;

import com.google.gson.annotations.Expose;
import com.google.gson.annotations.SerializedName;

public class InfoCvvRequest {
    @SerializedName("pin")
    @Expose
    private String pin;
    @SerializedName("card_token")
    @Expose
    private String cardToken;
    @SerializedName("exp_date")
    @Expose
    private String expDate;

    public InfoCvvRequest(String pin, String cardToken, String expDate) {
        this.pin = pin;
        this.cardToken = cardToken;
        this.expDate = expDate;
    }

    public String getPin() {
        return pin;
    }

    public void setPin(String pin) {
        this.pin = pin;
    }

    public String getCardToken() {
        return cardToken;
    }

    public void setCardToken(String cardToken) {
        this.cardToken = cardToken;
    }

    public String getExpDate() {
        return expDate;
    }

    public void setExpDate(String expDate) {
        this.expDate = expDate;
    }
}
