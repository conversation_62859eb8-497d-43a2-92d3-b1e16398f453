package id.co.bri.brimo.ui.fragments.applyccrevamp

import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import id.co.bri.brimo.R
import id.co.bri.brimo.adapters.applycc.ApplyVccPageOfferAdapter
import id.co.bri.brimo.adapters.applycc.ApplyVccPageProsAdapter
import id.co.bri.brimo.databinding.FragmentApplyVccProductCardBinding
import id.co.bri.brimo.models.applyccrevamp.ApplyCcDataFormModel
import id.co.bri.brimo.ui.fragments.BaseFragment
import id.co.bri.brimo.util.extension.loadImage
import id.co.bri.brimo.util.extension.toArrayList
import id.co.bri.brimo.util.recyclerview.setRecyclerView

class ApplyVccPageCardFragment(
    private var product: ApplyCcDataFormModel.ProductApplyCCDataFormModel,
    private var onListener: DialogDefaultListener,
) : BaseFragment() {

    private val prosListAdapter by lazy(LazyThreadSafetyMode.NONE) { ApplyVccPageProsAdapter() }
    private val offerAdapter by lazy(LazyThreadSafetyMode.NONE) { ApplyVccPageOfferAdapter() }

    private lateinit var binding: FragmentApplyVccProductCardBinding

    interface DialogDefaultListener {
        fun onDetailTab(product: ApplyCcDataFormModel.ProductApplyCCDataFormModel)
    }

    override fun onCreateView(
        inflater: LayoutInflater, container: ViewGroup?,
        savedInstanceState: Bundle?,
    ): View {
        binding = FragmentApplyVccProductCardBinding.inflate(layoutInflater, container, false)
        return binding.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        loadData()
    }

    private fun loadData() {
        binding.apply {
            tvFragmentApplyVccProductCardCardName.text = product.name
            ivProductCard.loadImage(product.imagePath, true, placeholder = R.drawable.bg_full_card)
            tvDesc.text = product.description

            rvCheckDesc.setRecyclerView(prosListAdapter, spacingItemDecoration = 0)
            prosListAdapter.setData(product.benefit.toArrayList())

            rvProductCard.setRecyclerView(offerAdapter, spacingItemDecoration = 0)
            offerAdapter.setData(product.getOfferByImage().toArrayList())

            llDetailCard.setOnClickListener { onListener.onDetailTab(product) }
        }
    }
}