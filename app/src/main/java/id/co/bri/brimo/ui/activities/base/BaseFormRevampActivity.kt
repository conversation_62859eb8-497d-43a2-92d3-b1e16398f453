package id.co.bri.brimo.ui.activities.base

import android.annotation.SuppressLint
import android.app.SearchManager
import android.content.Intent
import android.os.Bundle
import android.util.TypedValue
import android.view.View
import android.view.ViewGroup
import android.widget.EditText
import androidx.appcompat.widget.SearchView
import androidx.core.content.ContextCompat
import androidx.core.content.res.ResourcesCompat
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.ethanhua.skeleton.Skeleton
import com.ethanhua.skeleton.SkeletonScreen
import id.co.bri.brimo.R
import id.co.bri.brimo.adapters.HistoryAdapterRevamp
import id.co.bri.brimo.adapters.SavedAdapterRevamp
import id.co.bri.brimo.contract.IView.base.IBaseFormRevampView
import id.co.bri.brimo.databinding.ActivityFormRevampBinding
import id.co.bri.brimo.domain.config.Constant
import id.co.bri.brimo.domain.helpers.GeneralHelper
import id.co.bri.brimo.models.apimodel.response.HistoryResponse
import id.co.bri.brimo.models.apimodel.response.SavedResponse
import java.util.Objects

abstract class BaseFormRevampActivity : BaseActivity(),
    IBaseFormRevampView,
    HistoryAdapterRevamp.ClickItem,
    SavedAdapterRevamp.ClickItem {

    protected lateinit var savedAdapter: SavedAdapterRevamp
    protected var savedResponses = ArrayList<SavedResponse>()
    private lateinit var skeletonScreenHistory: SkeletonScreen
    private lateinit var skeletonScreenSaved: SkeletonScreen
    private lateinit var skeletonButtonAddSavedList: SkeletonScreen
    private lateinit var historyAdapter: HistoryAdapterRevamp
    private var errorMessage: String? = null
    open var historyResponses = ArrayList<HistoryResponse>()
    open var savedAdapterUseOutlineStyle = false
    open lateinit var binding: ActivityFormRevampBinding

    open fun getData(intent: Intent) {}
    open fun onNoSavedItemSearchNotFound(query: CharSequence?): Pair<String, String> = Pair(
        GeneralHelper.getString(R.string.no_favorit_found),
        String.format(GeneralHelper.getString(R.string.no_favorit_found_desc), "\"" + query + "\"")
    )

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)

        binding = ActivityFormRevampBinding.inflate(layoutInflater)
        setContentView(binding.root)
        getData(intent)

        GeneralHelper.setToolbarRevamp(this, binding.tbRevamp.toolbar, getTitleBar())

        if (intent.extras != null) {
            parseDataIntent(intent.extras)
        }

        setTextForm()
        searchDataSaved()
        initiateHistoryAdapter()
        initiateSavedAdapter()
        buttonClick()

        disableButtonSubmit(true)

        injectDependency()
    }

    private fun parseDataIntent(extras: Bundle?) {
        if (extras != null) {
            isFromFastMenu = extras.getBoolean(Constant.TAG_FROM_FASTMENU, false)
        }
    }

    open fun injectDependency() {
        activityComponent.inject(this)
    }

    open fun handleHistorySavedEmpty() {}

    override fun initiateHistoryAdapter() {
        binding.rvLastTrx.layoutManager =
            LinearLayoutManager(baseContext, RecyclerView.HORIZONTAL, false)
        historyAdapter = HistoryAdapterRevamp(
            baseContext,
            historyResponses,
            this,
            getDefaultIconResource(),
            isFromFastMenu
        )
        skeletonScreenHistory = Skeleton.bind(binding.rvLastTrx)
            .adapter(historyAdapter)
            .shimmer(true)
            .angle(20)
            .frozen(false)
            .duration(1200)
            .count(5)
            .load(R.layout.item_skeleton_history_revamp)
            .show()

        skeletonButtonAddSavedList = Skeleton.bind(binding.llAddSavedList)
            .shimmer(true)
            .angle(20)
            .duration(1200)
            .load(R.layout.item_button_add_saved_list_skeleton)
            .show()
    }

    override fun initiateSavedAdapter() {
        binding.rvSavedData.layoutManager =
            LinearLayoutManager(baseContext, RecyclerView.VERTICAL, false)

        if (baseContext != null) savedAdapter = SavedAdapterRevamp(
            baseContext,
            savedResponses,
            this,
            getDefaultIconResource(),
            isFromFastMenu,
            false,
            savedAdapterUseOutlineStyle
        )
        skeletonScreenSaved = Skeleton.bind(binding.rvSavedData)
            .adapter(savedAdapter)
            .shimmer(true)
            .angle(20)
            .frozen(false)
            .duration(1200)
            .count(5)
            .load(R.layout.item_skeleton_saved_transfer)
            .show()
    }

    override fun hideSkeleton(hide: Boolean) {
        if (hide) {
            if (!isFromFastMenu)
                disableButtonSubmit(false)

            skeletonScreenHistory.hide()
            skeletonScreenSaved.hide()
            skeletonButtonAddSavedList.hide()
        } else {
            disableButtonSubmit(true)
            skeletonScreenHistory.show()
            skeletonScreenSaved.show()
            skeletonButtonAddSavedList.show()
        }
    }

    override fun searchDataSaved() {
        val searchManager = this.getSystemService(SEARCH_SERVICE) as SearchManager
        binding.searchview.setSearchableInfo(searchManager.getSearchableInfo(this.componentName))

        binding.searchview.maxWidth = Int.MAX_VALUE

        val editText: EditText = binding.searchview.findViewById(androidx.appcompat.R.id.search_src_text)
        editText.setHintTextColor(ContextCompat.getColor(this, R.color.neutral_light60))
        editText.typeface = ResourcesCompat.getFont(this, R.font.bri_digital_text_medium)

        editText.setTextSize(TypedValue.COMPLEX_UNIT_SP, 14f)
        binding.searchview.setOnQueryTextFocusChangeListener { _, b ->
            if (b) {
                binding.searchview.setBackgroundResource(
                    GeneralHelper.getImageId(
                        this,
                        "bg_blue_border"
                    )
                )
            } else {
                binding.searchview.setBackgroundResource(
                    GeneralHelper.getImageId(
                        this,
                        "bg_grey_border"
                    )
                )
            }
        }

        binding.searchview.setOnQueryTextListener(object : SearchView.OnQueryTextListener {
            override fun onQueryTextSubmit(query: String): Boolean {
                Objects.requireNonNull(savedAdapter.filter).filter(query)
                return false
            }

            override fun onQueryTextChange(newText: String): Boolean {
                Objects.requireNonNull(savedAdapter.filter).filter(newText)

                if (newText.isEmpty()) {
                    if (savedResponses.isNotEmpty()) {
                        binding.llNoDataSavedFound.visibility = View.GONE
                        binding.rvSavedData.visibility = View.VISIBLE
                    } else {
                        binding.llNoDataSavedFound.visibility = View.GONE
                        binding.llNoDataSaved.visibility = View.VISIBLE
                        binding.rvSavedData.visibility = View.GONE
                    }
                }

                return false
            }
        })
    }

    @SuppressLint("NotifyDataSetChanged")
    override fun onSuccessGetHistoryForm(historyResponses: List<HistoryResponse>) {
        this.historyResponses.clear()
        this.historyResponses.addAll(historyResponses)
        historyAdapter.setHistoryResponses(this.historyResponses)
        historyAdapter.notifyDataSetChanged()

        binding.rvLastTrx.adapter = historyAdapter
        binding.rvLastTrx.visibility = View.VISIBLE
        if (this.historyResponses.size > 0) {
            binding.llNoDataHistory.visibility = View.GONE
            binding.rvLastTrx.visibility = View.VISIBLE
        } else {
            binding.llNoDataHistory.visibility = View.VISIBLE
            binding.rvLastTrx.visibility = View.GONE
        }
    }

    @SuppressLint("NotifyDataSetChanged")
    override fun onSuccessGetSavedForm(savedResponses: List<SavedResponse>) {
        this.savedResponses.clear()
        this.savedResponses.addAll(savedResponses)
        savedAdapter.setSavedResponses(this.savedResponses)
        savedAdapter.notifyDataSetChanged()
        binding.rvSavedData.adapter = savedAdapter
        binding.rvSavedData.visibility = View.VISIBLE
        if (this.savedResponses.size > 0) {
            enableSearchView(binding.searchview, true)
            binding.llNoDataSaved.visibility = View.GONE
        } else {
            binding.llNoDataSaved.visibility = View.VISIBLE
            binding.rvSavedData.visibility = View.GONE
        }
    }

    override fun onNoSavedItemSearch(isTrue: Boolean, query: CharSequence?) {
        if (isTrue) {
            if (savedResponses.isEmpty()) {
                binding.rvSavedData.visibility = View.GONE
                binding.llNoDataSavedFound.visibility = View.GONE
                binding.llNoDataSaved.visibility = View.VISIBLE
            } else {
                binding.rvSavedData.visibility = View.GONE
                binding.llNoDataSavedFound.visibility = View.VISIBLE
                val onNoSavedItemSearchNotFound = onNoSavedItemSearchNotFound(query)
                if (onNoSavedItemSearchNotFound.first.isNotEmpty()) binding.tvNoDataFoundTitle.text = onNoSavedItemSearchNotFound.first
                binding.tvNoDataFoundDesc.text =
                    onNoSavedItemSearchNotFound.second.ifEmpty {
                        String.format(
                            GeneralHelper.getString(R.string.no_favorit_found_desc),
                            "\"" + query + "\""
                        )
                    }
            }
        } else {
            binding.llNoDataSavedFound.visibility = View.GONE
            binding.rvSavedData.visibility = View.VISIBLE
        }
    }

    override fun onHistorySavedEmpty() {
        handleHistorySavedEmpty()
    }

    override fun disableButtonSubmit(disable: Boolean) {
        if (disable) {
            binding.btnSubmit.isEnabled = false
            binding.btnSubmit.setTextColor(GeneralHelper.getColor(R.color.neutral_light60))
        } else {
            binding.btnSubmit.isEnabled = true
            binding.btnSubmit.setTextColor(GeneralHelper.getColor(R.color.neutral_light10))
        }
    }

    /*
     *  method untuk menampilkan error snackbar dari Intent aktivity inquiry dan konfirmasi
     */
    protected open fun cekErrorMessage() {
        if (errorMessage != null) {
            //menampilkan snacknar error
            showSnackbarErrorMessageRevamp(errorMessage, ALERT_ERROR, this, false)

            //clear error message
            errorMessage = null
        }
    }

    @Suppress("DEPRECATION")
    @Deprecated("Deprecated in Java")
    override fun onActivityResult(requestCode: Int, resultCode: Int, data: Intent?) {
        super.onActivityResult(requestCode, resultCode, data)
        if (requestCode == Constant.REQ_PAYMENT && data != null) {
            if (resultCode == RESULT_OK) {
                this.setResult(RESULT_OK, data)
                finish()
            } else if (resultCode == RESULT_CANCELED) {
                //membalikan data error message agar muncul snackbar di dashboard
                errorMessage =
                    data.getStringExtra(Constant.TAG_ERROR_MESSAGE)

                cekErrorMessage()
            }
        }
    }

    //method untuk enable disable searchview
    open fun enableSearchView(view: View, enabled: Boolean) {
        view.isEnabled = enabled
        if (view is ViewGroup) {
            for (i in 0 until view.childCount) {
                val child = view.getChildAt(i)
                enableSearchView(child, enabled)
            }
        }
    }

    override fun checkDataHistorySavedList(){
        //implemented on each feature form
    }
}