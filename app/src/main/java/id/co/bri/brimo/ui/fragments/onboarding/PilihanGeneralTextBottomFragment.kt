package id.co.bri.brimo.ui.fragments.onboarding

import android.annotation.SuppressLint
import android.app.SearchManager
import android.content.Context
import android.os.Bundle
import android.util.TypedValue
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.EditText
import android.widget.ImageView
import android.widget.LinearLayout
import androidx.appcompat.widget.SearchView
import androidx.core.content.res.ResourcesCompat
import androidx.recyclerview.widget.LinearLayoutManager
import com.google.android.material.bottomsheet.BottomSheetDialogFragment
import id.co.bri.brimo.R
import id.co.bri.brimo.adapters.onboarding.OnboardingKodeNegaraAdapter
import id.co.bri.brimo.adapters.onboarding.OnboardingPilihanTextAdapter
import id.co.bri.brimo.databinding.FragmentPilihanGeneralTextBottomBinding
import id.co.bri.brimo.domain.helpers.GeneralHelper
import id.co.bri.brimo.models.apimodel.response.onboardingrevamp.CodeValueIcon

class PilihanGeneralTextBottomFragment(
    private val title: String,
    private val codeValues: MutableList<CodeValueIcon>,
    private val titleSearch: String,
    private val showSearch: Boolean,
    private val recylerCountry: Boolean,
    private val onOptionGeneralText: OnOptionGeneralText
) : BottomSheetDialogFragment(), OnboardingPilihanTextAdapter.OnCallbackPilihan,
    OnboardingKodeNegaraAdapter.OnClickKodeNegara {

    private lateinit var binding: FragmentPilihanGeneralTextBottomBinding

    private lateinit var pilihanTextAdapter: OnboardingPilihanTextAdapter

    private lateinit var pilihanKodeNegaraAdapter: OnboardingKodeNegaraAdapter

    @SuppressLint("ValidFragment")
    fun PilihanGeneralTextBottomFragment() {
        // do nothing
    }

    interface OnOptionGeneralText {
        fun itemOptionGeneralText(codeValue: CodeValueIcon)
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setStyle(STYLE_NORMAL, R.style.CustomBottomSheetDialogTheme)
    }

    override fun onCreateView(
        inflater: LayoutInflater, container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        binding = FragmentPilihanGeneralTextBottomBinding.inflate(inflater, container, false)
        return binding.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)

        if (showSearch) {
            binding.searchview.visibility = View.VISIBLE
            settingSearch()
            settingHeightLayout()
        }

        binding.tvTitle.text = title

        if (recylerCountry) {
            pilihanKodeNegaraAdapter =
                OnboardingKodeNegaraAdapter(requireContext(), codeValues, this)
            binding.recylerview.adapter = pilihanKodeNegaraAdapter
            binding.recylerview.layoutManager =
                LinearLayoutManager(requireContext(), LinearLayoutManager.VERTICAL, false)
        } else {
            pilihanTextAdapter =
                OnboardingPilihanTextAdapter(requireContext(), codeValues, this)
            binding.recylerview.adapter = pilihanTextAdapter
            binding.recylerview.layoutManager =
                LinearLayoutManager(requireContext(), LinearLayoutManager.VERTICAL, false)
        }
    }

    private fun settingHeightLayout() {
        val linearLayout = LinearLayout(requireContext())

        val heightInPixels: Int =
            resources.getDimension(R.dimen._500sdp)
                .toInt()

        val layoutParams =
            LinearLayout.LayoutParams(LinearLayout.LayoutParams.MATCH_PARENT, heightInPixels)
        linearLayout.layoutParams = layoutParams

        binding.layoutPilihan.addView(linearLayout)
        binding.layoutPilihan.minimumWidth = resources.getDimension(R.dimen._200sdp)
            .toInt()
    }

    @SuppressLint("NotifyDataSetChanged")
    private fun settingSearch() {
        val magImage: ImageView =
            binding.searchview.findViewById(androidx.appcompat.R.id.search_close_btn)
        magImage.setImageResource(R.drawable.clear_search)

        val editText =
            binding.searchview.findViewById<EditText>(androidx.appcompat.R.id.search_src_text)
        editText.setHintTextColor(GeneralHelper.getColor(R.color.colorTextSearch))
        editText.typeface =
            ResourcesCompat.getFont(requireActivity(), R.font.bri_digital_text_medium)
        editText.setTextSize(TypedValue.COMPLEX_UNIT_SP, 13f)
        editText.hint = titleSearch

        val searchManager =
            requireActivity().getSystemService(Context.SEARCH_SERVICE) as SearchManager
        binding.searchview.setSearchableInfo(searchManager.getSearchableInfo(requireActivity().componentName))
        binding.searchview.maxWidth = Int.MAX_VALUE
        binding.searchview.setOnQueryTextListener(object : SearchView.OnQueryTextListener {
            override fun onQueryTextSubmit(query: String): Boolean {
                if (recylerCountry) {
                    pilihanKodeNegaraAdapter.filter.filter(query)
                } else {
                    pilihanTextAdapter.filter.filter(query)
                }
                return false
            }

            override fun onQueryTextChange(newText: String): Boolean {
                if (recylerCountry) {
                    pilihanKodeNegaraAdapter.filter.filter(newText)
                } else {
                    pilihanTextAdapter.filter.filter(newText)
                }
                return false
            }
        })
    }

    override fun itemPilihan(codeValue: CodeValueIcon) {
        dismiss()
        onOptionGeneralText.itemOptionGeneralText(codeValue)
    }

    override fun onItemKodeNegara(kodeNegaraResponse: CodeValueIcon) {
        dismiss()
        onOptionGeneralText.itemOptionGeneralText(kodeNegaraResponse)
    }
}