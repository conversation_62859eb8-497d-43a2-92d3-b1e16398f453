package id.co.bri.brimo.ui.activities.ssc

import android.app.Activity
import android.app.PendingIntent
import android.content.DialogInterface
import android.content.Intent
import android.content.IntentFilter
import android.nfc.NfcAdapter
import android.nfc.NfcManager
import android.nfc.Tag
import android.nfc.tech.IsoDep
import android.os.Build
import android.os.Bundle
import android.provider.Settings
import androidx.activity.OnBackPressedCallback
import androidx.appcompat.app.AlertDialog
import com.google.gson.Gson
import id.co.bri.brimo.R
import id.co.bri.brimo.contract.IPresenter.ssc.IComplaintCekBrizziPresenter
import id.co.bri.brimo.contract.IView.ssc.IComplaintCekBrizziView
import id.co.bri.brimo.databinding.ActivityCekKartuBrizziBinding
import id.co.bri.brimo.domain.config.Constant
import id.co.bri.brimo.domain.helpers.GeneralHelper
import id.co.bri.brimo.models.apimodel.response.AktivasiBrizziResponse
import id.co.bri.brimo.models.apimodel.response.InquiryBrizziResponse
import id.co.bri.brimo.models.apimodel.response.ssc.ComplaintInformasiResponse
import id.co.bri.brimo.models.apimodel.response.ssc.ComplaintMutasiResponse
import id.co.bri.brimo.ui.activities.DashboardIBActivity
import id.co.bri.brimo.ui.activities.InfoSaldoBrizzi
import id.co.bri.brimo.ui.activities.RecieptBrizziActivty
import id.co.bri.brimo.ui.activities.base.BaseActivity
import id.co.bri.brimo.ui.fragments.BottomDialogHorizontalButtonFragment
import id.co.bri.brizzi.Brizzi
import id.co.bri.brizzi.BrizziHistory
import javax.inject.Inject

class ComplaintCekBrizziActivity : BaseActivity(), IComplaintCekBrizziView, BottomDialogHorizontalButtonFragment.OnCallback {

    private lateinit var binding: ActivityCekKartuBrizziBinding

    @Inject
    lateinit var presenter: IComplaintCekBrizziPresenter<IComplaintCekBrizziView>

    private var mNfcAdapter: NfcAdapter? = null
    private var mNfcManager: NfcManager? = null
    private var complaintInformasiResponse: ComplaintInformasiResponse? = null
    private var complaintTransaction: ComplaintMutasiResponse.Transaction? = null
    private var strToolbar: String = ""
    private var mBrizzi: Brizzi? = null
    private var aktivasiBrizziResponse: AktivasiBrizziResponse? = null
    private var brizziHistoryObjectArrayList: ArrayList<BrizziHistory>? = null
    private var isUpdateBalance = false
    private var isTransactionSuccess = false
    private var isSkipMutation = false


    companion object {

        private const val sBrizzi = "brizzi"
        private const val sInquiryBrizzi = "inquiry_brizzi"
        private const val sComplaintResponse = "complaint_response"
        private const val sTransaction = "transaction"
        private const val sToolbar = "toolbar"
        private const val sIsSkipMutation = "is_skip_mutation"
        private const val mJourneyType = Constant.CIAType.TYPE_COMPLAINT_IN_APPS_BRIZZI

        fun launchIntent(caller: Activity) {
            val intent = Intent(caller, ComplaintCekBrizziActivity::class.java)
            intent.putExtra(sIsSkipMutation, true)
            caller.startActivityForResult(intent, Constant.REQ_SCAN)
        }

        fun launchIntent(caller: Activity, toolbar: String, complaintResponse: String, transaction: String) {
            val intent = Intent(caller, ComplaintCekBrizziActivity::class.java)
            intent.putExtra(sToolbar, toolbar)
            intent.putExtra(sComplaintResponse, complaintResponse)
            intent.putExtra(sTransaction, transaction)
            caller.startActivityForResult(intent, Constant.REQ_NON_PAYMENT)
        }
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)

        binding = ActivityCekKartuBrizziBinding.inflate(layoutInflater)
        setContentView(binding.root)

        injectDependency()
        intentExtras()
        setupViews()
        handleBackPressed()

    }

    private fun intentExtras() {
        if (intent.hasExtra(sComplaintResponse))
            complaintInformasiResponse = Gson().fromJson(
                intent.getStringExtra(sComplaintResponse),
                ComplaintInformasiResponse::class.java
            )

        if (intent.hasExtra(sTransaction))
            complaintTransaction = Gson().fromJson(
                intent.getStringExtra(sTransaction),
                ComplaintMutasiResponse.Transaction::class.java
            )

        if (intent.hasExtra(sToolbar))
           strToolbar = intent.getStringExtra(sToolbar).toString()

        if (intent.hasExtra(sIsSkipMutation))
            isSkipMutation = intent.getBooleanExtra(sIsSkipMutation, false)
    }

    private fun injectDependency() {
        activityComponent.inject(this)
        presenter.view = this
        presenter.start()
        presenter.setInquiryUrl(GeneralHelper.getString(R.string.url_check_top_up_brizzi))
        presenter.setPaymentUrl(GeneralHelper.getString(R.string.url_aktivasi_top_up_brizzi))
        presenter.setValidateUrl(GeneralHelper.getString(R.string.url_validate_deposit_brizzi))
    }

    private fun setupViews() {
        GeneralHelper.setToolbar(
            this,
            binding.tbBriva.toolbar,
            GeneralHelper.getString(R.string.brizzi_titlebar)
        )

        checkingNfc()
    }

    private fun checkingNfc() {
        try {
            mNfcManager = applicationContext.getSystemService(NFC_SERVICE) as NfcManager
            mNfcAdapter = NfcAdapter.getDefaultAdapter(this)

            if (mNfcAdapter == null) {
                showSnackbarErrorMessage(
                    GeneralHelper.getString(R.string.brizzi_device_tidak_support_nfc),
                    ALERT_ERROR,
                    this,
                    false
                )
            } else {
                if (!mNfcAdapter!!.isEnabled) {
                    val alertBox = AlertDialog.Builder(this)
                    alertBox.setMessage(GeneralHelper.getString(R.string.brizzi_aktifkan_nfc_lanjut))
                    alertBox.setPositiveButton(
                        GeneralHelper.getString(R.string.aktifkan_2),
                        object : DialogInterface.OnClickListener {
                            override fun onClick(dialog: DialogInterface, which: Int) {
                                run {
                                    val intent = Intent(Settings.ACTION_NFC_SETTINGS)
                                    startActivity(intent)
                                }
                            }
                        })
                    alertBox.setNegativeButton(
                        GeneralHelper.getString(R.string.batal)
                    ) { _, _ -> finish() }
                    alertBox.show()
                } else {
                    onNewIntent(intent)
                }
            }
        } catch (e: Exception) {
            showSnackbarErrorMessage(
                GeneralHelper.getString(R.string.silakan_coba_kembali),
                ALERT_ERROR,
                this,
                false
            )
        }
    }

    override fun hideProgress() {
        GeneralHelper.dismissDialog()
    }

    override fun showProgress() {
        GeneralHelper.showDialog(this)
    }

    override fun onResume() {
        super.onResume()

        val intent = Intent(this, ComplaintCekBrizziActivity::class.java)
        intent.flags = Intent.FLAG_RECEIVER_REPLACE_PENDING
        intent.addFlags(Intent.FLAG_ACTIVITY_CLEAR_TOP)

        //fixing pendingIntent Android 31
        val currentFlags =
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.S)
                PendingIntent.FLAG_MUTABLE or PendingIntent.FLAG_UPDATE_CURRENT
            else 0
        val pendingIntent = PendingIntent.getActivity(this, 0, intent, currentFlags)
        val filters = arrayOf<IntentFilter>()

        try {
            if (mNfcAdapter != null) {
                if (!mNfcAdapter!!.isEnabled) {
                    val alertBox = AlertDialog.Builder(this)
                    alertBox.setMessage(GeneralHelper.getString(R.string.brizzi_aktifkan_nfc_lanjut))
                    alertBox.setPositiveButton(
                        GeneralHelper.getString(R.string.aktifkan_2)
                    ) { _, _ ->
                        val intent = Intent(Settings.ACTION_NFC_SETTINGS)
                        startActivity(intent)
                    }
                    alertBox.setNegativeButton(
                        GeneralHelper.getString(R.string.batal)
                    ) { _, _ -> finish() }
                    alertBox.show()
                } else {
                    mNfcAdapter!!.enableForegroundDispatch(this, pendingIntent, filters, null)
                }
            }
        } catch (e: java.lang.Exception) {
            showSnackbarErrorMessage("NFC", ALERT_ERROR, this, false)
        }
    }

    override fun onPause() {
        super.onPause()
        if (mNfcAdapter != null) {
            mNfcAdapter!!.disableForegroundDispatch(this)
        }
    }

    @Suppress("DEPRECATION")
    override fun onNewIntent(intent: Intent) {
        super.onNewIntent(intent)

        val action = intent.action
        try {
            if (NfcAdapter.ACTION_TAG_DISCOVERED == action || NfcAdapter.ACTION_TECH_DISCOVERED == action) {
                val tag: Tag? = intent.getParcelableExtra(NfcAdapter.EXTRA_TAG)
                val brizziTag: IsoDep? = IsoDep.get(tag)
                mBrizzi = Brizzi(brizziTag)

                if (isUpdateBalance) presenter.updateBalancePres(mBrizzi!!)
                else presenter.checkBalancePres(mBrizzi!!)
            }
        } catch (e: java.lang.Exception) {
            showSnackbarErrorMessage(
                GeneralHelper.getString(R.string.brizzi_kartu_gagal_terbaca),
                ALERT_ERROR,
                this,
                false
            )
        }
    }

    override fun onException(message: String?) {
        if (GeneralHelper.isContains(
                Constant.LIST_TYPE_GAGAL,
                message
            )
        ) GeneralHelper.showDialogGagalBack(this, message)
        else {
            val returnIntent = Intent()
            returnIntent.putExtra(Constant.TAG_ERROR_MESSAGE, message)
            this.setResult(RESULT_CANCELED, returnIntent)
            finish()
        }
    }

    override fun onException01(message: String) {
        GeneralHelper.showDialogGagalBackDescBerubah(this, Constant.TRANSAKSI_GAGAL, message)
        setResult(RESULT_OK)
    }

    override fun showErrorMessage(message: String) {
        showSnackbarErrorMessage(message, ALERT_ERROR, this, false)
    }

    override fun continueInfoSaldo(brizzi: Brizzi, inquiryBrizziResponse: InquiryBrizziResponse) {

        if(isSkipMutation){
            val resultIntent = Intent()
            resultIntent.putExtra(sBrizzi, Gson().toJson(brizzi))
            resultIntent.putExtra(sInquiryBrizzi, Gson().toJson(inquiryBrizziResponse))
            setResult(RESULT_OK, resultIntent)
            finish()

            brizziHistoryObjectArrayList = brizzi.cardData.cardHistory
        } else {
            val pendingBalance = inquiryBrizziResponse.pendingBalance ?: 0

            if(pendingBalance != 0){
                showUpdateBalanceDialog(pendingBalance)
            } else if (isTransactionSuccess){
                InfoSaldoBrizzi.launchIntent(
                    this,
                    brizzi,
                    inquiryBrizziResponse,
                    mJourneyType,
                    isFromFastMenu
                )
            } else {
                complaintInformasiResponse?.let {
                    complaintTransaction?.let { it1 ->
                        ComplaintBrizziActivity.launchIntentTopUpBrizzi(this, strToolbar,
                            it, it1, brizzi
                        )
                    }
                }
            }
        }
    }

    override fun onSuccessGetCommitAktivasi(aktivasiBrizziResponse: AktivasiBrizziResponse) {
        this.aktivasiBrizziResponse = aktivasiBrizziResponse
        presenter.commitContinuePres(aktivasiBrizziResponse, mBrizzi!!)
    }

    override fun onExceptionGagalDefault(message: String) {
        showErrorMessage(message)
    }

    override fun onExceptionNonValidateBalanceUpdate(message: String) {
        val returnIntent = Intent()

        returnIntent.putExtra(Constant.TAG_ERROR_MESSAGE, message)

        this.setResult(RESULT_CANCELED, returnIntent)
        this.finish()
    }

    override fun onSuccessGetValidateAktivasi() {
        isUpdateBalance = false
        RecieptBrizziActivty.launchIntent(this, aktivasiBrizziResponse, mJourneyType)
    }

    override fun onExceptionTrxExpired(message: String) {
        showErrorMessage(message)
    }

    override fun onExceptionValidate(messagePayment: String, messageValidate: String) {
        showErrorMessage(messagePayment)
    }

    override fun onException12(message: String) {
        showErrorMessage(message)
    }

    private fun showUpdateBalanceDialog(pendingBalance: Int){
        val formattedBalance = GeneralHelper.formatNominal(pendingBalance.toString())
         val dialogFragment = BottomDialogHorizontalButtonFragment(
            R.drawable.ic_brizzi_update_balance, GeneralHelper.getString(R.string.brizzi_update_balance_title),
            getString(R.string.brizzi_update_balance_desc, formattedBalance),
             GeneralHelper.getString(R.string.btnBatal),
             GeneralHelper.getString(R.string.brizzi_update_balance_submit_btn),
             this
        )
        dialogFragment.show(supportFragmentManager, "")
        dialogFragment.isCancelable = false
    }

    @Deprecated("Deprecated in Java")
    @Suppress("DEPRECATION")
    override fun onActivityResult(requestCode: Int, resultCode: Int, data: Intent?) {
        super.onActivityResult(requestCode, resultCode, data)
        if (requestCode == Constant.REQ_PAYMENT) {
            if (resultCode == RESULT_OK) {
                if (data != null){
                    isTransactionSuccess = data.getBooleanExtra(Constant.REQUEST_RECEIPT, false)
                }
                this.setResult(RESULT_OK, data)
            } else {
                this.setResult(RESULT_CANCELED, data)
            }

        }
    }

    override fun onClickSubmitButton() {
        isUpdateBalance = true
    }

    override fun onClickCancelButton() {
        finish()
    }

    override fun onDestroy() {
        presenter.stop()
        super.onDestroy()
    }

    private fun handleBackPressed(){
        onBackPressedDispatcher.addCallback(this, object : OnBackPressedCallback(true) {
            override fun handleOnBackPressed() {
                if (isTransactionSuccess){
                    DashboardIBActivity.launchIntentSuccess(this@ComplaintCekBrizziActivity, "", true)
                } else {
                    finish()
                }
            }
        })
    }
}