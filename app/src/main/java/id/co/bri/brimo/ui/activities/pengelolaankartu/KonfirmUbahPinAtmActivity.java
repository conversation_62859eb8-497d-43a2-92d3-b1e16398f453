package id.co.bri.brimo.ui.activities.pengelolaankartu;

import android.annotation.SuppressLint;
import android.app.Activity;
import android.content.Intent;
import android.os.Bundle;
import android.util.Log;
import android.view.KeyEvent;
import android.view.View;
import androidx.annotation.NonNull;
import androidx.recyclerview.widget.GridLayoutManager;
import javax.inject.Inject;
import id.co.bri.brimo.R;
import id.co.bri.brimo.adapters.PinNumberAdapter;
import id.co.bri.brimo.adapters.baseadapter.base.BasePinAdapter;
import id.co.bri.brimo.adapters.pinadapter.OtpInputAdapter;
import id.co.bri.brimo.contract.IPresenter.pengelolaankartu.IUbahPinAtmPresenter;
import id.co.bri.brimo.contract.IView.pengelolaankartu.IUbahPinAtmView;
import id.co.bri.brimo.databinding.ActivityUbahPinBinding;
import id.co.bri.brimo.di.modules.fragment.PinAllModule;
import id.co.bri.brimo.domain.config.AppConfig;
import id.co.bri.brimo.domain.config.Constant;
import id.co.bri.brimo.domain.helpers.GeneralHelper;
import id.co.bri.brimo.models.apimodel.request.pengelolaankartu.ReissuePinRequest;
import id.co.bri.brimo.models.apimodel.request.pengelolaankartu.UbahPinAtmRequest;
import id.co.bri.brimo.security.AES;
import id.co.bri.brimo.ui.activities.base.BaseActivity;
import id.co.bri.brimo.ui.customviews.pin.InsertPinNumbers;

public class KonfirmUbahPinAtmActivity extends BaseActivity implements
        IUbahPinAtmView,
        View.OnFocusChangeListener,
        View.OnKeyListener,
        PinNumberAdapter.OnPinNumberListener,
        BasePinAdapter.PinAdapterListener {

    private ActivityUbahPinBinding binding;

    @Inject
    IUbahPinAtmPresenter<IUbahPinAtmView> ubahPinPresenter;

    private OtpInputAdapter otpInputAdapter;

    private String sRefNum;
    private String sPin;

    private String sToolbar;

    private String sDesc;

    private static final String TAG_REFNUM = "refnum";
    private static final String TAG_PIN = "pin";
    private static final String TAG_ISPINSALAH = "pin_salah";
    private static final String TAG_TOOLBAR = "toolbar";

    private static final String TAG_DESC = "desc";

    private int countPin = 0;

    public static void launchIntent(Activity caller, String sRefnum, String pin, String sToolbar, String sDesc) {
        Intent intent = new Intent(caller, KonfirmUbahPinAtmActivity.class);
        intent.putExtra(TAG_REFNUM, sRefnum);
        intent.putExtra(TAG_PIN, pin);
        intent.putExtra(TAG_TOOLBAR, sToolbar);
        intent.putExtra(TAG_DESC, sDesc);
        caller.startActivityForResult(intent, Constant.REQ_CARD);
    }

    public static void launchIntentPinSalah(Activity caller, String sRefnum, String pin, String sToolbar, String sDesc) {
        Intent intent = new Intent(caller, KonfirmUbahPinAtmActivity.class);
        intent.putExtra(TAG_REFNUM, sRefnum);
        intent.putExtra(TAG_PIN, pin);
        intent.putExtra(TAG_ISPINSALAH, true);
        intent.putExtra(TAG_TOOLBAR, sToolbar);
        intent.putExtra(TAG_DESC, sDesc);
        caller.startActivityForResult(intent, Constant.REQ_REISSUE);
    }

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        binding = ActivityUbahPinBinding.inflate(getLayoutInflater());
        setContentView(binding.getRoot());

        if (getIntent().getExtras() != null) {
            if (getIntent().hasExtra(TAG_REFNUM))
                sRefNum = getIntent().getExtras().getString(TAG_REFNUM);
            if (getIntent().hasExtra(TAG_PIN))
                sPin = getIntent().getExtras().getString(TAG_PIN);
            if (getIntent().hasExtra(TAG_TOOLBAR))
                sToolbar = getIntent().getExtras().getString(TAG_TOOLBAR);
            if (getIntent().hasExtra(TAG_DESC))
                sDesc = getIntent().getExtras().getString(TAG_DESC);

        }

        injectDependency();
        setupLayout();
        setStatusColor(R.color.toolbar_blue);
    }

    private void setupLayout() {
        GeneralHelper.setToolbar(this, binding.toolbarPin.toolbar, sToolbar);
        binding.tvDeksripsi.setText(sDesc);
        binding.tvLupaPin.setVisibility(View.GONE);

        otpInputAdapter = new OtpInputAdapter(this);
        PinNumberAdapter pinNumberAdapter = new PinNumberAdapter(InsertPinNumbers.Companion.getPinNumberList(this));
        GridLayoutManager pinOtpLayoutManager = new GridLayoutManager(this, 6);
        GridLayoutManager pinPadLayoutManager = new GridLayoutManager(this, 3);

        pinNumberAdapter.setOnPinNumberListener(this);
        otpInputAdapter.setListener(this);

        binding.rvBox.setLayoutManager(pinOtpLayoutManager);
        binding.rvBox.setAdapter(otpInputAdapter);

        binding.rvInput.setLayoutManager(pinPadLayoutManager);
        binding.rvInput.setAdapter(pinNumberAdapter);
    }

    private void injectDependency() {
        getActivityComponent()
                .plusPinComponent(new PinAllModule())
                .inject(this);
        if (ubahPinPresenter != null) {
            ubahPinPresenter.setView(this);
            ubahPinPresenter.start();

            if (getIntent().hasExtra(TAG_ISPINSALAH))
                ubahPinPresenter.setUrl(GeneralHelper.getString(R.string.url_reissue_pin_atm));
            else
                ubahPinPresenter.setUrl(GeneralHelper.getString(R.string.url_change_pin_atm_submit));
        }
    }

    @Override
    protected void onResume() {
        super.onResume();
        if (ubahPinPresenter != null) {
            ubahPinPresenter.setView(this);
            ubahPinPresenter.start();

            if (getIntent().hasExtra(TAG_ISPINSALAH))
                ubahPinPresenter.setUrl(GeneralHelper.getString(R.string.url_reissue_pin_atm));
            else
                ubahPinPresenter.setUrl(GeneralHelper.getString(R.string.url_change_pin_atm_submit));
        }
    }

    @Override
    public void onFocusChange(View v, boolean hasFocus) {
        // do nothing
    }

    @Override
    public boolean onKey(View v, int keyCode, KeyEvent event) {
        return false;
    }

    @Override
    public void onPinClicked(int pinNumber) {
        otpInputAdapter.addPin(String.valueOf(pinNumber));
    }

    @Override
    public void onDeleteClicked() {
        otpInputAdapter.deletePin();
    }

    @SuppressLint("NotifyDataSetChanged")
    @Override
    public void notifyChanges() {
        otpInputAdapter.notifyDataSetChanged();
    }

    @Override
    public void onComplete(@NonNull String string) {
        if (sPin.equalsIgnoreCase(string)) {
            if (getIntent().hasExtra(TAG_ISPINSALAH)) {
                try {
                    String encrypt = AES.AESEncrypt(string, sRefNum, AppConfig.getReissueKey());
                    ubahPinPresenter.sendReissuePin(new ReissuePinRequest(encrypt));
                } catch (Exception e) {
                    if (!GeneralHelper.isProd())
                        Log.d("error", "onComplete: " + e);
                }
            } else {
                UbahPinAtmRequest request = new UbahPinAtmRequest(sRefNum, string);
                ubahPinPresenter.sendChangePin(request);
            }
        } else {
            if (countPin == 2) {
                Intent intent = new Intent();
                intent.putExtra(
                        Constant.TAG_ERROR_MESSAGE,
                        GeneralHelper.getString(R.string.pin_salah_ulangi_pin)
                );
                setResult(RESULT_CANCELED, intent);
                finish();
            } else {
                countPin += 1;
                GeneralHelper.showSnackBar(this.findViewById(R.id.content), GeneralHelper.getString(R.string.pin_tidak_sesuai));
                resetInputPin();
            }
        }

    }

    @Override
    public void onSuccess(String data) {
        Intent resultIntent = new Intent();
        resultIntent.putExtra(Constant.TAG_MESSAGE, "sukses");
        setResult(Activity.RESULT_OK, resultIntent);
        finish();
    }

    @Override
    public void resetInputPin() {
        otpInputAdapter.deleteAllPin();
    }

    @Override
    protected void onDestroy() {
        ubahPinPresenter.stop();
        binding = null;
        super.onDestroy();
    }
}
