package id.co.bri.brimo.ui.activities.rdn;

import android.app.Activity;
import android.content.Intent;
import android.os.Bundle;
import android.view.View;

import androidx.annotation.Nullable;

import com.google.gson.Gson;

import javax.inject.Inject;

import id.co.bri.brimo.R;
import id.co.bri.brimo.contract.IPresenter.rdn.IRiskProfileRdnPresenter;
import id.co.bri.brimo.contract.IView.rdn.IRiskProfileRdnView;
import id.co.bri.brimo.databinding.ActivityStep10RdnRiskProfileBinding;
import id.co.bri.brimo.domain.config.Constant;
import id.co.bri.brimo.domain.helpers.GeneralHelper;
import id.co.bri.brimo.models.apimodel.request.rdn.RdnFormRequest;
import id.co.bri.brimo.models.apimodel.response.rdn.RdnFormSofResponse;
import id.co.bri.brimo.ui.activities.base.BaseActivity;

public class Step10RdnRiskProfile extends BaseActivity implements View.OnClickListener, IRiskProfileRdnView {

    private ActivityStep10RdnRiskProfileBinding binding;

    private static final String sRendah = "rendah";
    private static final String sModerat = "moderat";
    private static final String sTinggi = "tinggi";

    private static final String TAG_RESPONSE = "response";
    private static final String TAG_FORM_REQUEST = "request";

    private int profileResiko1;

    @Inject
    IRiskProfileRdnPresenter<IRiskProfileRdnView> presenter;

    private RdnFormRequest rdnFormRequest;

    public static void launchIntent(Activity caller, String checkpointResponse, RdnFormRequest request) {
        Intent i = new Intent(caller, Step10RdnRiskProfile.class);
        i.putExtra(TAG_RESPONSE, checkpointResponse);
        i.putExtra(TAG_FORM_REQUEST, new Gson().toJson(request));
        caller.startActivityForResult(i, Constant.REQ_PAYMENT);
    }

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        binding = ActivityStep10RdnRiskProfileBinding.inflate(getLayoutInflater());
        setContentView(binding.getRoot());

        GeneralHelper.setToolbar(this, binding.toolbar.toolbar, "Buka Rekening Efek - RDN");

        injectDependency();

        checkButton();

        if (getIntent().getStringExtra(TAG_FORM_REQUEST) != null) {
            rdnFormRequest = new Gson().fromJson(getIntent().getStringExtra(TAG_FORM_REQUEST), RdnFormRequest.class);
        }
        binding.btnSubmit.setOnClickListener(this);
        binding.lyRendah.setOnClickListener(this);
        binding.lyModerat.setOnClickListener(this);
        binding.lyTinggi.setOnClickListener(this);
    }

    private void checkButton() {
        if (profileResiko1 == 0) {
            binding.btnSubmit.setAlpha(0.3f);
            binding.btnSubmit.setEnabled(false);
        } else {
            binding.btnSubmit.setAlpha(1);
            binding.btnSubmit.setEnabled(true);
        }
    }

    private void injectDependency() {
        getActivityComponent().inject(this);
        if (presenter != null) {
            presenter.setView(this);
            presenter.setUrl(GeneralHelper.getString(R.string.url_rdn_submit_data_form));
            presenter.setUrlInformation(GeneralHelper.getString(R.string.url_rdn_form_submit));
            presenter.start();
        }
    }


    @Override
    public void onClick(View view) {
        switch (view.getId()) {
            case R.id.btnSubmit:
                presenter.onGetRiskProfile(rdnFormRequest);
                break;
            case R.id.ly_rendah:
                checkLayout(sRendah);
                break;
            case R.id.ly_moderat:
                checkLayout(sModerat);
                break;
            case R.id.ly_tinggi:
                checkLayout(sTinggi);
                break;
        }
    }

    private void checkLayout(String sParam) {
        resetRadio();
        if (sParam.equals(sRendah)) {
            profileResiko1 = 1;

            binding.lyRendah.setBackgroundResource(R.drawable.rounded_grey);
            binding.rbRendah.setChecked(true);
        }
        if (sParam.equals(sModerat)) {
            profileResiko1 = 2;
            binding.lyModerat.setBackgroundResource(R.drawable.rounded_grey);
            binding.rbModerat.setChecked(true);
        }
        if (sParam.equals(sTinggi)) {
            profileResiko1 = 3;
            binding.lyTinggi.setBackgroundResource(R.drawable.rounded_grey);
            binding.rbTinggi.setChecked(true);
        }
        rdnFormRequest.setProfileResiko1(profileResiko1);
        checkButton();
    }

    private void resetRadio() {
        binding.lyRendah.setBackgroundResource(R.drawable.bg_white_border_grey);
        binding.rbRendah.setChecked(false);

        binding.lyModerat.setBackgroundResource(R.drawable.bg_white_border_grey);
        binding.rbModerat.setChecked(false);

        binding.lyTinggi.setBackgroundResource(R.drawable.bg_white_border_grey);
        binding.rbTinggi.setChecked(false);
    }

    @Override
    public void onSuccessGetMoreInformation(String checkpointId, RdnFormSofResponse response) {
        Step11RdnMoreInformationActivity.launchIntent(this, checkpointId, response);
    }

    @Override
    public void onException93(String message) {
        getIntent().putExtra(Constant.TAG_ERROR_MESSAGE, message);

        this.setResult(RESULT_CANCELED, getIntent());
        this.finish();
    }

    @Override
    protected void onActivityResult(int requestCode, int resultCode, @Nullable Intent data) {
        super.onActivityResult(requestCode, resultCode, data);
        if (resultCode == RESULT_OK && data != null) {
            setResult(RESULT_OK, data);
            finish();
        } else {
            if (data != null) {
                setResult(RESULT_CANCELED, data);
                finish();
            }
        }
        if (requestCode == Constant.REQ_PAYMENT && data == null) {
            if (resultCode == Activity.RESULT_CANCELED){
                setResult(RESULT_CANCELED);
                this.finish();
            }
            if (resultCode == RESULT_OK) {
                setResult(RESULT_OK);
                this.finish();
            }
        }

        if (requestCode == Constant.REQ_REGIS){
            if (resultCode == Activity.RESULT_OK){
                setResult(RESULT_OK);
                this.finish();
            }
            else if (resultCode == Activity.RESULT_CANCELED && data != null) {
                setResult(RESULT_CANCELED,data);
                this.finish();
            }
            else {
                setResult(RESULT_CANCELED);
                this.finish();
            }
        }
    }

    @Override
    protected void onDestroy() {
        super.onDestroy();
        binding = null;
    }
}