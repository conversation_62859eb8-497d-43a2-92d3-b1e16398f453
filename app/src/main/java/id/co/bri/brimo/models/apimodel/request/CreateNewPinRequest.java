package id.co.bri.brimo.models.apimodel.request;

import com.google.gson.annotations.Expose;
import com.google.gson.annotations.SerializedName;

public class CreateNewPinRequest {
    @SerializedName("reference_number")
    @Expose
    private String referenceNumber;
    @SerializedName("new_pin")
    @Expose
    private String newPin;

    public CreateNewPinRequest(String referenceNumber, String newPin) {
        this.referenceNumber = referenceNumber;
        this.newPin = newPin;
    }

    public String getReferenceNumber() {
        return referenceNumber;
    }

    public void setReferenceNumber(String referenceNumber) {
        this.referenceNumber = referenceNumber;
    }

    public String getNewPin() {
        return newPin;
    }

    public void setNewPin(String newPin) {
        this.newPin = newPin;
    }
}
