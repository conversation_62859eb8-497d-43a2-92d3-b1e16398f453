package id.co.bri.brimo.ui.widget.calendar

import android.content.Context
import android.util.AttributeSet
import android.view.LayoutInflater
import android.view.View
import androidx.constraintlayout.widget.ConstraintLayout
import id.co.bri.brimo.R
import id.co.bri.brimo.databinding.LayoutAgfTransactionBinding

class AutoGrabFundLayout @JvmOverloads constructor(
    context: Context,
    attrs: AttributeSet? = null,
    defStyleAttr: Int = 0
) : ConstraintLayout(context, attrs, defStyleAttr) {
    private var  binding: LayoutAgfTransactionBinding
    init {

        binding = LayoutAgfTransactionBinding.inflate(LayoutInflater.from(context))
        View.inflate(context, R.layout.layout_agf_transaction, this)
        attrs?.let { setAttributes(context, it) }
    }


    /* Remaining constructors here */
    init {

    }

    private fun setAttributes(context: Context, attrs: AttributeSet){

    }
}