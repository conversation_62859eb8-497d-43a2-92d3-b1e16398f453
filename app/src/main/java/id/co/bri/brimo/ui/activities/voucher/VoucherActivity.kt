package id.co.bri.brimo.ui.activities.voucher

import android.annotation.SuppressLint
import android.app.Activity
import android.content.Intent
import android.os.Bundle
import android.view.View
import androidx.recyclerview.widget.GridLayoutManager
import com.ethanhua.skeleton.Skeleton
import com.ethanhua.skeleton.SkeletonScreen
import id.co.bri.brimo.R
import id.co.bri.brimo.adapters.voucher.VoucherAdapter
import id.co.bri.brimo.contract.IPresenter.voucher.IVoucherPresenter
import id.co.bri.brimo.contract.IView.voucher.IVoucherView
import id.co.bri.brimo.databinding.ActivityVoucherBinding
import id.co.bri.brimo.domain.config.Constant
import id.co.bri.brimo.domain.helpers.GeneralHelper
import id.co.bri.brimo.models.apimodel.response.voucher.VoucherGameResponse
import id.co.bri.brimo.ui.activities.base.BaseActivity
import javax.inject.Inject

class VoucherActivity : BaseActivity(), IVoucherView, VoucherAdapter.OnClickItem {

    private val TAG = "VoucherActivity"
    private lateinit var binding: ActivityVoucherBinding

    private lateinit var voucherAdapter: VoucherAdapter
    private lateinit var skeletonScreen: SkeletonScreen
    private var gameModels: MutableList<VoucherGameResponse.Game> = ArrayList()

    @Inject
    lateinit var presenter: IVoucherPresenter<IVoucherView>

    companion object {

        private var errorMessage: String? = null
        private lateinit var voucherType: String
        private lateinit var voucherTitle: String

        @JvmStatic
        fun launchIntent(caller: Activity, title: String) {
            val intent = Intent(caller, VoucherActivity::class.java)
            setTitle(title)
            caller.startActivityForResult(intent, Constant.REQ_PAYMENT)
        }

        fun setTitle(source: String) {
            if (source.equals(Constant.Voucher.GAME.name)) {
                voucherType = Constant.Voucher.GAME.name
                voucherTitle = Constant.Voucher.GAME.voucherTitle
            } else if (source.equals(Constant.Voucher.STREAMING.name)) {
                voucherType = Constant.Voucher.STREAMING.name
                voucherTitle = Constant.Voucher.STREAMING.voucherTitle
            }
        }
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        binding = ActivityVoucherBinding.inflate(layoutInflater)
        setContentView(binding.root)

        injectDependency()
        setupView()
    }

    private fun injectDependency() {
        activityComponent.inject(this)
        if (this::presenter.isInitialized) {
            presenter.view = this
            presenter.start()
            if (voucherType.equals(Constant.Voucher.GAME.name)) {
                presenter.setUrlVoucher(GeneralHelper.getString(R.string.url_form_voucher_game))
            } else if (voucherType.equals(Constant.Voucher.STREAMING.name)) {
                presenter.setUrlVoucher(GeneralHelper.getString(R.string.url_form_voucher_streaming))
            }
            presenter.getDataVoucher()
        }
    }

    private fun setupView() {
        GeneralHelper.setToolbar(this, binding.tbGame, voucherTitle)

        voucherAdapter = VoucherAdapter(this, gameModels, this)
        val layoutManager = GridLayoutManager(this, 2)
        binding.rvVoucher.setHasFixedSize(true)
        binding.rvVoucher.layoutManager = layoutManager
        binding.rvVoucher.adapter = voucherAdapter

        cekErrorMessage()

        skeletonScreen = Skeleton.bind(binding.rvVoucher)
            .adapter(voucherAdapter)
            .shimmer(true)
            .angle(3)
            .count(2)
            .frozen(false)
            .duration(1200)
            .load(R.layout.item_skeleton_voucher_streaming)
            .show()

        binding.ivHistory.setOnClickListener {
            HistoryVoucherActivity.launchIntent(this, false, voucherType)
        }
    }

    override fun onDestroy() {
        presenter.stop()
        super.onDestroy()
    }

    private fun cekErrorMessage() {
        if (errorMessage != null) {
            //menampilkan snacknar error
            showSnackbarErrorMessageRevamp(errorMessage, ALERT_ERROR, this, false)

            //clear error message
            errorMessage = null.toString()
        }
    }

    override fun onClickVoucher(gameList: VoucherGameResponse.Game) {
        DetailVoucherActivity.launchIntent(this, gameList, voucherType)
    }

    @SuppressLint("NotifyDataSetChanged")
    override fun onSuccessGetData(voucherGameResponse: VoucherGameResponse?) {
        skeletonScreen.hide()
        binding.llPowered.visibility = View.VISIBLE
        voucherGameResponse?.let { gameModels.addAll(it.voucherGameList) }
        voucherAdapter.notifyDataSetChanged()
    }

    @Suppress("DEPRECATION")
    @Deprecated("Deprecated in Java")
    override fun onActivityResult(requestCode: Int, resultCode: Int, data: Intent?) {
        super.onActivityResult(requestCode, resultCode, data)
        if (requestCode == Constant.REQ_PAYMENT) {
            if (resultCode == RESULT_OK) {
                this.setResult(RESULT_OK, data)
                finish()
            } else {
                this.setResult(RESULT_CANCELED, data)
            }
        } else {
            this.setResult(RESULT_CANCELED, data)
            if (data != null) {
                if (data.getStringExtra(Constant.TAG_ERROR_MESSAGE) != null) {
                    errorMessage = data.getStringExtra(Constant.TAG_ERROR_MESSAGE)
                    cekErrorMessage()
                } else {
                    this.setResult(RESULT_CANCELED, data)
                    finish()
                }
            }
        }
    }

    @Deprecated("Deprecated in Java")
    override fun onBackPressed() {
        val intent = Intent()
        intent.putExtra(TAG, title)
        setResult(Activity.RESULT_CANCELED, intent)
        super.onBackPressed()
    }
}