package id.co.bri.brimo.ui.activities.simpedes;


import android.app.Activity;
import android.content.Intent;
import android.os.Bundle;

import id.co.bri.brimo.R;
import id.co.bri.brimo.databinding.ActivityFormTopupBrifineBinding;
import id.co.bri.brimo.domain.config.Constant;
import id.co.bri.brimo.domain.helpers.GeneralHelper;
import id.co.bri.brimo.domain.helpers.textwatcher.AmountFormatWatcher;

public class FormTopupBrifineActivity extends Activity implements
        AmountFormatWatcher.onAmountChange {

    private ActivityFormTopupBrifineBinding binding;

    public static void launchIntent(Activity caller) {
        Intent intent = new Intent(caller, FormTopupBrifineActivity.class);
        caller.startActivityForResult(intent, Constant.REQ_PAYMENT);
    }

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        binding = ActivityFormTopupBrifineBinding.inflate(getLayoutInflater());
        setContentView(binding.getRoot());

        GeneralHelper.setToolbar(this, binding.toolbar.toolbar, GeneralHelper.getString(R.string.toolbar_simpedes));

        binding.etNominal.addTextChangedListener(new AmountFormatWatcher(binding.etNominal, this, false));
    }

    @Override
    public void onAmountChange(String amount) {
        // TODO document why this method is empty
    }

    @Override
    public void setAmountListener() {
        // TODO document why this method is empty
    }

    @Override
    protected void onDestroy() {
        super.onDestroy();
        binding = null;
    }
}