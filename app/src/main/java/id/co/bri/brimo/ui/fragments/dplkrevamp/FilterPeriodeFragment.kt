package id.co.bri.brimo.ui.fragments.dplkrevamp

import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import com.google.android.material.bottomsheet.BottomSheetDialogFragment
import id.co.bri.brimo.R
import id.co.bri.brimo.databinding.FragmentFilterPeriodeBinding
import id.co.bri.brimo.domain.helpers.calendar.CalendarHelper
import id.co.bri.brimo.models.YearModel

class FilterPeriodeFragment(
    private val navigation: String,
    private val callback: OnCallback,
    private val listYear: List<YearModel>?,
    private val mSelectedMonth: String,
    private val mSelectedYear: String
) : BottomSheetDialogFragment() {

    private lateinit var binding: FragmentFilterPeriodeBinding
    private var selectedMonth = ""
    private var selectedYear = ""

    private val maxYear = listYear?.maxByOrNull { it.year.toIntOrNull() ?: 0 }?.year
    private val years = listYear?.map { it.year }?.reversed()?.toTypedArray()
    private var months =
        listYear?.find { it.year == maxYear }?.listMonth?.map { it.monthString }?.toTypedArray()
            ?: arrayOf("")

    interface OnCallback {
        fun onMonthFilter(month: String, year: String, navigation: String)
        fun onPeriodFilter(selectedMonth: String, selectedYear: String, navigation: String)
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setStyle(STYLE_NORMAL, R.style.CustomBottomSheetDialogThemeInput)
    }

    override fun onCreateView(
        inflater: LayoutInflater, container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        // Inflate the layout for this fragment
        binding = FragmentFilterPeriodeBinding.inflate(inflater, container, false)
        return binding.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        setupView()
        initListener()
    }

    private fun setupView() {
        with(binding) {
            npYear.minValue = 0
            npYear.maxValue = years?.size?.minus(1) ?: 1
            npYear.displayedValues = years
            npYear.wrapSelectorWheel = false

            npMonth.minValue = 0
            npMonth.maxValue = months.size - 1
            npMonth.displayedValues = months
            npMonth.wrapSelectorWheel = false

            if (mSelectedMonth.isNotEmpty()) {
                val monthIndex = months.indexOf(mSelectedMonth)
                selectedMonth = if (monthIndex != -1) {
                    npMonth.value = monthIndex
                    mSelectedMonth
                } else {
                    months[0]
                }
            } else {
                selectedMonth = months[0]
            }

            if (mSelectedYear.isNotEmpty()) {
                val yearIndex = years?.indexOf(mSelectedYear) ?: -1
                selectedYear = if (yearIndex != -1) {
                    npYear.value = yearIndex
                    mSelectedYear
                } else {
                    years?.get(0) ?: ""
                }
            } else {
                selectedYear = years?.get(0) ?: ""
            }
        }
    }


    private fun initListener() {
        with(binding) {
            npMonth.setOnValueChangedListener { _, _, newVal ->
                selectedMonth = binding.npMonth.displayedValues[newVal] as String
            }
            npYear.setOnValueChangedListener { _, _, newVal ->
                selectedYear = years?.get(newVal) ?: ""

                val yearObject = listYear?.find { it.year == selectedYear }
                val monthsArray =
                    yearObject?.listMonth?.map { it.monthString }?.toTypedArray() ?: emptyArray()

                try {
                    npMonth.displayedValues = monthsArray
                    npMonth.maxValue = monthsArray.size - 1  // Update the max value
                    npMonth.value = 0  // Reset the selected value
                    npMonth.invalidate()  // Force refresh
                } catch (e: ArrayIndexOutOfBoundsException) {
                    npMonth.displayedValues = monthsArray
                    npMonth.maxValue = monthsArray.size - 1  // Update the max value
                    npMonth.value = 0  // Reset the selected value
                    npMonth.invalidate()  // Force refresh
                }
            }

            btnSubmit.setOnClickListener {
                when (navigation) {
                    CalendarHelper.TypeFilterPeriode.MONTH_MONTHLY -> {
                        callback.onMonthFilter(selectedMonth, selectedYear, navigation)
                        dismiss()
                    }

                    CalendarHelper.TypeFilterPeriode.YEAR_MONTHLY -> {
                        callback.onMonthFilter(selectedMonth, selectedYear, navigation)
                        dismiss()
                    }

                    else -> {
                        callback.onPeriodFilter(selectedMonth, selectedYear, navigation)
                        dismiss()
                    }
                }
            }
        }
    }
}
