package id.co.bri.brimo.ui.activities.cardless;

import android.content.Context;
import android.net.Uri;
import android.os.Bundle;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.fragment.app.Fragment;

import id.co.bri.brimo.R;
import id.co.bri.brimo.databinding.FragmentPetunjukBinding;

/**
 * A simple {@link Fragment} subclass.
 * Activities that contain this fragment must implement the
 * {@link PetunjukSetorFragment.OnFragmentInteractionListener} interface
 * to handle interaction events.
 * Use the {@link PetunjukSetorFragment#newInstance} factory method to
 * create an instance of this fragment.
 */
public class PetunjukSetorFragment extends Fragment {

    private FragmentPetunjukBinding binding;

    int position;
    String keterangan;
    static boolean mbutton;
    private static String mNama = null;
    private int image_pos;
    private int[] image_id = {R.drawable.ic_ilustrasi_1_setun, R.drawable.ic_ilustrasi_2_setun,R.drawable.ic_ilustrasi_3_setun
            , R.drawable.ic_ilustrasi_4_setun
            , R.drawable.ic_ilustrasi_5_setun
            , R.drawable.ic_ilustrasi_6_setun
            , R.drawable.ic_ilustrasi_7_setun
            , R.drawable.ic_ilustrasi_8_setun
    };

    public PetunjukSetorFragment(int position, String keterangan) {
        this.position = position;

        this.keterangan = keterangan;
    }


    public static PetunjukSetorFragment newInstance(int position,String keterangan ,boolean button) {
        PetunjukSetorFragment fragment = new PetunjukSetorFragment(position, keterangan);
        Bundle args = new Bundle();
        args.putInt("someInt", position);
        fragment.image_pos=position;
        mNama = keterangan;
        mbutton = button;
        fragment.setArguments(args);
        return fragment;
    }

    @Override
    public void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);

    }

    @Override
    public void onViewCreated(@NonNull View view, @Nullable Bundle savedInstanceState) {
        super.onViewCreated(view, savedInstanceState);
        ImageView imageView = view.findViewById(R.id.imageView);
        TextView textView = view.findViewById(R.id.txt_keterangan);

        textView.setText(keterangan);
        imageView.setImageResource(image_id[image_pos]);

    }

    @Override
    public View onCreateView(LayoutInflater inflater, ViewGroup container,
                             Bundle savedInstanceState) {
        binding = FragmentPetunjukBinding.inflate(inflater, container, false);
        return binding.getRoot();
    }

    // TODO: Rename method, update argument and hook method into UI event
    public void onButtonPressed(Uri uri) {

    }

    @Override
    public void onAttach(Context context) {
        super.onAttach(context);

    }

    @Override
    public void onDetach() {
        super.onDetach();

    }


    public interface OnFragmentInteractionListener {
        // TODO: Update argument type and name
        void onFragmentInteraction(Uri uri);
    }
}
