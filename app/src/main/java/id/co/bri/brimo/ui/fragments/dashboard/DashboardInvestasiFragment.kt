package id.co.bri.brimo.ui.fragments.dashboard

import android.content.Context
import android.os.Bundle
import android.os.Handler
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.recyclerview.widget.GridLayoutManager
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.swiperefreshlayout.widget.SwipeRefreshLayout
import com.ethanhua.skeleton.Skeleton
import com.ethanhua.skeleton.SkeletonScreen
import id.co.bri.brimo.R
import id.co.bri.brimo.adapters.dashboardInvestasi.FaqInvestasiAdapter
import id.co.bri.brimo.adapters.dashboardInvestasi.StoryInvestasiAdapter
import id.co.bri.brimo.contract.IPresenter.dashboard.IDashboardInvestPresenter
import id.co.bri.brimo.contract.IView.dashboard.IDashboardInvestView
import id.co.bri.brimo.databinding.FragmentDashboardInvestasiBinding
import id.co.bri.brimo.domain.helpers.GeneralHelper
import id.co.bri.brimo.models.apimodel.response.dashboardInvestasi.BannerResponse
import id.co.bri.brimo.models.apimodel.response.dashboardInvestasi.ContentResponse
import id.co.bri.brimo.models.apimodel.response.dashboardInvestasi.DashboardInvestasiResponse
import id.co.bri.brimo.models.apimodel.response.dashboardInvestasi.InvenstasiFaqResponse
import id.co.bri.brimo.models.apimodel.response.dashboardInvestasi.MenuInvestasiResponse
import id.co.bri.brimo.models.apimodel.response.dashboardInvestasi.RecomendationResponse
import id.co.bri.brimo.models.daomodel.DashboardMenu.MenuDashboard
import id.co.bri.brimo.ui.fragments.BaseFragment
import javax.inject.Inject


class DashboardInvestasiFragment(
    private val mContext: Context,
    private var mPosition: Int, private val onListener: DialogDefaultListener,
    private val response: DashboardInvestasiResponse
) : BaseFragment(), View.OnClickListener, SwipeRefreshLayout.OnRefreshListener,
    IDashboardInvestView, StoryInvestasiAdapter.OnClickListener {
    var _binding: FragmentDashboardInvestasiBinding? = null
    val binding get() = _binding!!
    private var handlerPromo: Handler? = null
    private var runnablePromo: Runnable? = null
    private val speedScroll = 3500
    var skeletonScreenPromo: SkeletonScreen? = null
    var skeletonScreenContent: SkeletonScreen? = null
    var skeletonScreenFaq: SkeletonScreen? = null
    val menuDashboardInvestasi: MutableList<MenuDashboard> = mutableListOf()


    @Inject
    lateinit var presenter: IDashboardInvestPresenter<IDashboardInvestView>

    interface DialogDefaultListener {
        fun onType()
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
    }

    override fun onCreateView(
        inflater: LayoutInflater, container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View? {
        // Inflate the layout for this fragment
        _binding = FragmentDashboardInvestasiBinding.inflate(inflater, container, false)

        return binding.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)

        binding.tvTotalSaldo.text = response.investment!![mPosition].totalString
        binding.tvTerakhirPerbarui.text = response.lastUpdate


        injectDepedency()
        listener()

        if (response.rdn!!.assetStatus.equals("01")) {
            binding.tvTitleBukaRdn.visibility = View.VISIBLE
            binding.tvSubtitleBukaRdn.visibility = View.VISIBLE
            binding.tvTitleSaldoRdn.visibility = View.GONE
            binding.tvNominalSaldoRdn.visibility = View.GONE
            binding.ivArrow.visibility = View.GONE

            binding.tvTitleBukaRdn.text = response.rdn.title
            binding.tvSubtitleBukaRdn.text = response.rdn.subtitle
            binding.btnBuy.text = response.rdn.buttonString

        } else if (response.rdn.assetStatus.equals("12")) {
            binding.tvTitleSaldoRdn.visibility = View.GONE
            binding.ivArrow.visibility = View.GONE
            binding.btnBuy.visibility = View.GONE
            binding.tvTitleBukaRdn.visibility = View.GONE
            binding.tvSubtitleBukaRdn.visibility = View.GONE
            binding.tvNominalSaldoRdn.visibility = View.VISIBLE
            binding.tvNominalSaldoRdn.text = response.rdn.totalString
        } else {
            binding.tvTitleBukaRdn.visibility = View.GONE
            binding.tvSubtitleBukaRdn.visibility = View.GONE
            binding.ivArrow.visibility = View.VISIBLE
            binding.tvNominalSaldoRdn.text = response.rdn.totalString
            binding.btnBuy.text = response.rdn.buttonString

        }


//        val adapter = AsetInvestasiAdapter(mContext, response.investment[mPosition].assets!!)
//        binding.rvListAset.adapter = adapter
//        binding.rvListAset.layoutManager = LinearLayoutManager(requireContext(), LinearLayoutManager.VERTICAL, false)

    }

    private fun injectDepedency() {
        activityComponent.inject(this)
        presenter.view = this
        presenter.start()
        presenter.setUrlInvestasiFaq(GeneralHelper.getString(R.string.url_dashboard_investasi_faq))
        presenter.setUrlInvestasiPromo(GeneralHelper.getString(R.string.url_dashboard_investasi_promo))
        presenter.setUrlInvestasiContent(GeneralHelper.getString(R.string.url_dashboard_investasi_story))
        presenter.setUrlInvestasiRecomendation(GeneralHelper.getString(R.string.url_dashboard_investasi_recommend))
        presenter.setUrlInvestasiMenu(GeneralHelper.getString(R.string.url_dashboard_investasi_menu))
        presenter.getDataInvestasiFaq()
        presenter.getDataInvestasiPromo()
        presenter.getDataInvestasiContent()
        presenter.getDataInvestasiRecommedantion()
        presenter.getDataInvestasiMenu()
    }

    private fun listener() {
        binding.btnKomposisi.setOnClickListener(this)
        binding.imgBawah.setOnClickListener(this)
        binding.swipeRefresh.setOnRefreshListener(this)
    }

    override fun onClick(p0: View?) {
        when (p0!!.id) {
            R.id.btn_komposisi -> {
                val bottomFragmentKomposisi = BottomFragmentKomposisi(mContext, response, mPosition)
                bottomFragmentKomposisi.show(requireFragmentManager(), "")
            }

            R.id.img_bawah -> {

                binding.scrollView.smoothScrollTo(0, 0)
            }
        }
    }


    override fun onSuccessInvestasiFaq(response: InvenstasiFaqResponse) {
        binding.swipeRefresh.setRefreshing(false)
        val adapterFaq = FaqInvestasiAdapter(mContext, response.faq!!)
        skeletonScreenFaq = Skeleton.bind(binding.rvFaq)
            .adapter(adapterFaq)
            .shimmer(true)
            .angle(20)
            .duration(1200)
            .count(4)
            .load(R.layout.skeleton_faq_investasi)
            .show()
        binding.rvFaq.layoutManager =
            LinearLayoutManager(mContext, LinearLayoutManager.VERTICAL, false)
    }

    override fun onSuccessInvestasiPromo(response: BannerResponse) {
//        binding.swipeRefresh.setRefreshing(false)
//        promoInvestasiAdapter = PromoInvestasiAdapter(mContext,response.banner!!)
//        binding.rvPromo.adapter = promoInvestasiAdapter
//        skeletonScreenPromo = Skeleton.bind(binding.rvPromo)
//                .adapter(promoInvestasiAdapter)
//                .shimmer(true)
//                .angle(20)
//                .duration(1200)
//                .load(R.layout.skeleton_promo_investasi)
//                .count(5)
//                .show()
//
//        binding.rvPromo.layoutManager = LinearLayoutManager(mContext, LinearLayoutManager.HORIZONTAL, false)
//        autoScrol()

    }

    override fun onSuccessInvestasiContent(response: ContentResponse) {
        binding.swipeRefresh.setRefreshing(false)
        val storyInvestasiAdapter = StoryInvestasiAdapter(mContext, response.content!!, this)
        skeletonScreenContent = Skeleton.bind(binding.rvListContent)
            .adapter(storyInvestasiAdapter)
            .shimmer(true)
            .angle(20)
            .duration(1200)
            .count(5)
            .load(R.layout.skeleton_content_investasi)
            .show()
        binding.rvListContent.layoutManager =
            LinearLayoutManager(mContext, LinearLayoutManager.HORIZONTAL, false)
    }

    override fun onSuccessInvestasiRecommendation(response: RecomendationResponse) {

    }

    override fun onSuccessInvestasiMenu(response: MenuInvestasiResponse) {
        binding.swipeRefresh.setRefreshing(false)
        menuDashboardInvestasi.clear()
        binding.rvMenu.layoutManager =
            GridLayoutManager(mContext, 4, GridLayoutManager.VERTICAL, false)
    }

    override fun showSekelton(isShow: Boolean) {
        if (isShow) {
            skeletonScreenPromo = Skeleton.bind(binding.rvPromo)
                .shimmer(true)
                .angle(20)
                .duration(1200)
                .load(R.layout.skeleton_promo_investasi)
                .count(5)
                .show()

            skeletonScreenContent = Skeleton.bind(binding.rvListContent)
                .shimmer(true)
                .angle(20)
                .duration(1200)
                .count(5)
                .load(R.layout.skeleton_content_investasi)
                .show()

            skeletonScreenFaq = Skeleton.bind(binding.rvFaq)
                .shimmer(true)
                .angle(20)
                .duration(1200)
                .count(4)
                .load(R.layout.skeleton_faq_investasi)
                .show()
        } else {
            skeletonScreenPromo!!.hide()
            skeletonScreenContent!!.hide()
            skeletonScreenFaq!!.hide()
        }

    }

    override fun onRefresh() {
        presenter.getDataInvestasiFaq()
        presenter.getDataInvestasiPromo()
        presenter.getDataInvestasiContent()
        presenter.getDataInvestasiRecommedantion()
        presenter.getDataInvestasiMenu()
    }

    override fun onShowProgress() {
        //do nothing
    }

    override fun onDismissProgress() {
        //do nothing
    }
}