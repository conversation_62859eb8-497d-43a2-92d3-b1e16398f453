package id.co.bri.brimo.ui.activities.dplk;

import android.app.Activity;
import android.content.Intent;
import android.graphics.Color;
import android.os.Build;
import android.os.Bundle;
import android.view.DragEvent;
import android.view.View;
import android.widget.SeekBar;
import android.widget.Toast;
import androidx.annotation.RequiresApi;
import com.github.mikephil.charting.animation.Easing;
import com.github.mikephil.charting.components.Legend;
import com.github.mikephil.charting.data.Entry;
import com.github.mikephil.charting.data.PieData;
import com.github.mikephil.charting.data.PieDataSet;
import com.github.mikephil.charting.data.PieEntry;
import com.github.mikephil.charting.highlight.Highlight;
import com.github.mikephil.charting.listener.OnChartValueSelectedListener;
import com.github.mikephil.charting.utils.MPPointF;
import com.google.gson.Gson;
import java.util.ArrayList;
import id.co.bri.brimo.R;
import id.co.bri.brimo.databinding.ActivityBrifineKombinasiBinding;
import id.co.bri.brimo.domain.config.Constant;
import id.co.bri.brimo.domain.helpers.GeneralHelper;
import id.co.bri.brimo.domain.helpers.MyFormatter;
import id.co.bri.brimo.models.apimodel.request.dplk.BrifineKombinasiRequest;
import id.co.bri.brimo.ui.customviews.BasePieChart;

public class BrifineKombinasiActivity extends BasePieChart implements OnChartValueSelectedListener, View.OnClickListener {

    private ActivityBrifineKombinasiBinding binding;

    private static String titleBrifine="";
    private static String kodeProduct="";
    private static boolean mFristTime;

    public static boolean combination;
    public static BrifineKombinasiRequest kombinasiRequest;

    int x = 0;
    int y = 0;
    int z = 0;
    int value = 100;
    int sum = 0;

    public static void launchIntent(Activity caller, String title, String kode , boolean firstTime){
        Intent i = new Intent(caller, BrifineKombinasiActivity.class);
        titleBrifine = title;
        kodeProduct = kode;
        mFristTime = firstTime;
        caller.startActivityForResult(i, Constant.REQ_PAYMENT);
    }

    public static void launchIntent(Activity caller, boolean kombinasi, BrifineKombinasiRequest request,boolean firstTime){
        Intent i = new Intent(caller, BrifineKombinasiActivity.class);
        combination = kombinasi;
        kombinasiRequest = request;
        mFristTime = firstTime;
        caller.startActivityForResult(i, Constant.REQ_PAYMENT);
    }

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        binding = ActivityBrifineKombinasiBinding.inflate(getLayoutInflater());
        setContentView(binding.getRoot());

        GeneralHelper.setToolbar(this, binding.toolbar.toolbar, "BRIFINE Kombinasi");

        setView();

        binding.btnSubmit.setOnClickListener(this);
        binding.tvGantiBrifine.setOnClickListener(this);

//        edPasarUang.addTextChangedListener(new TextWatcher() {
//            @Override
//            public void beforeTextChanged(CharSequence s, int start, int count, int after) {
//
//            }
//
//            @Override
//            public void onTextChanged(CharSequence s, int start, int before, int count) {
//                Log.i("TAG", "onTextChanged: "+s.toString());
//                if (!s.toString().isEmpty()){
//                    int a = Integer.valueOf(s.toString());
//                    int b = Integer.valueOf(edPendapatan.getText().toString());
//                    int c = Integer.valueOf(edSaham.getText().toString());
//                    int sum = a+b+c;
//
//                    if (sum <=value){
//                        Log.i("TAG", "onTextChanged: BOLEH");
//                        seekBar1.setProgress(a);
//                    }else if (sum >value){
//                        Log.i("TAG", "onTextChanged: TIDAK BOLEH");
//                        GeneralHelper.showSnackBar(findViewById(R.id.content), "Kuota maksimal tersedia "+(value-(b+c)));
//                        seekBar1.setProgress(value-(b+c));
//                    }
//                }
//            }
//
//            @Override
//            public void afterTextChanged(Editable s) {
//                if (!s.toString().isEmpty()){
//                    int a = Integer.valueOf(s.toString());
//                    int b = Integer.valueOf(edPendapatan.getText().toString());
//                    int c = Integer.valueOf(edSaham.getText().toString());
//                    int sum = a+b+c;
//
//                    validationButton();
//                    setData(Integer.valueOf(edPasarUang.getText().toString()), Integer.valueOf(edPendapatan.getText().toString()), Integer.valueOf(edSaham.getText().toString()), sum);
//                }
//            }
//        });
//
//        edPendapatan.addTextChangedListener(new TextWatcher() {
//            @Override
//            public void beforeTextChanged(CharSequence s, int start, int count, int after) {
//
//            }
//
//            @Override
//            public void onTextChanged(CharSequence s, int start, int before, int count) {
//                Log.i("TAG", "onTextChanged: "+s.toString());
//                if (!s.toString().isEmpty()){
//                    int a = Integer.valueOf(s.toString());
//                    int b = Integer.valueOf(edPasarUang.getText().toString());
//                    int c = Integer.valueOf(edSaham.getText().toString());
//                    int sum = a+b+c;
//
//                    if (sum <=value){
//                        Log.i("TAG", "onTextChanged: BOLEH");
//                        seekBar2.setProgress(a);
//                    }else if (sum >value){
//                        Log.i("TAG", "onTextChanged: TIDAK BOLEH");
//                        GeneralHelper.showSnackBar(findViewById(R.id.content), "Kuota maksimal tersedia "+(value-(b+c)));
//                        seekBar2.setProgress(value-(b+c));
//                    }
//                }
//            }
//
//            @Override
//            public void afterTextChanged(Editable s) {
//                if (!s.toString().isEmpty()){
//                    int a = Integer.valueOf(s.toString());
//                    int b = Integer.valueOf(edPasarUang.getText().toString());
//                    int c = Integer.valueOf(edSaham.getText().toString());
//                    int sum = a+b+c;
//
//                    validationButton();
//                    setData(Integer.valueOf(edPasarUang.getText().toString()), Integer.valueOf(edPendapatan.getText().toString()), Integer.valueOf(edSaham.getText().toString()), sum);
//                }
//            }
//        });
//
//        edSaham.addTextChangedListener(new TextWatcher() {
//            @Override
//            public void beforeTextChanged(CharSequence s, int start, int count, int after) {
//
//            }
//
//            @Override
//            public void onTextChanged(CharSequence s, int start, int before, int count) {
//                Log.i("TAG", "onTextChanged: "+s.toString());
//                if (!s.toString().isEmpty()){
//                    int a = Integer.valueOf(s.toString());
//                    int b = Integer.valueOf(edPendapatan.getText().toString());
//                    int c = Integer.valueOf(edPasarUang.getText().toString());
//                    int sum = a+b+c;
//
//                    if (sum <=value){
//                        Log.i("TAG", "onTextChanged: BOLEH");
//                        seekBar3.setProgress(a);
//                    }else if (sum >value){
//                        Log.i("TAG", "onTextChanged: TIDAK BOLEH");
//                        GeneralHelper.showSnackBar(findViewById(R.id.content), "Kuota maksimal tersedia "+(value-(b+c)));
//                        seekBar3.setProgress(value-(b+c));
//                    }
//                }
//            }
//
//            @Override
//            public void afterTextChanged(Editable s) {
//                if (!s.toString().isEmpty()){
//                    int a = Integer.valueOf(s.toString());
//                    int b = Integer.valueOf(edPendapatan.getText().toString());
//                    int c = Integer.valueOf(edPasarUang.getText().toString());
//                    int sum = a+b+c;
//
//                    validationButton();
//                    setData(Integer.valueOf(edPasarUang.getText().toString()), Integer.valueOf(edPendapatan.getText().toString()), Integer.valueOf(edSaham.getText().toString()), sum);
//                }
//            }
//        });
    }

    private void setView(){

        if (mFristTime){
            binding.llGantiBrifine.setVisibility(View.VISIBLE);
        }else {
            binding.llGantiBrifine.setVisibility(View.GONE);
        }

        if (combination){
            binding.seekBar.setProgress(kombinasiRequest.getPasarUang());
            binding.seekBar1.setProgress(kombinasiRequest.getPendapatanTetap());
            binding.seekBar2.setProgress(kombinasiRequest.getSaham());
        }else {
            binding.seekBar.setProgress(40);
            binding.seekBar1.setProgress(30);
            binding.seekBar2.setProgress(30);
        }

        x = binding.seekBar.getProgress();
        y = binding.seekBar1.getProgress();
        z = binding.seekBar2.getProgress();

        binding.seekBar.setOnSeekBarChangeListener(new SeekBar.OnSeekBarChangeListener() {
            @RequiresApi(api = Build.VERSION_CODES.O)
            @Override
            public void onProgressChanged(SeekBar seekBar, int progress, boolean fromUser) {
                binding.tvPasar.setText(binding.seekBar.getProgress()+"%");
//                edPasarUang.setText(seekBar1.getProgress()+"");
                int limit = -1;

                if (binding.seekBar1.getProgress()!=0 && binding.seekBar2.getProgress()==0 || binding.seekBar1.getProgress()==0 && binding.seekBar2.getProgress()!=0){
                    limit = 0;
                    binding.seekBar.setMin(1);
//                }else if (seekBar2.getProgress()==0 && seekBar3.getProgress()==0){
//                    limit = 0;
                }else if (binding.seekBar1.getProgress()>0 && binding.seekBar2.getProgress()>0){
                    limit= 0;
                    binding.seekBar.setMin(0);
                }
                int limitUp = -1;
                if (value!=0){
                    limitUp = value - (binding.seekBar2.getProgress()+binding.seekBar1.getProgress());
                }
                if(seekBar.getProgress() >= limitUp){
                    seekBar.setProgress(limitUp);
                }else if(seekBar.getProgress() <= limit){
                    seekBar.setProgress(limit);
                }
//                int limit = ((20 * 200) / 100);
//                int maxValue = seekBar.getMax();
//                int limitUp = maxValue - limit;
//                if(seekBar.getProgress() >= limitUp){
//                    seekBar.setProgress(limitUp);
//                }else if(seekBar.getProgress() <= limit){
//                    seekBar.setProgress(limit);
//                }
            }

            @Override
            public void onStartTrackingTouch(SeekBar seekBar) {
                x = seekBar.getProgress();
            }

            @Override
            public void onStopTrackingTouch(SeekBar seekBar) {
//                Log.i("TAG", "onStopTrackingTouch: x"+x);
//                int diff = 0;
//                int type;
//                if (seekBar.getProgress()>x){
//                    type = 1;
//                    diff = seekBar.getProgress()-x;
//                    Log.i("TAG", "onStopTrackingTouch A: "+diff);
//                    seekBar2.setProgress(y - proporsiValue(type, diff, y, z));
//                    seekBar3.setProgress(z - proporsiValue(type, diff, z, y));
//                }else {
//                    type = 0;
//                    diff = x-seekBar.getProgress();
//                    Log.i("TAG", "onStopTrackingTouch B: "+diff);
//                    seekBar2.setProgress(y + proporsiValue(type, diff, y, z));
//                    seekBar3.setProgress(z + proporsiValue(type, diff, z, y));
//                }
                validationButton();
                setData(binding.seekBar.getProgress(), binding.seekBar1.getProgress(), binding.seekBar2.getProgress(), sum);
//                tvPendapatan.setText(seekBar2.getProgress()+"%");
//                tvSaham.setText(seekBar3.getProgress()+"%");
//                x = seekBar.getProgress();
//                y = seekBar2.getProgress();
//                z = seekBar3.getProgress();
            }
        });

        binding.seekBar1.setOnSeekBarChangeListener(new SeekBar.OnSeekBarChangeListener() {
            @RequiresApi(api = Build.VERSION_CODES.O)
            @Override
            public void onProgressChanged(SeekBar seekBar, int progress, boolean fromUser) {
                binding.tvPendapatan.setText(binding.seekBar1.getProgress()+"%");
//                edPendapatan.setText(seekBar2.getProgress()+"");
                int limit = -1;
                if (binding.seekBar.getProgress()!=0 && binding.seekBar2.getProgress()==0 || binding.seekBar.getProgress()==0 && binding.seekBar2.getProgress()!=0){
                    limit = 0;
                    binding.seekBar1.setMin(1);
//                else if (seekBar1.getProgress()==0 && seekBar3.getProgress()==0){
//                    limit = 0;
//                }
                }else if (binding.seekBar.getProgress()>0 && binding.seekBar2.getProgress()>0){
                    limit= 0;
                    binding.seekBar1.setMin(0);
                }
                int limitUp = -1;
                if (value!=0){
                    limitUp = value - (binding.seekBar2.getProgress()+binding.seekBar.getProgress());
                }
                if(seekBar.getProgress() >= limitUp){
                    seekBar.setProgress(limitUp);
                }else if(seekBar.getProgress() <= limit){
                    seekBar.setProgress(limit);
                }
            }

            @Override
            public void onStartTrackingTouch(SeekBar seekBar) {
                y = seekBar.getProgress();
            }

            @Override
            public void onStopTrackingTouch(SeekBar seekBar) {
//                Log.i("TAG", "onStopTrackingTouch: x"+x);
//                int diff = 0;
//                int type;
//                if (seekBar.getProgress()>y){
//                    type = 1;
//                    diff = seekBar.getProgress()-y;
//                    Log.i("TAG", "onStopTrackingTouch A: "+diff);
//                    seekBar1.setProgress(x - proporsiValue(type, diff, x, z));
//                    seekBar3.setProgress(z - proporsiValue(type, diff, z, x));
//                }else {
//                    type = 0;
//                    diff = y-seekBar.getProgress();
//                    Log.i("TAG", "onStopTrackingTouch B: "+diff);
//                    seekBar1.setProgress(x + proporsiValue(type, diff, x, z));
//                    seekBar3.setProgress(z + proporsiValue(type, diff, z, x));
//                }
                validationButton();
                setData(binding.seekBar.getProgress(), binding.seekBar1.getProgress(), binding.seekBar2.getProgress(), sum);
//                tvPasar.setText(seekBar1.getProgress()+"%");
//                tvSaham.setText(seekBar3.getProgress()+"%");
//                y = seekBar.getProgress();
//                x = seekBar1.getProgress();
//                z = seekBar3.getProgress();
            }
        });

        binding.seekBar2.setOnSeekBarChangeListener(new SeekBar.OnSeekBarChangeListener() {
            @RequiresApi(api = Build.VERSION_CODES.O)
            @Override
            public void onProgressChanged(SeekBar seekBar, int progress, boolean fromUser) {
                binding.tvSaham.setText(binding.seekBar2.getProgress()+"%");
//                edSaham.setText(seekBar3.getProgress()+"");
                int limit = -1;
                if (binding.seekBar.getProgress()!=0 && binding.seekBar1.getProgress()==0 || binding.seekBar.getProgress()==0 && binding.seekBar1.getProgress()!=0){
                    limit = 0;
                    binding.seekBar2.setMin(1);
//                else if (seekBar1.getProgress()==0 && seekBar3.getProgress()==0){
//                    limit = 0;
//                }
                }else if (binding.seekBar.getProgress()>0 && binding.seekBar1.getProgress()>0){
                    limit= 0;
                    binding.seekBar2.setMin(0);
                }
                int limitUp = -1;
                if (value!=0){
                    limitUp = value - (binding.seekBar1.getProgress()+binding.seekBar.getProgress());
                }
                if(seekBar.getProgress() >= limitUp){
                    seekBar.setProgress(limitUp);
                }else if(seekBar.getProgress() <= limit){
                    seekBar.setProgress(limit);
                }
            }


            @Override
            public void onStartTrackingTouch(SeekBar seekBar) {
                z = seekBar.getProgress();
            }

            @Override
            public void onStopTrackingTouch(SeekBar seekBar) {
//                Log.i("TAG", "onStopTrackingTouch: x"+x);
//                int diff = 0;
//                int type;
//                if (seekBar.getProgress()>z){
//                    type = 1;
//                    diff = seekBar.getProgress()-z;
//                    Log.i("TAG", "onStopTrackingTouch A: "+diff);
//                    seekBar1.setProgress(x - proporsiValue(type, diff, x, y));
//                    seekBar2.setProgress(y - proporsiValue(type, diff, y, x));
//                }else {
//                    type = 0;
//                    diff = z-seekBar.getProgress();
//                    Log.i("TAG", "onStopTrackingTouch B: "+diff);
//                    seekBar1.setProgress(x + proporsiValue(type, diff, x, y));
//                    seekBar2.setProgress(y + proporsiValue(type, diff, y, x));
//                }
                validationButton();
                setData(binding.seekBar.getProgress(), binding.seekBar1.getProgress(), binding.seekBar2.getProgress(), sum);
//                tvPasar.setText(seekBar1.getProgress()+"%");
//                tvPendapatan.setText(seekBar2.getProgress()+"%");
//                z = seekBar.getProgress();
//                x = seekBar1.getProgress();
//                y = seekBar2.getProgress();
            }
        });

        binding.tvPasar.setText(binding.seekBar.getProgress()+"%");
//        edPasarUang.setText(seekBar1.getProgress()+"");
        binding.tvPendapatan.setText(binding.seekBar1.getProgress()+"%");
//        edPendapatan.setText(seekBar2.getProgress()+"");
        binding.tvSaham.setText(binding.seekBar2.getProgress()+"%");
//        edSaham.setText(seekBar3.getProgress()+"");

        binding.chart1.setUsePercentValues(false);
        binding.chart1.getDescription().setEnabled(false);

        binding.chart1.setDragDecelerationFrictionCoef(0.95f);

        binding.chart1.setDrawHoleEnabled(false);

        binding.chart1.setTransparentCircleColor(Color.WHITE);
        binding.chart1.setTransparentCircleAlpha(0);
        binding.chart1.setTransparentCircleRadius(0f);

        binding.chart1.setDrawCenterText(false);

        binding.chart1.setRotationAngle(0);
        // enable rotation of the chart by touch
        binding.chart1.setRotationEnabled(false);
        binding.chart1.setHighlightPerTapEnabled(false);

        // add a selection listener
        binding.chart1.setOnChartValueSelectedListener(this);
        binding.chart1.setTouchEnabled(true);

        binding.chart1.animateY(1400, Easing.EaseInOutQuad);
        // chart.spin(2000, 0, 360);

        binding.chart1.setOnDragListener(new View.OnDragListener() {
            @Override
            public boolean onDrag(View v, DragEvent event) {
                Toast.makeText(BrifineKombinasiActivity.this, ""+event.getX(), Toast.LENGTH_SHORT).show();
                return false;
            }
        });

        Legend l = binding.chart1.getLegend();
        l.setVerticalAlignment(Legend.LegendVerticalAlignment.CENTER);
        l.setHorizontalAlignment(Legend.LegendHorizontalAlignment.RIGHT);
        l.setOrientation(Legend.LegendOrientation.VERTICAL);
        l.setDrawInside(false);
        l.setTextSize(14f);
        l.setYEntrySpace(12);
        l.setYOffset(12);
        l.setForm(Legend.LegendForm.CIRCLE);


        // entry label styling
        binding.chart1.setDrawEntryLabels(false);

        validationButton();
        setData(binding.seekBar.getProgress(), binding.seekBar1.getProgress(), binding.seekBar2.getProgress(), sum);
    }

    @Override
    public void onValueSelected(Entry e, Highlight h) {

    }

    @Override
    public void onNothingSelected() {

    }

    private void setData(int a, int b, int c, int d) {
        ArrayList<PieEntry> entries = new ArrayList<>();

        if (a != 0){
            entries.add(new PieEntry(a, "Pasar Uang"));
        }
        if (b != 0){
            entries.add(new PieEntry(b, "Pendapatan Tetap"));
        }
        if (c != 0){
            entries.add(new PieEntry(c, "Saham"));
        }
        if (d!=0){
            if ((value-d) != 0 && (value-d) > 0){
                entries.add(new PieEntry((value-d), "Kosong"));
            }
        }

        PieDataSet dataSet = new PieDataSet(entries, "");

        dataSet.setDrawIcons(false);
        dataSet.setSliceSpace(1.5f);
        dataSet.setIconsOffset(new MPPointF(0, 40));
        dataSet.setSelectionShift(5f);

        // add a lot of colors

        ArrayList<Integer> colors = new ArrayList<>();
        if (a != 0){
            colors.add(GeneralHelper.getColor(R.color.secondaryColor));
        }
        if (b != 0){
            colors.add(GeneralHelper.getColor(R.color.successColor));
        }
        if (c != 0){
            colors.add(GeneralHelper.getColor(R.color.highlightColor));
        }
        if (d!=0){
            if ((value-d) != 0){
                colors.add(GeneralHelper.getColor(R.color.accent2Color));
            }
        }

        dataSet.setColors(colors);

        PieData data = new PieData(dataSet);
        data.setValueFormatter(new MyFormatter(0));
        data.setValueTextSize(14f);
        data.setValueTextColor(Color.WHITE);
        binding.chart1.setData(data);

        // undo all highlights
        binding.chart1.highlightValues(null);

        binding.chart1.invalidate();
    }

    private int proporsiValue(int type, double om, double oji, double yudit){
        double y = 0;
        double badru = oji+yudit;

        if (type == 1){
            y = oji/badru*om;
        }else {
            y = (1-oji/badru)*om;
        }

        return (int) Math.round(y);
    }

    @Override
    public void onBackPressed() {
        if (combination){
            Intent resultIntentOk = new Intent();
            resultIntentOk.putExtra("brifineKombinasi", new Gson().toJson(kombinasiRequest));
            setResult(Constant.REQ_KATEGORI, resultIntentOk);
            finish();
        }
        super.onBackPressed();
    }

    @Override
    public void onClick(View v) {
        switch (v.getId()){
            case R.id.btnSubmit:
                BrifineKombinasiRequest request = new BrifineKombinasiRequest(titleBrifine, kodeProduct, binding.seekBar.getProgress(), binding.seekBar1.getProgress(), binding.seekBar2.getProgress());
                Intent resultIntentOk = new Intent();
                resultIntentOk.putExtra("brifineKombinasi", new Gson().toJson(request));
                setResult(Constant.REQ_KATEGORI, resultIntentOk);
                finish();
                break;
            case R.id.tv_ganti_brifine:
                finish();
                break;
        }

    }

    private void validationButton(){
        sum = binding.seekBar.getProgress()+binding.seekBar1.getProgress()+binding.seekBar2.getProgress();
//        sum = Integer.valueOf(edPasarUang.getText().toString())+Integer.valueOf(edPendapatan.getText().toString())+Integer.valueOf(edSaham.getText().toString());
        if (sum == 100){
            binding.btnSubmit.setAlpha(1);
            binding.btnSubmit.setEnabled(true);
        }else {
            binding.btnSubmit.setAlpha((float) 0.3);
            binding.btnSubmit.setEnabled(false);
        }
    }

    @Override
    protected void onDestroy() {
        super.onDestroy();
        binding = null;
    }
}