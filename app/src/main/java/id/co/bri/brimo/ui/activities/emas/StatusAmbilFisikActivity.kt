package id.co.bri.brimo.ui.activities.emas

import android.app.Activity
import android.content.Intent
import android.os.Bundle
import androidx.recyclerview.widget.LinearLayoutManager
import id.co.bri.brimo.R
import id.co.bri.brimo.adapters.emas.DetailBiayaCetakEmasAdapter
import id.co.bri.brimo.adapters.emas.DetailTransaksiCetakEmasAdapter
import id.co.bri.brimo.adapters.emas.StatusGoldMouldingAdapter
import id.co.bri.brimo.adapters.emas.AmountGoldMoulding
import id.co.bri.brimo.contract.IPresenter.emas.IDetailGoldMouldingPresenter
import id.co.bri.brimo.contract.IView.emas.IDetailAmbilFisikEmasView
import id.co.bri.brimo.databinding.ActivityStatusAmbilFisikBinding
import id.co.bri.brimo.domain.config.Constant
import id.co.bri.brimo.domain.helpers.GeneralHelper
import id.co.bri.brimo.models.apimodel.request.emas.DetailAmbilEmasFisikRequest
import id.co.bri.brimo.models.apimodel.request.emas.DetailTrxAmbilFisikEmasRequest
import id.co.bri.brimo.models.apimodel.response.ReceiptRevampResponse
import id.co.bri.brimo.models.apimodel.response.emas.DetailAmbilEmasFisikResponse
import id.co.bri.brimo.ui.activities.base.BaseActivity
import id.co.bri.brimo.ui.activities.receipt.ReceiptRevampActivity
import javax.inject.Inject

class StatusAmbilFisikActivity : BaseActivity(), IDetailAmbilFisikEmasView {

    lateinit var binding : ActivityStatusAmbilFisikBinding

    private lateinit var requestDetailAmbilFisik: DetailAmbilEmasFisikRequest
    private lateinit var mAdapterStep: StatusGoldMouldingAdapter
    private lateinit var mAdapterDetailTransaksi: DetailTransaksiCetakEmasAdapter
    private lateinit var mAdapterDetailBiaya: DetailBiayaCetakEmasAdapter
    private lateinit var mAdapterTotalBiaya: AmountGoldMoulding
    private var headerString = "<head><meta name='viewport' content='width=device-width, initial-scale=1.0, maximum-scale=1.0, minimum-scale=1.0, user-scalable=no'></head>"

    @Inject
    lateinit var presenter : IDetailGoldMouldingPresenter<IDetailAmbilFisikEmasView>

    companion object {
        private var mRefNum : String = ""
        private lateinit var mResponse: DetailAmbilEmasFisikResponse
        private var mFromDashboardEmas = false

        fun launchIntent(caller: Activity, refNum: String,response: DetailAmbilEmasFisikResponse, fromDashboardEmas : Boolean) {
            val intent = Intent(caller, StatusAmbilFisikActivity::class.java)
            mRefNum = refNum
            mResponse = response
            mFromDashboardEmas = fromDashboardEmas
            caller.startActivityForResult(intent, Constant.REQ_PAYMENT)
        }
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        binding = ActivityStatusAmbilFisikBinding.inflate(layoutInflater)
        setContentView(binding.root)
        injectDependency()
        setupView()
        initListener()
    }

    private fun initListener() {
        with(binding){
            btLihatBuktiTrx.setOnClickListener {
                presenter.getDataPaymentRevamp(DetailTrxAmbilFisikEmasRequest(mRefNum))
            }
        }
    }

    private fun injectDependency() {
        activityComponent.inject(this)
        presenter.view = this

        requestDetailAmbilFisik = DetailAmbilEmasFisikRequest(mRefNum)
        presenter.setUrlGetDetailAmbilFisikEmas(getString(R.string.url_detail_ambil_fisik_emas))
        if (mFromDashboardEmas){
            presenter.getDetailAmbilFisikEmas(requestDetailAmbilFisik)
        } else {
            setupData(mResponse)
        }
        presenter.setUrlGetDetailTransaction(getString(R.string.url_get_detail_transaction_cetak_emas))
        presenter.start()
    }

    private fun setupView() {
        with(binding){
            GeneralHelper.setToolbar(this@StatusAmbilFisikActivity, binding.tbStatusAmbilFisik.toolbar, getString(R.string.tb_status_ambil_fisik))
        }
    }

    private fun setupData(response : DetailAmbilEmasFisikResponse){
        GeneralHelper.setWebView(binding.wvEstimasiWaktu,"",headerString+response.timeEstimateInfo)
        GeneralHelper.setWebView(binding.wvDescInfoPengambilan,"",headerString+response.maintenanceInfo)
        mAdapterStep = response.trackingDataView.let { StatusGoldMouldingAdapter(it,this) }
        binding.rvTracking.layoutManager = LinearLayoutManager(this,LinearLayoutManager.VERTICAL,false)
        binding.rvTracking.adapter = mAdapterStep

        mAdapterDetailTransaksi = response.detailDataView.let { DetailTransaksiCetakEmasAdapter(it,this) }
        binding.rvDetailTransaksi.layoutManager = LinearLayoutManager(this,LinearLayoutManager.VERTICAL,false)
        binding.rvDetailTransaksi.adapter = mAdapterDetailTransaksi

        mAdapterDetailBiaya = response.amountDataView.let { DetailBiayaCetakEmasAdapter(it,this) }
        binding.rvBiayaCetak.layoutManager = LinearLayoutManager(this,LinearLayoutManager.VERTICAL,false)
        binding.rvBiayaCetak.adapter = mAdapterDetailBiaya

        mAdapterTotalBiaya = response.totalDataView.let { AmountGoldMoulding(it,this) }
        binding.rvTotalPayment.layoutManager = LinearLayoutManager(this,LinearLayoutManager.VERTICAL,false)
        binding.rvTotalPayment.adapter = mAdapterTotalBiaya
        hideProgress()
    }

    override fun onSuccessGetDetailAmbilFisikEmas(response: DetailAmbilEmasFisikResponse) {
        mResponse = response
        setupData(mResponse)
    }

    override fun onExceptionAlert(message: String) {
        hideProgress()
        val intent = Intent()
        intent.putExtra(Constant.TAG_ERROR_MESSAGE,message)
        setResult(RESULT_CANCELED,intent)
        finish()
    }

    override fun onSuccessGetPaymentRevamp(receiptRevampResponse: ReceiptRevampResponse?) {
        ReceiptRevampActivity.launchIntentTrackingEmas(false, false,false,this,receiptRevampResponse)
    }

    override fun onExceptionTrxExpired(message: String) {
        val i = Intent()
        i.putExtra(Constant.TAG_ERROR_MESSAGE, message)
        setResult(RESULT_CANCELED, i)
        finish()
    }


    override fun onActivityResult(requestCode: Int, resultCode: Int, data: Intent?) {
        super.onActivityResult(requestCode, resultCode, data)
        if (requestCode == Constant.REQ_PAYMENT){
            if (resultCode == RESULT_OK){
            }
        }
    }
}