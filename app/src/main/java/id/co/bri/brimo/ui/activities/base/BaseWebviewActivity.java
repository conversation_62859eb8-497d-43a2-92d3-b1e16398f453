package id.co.bri.brimo.ui.activities.base;

import android.annotation.SuppressLint;
import android.app.Activity;
import android.content.ActivityNotFoundException;
import android.content.Intent;
import android.graphics.Bitmap;
import android.net.Uri;
import android.net.http.SslError;
import android.os.Build;
import android.os.Bundle;
import android.os.CountDownTimer;
import android.os.Environment;
import android.provider.MediaStore;
import android.util.Log;
import android.view.View;
import android.webkit.CookieManager;
import android.webkit.PermissionRequest;
import android.webkit.SslErrorHandler;
import android.webkit.ValueCallback;
import android.webkit.WebChromeClient;
import android.webkit.WebResourceRequest;
import android.webkit.WebSettings;
import android.webkit.WebStorage;
import android.webkit.WebView;
import android.webkit.WebViewClient;
import android.widget.ImageView;
import android.widget.LinearLayout;

import java.io.File;
import java.io.IOException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.List;

import androidx.activity.result.ActivityResult;
import androidx.activity.result.ActivityResultCallback;
import androidx.activity.result.ActivityResultLauncher;
import androidx.activity.result.contract.ActivityResultContracts;
import androidx.annotation.Nullable;
import androidx.appcompat.widget.Toolbar;
import androidx.core.content.FileProvider;
import androidx.fragment.app.FragmentTransaction;
import butterknife.Bind;
import butterknife.ButterKnife;
import id.co.bri.brimo.R;
import id.co.bri.brimo.domain.config.Constant;
import id.co.bri.brimo.domain.helpers.GeneralHelper;
import id.co.bri.brimo.ui.customviews.dialog.DialogContinueCustom;
import id.co.bri.brimo.ui.customviews.dialog.DialogExitCustom;

public abstract class BaseWebviewActivity extends BaseActivity implements
        DialogExitCustom.DialogDefaultListener,
        DialogContinueCustom.DialogContinueDefaultListener {

    @Bind(R.id.toolbar)
    Toolbar toolbar;
    @Bind(R.id.iv_close)
    ImageView ivClose;
    @Bind(R.id.webview)
    protected WebView webView;
    @Bind(R.id.ly_loading)
    LinearLayout lyLoading;

    protected static final String TAG = "BaseWebviewActivity";

    protected static final String TAG_URL = "url_webview";
    protected static final String TAG_SESSION = "session_id";

    protected int second = 1000;

    protected boolean isFinished = false;

    protected static boolean mIsFromTravel = false;

    protected String userName;
    protected String tokenKey;
    protected String camPath;

    protected ValueCallback<Uri[]> uploadMessage;
    protected ValueCallback<Uri[]> fString;

    protected WebChromeClient.FileChooserParams fileChooserParam;

    protected List<String> pathPhotos = new ArrayList<>();

    protected static final int REQUEST_SELECT_FILE = 100;

    @Override
    protected void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);

        setContentView(getLayoutResource());
        ButterKnife.bind(this);

        setupViews();
    }

    private void setupViews() {
        if (mIsFromTravel) {
            GeneralHelper.setToolbarNoIconBack(this, toolbar, GeneralHelper.getString(R.string.title_injourney));
        } else {
            GeneralHelper.setToolbarNoIconBack(this, toolbar, GeneralHelper.getString(R.string.pengajuan_pinjaman));

        }
        ivClose.setOnClickListener(v -> callDialog());

        setTextTimer(30);
        initWebView();
    }

    //set time about 5 second to load
    private void setTextTimer(int timer) {
        int countDown = second * timer;
        // Do nothing
        new CountDownTimer(countDown, second) {

            public void onTick(long millisUntilFinished) {
                if (isFinished)
                    onFinish();
            }

            public void onFinish() {
                if (isFinished) {
                    lyLoading.setVisibility(View.GONE);
                    webView.setVisibility(View.VISIBLE);
                } else {
                    Intent i = new Intent();
                    i.putExtra(Constant.TAG_ERROR_MESSAGE, GeneralHelper.getString(R.string.halaman_gagal_coba_lagi));
                    setResult(Activity.RESULT_CANCELED, i);
                    finish();
                }
            }
        }.start();
    }

    protected void initWebView() {
        WebSettings webSettings = webView.getSettings();
        webSettings.setJavaScriptEnabled(true);
        webSettings.setDefaultTextEncodingName("utf-8");
        webSettings.setDomStorageEnabled(true);
        webSettings.setDatabaseEnabled(true);
//        webSettings.setAppCacheEnabled(true);
        webSettings.setAllowFileAccess(true);
        webSettings.setAllowFileAccessFromFileURLs(true);
        webSettings.setAllowUniversalAccessFromFileURLs(true);
        webSettings.setAllowContentAccess(true);
        webSettings.setLoadsImagesAutomatically(true);

        webView.getSettings().setPluginState(WebSettings.PluginState.ON);
        webView.setVerticalScrollBarEnabled(true);
        webView.getSettings().setMediaPlaybackRequiresUserGesture(false);
        webView.getSettings().setLoadWithOverviewMode(true);
        webView.getSettings().setUseWideViewPort(true);
        webView.requestFocus();

        webView.loadUrl(getIntent().getStringExtra(TAG_URL));

        if (18 < Build.VERSION.SDK_INT) {
            //18 = JellyBean MR2, KITKAT=19
            webView.getSettings().setCacheMode(WebSettings.LOAD_NO_CACHE);
        }
        webView.setWebViewClient(new WebViewClient() {
            @Override
            public boolean shouldOverrideUrlLoading(
                    WebView view, WebResourceRequest request) {
                isFinished = false;
                webView.loadUrl(request.getUrl().toString());
                return true;
            }

            @Override
            public void onPageStarted(WebView view, String url, Bitmap favicon) {
                super.onPageStarted(view, url, favicon);
            }

            @Override
            public void onPageFinished(WebView view, String url) {
                isFinished = true;
                super.onPageFinished(view, url);
            }

            @Override
            public void onReceivedSslError(WebView view, SslErrorHandler handler, SslError error) {
                if (!GeneralHelper.isProd()) handler.proceed();
            }

        });
        webView.setWebChromeClient(new WebChromeClient() {
            @Override
            public boolean onShowFileChooser(WebView webView, ValueCallback<Uri[]> filePathCallback, FileChooserParams fileChooserParams) {

                fString = filePathCallback;

                uploadMessage = filePathCallback;
                fileChooserParam = fileChooserParams;

                DialogContinueCustom dialogNotice = new DialogContinueCustom(BaseWebviewActivity.this,
                        GeneralHelper.getString(R.string.dialog_title_upload), GeneralHelper.getString(R.string.dialog_desc_upload),
                        GeneralHelper.getString(R.string.kamera), GeneralHelper.getString(R.string.galeri), false);
                FragmentTransaction ft = BaseWebviewActivity.this.getSupportFragmentManager().beginTransaction();
                ft.add(dialogNotice, null);
                ft.commitAllowingStateLoss();

                return true;
            }

            @Override
            public void onPermissionRequest(final PermissionRequest request) {
                if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.LOLLIPOP) {
                    request.grant(request.getResources());
                }
            }
        });

        clearWebView();
    }

    private void openCamera() {
        Intent takePictureIntent;
        takePictureIntent = new Intent(MediaStore.ACTION_IMAGE_CAPTURE);

        if (takePictureIntent.resolveActivity(BaseWebviewActivity.this.getPackageManager()) != null) {
            File photoFile = null;
            try {
                photoFile = createImage();
                takePictureIntent.putExtra("PhotoPath", camPath);
            } catch (IOException ex) {
                if (!GeneralHelper.isProd())
                    Log.e(TAG, "Image file creation failed", ex);
            }
            if (photoFile != null) {
                camPath = "file:" + photoFile.getAbsolutePath();
                takePictureIntent.putExtra(MediaStore.EXTRA_OUTPUT,
                        FileProvider.getUriForFile(BaseWebviewActivity.this, getPackageName() + ".provider", photoFile));
            } else {
                takePictureIntent = null;
            }
        }
        Intent chooser = new Intent(MediaStore.ACTION_IMAGE_CAPTURE);
        chooser.putExtra(Intent.EXTRA_INTENT, takePictureIntent);
        myArl.launch(chooser);
    }

    private void openGaleri() {
        Intent intent = null;
        if (android.os.Build.VERSION.SDK_INT >= android.os.Build.VERSION_CODES.LOLLIPOP) {
            intent = fileChooserParam.createIntent();
        }
        try {
            startActivityForResult(intent, REQUEST_SELECT_FILE);
        } catch (ActivityNotFoundException e) {
            uploadMessage = null;
        }
    }

    private File createImage() throws IOException {
        @SuppressLint("SimpleDateFormat")
        String fileName = new SimpleDateFormat("yyyy_mm_ss").format(new Date());
        String newName = "file_" + fileName + "_";
        File savedPhoto = null;
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.R) {
            savedPhoto = new File(Environment.getExternalStoragePublicDirectory(Environment.DIRECTORY_DOCUMENTS), newName);
        } else savedPhoto = new File(Environment.getExternalStorageDirectory(), newName);
        return File.createTempFile(newName, ".jpg", savedPhoto);
    }

    protected void clearWebView() {
        webView.clearCache(true);
        webView.clearFormData();
        webView.clearHistory();
        webView.clearSslPreferences();
        webView.clearMatches();
        CookieManager.getInstance().removeAllCookies(null);
        CookieManager.getInstance().flush();
        WebStorage.getInstance().deleteAllData();
    }

    ActivityResultLauncher<Intent> myArl = registerForActivityResult(
            new ActivityResultContracts.StartActivityForResult(),
            new ActivityResultCallback<ActivityResult>() {
                @Override
                public void onActivityResult(ActivityResult result) {
                    Uri[] results = null;
                    String stringData;
                    if (result.getResultCode() == Activity.RESULT_CANCELED) {
                        fString.onReceiveValue(null);
                        return;
                    }
                    if (result.getResultCode() == Activity.RESULT_OK) {
                        if (null == fString) {
                            return;
                        }
                        try {
                            stringData = result.getData().getDataString();
                        } catch (Exception e) {
                            stringData = null;
                        }

                        if (stringData == null && camPath != null) {
                            results = new Uri[]{Uri.parse(camPath)};

                        } else {
                            try {
                                Bitmap camPhoto = (Bitmap) result.getData().getExtras().get("data");
                                stringData = MediaStore.Images.Media.insertImage(getContentResolver(),
                                        camPhoto, "IMG_" + Calendar.getInstance().getTime(), null);
                                pathPhotos.add(stringData);
                            } catch (Exception ignored) {
                                if (!GeneralHelper.isProd()){
                                    Log.e(TAG, "generateTransaksiModel: ", ignored);
                                }
                            }
                            results = new Uri[]{Uri.parse(stringData)};
                        }
                    }
                    fString.onReceiveValue(results);
                    fString = null;
                }
            });

    protected abstract int getLayoutResource();

    protected abstract String getTitleBar();

    protected abstract void revokeSession(boolean isBack);

    protected void callDialog() {
        DialogExitCustom dialogExitCustom = new DialogExitCustom(this,
                GeneralHelper.getString(R.string.dialog_title_exit_webview),
                GeneralHelper.getString(R.string.dialog_desc_exit_webview));
        FragmentTransaction ft = getSupportFragmentManager().beginTransaction();
        ft.add(dialogExitCustom, null);
        ft.commitAllowingStateLoss();
    }

    @Override
    public void onClickYes() {
        revokeSession(true);
    }

    @Override
    public void onClickContinueYes() {
        openCamera();
    }

    @Override
    public void onClickContinueNo() {
        openGaleri();
    }

    @Override
    protected void onActivityResult(int requestCode, int resultCode, @Nullable Intent data) {
        super.onActivityResult(requestCode, resultCode, data);

        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.LOLLIPOP
                && requestCode == REQUEST_SELECT_FILE) {
            if (uploadMessage == null)
                return;
            uploadMessage.onReceiveValue(WebChromeClient.FileChooserParams.parseResult(resultCode, data));
            uploadMessage = null;
        }
    }

}
