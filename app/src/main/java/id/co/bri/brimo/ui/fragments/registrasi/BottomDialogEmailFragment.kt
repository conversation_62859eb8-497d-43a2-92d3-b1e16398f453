package id.co.bri.brimo.ui.fragments.registrasi

import android.os.Bundle
import android.text.Editable
import android.text.TextWatcher
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import com.google.android.material.bottomsheet.BottomSheetDialogFragment
import id.co.bri.brimo.R
import id.co.bri.brimo.databinding.FragmentBottomDialogEmailBinding
import id.co.bri.brimo.domain.helpers.ValidationHelper

class BottomDialogEmailFragment(
    private var email: String,
    private var onCallbackEmail: OnCallbackEmail
) :
    BottomSheetDialogFragment() {

    private lateinit var binding: FragmentBottomDialogEmailBinding

    interface OnCallbackEmail {
        fun itemCallbackEmail(email: String)
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setStyle(STYLE_NORMAL, R.style.CustomBottomSheetDialogTheme)
    }

    override fun onCreateView(
        inflater: LayoutInflater, container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        super.onCreateView(inflater, container, savedInstanceState)
        binding = FragmentBottomDialogEmailBinding.inflate(inflater, container, false)
        return binding.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)

        binding.etUbahEmail.addTextChangedListener(textWatcher)
        binding.btnUbah.setOnClickListener {
            dismiss()
            onCallbackEmail.itemCallbackEmail(binding.etUbahEmail.text.toString())
        }
    }

    private val textWatcher = object : TextWatcher {
        override fun beforeTextChanged(p0: CharSequence?, p1: Int, p2: Int, p3: Int) {
            // do nothing
        }

        override fun onTextChanged(p0: CharSequence?, p1: Int, p2: Int, p3: Int) {
            // do nothing
            if (ValidationHelper.validateEmail(binding.etUbahEmail) &&
                binding.etUbahEmail.text.toString() != email) {
                binding.btnUbah.alpha = 1f
                binding.btnUbah.isEnabled = true
            } else {
                binding.btnUbah.alpha = 0.3f
                binding.btnUbah.isEnabled = false
            }
        }

        override fun afterTextChanged(p0: Editable?) {
            // do nothing
        }
    }
}