package id.co.bri.brimo.ui.fragments.investasi

import android.os.Bundle
import androidx.fragment.app.Fragment
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.FrameLayout
import com.google.android.material.bottomsheet.BottomSheetBehavior
import com.google.android.material.bottomsheet.BottomSheetDialogFragment
import id.co.bri.brimo.R
import id.co.bri.brimo.databinding.FragmentBottomSheetGeneralInvestasiBinding

class BottomSheetGeneralInvestasiFragment : BottomSheetDialogFragment() {

    enum class DialogType {

    }

    private lateinit var binding: FragmentBottomSheetGeneralInvestasiBinding

    private var dismissListener: () -> Unit = {}
    private var btnFirstListener: () -> Unit = {}
    private var btnSecondListener: () -> Unit = {}
    private var btnActionListener: () -> Unit = {}

    var imagePath: String = ""
    var imageName: String = ""
    var titleTopText: String = ""
    var titleText: String = ""
    var subtitleText: String = ""
    var firstBtnText: String = ""
    var secondBtnText: String = ""
    var actionBtnText: String = ""
    var isClickable: Boolean = true

    fun setOnDismiss(listener: () -> Unit) {
        dismissListener = listener
    }

    fun setOnBtnFirst(listener: () -> Unit) {
        btnFirstListener = listener
    }

    fun setOnBtnSecond(listener: () -> Unit) {
        btnSecondListener = listener
    }

    fun setOnBtnAction(listener: () -> Unit) {
        btnActionListener = listener
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setStyle(STYLE_NORMAL, R.style.CustomBottomSheetDialogTheme)
    }

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View? {
        binding = FragmentBottomSheetGeneralInvestasiBinding.inflate(inflater, container, false)
        return binding.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)

        dialog?.setOnShowListener{
            val bottomSheet = dialog?.findViewById(R.id.design_bottom_sheet) as FrameLayout
            val behavior = BottomSheetBehavior.from(bottomSheet)
            behavior.state = BottomSheetBehavior.STATE_EXPANDED
        }

        dialog?.setCancelable(isCancelable)
        dialog?.setCanceledOnTouchOutside(isClickable)
    }
}