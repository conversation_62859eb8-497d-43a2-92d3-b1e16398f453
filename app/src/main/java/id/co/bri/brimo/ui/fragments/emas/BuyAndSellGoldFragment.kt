package id.co.bri.brimo.ui.fragments.emas

import android.annotation.SuppressLint
import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import id.co.bri.brimo.R
import id.co.bri.brimo.databinding.FragmentBuyAndSellGoldBinding
import id.co.bri.brimo.models.apimodel.response.emas.GrafikEmasResponse
import id.co.bri.brimo.ui.fragments.BaseFragment

@SuppressLint("ValidFragment")
class BuyAndSellGoldFragment(private var mResponse: GrafikEmasResponse, private var onListener : DialogDefaultListener, private var boolean: Boolean) : BaseFragment(),View.OnClickListener {
    private var _binding: FragmentBuyAndSellGoldBinding? = null
    private val binding get() = _binding!!
    var date : String? = null


    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
    }

    override fun onCreateView(
            inflater: LayoutInflater, container: ViewGroup?,
            savedInstanceState: Bundle?
    ): View {
        _binding = FragmentBuyAndSellGoldBinding.inflate(inflater, container, false);
        return binding.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        onListener.onType(boolean,mResponse)
        if (boolean){
            binding.tvTitle.text = "Harga Beli"
            binding.tvSellRate.text = mResponse.buyInfo!!.buyRate!!.buyRateString
            binding.btnBuy.text = "Beli Emas"
            binding.btnBuy.setBackgroundResource(R.drawable.rounded_button_blue)
            binding.btnBuy.setOnClickListener(this)
        }else{
            binding.tvTitle.text = "Harga Jual"
            binding.tvSellRate.text = mResponse.sellInfo!!.sellRate!!.sellRateString
            binding.btnBuy.setBackgroundResource(R.drawable.rounded_button_orange)
            binding.btnBuy.text = "Jual Emas"
            binding.btnBuy.setOnClickListener(this)

        }

    }

    interface DialogDefaultListener {
        fun onType(boolean: Boolean,mResponse: GrafikEmasResponse)

        fun onClickItem(boolean: Boolean)
    }

    override fun onClick(p0: View?) {
        onListener.onClickItem(boolean)
    }


}