package id.co.bri.brimo.ui.fragments.emas

import android.app.Activity
import android.content.Context
import android.content.Intent
import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.recyclerview.widget.LinearLayoutManager
import id.co.bri.brimo.R
import id.co.bri.brimo.adapters.RiwayatTransaksiJualAdapter
import id.co.bri.brimo.contract.IPresenter.emas.IRiwayatEmasJualPresenter
import id.co.bri.brimo.contract.IView.emas.IRiwayatEmasJualView
import id.co.bri.brimo.databinding.FragmentRiwayatJualEmasBinding
import id.co.bri.brimo.domain.config.Constant
import id.co.bri.brimo.domain.config.Constant.EMAS_SELL_TYPE
import id.co.bri.brimo.domain.helpers.GeneralHelper
import id.co.bri.brimo.domain.helpers.calendar.CalendarHelper
import id.co.bri.brimo.models.TransactionTypeModel
import id.co.bri.brimo.models.YearModel
import id.co.bri.brimo.models.apimodel.request.emas.RiwayatFilterRequest
import id.co.bri.brimo.models.apimodel.response.emas.RiwayatTransaksiEmasResponse
import id.co.bri.brimo.ui.activities.MutationFilterActivity
import id.co.bri.brimo.ui.fragments.BaseFragment
import javax.inject.Inject

class RiwayatJualEmasFragment(private var mResponse: RiwayatTransaksiEmasResponse, private var mContext : Context) : BaseFragment(),IRiwayatEmasJualView {
    private var _binding: FragmentRiwayatJualEmasBinding? = null
    private val binding get() = _binding!!
    private var adapter : RiwayatTransaksiJualAdapter? = null
    var rangeFilter:String? = null
    var transactionTypeId:String? = EMAS_SELL_TYPE
    var years:String? = null
    var monthId:String? = null
    var accountNumber:String? = null
    var transactionTypeName:String? = null
    var month:String? = null
    protected var mutationDateRangeFilterRequest: RiwayatFilterRequest? = null
    private var endDate: String? = null
    private var startDate: String? = null
    protected var transactionType = ArrayList<TransactionTypeModel>()
    protected var yearList: List<YearModel> = mutableListOf()

    @Inject
    lateinit var presenter : IRiwayatEmasJualPresenter<IRiwayatEmasJualView>
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)

    }

    override fun onCreateView(inflater: LayoutInflater, container: ViewGroup?,
                              savedInstanceState: Bundle?): View? {
        // Inflate the layout for this fragment
        _binding = FragmentRiwayatJualEmasBinding.inflate(inflater, container, false)
        val view = binding.root;

        return view
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)

        if (mResponse.sellHistory!!.isEmpty()) {
            binding.llEmptyTransaksi.visibility = View.VISIBLE
            binding.llFilter.visibility = View.GONE
        }else {
            adapter = RiwayatTransaksiJualAdapter(mResponse.sellHistory!!, mContext)
            val layoutManager = LinearLayoutManager(mContext, LinearLayoutManager.VERTICAL, false)
            binding.rvFragmentTransaksi.layoutManager = layoutManager
            binding.rvFragmentTransaksi.adapter = adapter
        }

        injectDepedency()

        yearList = mResponse.listYear!!
        binding.tvNoRek.text = mResponse.accountNumber
        binding.llFilter.setOnClickListener {
            transactionTypeId = EMAS_SELL_TYPE
            MutationFilterActivity.launchIntent(requireActivity(), transactionType, yearList, rangeFilter, transactionTypeId, monthId, years, startDate, endDate,true)

        }
    }

    private fun injectDepedency() {
        activityComponent.inject(this)
        presenter.view = this
        presenter.start()
        presenter.setUrlFilter(GeneralHelper.getString(R.string.url_filter_riwayat))
        presenter.setUrlRiwayatTransaksi(GeneralHelper.getString(R.string.url_riwayat_emas))

    }

    override fun onSuccessGetMutation(response: RiwayatTransaksiEmasResponse) {
        hideProgress()
        if (response.sellHistory!!.isEmpty()) {
            binding.llEmptyTransaksi.visibility = View.VISIBLE
            binding.rvFragmentTransaksi.visibility = View.GONE
        }else{
            binding.llEmptyTransaksi.visibility = View.GONE
            binding.rvFragmentTransaksi.visibility = View.VISIBLE
            adapter = RiwayatTransaksiJualAdapter(response.sellHistory!!, mContext)
            val layoutManager = LinearLayoutManager(mContext, LinearLayoutManager.VERTICAL, false)
            binding.rvFragmentTransaksi.layoutManager = layoutManager
            binding.rvFragmentTransaksi.adapter = adapter
        }

    }


    private fun getMutationWithFilter(data: Intent) {
        showProgress()
        if (rangeFilter.equals(Constant.TODAY_FILTER, ignoreCase = true) || rangeFilter.equals(Constant.SEVEN_DAY_FILTER, ignoreCase = true)) {
            startDate = data.getStringExtra(Constant.TAG_START_DATE)
            endDate = data.getStringExtra(Constant.TAG_END_DATE)
            years = data.getStringExtra(Constant.YEAR)
            monthId = data.getStringExtra(Constant.MONTH)
            mutationDateRangeFilterRequest = RiwayatFilterRequest(rangeFilter, startDate, endDate, years, monthId)
            presenter.getMutationRange(mutationDateRangeFilterRequest)
        } else if (rangeFilter.equals(Constant.MONTH_FILTER, ignoreCase = true)) {
            startDate = data.getStringExtra(Constant.TAG_START_DATE)
            endDate = data.getStringExtra(Constant.TAG_END_DATE)
            years = data.getStringExtra(Constant.YEAR)
            monthId = data.getStringExtra(Constant.MONTH)
            month = data.getStringExtra(Constant.MONTH_TEXT)
            mutationDateRangeFilterRequest = RiwayatFilterRequest(rangeFilter, startDate, endDate, years, monthId)
            presenter.getMutationRange(mutationDateRangeFilterRequest)
        }
        else if (rangeFilter.equals(Constant.RANGE_FILTER, ignoreCase = true)) {
                startDate = data.getStringExtra(Constant.TAG_START_DATE)
                endDate = data.getStringExtra(Constant.TAG_END_DATE)
                years = data.getStringExtra(Constant.YEAR)
                monthId = data.getStringExtra(Constant.MONTH)
                mutationDateRangeFilterRequest = RiwayatFilterRequest(rangeFilter, startDate, endDate, years, monthId)
                presenter.getMutationRange(mutationDateRangeFilterRequest)
            }
        binding.tvTime.setTextColor(resources.getColor(R.color.whiteColor))
        binding.llTimeFilter.setBackground(resources.getDrawable(R.drawable.rounded_button_blue))
        binding.ivFilter.setVisibility(View.GONE)
        if (rangeFilter.equals(Constant.TODAY_FILTER, ignoreCase = true)) {
            binding.tvTime.setText(Constant.TODAY_TEXT)
        } else if (rangeFilter.equals(Constant.SEVEN_DAY_FILTER, ignoreCase = true)) {
            binding.tvTime.setText(Constant.SEVEN_DAY_TEXT)
        } else if (rangeFilter.equals(Constant.MONTH_FILTER, ignoreCase = true)) {
            binding.tvTime.setText(month + " " + years)
        } else {
            binding.tvTime.setText(CalendarHelper.dateFormatyyyyMMdd(startDate) + " - " + CalendarHelper.dateFormatyyyyMMdd(endDate))
        }
    }

    private fun setResetFilter() {
        binding.tvTime.setText(GeneralHelper.getString(R.string.date_range))
        binding.tvTime.setTextColor(resources.getColor(R.color.system10))
        binding.llTimeFilter.setBackground(resources.getDrawable(R.drawable.bg_white_border_accent2))
        binding.ivFilter.setVisibility(View.VISIBLE)
        rangeFilter = null
        transactionTypeId = null
        monthId = null
        years = null
        startDate = null
        endDate = null
        presenter.getDataRiwayatTransaksi()
    }

    override fun onException(message: String?) {
        super.onException(message)
        hideProgress()
        GeneralHelper.showSnackBarRevamp(binding.content, message)
    }
    override fun onActivityResult(requestCode: Int, resultCode: Int, data: Intent?) {
        super.onActivityResult(requestCode, resultCode, data)
        if (requestCode == Constant.REQ_DURATION) {
            if (resultCode == Activity.RESULT_OK) {
                if (data != null) {
                    if (data.getStringExtra(Constant.TRANSACTION_TYPE_ID).equals(Constant.EMAS_SELL_TYPE)) {
                            if (data.getStringExtra(Constant.FILTER) != null) {
                                    rangeFilter = data.getStringExtra(Constant.FILTER)
                                    getMutationWithFilter(data)
                            } else {
                                setResetFilter()
                            }
                        }
                } else {
                    setResetFilter()
                }
            }
        }
    }
}