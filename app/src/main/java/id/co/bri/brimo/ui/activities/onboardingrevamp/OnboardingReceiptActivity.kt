package id.co.bri.brimo.ui.activities.onboardingrevamp

import android.app.Activity
import android.content.ClipData
import android.content.ClipboardManager
import android.content.Intent
import android.os.Bundle
import android.util.Log
import android.view.View
import androidx.activity.OnBackPressedCallback
import androidx.activity.result.ActivityResultLauncher
import androidx.activity.result.contract.ActivityResultContracts
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.google.gson.Gson
import id.co.bri.brimo.R
import id.co.bri.brimo.adapters.DataTransaksiRevampAdapter
import id.co.bri.brimo.contract.IPresenter.onboardingrevamp.IOnboardingReceiptPresenter
import id.co.bri.brimo.contract.IView.onboardingrevamp.IOnboardingReceiptView
import id.co.bri.brimo.databinding.ActivityOnboardingReceiptBinding
import id.co.bri.brimo.domain.config.Constant
import id.co.bri.brimo.domain.helpers.GeneralHelper
import id.co.bri.brimo.models.apimodel.response.DataView
import id.co.bri.brimo.models.apimodel.response.onboardingrevamp.OnboardingLoginResponse
import id.co.bri.brimo.models.apimodel.response.onboardingrevamp.OnboardingReceiptResponse
import id.co.bri.brimo.ui.activities.AskActivity
import id.co.bri.brimo.ui.activities.DashboardIBActivity
import id.co.bri.brimo.ui.activities.SyaratKetentuanActivity
import id.co.bri.brimo.ui.activities.base.BaseActivity
import java.util.HashMap
import javax.inject.Inject

class OnboardingReceiptActivity : BaseActivity(),
    IOnboardingReceiptView,
    View.OnClickListener {

    private lateinit var binding: ActivityOnboardingReceiptBinding

    private lateinit var receiptResponse: OnboardingReceiptResponse

    @Inject
    lateinit var presenter: IOnboardingReceiptPresenter<IOnboardingReceiptView>

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        binding = ActivityOnboardingReceiptBinding.inflate(layoutInflater)
        setContentView(binding.root)

        intentExtra()
        injectDependency()
        setupViews()
        setListener()
        setEventAppsFlyerFirstInit()
    }

    private fun setEventAppsFlyerFirstInit() {
        val eventValue: MutableMap<String, Any> = HashMap()
        eventValue[Constant.CUSTOMER_ID] = presenter.persistenceId
        trackAppsFlyerAnalyticEvent("openaccount_success", eventValue)
    }

    private fun intentExtra() {
        if (intent.hasExtra(Constant.GENRES)) {
            receiptResponse = Gson().fromJson(
                intent.getStringExtra(Constant.GENRES),
                OnboardingReceiptResponse::class.java
            )
        }
    }

    private fun injectDependency() {
        activityComponent.inject(this)
        presenter.view = this
        presenter.start()
        presenter.setUrlRevokeLogin(GeneralHelper.getString(R.string.url_onboarding_login_v3))
    }

    private fun setupViews() {
        GeneralHelper.setToolbarNoIconBack(this, binding.toolbarRevamp.toolbar, "")

        GeneralHelper.loadImageUrl(
            this,
            receiptResponse.imageProductUrl,
            binding.imgRekening,
            R.drawable.bri,
            0
        )

        binding.tvNomorRekening.text = receiptResponse.accountNumber
        transactionDataView()
    }

    private fun transactionDataView() {
        val dataViews = ArrayList<DataView>()

        for (i in 0 until receiptResponse.detail.size) {
            dataViews.add(
                DataView(
                    receiptResponse.detail[i].title,
                    receiptResponse.detail[i].value,
                    ""
                )
            )
            Log.d("TestAPI-adapter", "transactionDataView: " + Gson().toJson(dataViews))
        }

        if (dataViews.size > 0) {
            val dataTransaksiRevampAdapter =
                DataTransaksiRevampAdapter(dataViews, this)

            binding.recyclerview.adapter = dataTransaksiRevampAdapter
            binding.recyclerview.setHasFixedSize(true)
            binding.recyclerview.layoutManager = LinearLayoutManager(
                applicationContext, RecyclerView.VERTICAL, false
            )
        }
    }

    private fun setListener() {
        binding.tvCaraSetoran.setOnClickListener(this)
        binding.btnSalin.setOnClickListener(this)
        binding.btnLanjut.setOnClickListener(this)
    }

    override fun onClick(v: View) {
        when (v.id) {
            binding.tvCaraSetoran.id -> launchWebview(receiptResponse.depositProcedure)
            binding.btnSalin.id -> copyAccountNumber(receiptResponse.accountNumber.replace(" ", ""))
            binding.btnLanjut.id -> {
                presenter.saveBubbleShowRek(receiptResponse.depositProcedure)
                if (receiptResponse.bypassLogin)
                    presenter.sendRevokeLogin()
                else
                    launchLogin()
            }
        }
    }

    private fun launchWebview(depositProcedure: String) {
        SyaratKetentuanActivity.launchIntentOnboarding(
            this,
            depositProcedure,
            GeneralHelper.getString(R.string.toolbar_pusat_bantuan),
            GeneralHelper.getString(R.string.kembali),
            true
        )
    }

    private fun copyAccountNumber(accountNumber: String) {
        val myClipboard = getSystemService(CLIPBOARD_SERVICE) as ClipboardManager
        val myClip = ClipData.newPlainText("text", accountNumber)
        myClipboard.setPrimaryClip(myClip)
        showSnackbarErrorMessageRevamp(
            GeneralHelper.getString(R.string.rekening_berhasil_disalin),
            ALERT_CONFIRM,
            this,
            false
        )
    }

    private fun launchDashboardIb() {
        val intent = Intent(this, DashboardIBActivity::class.java)
        intent.addFlags(Intent.FLAG_ACTIVITY_CLEAR_TOP or Intent.FLAG_ACTIVITY_NEW_TASK)
        startActivityIntent.launch(intent)
        setResult(RESULT_OK)
        finish()
    }

    private fun launchLogin() {
        val intent = Intent(this, AskActivity::class.java)
        intent.flags = Intent.FLAG_ACTIVITY_NEW_TASK or Intent.FLAG_ACTIVITY_CLEAR_TASK
        intent.putExtra(Constant.ONBOARDING_BRIMO, true)
        startActivityIntent.launch(intent)
        finish()
    }

    override fun onSuccessRevoke(onboardingLogin: OnboardingLoginResponse) {
        presenter.saveUserLogin(onboardingLogin)
        launchDashboardIb()
    }

    override fun onExceptionLogin() {
        val intent = Intent(this, AskActivity::class.java)
        intent.flags = Intent.FLAG_ACTIVITY_NEW_TASK or Intent.FLAG_ACTIVITY_CLEAR_TASK
        intent.putExtra(Constant.ONBOARDING_BRIMO, true)
        startActivityIntent.launch(intent)
        finish()
    }

    override fun onExceptionRevamp(message: String?) {
        GeneralHelper.showBottomDialog(this, message)
    }

    private var startActivityIntent: ActivityResultLauncher<Intent> = registerForActivityResult(
        ActivityResultContracts.StartActivityForResult()
    ) { result ->
        if (result.resultCode == Activity.RESULT_CANCELED && result.data != null) {
            setResult(RESULT_CANCELED, result.data)
            finish()
        }
    }

    override fun onDestroy() {
        presenter.stop()
        super.onDestroy()
    }
}