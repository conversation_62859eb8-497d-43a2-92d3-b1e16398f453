package id.co.bri.brimo.models.apimodel.request;

import com.google.gson.annotations.Expose;
import com.google.gson.annotations.SerializedName;

public class DetailStatusRequest {
    @SerializedName("account_number")
    @Expose
    private String accountNumber;
    @SerializedName("ticket_id")
    @Expose
    private String ticketId;

    public DetailStatusRequest(String accountNumber, String ticketId) {
        this.accountNumber = accountNumber;
        this.ticketId = ticketId;
    }

    public String getAccountNumber() {
        return accountNumber;
    }

    public void setAccountNumber(String accountNumber) {
        this.accountNumber = accountNumber;
    }

    public String getTicketId() {
        return ticketId;
    }

    public void setTicketId(String ticketId) {
        this.ticketId = ticketId;
    }
}