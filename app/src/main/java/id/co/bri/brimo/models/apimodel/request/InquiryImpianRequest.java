package id.co.bri.brimo.models.apimodel.request;

import com.google.gson.annotations.Expose;
import com.google.gson.annotations.SerializedName;

public class InquiryImpianRequest {
    @SerializedName("account_number")
    @Expose
    private String accountNumber;
    @SerializedName("target_amount")
    @Expose
    private Long targetAmount;
    @SerializedName("month")
    @Expose
    private Integer month;
    @SerializedName("dream_name")
    @Expose
    private String dreamName;

    public InquiryImpianRequest(String accountNumber, Long targetAmount, Integer month, String dreamName) {
        this.accountNumber = accountNumber;
        this.targetAmount = targetAmount;
        this.month = month;
        this.dreamName = dreamName;
    }

    public String getAccountNumber() {
        return accountNumber;
    }

    public void setAccountNumber(String accountNumber) {
        this.accountNumber = accountNumber;
    }

    public Long getTargetAmount() {
        return targetAmount;
    }

    public void setTargetAmount(Long targetAmount) {
        this.targetAmount = targetAmount;
    }

    public Integer getMonth() {
        return month;
    }

    public void setMonth(Integer month) {
        this.month = month;
    }

    public String getDreamName() {
        return dreamName;
    }

    public void setDreamName(String dreamName) {
        this.dreamName = dreamName;
    }
}
