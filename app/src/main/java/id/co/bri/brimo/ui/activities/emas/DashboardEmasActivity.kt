package id.co.bri.brimo.ui.activities.emas

import android.app.Activity
import android.content.Intent
import android.graphics.Color
import android.os.Bundle
import android.view.View
import androidx.core.content.ContextCompat
import androidx.swiperefreshlayout.widget.SwipeRefreshLayout.OnRefreshListener
import com.ethanhua.skeleton.Skeleton
import com.ethanhua.skeleton.SkeletonScreen
import id.co.bri.brimo.R
import id.co.bri.brimo.contract.IPresenter.emas.IDashboardEmasPresenter
import id.co.bri.brimo.contract.IView.emas.IDashboardEmasView
import id.co.bri.brimo.data.preference.BRImoPrefRepository
import id.co.bri.brimo.databinding.ActivityDashboardEmasBinding
import id.co.bri.brimo.domain.config.Constant
import id.co.bri.brimo.domain.config.Constant.REQ_AMBIL_FISIK
import id.co.bri.brimo.domain.helpers.GeneralHelper
import id.co.bri.brimo.domain.helpers.bubbleShowCaseView.BubbleShowCase
import id.co.bri.brimo.domain.helpers.bubbleShowCaseView.BubbleShowCaseBuilder
import id.co.bri.brimo.models.apimodel.request.emas.GrafikEmasRequest
import id.co.bri.brimo.models.apimodel.request.emas.RiwayatFilterRequest
import id.co.bri.brimo.models.apimodel.response.InfoResponse
import id.co.bri.brimo.models.apimodel.response.QuestionResponse
import id.co.bri.brimo.models.apimodel.response.TopupEmasResponse
import id.co.bri.brimo.models.apimodel.response.emas.DashboardEmasMonthlyResponse
import id.co.bri.brimo.models.apimodel.response.emas.DashboardEmasPortofolioResponse
import id.co.bri.brimo.models.apimodel.response.emas.DashboardEmasRateResponse
import id.co.bri.brimo.models.apimodel.response.emas.DetailAmbilEmasFisikResponse
import id.co.bri.brimo.models.apimodel.response.emas.DetailEmasResponse
import id.co.bri.brimo.models.apimodel.response.emas.GrafikEmasResponse
import id.co.bri.brimo.models.apimodel.response.emas.InquiryAmbilFisikEmasResponse
import id.co.bri.brimo.models.apimodel.response.emas.InquiryOpenEmasResponse
import id.co.bri.brimo.models.apimodel.response.emas.OnboardingSliderResponse
import id.co.bri.brimo.models.apimodel.response.emas.RiwayatAmbilFisikEmasResponse
import id.co.bri.brimo.models.apimodel.response.emas.RiwayatTransaksiEmasResponse
import id.co.bri.brimo.models.apimodel.response.emas.SafetyModeDrawerResponse
import id.co.bri.brimo.models.apimodel.response.onExceptionWH
import id.co.bri.brimo.ui.activities.DetailPusatBantuanActivity
import id.co.bri.brimo.ui.activities.base.BaseActivity
import id.co.bri.brimo.ui.customviews.dialog.DialogCase58
import id.co.bri.brimo.ui.customviews.dialog.DialogSetDefaultRevamp
import id.co.bri.brimo.ui.fragments.BottomFragmentNoImgDescHtml
import id.co.bri.brimo.ui.fragments.BottomFragmentSafteyMode
import id.co.bri.brimo.ui.fragments.DialogFragmentRc02
import id.co.bri.brimo.ui.fragments.bottomsheet.OpenBottomSheetGeneralFragment
import id.co.bri.brimo.ui.fragments.esbn.BottomFragmentWithDataView
import id.co.bri.brimo.util.extension.gone
import javax.inject.Inject

class DashboardEmasActivity : BaseActivity(), IDashboardEmasView,
    DialogFragmentRc02.DialogDefaultListener, BottomFragmentSafteyMode.DialogDefaulListener,
    View.OnClickListener, OnRefreshListener, DialogSetDefaultRevamp.DialogDefaultListener,
    DialogCase58.DialogDefaultListener {
    lateinit var binding: ActivityDashboardEmasBinding
    var skeletonScreen: SkeletonScreen? = null
    var skeletonScreen1: SkeletonScreen? = null
    var skeletonScreen2: SkeletonScreen? = null
    var dialog: DialogFragmentRc02? = null
    var dialog58: DialogCase58? = null
    private var dataPortofolio: DashboardEmasPortofolioResponse? = null
    private var dataMonthly: DashboardEmasMonthlyResponse? = null
    var onClickAktivitas: BottomFragmentNoImgDescHtml? = null
    var onClickSaldoEmas: BottomFragmentWithDataView? = null

    private lateinit var bubbleSaldoEmas: BubbleShowCaseBuilder

    private var responseRiwayatTrx: RiwayatTransaksiEmasResponse? = null
    private var requestRiwayatAmbilFisik : RiwayatFilterRequest? = null
    private var balanceEmas : Double? = 0.0
    private var brImoPrefRepository: BRImoPrefRepository? = BRImoPrefRepository(this)


    @Inject
    lateinit var presenter: IDashboardEmasPresenter<IDashboardEmasView>

    companion object {
        private var isFromInvestasi: Boolean = false

        @JvmStatic
        fun launchIntent(caller: Activity, fromInvestasi: Boolean) {
            val intent = Intent(caller, DashboardEmasActivity::class.java)
            isFromInvestasi = fromInvestasi
            caller.startActivityForResult(intent, Constant.REQ_PAYMENT)
        }

        @JvmStatic
        fun launchIntentRegis(caller: Activity, fromInvestasi: Boolean) {
            val intent = Intent(caller, DashboardEmasActivity::class.java)
            isFromInvestasi = fromInvestasi
            caller.startActivityForResult(intent, Constant.REQ_BUKA_REKENING)
        }
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        binding = ActivityDashboardEmasBinding.inflate(layoutInflater)
        setContentView(binding.root)
        injectDependecy()
        setupView()
    }

    private fun setupBubble() {
        try {
            bubbleSaldoEmas = BubbleShowCaseBuilder(this@DashboardEmasActivity)
                .title(getString(R.string.title_bubble_emas))
                .description(getString(R.string.desc_bubble_emas))
                .backgroundColor(Color.WHITE)
                .textColor(GeneralHelper.getColor(R.color.neutral_dark40))
                .textColorDesc(GeneralHelper.getColor(R.color.neutral_dark10))
                .targetView(binding.llSaldoEmas)
                .buttonTitle(getString(R.string.btn_tutup))
                .enableLewati(true)
                .arrowPosition(BubbleShowCase.ArrowPosition.TOP)
                .enableViewButtonSkip(false)

            bubbleSaldoEmas.show()
        } catch (e: Exception) {

        }
    }

    private fun setupView() {
        binding.rlLihatDetaiRekeningEmas.setOnClickListener(this)
        binding.rlLihatGrafik.setOnClickListener(this)
        binding.llAmbilFisikEmas.setOnClickListener(this)
        binding.llRiwayat.setOnClickListener(this)
        binding.swipeRefresh.setOnRefreshListener(this)
        GeneralHelper.setToolbarRevamp(
            this,
            binding.tbDashboard.toolbar,
            GeneralHelper.getString(R.string.tb_rekening_emas)
        )
        showSkeleton()
        binding.btnSubmitBeli.setOnClickListener(this)
        binding.btnSubmitJual.setOnClickListener(this)
        binding.btnSubmitBeli.isEnabled = false
        binding.btnSubmitJual.isEnabled = false
        binding.btnSubmitBeli.setTextColor(resources.getColor(R.color.whiteColor))
        binding.btnSubmitBeli.setBackgroundResource(R.drawable.rounded_button_disabled_revamp)
        binding.btnSubmitJual.setTextColor(resources.getColor(R.color.whiteColor))
        binding.btnSubmitJual.setBackgroundResource(R.drawable.rounded_button_disabled_revamp)
        binding.rlRefreshPortofolio.visibility = View.GONE
    }

    private fun injectDependecy() {
        activityComponent.inject(this)
        presenter.view = this
        presenter.setUrlDashboard(GeneralHelper.getString(R.string.url_onboard_emas))
        presenter.setUrlPortofolio(GeneralHelper.getString(R.string.url_dashboard_portofolio))
        presenter.setUrlRate(GeneralHelper.getString(R.string.url_dashboard_rates))
        presenter.setUrlMonthly(GeneralHelper.getString(R.string.url_dashboard_monthly))
        presenter.setUrlPusatBantuan(GeneralHelper.getString(R.string.url_safety_pusat_bantuan))
        presenter.setUrlDetailEmas(GeneralHelper.getString(R.string.url_detail_emas))
        presenter.setUrlGetFormBeli(GeneralHelper.getString(R.string.url_gold_buy_form))
        presenter.setUrlFormJual(GeneralHelper.getString(R.string.url_jual_inquiry))
        presenter.setUrlGrafikJual(GeneralHelper.getString(R.string.url_grafik_emas))
        presenter.setUrlRiwayatTransaksi(GeneralHelper.getString(R.string.url_riwayat_emas))
        presenter.setUrlAmbilFisikEmas(GeneralHelper.getString(R.string.url_ambil_fisik_emas))
        presenter.setUrlRiwayatAmbilFisikEmas(getString(R.string.url_riwayat_ambil_fisik_emas))

        if (isFromInvestasi){
            presenter.getBeliEmas()
        }
        presenter.getDataDashboardEmas()
        presenter.start()
    }

    override fun onSuccessGetDasboardData() {
        presenter.getDataPortofolio()
        presenter.getDataRate()
        presenter.getDataMonthly()
    }

    override fun onSuccessRegistrasiEmas(onboardingSliderResponse: OnboardingSliderResponse) {
        OnboardEmsActivity.launchIntent(this, onboardingSliderResponse)
    }

    override fun onGoldAccountNotActive(response: DashboardEmasPortofolioResponse) {
        OpenBottomSheetGeneralFragment.showDialogInformation(
            supportFragmentManager,
            "",
            "img_counter_limit",
            response.content?.infoDrawerGoldAccount?.title.orEmpty(),
            response.content?.infoDrawerGoldAccount?.desc.orEmpty(),
            {
                presenter.getDataDashboardEmas()
                showSkeleton()

                with(binding) {
                    rlRefreshPortofolio.gone()
                    rlRefreshRates.gone()
                    rlRefreshMonthly.gone()
                }

            },
            false,
            GeneralHelper.getString(R.string.mengerti)
        )
    }

    override fun onSuccessPortofolio(dashboardEmasPortofolioResponse: DashboardEmasPortofolioResponse) {
        binding.swipeRefresh.setRefreshing(false)
        binding.ivInfoSaldo.setOnClickListener(this)
        skeletonScreen!!.hide()
        presenter.showBubbleDashboardEmas()
        dataPortofolio = dashboardEmasPortofolioResponse
        binding.layoutSaldoEmas.visibility = View.VISIBLE
        binding.rlRefreshPortofolio.visibility = View.GONE
        binding.totalSaldoEmas.text = dashboardEmasPortofolioResponse.portofolio!!.balanceString
        binding.tvTotalGram.text = dashboardEmasPortofolioResponse.portofolio!!.goldBalanceString
        binding.tvProfit.text = dashboardEmasPortofolioResponse.portofolio!!.profitString
        if (dashboardEmasPortofolioResponse.portofolio!!.profit == 0.00) {
            binding.ivProfit.setImageDrawable(resources.getDrawable(R.drawable.ic_no_saldo))
            binding.rlHarga.background = ContextCompat.getDrawable(this, R.drawable.bg_blue_full)
        } else {
            if (dashboardEmasPortofolioResponse.portofolio!!.profitStatus == 0) {
                binding.ivProfit.setImageDrawable(resources.getDrawable(R.drawable.ic_arrow_circle_down))
                binding.rlHarga.background =
                    ContextCompat.getDrawable(this, R.drawable.bg_red_border)
            } else {
                binding.ivProfit.setImageDrawable(resources.getDrawable(R.drawable.ic_arrow_circle_up))
                binding.rlHarga.background =
                    ContextCompat.getDrawable(this, R.drawable.bg_green_border)
            }
        }
        binding.btnSubmitBeli.isEnabled = true
        binding.btnSubmitJual.isEnabled = true
        binding.btnSubmitBeli.setTextColor(resources.getColor(R.color.whiteColor))
        binding.btnSubmitBeli.setBackgroundResource(R.drawable.rounded_button_blue)
        binding.btnSubmitJual.setTextColor(resources.getColor(R.color.whiteColor))
        binding.btnSubmitJual.setBackgroundResource(R.drawable.rounded_button_orange)

        setupRedmark()

        if(dashboardEmasPortofolioResponse.portofolio?.ongoingTrx != 0){
            binding.rvOngoingTrx.visibility = View.VISIBLE
            binding.ivBorderOngoingTrx.visibility = View.VISIBLE
            binding.tvOngoingTrx.visibility = View.VISIBLE
            binding.tvOngoingTrx.text = dashboardEmasPortofolioResponse.portofolio?.ongoingTrx.toString()
        } else {
            binding.ivBorderOngoingTrx.visibility = View.GONE
            binding.rvOngoingTrx.visibility = View.GONE
        }
    }

    private fun setupRedmark() {
        if (brImoPrefRepository?.isFirstTimeAmbilFisik == true) {
            binding.ivAmbilFisikCircleRed.visibility = View.VISIBLE
        } else {
            binding.ivAmbilFisikCircleRed.visibility = View.GONE
        }
    }

    override fun onSuccessRate(dashboardEmasRateResponse: DashboardEmasRateResponse) {
        binding.swipeRefresh.setRefreshing(false)
        skeletonScreen1!!.hide()
        binding.layoutHargaEmas.visibility = View.VISIBLE
        binding.rlRefreshRates.visibility = View.GONE
        binding.tvHargaBeli.text = dashboardEmasRateResponse.rates!!.buyRateString
        binding.tvHargaJual.text = dashboardEmasRateResponse.rates.sellRateString
        binding.tvHargaBeliDivider.text = dashboardEmasRateResponse.rates.dividerString
        binding.tvHargaJualDivider.text = dashboardEmasRateResponse.rates.dividerString
    }

    override fun onSuccessMonthly(dashboardEmasMonthlyResponse: DashboardEmasMonthlyResponse) {
        binding.swipeRefresh.setRefreshing(false)
        binding.ivInfoAktivitas.setOnClickListener(this)
        dataMonthly = dashboardEmasMonthlyResponse
        skeletonScreen2!!.hide()
        binding.layoutMonthlyEmas.visibility = View.VISIBLE
        binding.rlRefreshMonthly.visibility = View.GONE

        binding.tvHargaBeliRp.text = dashboardEmasMonthlyResponse.monthlyActivity!!.totalBuyString
        binding.tvHargaBeliGram.text = dashboardEmasMonthlyResponse.monthlyActivity.goldBuyString
        binding.tvHargaJualRp.text = dashboardEmasMonthlyResponse.monthlyActivity.totalSellString
        binding.tvHargaJualGram.text = dashboardEmasMonthlyResponse.monthlyActivity.goldSellString
    }

    override fun onSuccessDetailEmas(detailEmasResponse: DetailEmasResponse) {
        DetailRekeningEmasActivity.launchIntent(this, detailEmasResponse)
    }

    override fun onException02(onOnExceptionWH: onExceptionWH) {
        dialog = DialogFragmentRc02.newInstance(
            this,
            onOnExceptionWH
        )
        dialog!!.show(supportFragmentManager, "")
    }

    override fun onSafetyMode(response: SafetyModeDrawerResponse) {
        val bottomFragmentSafteyMode = BottomFragmentSafteyMode(
            this,
            response.drawerContent!!.title,
            response.drawerContent!!.description,
            getString(R.string.txt_saya_mengerti),
            getString(R.string.txt_pelajari_lebih_lanjut_rekening),
            Constant.OPEN_ACCOUNT_S3f
        )
        bottomFragmentSafteyMode.show(supportFragmentManager, "")
    }

    override fun exceptionEODEOM(response: onExceptionWH) {
        dialog = DialogFragmentRc02.newInstance(
            this,
            response
        )
        dialog!!.show(supportFragmentManager, "")
    }

    override fun onSuccessGetPusatBantuan(response: QuestionResponse) {
        DetailPusatBantuanActivity.launchIntent(this, response, response.topicName)
    }

    override fun onExceptionPortofolio12() {
        binding.swipeRefresh.setRefreshing(false)
        skeletonScreen!!.hide()
        binding.layoutSaldoEmas.visibility = View.GONE
        binding.rlRefreshPortofolio.visibility = View.VISIBLE
        binding.rlRefreshPortofolio.setOnClickListener(View.OnClickListener {
            presenter.getDataPortofolio()
            binding.rlRefreshPortofolio.visibility = View.GONE
            onShowSkeletonPortofolio()
        })

    }

    override fun onExceptionRate12() {
        binding.swipeRefresh.setRefreshing(false)
        skeletonScreen1!!.hide()
        binding.layoutHargaEmas.visibility = View.GONE
        binding.rlRefreshRates.visibility = View.VISIBLE
        binding.rlRefreshRates.setOnClickListener(View.OnClickListener {
            presenter.getDataRate()
            binding.rlRefreshRates.visibility = View.GONE
            onShowSkeletonRates()
        })

    }

    override fun onExceptionMonthly12() {
        binding.swipeRefresh.setRefreshing(false)
        skeletonScreen2!!.hide()
        binding.layoutMonthlyEmas.visibility = View.GONE
        binding.rlRefreshMonthly.visibility = View.VISIBLE
        binding.rlRefreshMonthly.setOnClickListener(View.OnClickListener {
            presenter.getDataMonthly()
            binding.rlRefreshMonthly.visibility = View.GONE
            onShowSkeletonMonthly()
        })
    }

    override fun onShowSkeletonPortofolio() {
        skeletonScreen = Skeleton.bind(binding.layoutSaldoEmas)
            .shimmer(true)
            .angle(20)
            .duration(1200)
            .load(R.layout.skeleton_portofolio_emas)
            .show()
    }

    override fun onShowSkeletonMonthly() {
        skeletonScreen2 = Skeleton.bind(binding.layoutMonthlyEmas)
            .shimmer(true)
            .angle(20)
            .duration(1200)
            .load(R.layout.skeleton_monthly_emas)
            .show()
    }

    override fun onShowSkeletonRates() {
        skeletonScreen1 = Skeleton.bind(binding.layoutHargaEmas)
            .shimmer(true)
            .angle(20)
            .duration(1200)
            .load(R.layout.skeleton_rates_emas)
            .show()
    }

    override fun onSuccessFormJualEmas(response: InquiryOpenEmasResponse) {
        FormJualEmasActivity.launchIntent(this, response)
    }


    override fun onSuccessFormBeliEmas(response: TopupEmasResponse) {
//        FormBeliEmasActivity.launchIntent(this,response,false,isFromInvestasi)
        FormBeliEmasActivity.launchIntent(this,response)

    }

    override fun onSuccessGrafikEmas(response: GrafikEmasResponse) {
        GrafikEmasInfoActivity.launchIntent(this, response)
    }

    override fun onSuccessRiwayatTransaksi(response: RiwayatTransaksiEmasResponse) {
        responseRiwayatTrx = response
        requestRiwayatAmbilFisik =  RiwayatFilterRequest("","","")
        presenter.getRiwayatAmbilFisikEmas(requestRiwayatAmbilFisik)
    }

    override fun hideSkeletonPortofolio() {
        skeletonScreen!!.hide()
    }

    override fun hideSkeletonRate() {
        skeletonScreen1!!.hide()
    }

    override fun hideSkeletonMonthly() {
        skeletonScreen2!!.hide()
    }

    override fun onException58(response: InfoResponse) {
        dialog58 = DialogCase58.newInstance(
            this,
            response.title!!,
            response.desc!!,
            GeneralHelper.getString(R.string.saya_mengerti)
        )
        dialog58!!.show(supportFragmentManager, "")
    }

    override fun showBubbleDashboardEmas() {
        setupBubble()
    }

    override fun onSuccessAmbilFisikEmas(response: InquiryAmbilFisikEmasResponse) {
        if (response.canAmbilFisik == true) {
            InquiryCetakEmasActivity.launchIntent(this@DashboardEmasActivity, response)
        } else {
            OpenBottomSheetGeneralFragment.showDialogInformationHtmlSubtitle(
                fragmentManager = supportFragmentManager,
                imgPath = "",
                imgName = "",
                titleTxt = response.popupFailed?.title.orEmpty(),
                subTitleTxt = response.popupFailed?.description.orEmpty(),
                btnFirstFunction = {},
                isClickableOutside = false,
                firstBtnTxt = getString(R.string.txt_saya_mengerti)
            )
        }
    }

    override fun onSuccessGetRiwayatAmbilFisik(response: RiwayatAmbilFisikEmasResponse) {
        responseRiwayatTrx?.let { RiwayatTransaksiEmasActivity.launchIntent(this, it,response) }
    }

    override fun onClickDialogRc02() {
        dialog!!.dismiss()
    }

    override fun onClickToSafety() {
        presenter.getPusatBantuanSafety(Constant.ID_PUSAT_BANTUAN_SAFETY_MODE)
    }

    override fun onClick(p0: View?) {
        when (p0!!.id) {
            R.id.rl_lihat_detai_rekening_emas -> {
                presenter.getDetailEmas()
            }
            R.id.btnSubmitBeli -> {
                setEventAppsFlyerClickedBuy()
                presenter.getBeliEmas()
            }

            R.id.btnSubmitJual -> {
                setEventAppsFlyerClickedSell()
                presenter.getJualEmas()
            }

            R.id.rl_lihat_grafik -> {
                presenter.getDataGrafikJual(GrafikEmasRequest("INFO"))
            }
            R.id.iv_info_saldo -> {
                onClickSaldoEmas = BottomFragmentWithDataView.newInstance(
                    dataPortofolio!!.content!!.descParameter!!.title,
                    dataPortofolio!!.content!!.descParameter!!.desc,
                    GeneralHelper.getString(R.string.btn_tutup),
                    dataPortofolio!!.content!!.descParameter!!.amountDetail,
                    dataPortofolio!!.content!!.descParameter!!.amountTotal
                )
                onClickSaldoEmas!!.show(supportFragmentManager, "")
            }

            R.id.iv_info_aktivitas -> {
                onClickAktivitas = BottomFragmentNoImgDescHtml.newInstance(
                    dataMonthly!!.content!!.monthlyActivityInfo!!.title!!,
                    dataMonthly!!.content!!.monthlyActivityInfo!!.desc!!,
                    GeneralHelper.getString(R.string.btn_tutup)
                )
                onClickAktivitas!!.show(supportFragmentManager, "")
            }
            R.id.ll_ambil_fisik_emas -> {
                brImoPrefRepository?.saveIsFirstAmbilFisik(false)
                presenter.getDataAmbilFisikEmas()
            }
            R.id.ll_riwayat -> {
                presenter.getDataRiwayatTransaksi()
            }
        }
    }

    private fun setEventAppsFlyerClickedSell() {
        val eventValue: MutableMap<String, Any> = HashMap()
        eventValue[Constant.CUSTOMER_ID] = presenter.persistenceId
        trackAppsFlyerAnalyticEvent("emas_jual", eventValue)
    }

    private fun setEventAppsFlyerClickedBuy() {
        val eventValue: MutableMap<String, Any> = HashMap()
        eventValue[Constant.CUSTOMER_ID] = presenter.persistenceId
        trackAppsFlyerAnalyticEvent("emas_beli", eventValue)
    }

    fun showSkeleton() {
        skeletonScreen = Skeleton.bind(binding.layoutSaldoEmas)
            .shimmer(true)
            .angle(20)
            .duration(1200)
            .load(R.layout.skeleton_portofolio_emas)
            .show()
        skeletonScreen1 = Skeleton.bind(binding.layoutHargaEmas)
            .shimmer(true)
            .angle(20)
            .duration(1200)
            .load(R.layout.skeleton_rates_emas)
            .show()
        skeletonScreen2 = Skeleton.bind(binding.layoutMonthlyEmas)
            .shimmer(true)
            .angle(20)
            .duration(1200)
            .load(R.layout.skeleton_monthly_emas)
            .show()
    }

    override fun onActivityResult(requestCode: Int, resultCode: Int, data: Intent?) {
        super.onActivityResult(requestCode, resultCode, data)

        if (requestCode == Constant.REQ_PAYMENT && resultCode == RESULT_OK && data != null) {
            showSkeleton()
            onSuccessGetDasboardData()
            setResult(RESULT_OK, data)
        } else if(requestCode == REQ_AMBIL_FISIK && resultCode == RESULT_OK && data != null) {
            val reffnum = data.getStringExtra(Constant.TAG_REFFNUM)
            if (reffnum != null){
                StatusAmbilFisikActivity.launchIntent(this,reffnum.orEmpty(),
                    DetailAmbilEmasFisikResponse(),true
                )
            }

        } else if (requestCode == REQ_AMBIL_FISIK && resultCode == RESULT_CANCELED && data != null){
            if (data.getStringExtra(Constant.TAG_ERROR_MESSAGE) != null){
                showSnackbarErrorMessage(
                    data.getStringExtra(Constant.TAG_ERROR_MESSAGE),
                    ALERT_ERROR,
                    this,
                    false
                )
                showSkeleton()
                onSuccessGetDasboardData()
            }
        }
        else if(requestCode == Constant.REQ_PAYMENT && resultCode == RESULT_CANCELED && data != null){
            if (data.getStringExtra(Constant.TAG_ERROR_MESSAGE) != null){
                showSnackbarErrorMessage(
                    data.getStringExtra(Constant.TAG_ERROR_MESSAGE),
                    ALERT_ERROR,
                    this,
                    false
                )
                showSkeleton()
                onSuccessGetDasboardData()
            } else {
                showSkeleton()
                onSuccessGetDasboardData()
                this.setResult(RESULT_CANCELED, data)
            }

        } else if (requestCode == Constant.REQ_BUKA_REKENING && resultCode == Constant.REQ_UPDATE && data != null) {
            finish()
        } else if (resultCode == RESULT_OK && data != null) {
            this.setResult(Constant.REQ_UPDATE, data)
            finish()
        } else if (resultCode == RESULT_FIRST_USER && data != null) {
            finish()
        } else {
            onSuccessGetDasboardData()
            this.setResult(RESULT_CANCELED, data)
        }
    }

    override fun onRefresh() {
        showSkeleton()
        binding.rlRefreshPortofolio.visibility = View.GONE
        binding.rlRefreshRates.visibility = View.GONE
        binding.rlRefreshMonthly.visibility = View.GONE
        onSuccessGetDasboardData()
    }

    override fun onResume() {
        super.onResume()
        setupRedmark()
        if (presenter == null) {
            injectDependecy()
        }

    }

    override fun onDestroy() {
        super.onDestroy()
        presenter.stop()
    }

    override fun onClickDialogRc58() {
        dialog58!!.dismiss()
    }
}