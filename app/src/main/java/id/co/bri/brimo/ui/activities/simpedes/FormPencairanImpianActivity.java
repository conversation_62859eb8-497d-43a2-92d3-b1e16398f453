package id.co.bri.brimo.ui.activities.simpedes;

import android.app.Activity;
import android.content.Intent;
import android.os.Bundle;
import android.view.View;

import androidx.annotation.Nullable;

import com.google.gson.Gson;

import javax.inject.Inject;

import id.co.bri.brimo.R;
import id.co.bri.brimo.contract.IPresenter.simpedes.IPencairanSimpedesPresenter;
import id.co.bri.brimo.contract.IView.simpedes.IPencairanSimpedesView;
import id.co.bri.brimo.databinding.ActivityFormPencairanImpianBinding;
import id.co.bri.brimo.domain.config.Constant;
import id.co.bri.brimo.domain.helpers.GeneralHelper;
import id.co.bri.brimo.domain.helpers.textwatcher.AmountFormatWatcher;
import id.co.bri.brimo.models.ParameterKonfirmasiModel;
import id.co.bri.brimo.models.apimodel.response.GeneralConfirmationResponse;
import id.co.bri.brimo.models.apimodel.response.SimpedesPencairanResponse;
import id.co.bri.brimo.ui.activities.base.BaseActivity;

public class FormPencairanImpianActivity extends BaseActivity implements
        IPencairanSimpedesView,
        AmountFormatWatcher.onAmountChange {

    private ActivityFormPencairanImpianBinding binding;

    protected static final String TAG_RESPONSE = "response";
    protected static final String TAG_PARENT_ACCOUNT = "parent_account";
    protected static final String TAG_ACCOUNT = "account";

    @Inject
    IPencairanSimpedesPresenter<IPencairanSimpedesView> presenter;

    protected Long nominalPencairan;
    protected String sAccount;
    protected String sParentAccount;
    protected SimpedesPencairanResponse pencairanResponse;

    public static void launchIntent(Activity caller, String parentAccount, String account, String response) {
        Intent intent = new Intent(caller, FormPencairanImpianActivity.class);
        intent.putExtra(TAG_RESPONSE, response);
        intent.putExtra(TAG_PARENT_ACCOUNT, parentAccount);
        intent.putExtra(TAG_ACCOUNT, account);
        caller.startActivityForResult(intent, Constant.REQ_PAYMENT);
    }

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        binding = ActivityFormPencairanImpianBinding.inflate(getLayoutInflater());
        setContentView(binding.getRoot());

        if (getIntent().getExtras() != null) {
            if (getIntent().getExtras().getString(TAG_RESPONSE) != null)
                pencairanResponse = new Gson().fromJson(getIntent().getExtras().getString(TAG_RESPONSE), SimpedesPencairanResponse.class);
            if (getIntent().getExtras().getString(TAG_ACCOUNT) != null)
                sAccount = getIntent().getExtras().getString(TAG_ACCOUNT);
            if (getIntent().getExtras().getString(TAG_PARENT_ACCOUNT) != null)
                sParentAccount = getIntent().getExtras().getString(TAG_PARENT_ACCOUNT);

        }

        GeneralHelper.setToolbar(this, binding.toolbar.toolbar, "Pencairan Sebagian");

        injectDependency();

        setupView();

        binding.btLanjut.setOnClickListener(v -> presenter.getDataKonfirmasi(sParentAccount, sAccount, nominalPencairan));
    }

    private void injectDependency() {
        getActivityComponent().inject(this);
        if (presenter != null) {
            presenter.setView(this);
            presenter.start();
            presenter.setUrlKonfirmasi(GeneralHelper.getString(R.string.url_s3f_withdraw_confirmation));
        }
    }

    private void setupView() {
        String initial = GeneralHelper.formatInitialName(pencairanResponse.getDreamName());

        binding.tvInisial.setText(initial);
        binding.tvNama.setText(pencairanResponse.getDreamName());
        binding.tvSaldo.setText(pencairanResponse.getAvailableBalance());
        showAmountMinimal(pencairanResponse);
        binding.etNominal.addTextChangedListener(new AmountFormatWatcher(binding.etNominal, this, false));
    }

    @Override
    public void onAmountChange(String amount) {
        nominalPencairan = Long.parseLong(binding.etNominal.getText().toString().replace(".", "").replace("Rp", "").replace("-", ""));
        countpenarikan(nominalPencairan);
    }

    private void countpenarikan(Long nominalSimpedes) {
        if (nominalSimpedes != 0) {
            if (nominalSimpedes < pencairanResponse.getMinWithdraw()) {
                showAmountMinimal(pencairanResponse);
                binding.layoutError.setVisibility(View.VISIBLE);
                enableButton(false);
            } else if (nominalSimpedes > pencairanResponse.getMaxWithdraw()) {
                showAmountInsuficient();
                binding.layoutError.setVisibility(View.VISIBLE);
                enableButton(false);
            } else {
                binding.layoutError.setVisibility(View.GONE);
                enableButton(true);
            }
        } else {
            enableButton(false);
        }
    }

    /**
     * Show parameter minimal di field
     *
     * @param pencairanResponse Parameter Model
     */
    protected void showAmountMinimal(SimpedesPencairanResponse pencairanResponse) {
        if (pencairanResponse.getMinWithdraw() != null) {
            binding.tvError.setText(String.format("Minimal Pencairan Rp%s", GeneralHelper.formatNominal(pencairanResponse.getMinWithdraw())));
            binding.tvError.setTextColor(getResources().getColor(R.color.colorErrorMinTrx));
        }
    }

    /**
     * Show parameter saldo tidak cukup
     */
    protected void showAmountInsuficient() {
        binding.tvError.setText("Jumlah pencairan melebihi batas saldo impian");
        binding.tvError.setTextColor(getResources().getColor(R.color.red));
    }

    @Override
    public void setAmountListener() {
        // Do nothing
    }

    protected void enableButton(boolean isEnable) {
        if (isEnable) {
            binding.btLanjut.setAlpha(1);
            binding.btLanjut.setEnabled(true);
        } else {
            binding.btLanjut.setAlpha(0.3f);
            binding.btLanjut.setEnabled(false);
        }
    }

    public ParameterKonfirmasiModel setParameterKonfirmasi() {
        ParameterKonfirmasiModel parameterKonfirmasiModel = new ParameterKonfirmasiModel();

        parameterKonfirmasiModel.setStringLabelTujuan("Nomor Tujuan");
        parameterKonfirmasiModel.setStringButtonSubmit("Cairkan");

        return parameterKonfirmasiModel;
    }

    @Override
    public void getSuccessData(GeneralConfirmationResponse confirmationResponse) {
        KonfirmasiSimpedesActivity.launchIntent(this, confirmationResponse, GeneralHelper.getString(R.string.url_s3f_withdraw_payment), "Konfirmasi", setParameterKonfirmasi(), false, false, false);
    }

    @Override
    protected void onActivityResult(int requestCode, int resultCode, @Nullable Intent data) {
        super.onActivityResult(requestCode, resultCode, data);

        if (resultCode == RESULT_OK && data != null) {
            setResult(RESULT_OK, data);
            finish();
        } else {
            if (data != null) {
                setResult(RESULT_CANCELED, data);
                finish();
            }
        }
    }

    @Override
    protected void onDestroy() {
        super.onDestroy();
        binding = null;
    }
}