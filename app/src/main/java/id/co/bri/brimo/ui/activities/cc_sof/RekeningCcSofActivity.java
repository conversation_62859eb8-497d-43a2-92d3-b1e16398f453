package id.co.bri.brimo.ui.activities.cc_sof;

import android.app.Activity;
import android.content.Intent;
import android.os.Bundle;
import android.util.Log;
import android.view.View;
import android.widget.Button;
import android.widget.LinearLayout;
import android.widget.RelativeLayout;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.appcompat.widget.Toolbar;
import androidx.fragment.app.FragmentTransaction;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;
import androidx.swiperefreshlayout.widget.SwipeRefreshLayout;

import com.ethanhua.skeleton.Skeleton;
import com.ethanhua.skeleton.SkeletonScreen;
import com.eyalbira.loadingdots.LoadingDots;
import com.google.gson.Gson;

import java.util.ArrayList;
import java.util.List;

import javax.inject.Inject;

import id.co.bri.brimo.R;
import id.co.bri.brimo.adapters.ListCcSofAdapter;
import id.co.bri.brimo.contract.IPresenter.cc_sof.IRekeningCcSofPresenter;
import id.co.bri.brimo.contract.IView.cc_sof.IRekeningCcSofView;
import id.co.bri.brimo.databinding.ActivityRekeningCcSofBinding;
import id.co.bri.brimo.domain.config.Constant;
import id.co.bri.brimo.domain.helpers.GeneralHelper;
import id.co.bri.brimo.models.apimodel.response.cc.DetailCcSofResponse;
import id.co.bri.brimo.models.apimodel.response.cc.LimitCcSofResponse;
import id.co.bri.brimo.models.apimodel.response.ListCcSofResponse;
import id.co.bri.brimo.models.apimodel.response.SnkResponse;
import id.co.bri.brimo.ui.activities.FastMenuActivity;
import id.co.bri.brimo.ui.activities.base.BaseActivity;
import id.co.bri.brimo.ui.fragments.BottomFragmentImage;
import id.co.bri.brimo.ui.fragments.BottomFragmentInfoImage;

public class RekeningCcSofActivity extends BaseActivity implements
        IRekeningCcSofView,
        SwipeRefreshLayout.OnRefreshListener,
        ListCcSofAdapter.SetTotalLimitInterface,
        View.OnClickListener,
        ListCcSofAdapter.OnClickItem,
        BottomFragmentImage.OnCallBackBottomImage,
        BottomFragmentInfoImage.OnCallBackBottomInfoImage {

    private ActivityRekeningCcSofBinding binding;

    ArrayList<ListCcSofResponse.Account> listCcSof = new ArrayList<>();
    ListCcSofAdapter listCcSofAdapter;

    double totalLimit = 0;

    @Inject
    IRekeningCcSofPresenter<IRekeningCcSofView> activityPresenter;

    private boolean isLoading = false;
    private String currentCurrency = "Rp";
    private SkeletonScreen skeletonScreen;
    private List<ListCcSofResponse.Account> listFiltered;

    LimitCcSofResponse limitCcSofResponse;

    public static void launchIntent(Activity caller) {
        Intent intent = new Intent(caller, RekeningCcSofActivity.class);
        caller.startActivityForResult(intent, Constant.REQ_SET_DEFAULT);
    }

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        binding = ActivityRekeningCcSofBinding.inflate(getLayoutInflater());
        setContentView(binding.getRoot());

        //inject presenter
        injectDependency();

        setupLayout();
    }

    private void setupLayout() {
        GeneralHelper.setToolbar(this, binding.tbRekening.toolbar, GeneralHelper.getString(R.string.toolbar_cc));

        LinearLayoutManager linearLayoutManager = new LinearLayoutManager(this);
        binding.rvRekening.setLayoutManager(linearLayoutManager);
        binding.rvRekening.setHasFixedSize(true);

        listCcSofAdapter = new ListCcSofAdapter(this, listCcSof, this, this, limitCcSofResponse);
        skeletonScreen = Skeleton.bind(binding.rvRekening)
                .adapter(listCcSofAdapter)
                .shimmer(true)
                .angle(20)
                .frozen(false)
                .duration(1200)
                .load(R.layout.item_skeleton_list_rekening)
                .show(); //default count is 10

        binding.swipeRefreshRekening.setOnRefreshListener(this);

        binding.btnRekeningBaru.setOnClickListener(this);
    }

    /**
     * @param currency
     * @param total    Update total Limit dari saldo yang sudah di dapat
     */
    protected void updateTotalSaldo(String currency, double total) {
        String tempCurrency;

        if (total < 0) {
            tempCurrency = "-" + currency;
            binding.totalSaldoIb.setText(GeneralHelper.formatNominalIDR(total));
        } else if (total == 0.0) {
            tempCurrency = currency;
            binding.totalSaldoIb.setText("-");
        } else {
            tempCurrency = currency;
            binding.totalSaldoIb.setText(GeneralHelper.formatNominalIDR(total));
        }

        binding.tvCurrency.setText(tempCurrency);

        binding.totalSaldoIb.setText(GeneralHelper.formatNominalIDR(totalLimit));

    }

    @Override
    public void onListCcSof(ListCcSofResponse listCcSofResponse) {
        skeletonScreen.hide();
        binding.rlListCc.setVisibility(View.VISIBLE);
        binding.rlListKosong.setVisibility(View.GONE);
        listCcSof = listCcSofResponse.getAccount();
        listCcSofAdapter.setItems(listCcSof);
        listCcSofAdapter.notifyDataSetChanged();
        binding.rvRekening.scheduleLayoutAnimation();
    }

    @Override
    public void onSuccessTerm(SnkResponse snkResponse) {
        TambahKartuCcActivity.launchIntent(this, snkResponse.getSnk());
    }

    @Override
    public void showProgressTerm() {
        GeneralHelper.showDialog(this);
    }

    @Override
    public void hideProgressTerm() {
        GeneralHelper.dismissDialog();
    }

    @Override
    public void onGetLimit(List<ListCcSofResponse.Account> accountList, boolean isRefreshed) {
        double totalSaldo = 0.0;

        for (ListCcSofResponse.Account account : accountList) {
            DetailCcSofResponse detailLimit = account.getDetailCcSofResponse();
            if (detailLimit != null
                    && account.getCurrency().equalsIgnoreCase(accountList.get(0).getCurrency())) {
                if (detailLimit.getBalance() == null) {
                    binding.totalSaldoIb.setText("-");
                } else {
                    totalSaldo = totalSaldo + detailLimit.getBalance();
                }

            }

            currentCurrency = accountList.get(0).getCurrency();
            updateTotalSaldo(currentCurrency, totalSaldo);
        }

        listCcSof = (ArrayList<ListCcSofResponse.Account>) accountList;

        listCcSofAdapter.notifyDataSetChanged();
    }

    @Override
    public void onLimitCheckComplete(LimitCcSofResponse limitCcSofResponse) {
        this.limitCcSofResponse = limitCcSofResponse;
        totalLimit = limitCcSofResponse.getLimit().doubleValue();
        listCcSofAdapter.setLimit(limitCcSofResponse);
        binding.totalSaldoIb.setText(limitCcSofResponse.getCurrency() + limitCcSofResponse.getLimitString());
        listCcSofAdapter.notifyDataSetChanged();
    }

    /**
     * Callback ketika semua saldo selesai didapatkan
     */
    @Override
    public void onGetLimitComplete() {
        isLoading = false;
        binding.swipeRefreshRekening.setEnabled(true);
    }

    /**
     * Callback ketika mendapatkan RC General Error dari Backaend
     */
    @Override
    public void onException12(String message) {
        Intent intentReturn = new Intent();
        intentReturn.putExtra(Constant.TAG_ERROR_MESSAGE, message);
        setResult(RESULT_CANCELED, intentReturn);
        finish();
    }

    @Override
    public void onSuccess01(String message) {
        binding.rlListCc.setVisibility(View.GONE);
        binding.rlListKosong.setVisibility(View.VISIBLE);
        binding.tvDescKosong.setText(message);
    }

    @Override
    public void onExceptionTotalSaldo() {
        binding.totalSaldoIb.setText("-");
    }

    @Override
    public void enableButton(boolean enable) {
        // Do nothing
    }

    @Override
    public void showProgress() {
        isLoading = true;
        binding.swipeRefreshRekening.setEnabled(false);
    }

    @Override
    public void hideProgress() {
        binding.swipeRefreshRekening.setRefreshing(false);

        binding.pbSaldo.setVisibility(View.GONE);
        onAnimator(binding.layoutTotalSaldo, true, ANIMATE_SHOW, Constant.REQUEST_SALDO);
    }

    @Override
    public void onSessionEnd(String message) {
        FastMenuActivity.launchIntentSessionEnd(this, message);
    }

    @Override
    public void onException(String message) {

        binding.totalSaldoIb.setText("-");

        if (GeneralHelper.isContains(Constant.LIST_TYPE_GAGAL, message))
            GeneralHelper.showDialogGagalBack(this, message);
        else
            showSnackbarErrorMessage(message, ALERT_ERROR, this, false);
    }

    private void injectDependency() {
        getActivityComponent().inject(this);

        if (activityPresenter != null) {
            activityPresenter.setView(this);
            activityPresenter.setUrlCheckLimitCcSof(GeneralHelper.getString(R.string.url_cc_sof_check_limit));
            activityPresenter.setUrlListCcSof(GeneralHelper.getString(R.string.url_cc_sof_v3));
            activityPresenter.setUrlDetailCcSof(GeneralHelper.getString(R.string.url_cc_sof_detail_v3));
            activityPresenter.setUrlTerm(GeneralHelper.getString(R.string.url_cc_binding_form));
            activityPresenter.start();
        }
    }

    @Override
    public void onRefresh() {
        if (!isLoading && listFiltered == null) {
            skeletonScreen = Skeleton.bind(binding.rvRekening)
                    .adapter(listCcSofAdapter)
                    .shimmer(true)
                    .angle(20)
                    .frozen(false)
                    .duration(1200)
                    .load(R.layout.item_skeleton_list_rekening)
                    .show(); //default count is 10
            if (limitCcSofResponse == null) {
                activityPresenter.getLimitCcSof();
            }
            activityPresenter.getAccountWithSaldo();
        }
    }


    @Override
    public void onClick(View view) {
        if (view.getId() == R.id.btn_rekening_baru) {
            BottomFragmentImage bottomFragmentImage = new BottomFragmentImage(
                    this, this, "ic_tambah_cc",
                    GeneralHelper.getString(R.string.tambah_kartu_kredit),
                    GeneralHelper.getString(R.string.desc_kartu_kredit),
                    GeneralHelper.getString(R.string.ajukan_kartu_kredit_baru),
                    GeneralHelper.getString(R.string.tambahkan_kartu_kredit));
            FragmentTransaction ft = this.getSupportFragmentManager().beginTransaction();
            ft.add(bottomFragmentImage, null);
            ft.commitAllowingStateLoss();
        }
    }


    @Override
    protected void onActivityResult(int requestCode, int resultCode, @Nullable Intent data) {
        super.onActivityResult(requestCode, resultCode, data);
        if (resultCode == Activity.RESULT_OK && requestCode == Constant.REQ_CC_SOF &&
                data != null && data.hasExtra(Constant.DESCRIPTION)) {
            onRefresh();
        } else if (requestCode == Constant.REQ_UPDATE && resultCode == Activity.RESULT_OK) {
            if (limitCcSofResponse == null) {
                activityPresenter.getLimitCcSof();
            }
            activityPresenter.getAccountWithSaldo();

            BottomFragmentInfoImage bottomFragmentInfoImage = new BottomFragmentInfoImage(
                    this, this, "ic_shopping",
                    GeneralHelper.getString(R.string.kartu_kredit_berhasil_ditambahkan),
                    GeneralHelper.getString(R.string.desc_kartu_kredit_berhasil),
                    GeneralHelper.getString(R.string.oke)
            );
            FragmentTransaction ft = getSupportFragmentManager().beginTransaction();
            ft.add(bottomFragmentInfoImage, null);
            ft.commitAllowingStateLoss();
        }
    }

    @Override
    public void onBackPressed() {
        activityPresenter.stop();
        super.onBackPressed();
    }

    @Override
    public void onClickDetail(DetailCcSofResponse detailCcSofResponse) {
        if (detailCcSofResponse != null) {
            InfoRekeningCcSofActivity.launchIntent(this, detailCcSofResponse);
        }
    }

    @Override
    public void onSetTotalLimit(double total, String currency, List<ListCcSofResponse.Account> listFiltered) {
        currentCurrency = currency;
        updateTotalSaldo(currentCurrency, total);
        this.listFiltered = listFiltered;
    }

    @Override
    public void clickButton(@NonNull String typeButton) {
        switch (typeButton) {
            case BottomFragmentImage.ButtonType.WHITE:
                activityPresenter.getTerm();
                break;
        }
    }

    @Override
    public void onCallInfoImage() {
        // do nothing
    }

    @Override
    protected void onDestroy() {
        super.onDestroy();
    }
}