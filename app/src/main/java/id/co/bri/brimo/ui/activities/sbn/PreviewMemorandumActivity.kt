package id.co.bri.brimo.ui.activities.sbn

import android.annotation.SuppressLint
import android.app.Activity
import android.app.DownloadManager
import android.content.BroadcastReceiver
import android.content.Context
import android.content.Intent
import android.content.IntentFilter
import android.content.pm.PackageManager
import android.net.Uri
import android.os.Build
import android.os.Bundle
import android.os.Environment
import android.util.Log
import android.view.View
import androidx.annotation.RequiresApi
import androidx.core.app.ActivityCompat
import id.co.bri.brimo.R
import id.co.bri.brimo.databinding.ActivityPreviewMemorandumBinding
import id.co.bri.brimo.domain.config.Constant
import id.co.bri.brimo.domain.helpers.GeneralHelper
import id.co.bri.brimo.domain.helpers.PdfWebViewClient
import id.co.bri.brimo.ui.activities.base.BaseActivity
import java.io.File
import java.net.MalformedURLException
import java.net.URL
import java.text.SimpleDateFormat
import java.util.Date
import java.util.Locale


class PreviewMemorandumActivity : BaseActivity(), PdfWebViewClient.onReceivedFeedback {
    lateinit var binding: ActivityPreviewMemorandumBinding
    var isLoaded: Boolean = false
    var downloadManager: DownloadManager? = null
    private var filename: String? = null
    var url: URL? = null
    private var downloadID: Long = 0

    companion object{
        var urlMemo= ""
        @JvmStatic
        fun launchIntent(caller: Activity, data: String) {
            val intent = Intent(caller, PreviewMemorandumActivity::class.java)
            urlMemo = data
            caller.startActivityForResult(intent, Constant.REQ_PAYMENT)
        }
    }

    @RequiresApi(Build.VERSION_CODES.O)
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        binding = ActivityPreviewMemorandumBinding.inflate(layoutInflater)
        setContentView(binding.root)

        registerReceiverDownload()

        setView()
    }

    private fun registerReceiverDownload() {
        val filter = IntentFilter(DownloadManager.ACTION_DOWNLOAD_COMPLETE)
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            registerReceiver(onDownloadMemorandumComplete, filter, Context.RECEIVER_NOT_EXPORTED)
        } else {
            registerReceiver(onDownloadMemorandumComplete, filter)
        }
    }

    @SuppressLint("SetJavaScriptEnabled")
    fun setView(){
        GeneralHelper.setToolbar(this, binding.toolbar.toolbar, "Memorandum Informasi")
        if (urlMemo.isNotEmpty()){
            showProgress()
            val pdfWebViewClient = PdfWebViewClient(this, binding.webview, this, 60)
            pdfWebViewClient.loadPdfUrl(urlMemo)
            binding.lyDownlaod.setOnClickListener {
                requestPermission()
            }
        }
    }

    override fun onBackPressed() {
        if (isLoaded){
            val resultIntentOk = Intent()
            resultIntentOk.putExtra("checkbox", true)
            setResult(Constant.REQ_PETTUNJUK1, resultIntentOk)
            finish()
        }
    }

    fun failedLoadPdf(){
        val resultIntentOk = Intent()
        setResult(Constant.REQ_PETTUNJUK2, resultIntentOk)
        finish()
    }

    fun requestPermission() {
        if (!hasPermissions(this, *PERMISSIONS)) {
            ActivityCompat.requestPermissions(this, PERMISSIONS, PERMISSIONS_ALL)
        } else {
            val urlPdf: String = urlMemo
            try {
                url = URL(urlPdf)
            } catch (e: MalformedURLException) {
                if (!GeneralHelper.isProd()) {
                    Log.e("TAG", "generateTransaksiModel: ", e)
                }
            }

            val timeStamp = SimpleDateFormat("yyyyMMdd_HHmmss", Locale.getDefault()).format(Date())
            val filename2 = url!!.path.substring(url!!.path.lastIndexOf('/') + 1)
            val names: Array<String> = filename2.split("\\.".toRegex()).dropLastWhile { it.isEmpty() }.toTypedArray()
            filename = names[0] + "-" + timeStamp + ".pdf"
            callDownloadManager()
        }
    }

    private fun callDownloadManager() {
        showProgress()
        downloadManager = getSystemService(DOWNLOAD_SERVICE) as DownloadManager
        val request = DownloadManager.Request(Uri.parse(url.toString() + ""))
        request.setAllowedNetworkTypes(DownloadManager.Request.NETWORK_WIFI or
                DownloadManager.Request.NETWORK_MOBILE)
        request.setTitle(filename)
        request.allowScanningByMediaScanner()
        request.setAllowedOverMetered(true)
        request.setNotificationVisibility(DownloadManager.Request.VISIBILITY_VISIBLE_NOTIFY_COMPLETED)
        request.setDestinationInExternalPublicDir(Environment.DIRECTORY_DOWNLOADS, filename)
        request.setMimeType("application/pdf")
        downloadID = downloadManager!!.enqueue(request)
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU) {
            hideProgress()
            val resultIntentOk = Intent()
            resultIntentOk.putExtra("checkbox", true)
            setResult(Constant.REQ_PETTUNJUK1, resultIntentOk)
            finish()
        }
    }

    public val onDownloadMemorandumComplete: BroadcastReceiver = object : BroadcastReceiver() {
        override fun onReceive(context: Context, intent: Intent) {
            hideProgress()
            val id = intent.getLongExtra(DownloadManager.EXTRA_DOWNLOAD_ID, -1)
            if (downloadID == id) {
                val file = File(Environment.getExternalStoragePublicDirectory(Environment.DIRECTORY_DOWNLOADS).toString() + "/" + filename)
                val resultIntentOk = Intent()
                resultIntentOk.putExtra("checkbox", true)
                setResult(Constant.REQ_PETTUNJUK1, resultIntentOk)
                finish()
            }else{
                GeneralHelper.showSnackBar(binding.content, "Dokumen gagal dimuat. Silahkan coba lagi")
            }
        }
    }

    override fun onDestroy() {
        super.onDestroy()
        unregisterReceiver(onDownloadMemorandumComplete)
    }

    override fun onRequestPermissionsResult(requestCode: Int, permissions: Array<out String>, grantResults: IntArray) {
        super.onRequestPermissionsResult(requestCode, permissions, grantResults)

        var grantAll: Boolean = true
        if (grantResults.isNotEmpty()) {
            for (i in grantResults.indices) {
                if (grantResults[i] != PackageManager.PERMISSION_GRANTED) {
                    grantAll = false
                    break
                }
            }
        }

        if (!grantAll) {
            showAlertFinish(getString(R.string.notes_need_permission));
        } else {
            requestPermission();
        }
    }

    override fun onFinished() {
        binding.lyDownlaod.visibility = View.VISIBLE
        binding.webview.visibility = View.VISIBLE
        hideProgress()
        isLoaded = true
    }

    override fun onStarted() {
        binding.webview.visibility = View.INVISIBLE
        binding.lyDownlaod.visibility = View.INVISIBLE
    }

    override fun onError() {
        failedLoadPdf()
    }

}