package id.co.bri.brimo.ui.activities.dashboardrevamp

import android.app.Activity
import android.app.ActivityManager
import android.content.Context
import android.content.Intent
import android.os.Bundle
import androidx.recyclerview.widget.GridLayoutManager
import com.google.gson.Gson
import com.google.gson.reflect.TypeToken
import id.co.bri.brimo.BuildConfig
import id.co.bri.brimo.R
import id.co.bri.brimo.adapters.dashboard.InnerMenuAdapter
import id.co.bri.brimo.contract.IPresenter.revamp.dashboard.IMenuListGeneralPresenter
import id.co.bri.brimo.contract.IView.revamp.dashboard.IMenuListGeneralView
import id.co.bri.brimo.databinding.ActivityMenuListGeneralBinding
import id.co.bri.brimo.domain.config.Constant
import id.co.bri.brimo.domain.config.MenuConfig
import id.co.bri.brimo.domain.helpers.GeneralHelper
import id.co.bri.brimo.models.daomodel.DashboardMenu.MenuDashboard
import id.co.bri.brimo.ui.activities.autograbfund.DashboardAutoGrabFundActivity
import id.co.bri.brimo.ui.activities.base.BaseActivity
import id.co.bri.brimo.ui.activities.ccqrismpm.SofQrisActivity
import javax.inject.Inject
import android.os.SystemClock
import android.view.View
import androidx.core.content.ContextCompat

class MenuListGeneralActivity :
    BaseActivity(), IMenuListGeneralView, InnerMenuAdapter.onClickMenuListener,
    View.OnClickListener {

    @Inject
    lateinit var menuListPresenter: IMenuListGeneralPresenter<IMenuListGeneralView>
    lateinit var binding: ActivityMenuListGeneralBinding
    private lateinit var innerMenuAdapter: InnerMenuAdapter

    val context = this

    companion object {
        private var mTitle: String? = null
        private var mMenu: List<MenuDashboard>? = null
        private var mIdKategori: Int = 0

        fun launchIntent(
            caller: Activity?,
            title: String,
            menu: MutableList<MenuDashboard>?,
            idKategori: Int
        ) {
            val intent = Intent(caller, MenuListGeneralActivity::class.java)
            mTitle = title
            mMenu = menu
            mIdKategori = idKategori
            caller?.startActivityForResult(intent, Constant.REQ_PAYMENT)
        }
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        binding = ActivityMenuListGeneralBinding.inflate(layoutInflater)
        setContentView(binding.root)

        //inject presenter
        injectDependency()

        if (mMenu == null)
            restoreIntentData(savedInstanceState)
        else {
            setupView()
            setupCustomView()
        }

    }

    private fun setupView() {
        GeneralHelper.setToolbarRevamp(this, binding.tbRevamp.toolbar, mTitle)
        binding.rvMenu.layoutManager =
            GridLayoutManager(this, 3, GridLayoutManager.VERTICAL, false)

        mMenu = mMenu?.sortedBy { it.sort }
        innerMenuAdapter = InnerMenuAdapter(context, mMenu!!, this)
        binding.rvMenu.adapter = innerMenuAdapter
    }

    private fun setupCustomView() {
        when (mIdKategori) {
            MenuConfig.MenuKategoriId.TAGIHAN -> {
                binding.clBannerAgf.apply {
                    visibility = View.VISIBLE
                    setTitleBanner(GeneralHelper.getString(R.string.banner_agf_title))
                    setDescriptionBanner(GeneralHelper.getString(R.string.banner_agf_desc))
                    setTextButton(GeneralHelper.getString(R.string.banner_agf_button))
                    ContextCompat.getDrawable(
                        this@MenuListGeneralActivity,
                        R.drawable.ic_ilustrasi_calendar
                    )
                        ?.let { setIconBanner(it) }
                    setOnButtonBannerClickListener { DashboardAutoGrabFundActivity.launchIntent(this@MenuListGeneralActivity) }

                }
            }

            MenuConfig.MenuKategoriId.KARTU_KREDIT -> {
                binding.clBannerAgf.apply {
                    visibility = View.VISIBLE
                    setTitleBanner(GeneralHelper.getString(R.string.qris_payment_can_use_credit_card))
                    setDescriptionBanner(GeneralHelper.getString(R.string.set_credit_card_to_sof_make_not_difficult_every_for_payment))
                    setTextButton(GeneralHelper.getString(R.string.set_now))
                    ContextCompat.getDrawable(
                        this@MenuListGeneralActivity,
                        R.drawable.ic_ilustrasi_credit_card
                    )
                        ?.let { setIconBanner(it) }
                    setOnButtonBannerClickListener { SofQrisActivity.launchIntent(this@MenuListGeneralActivity) }

                }
            }

            else -> Unit
        }
    }


    override fun onActivityResult(requestCode: Int, resultCode: Int, data: Intent?) {
        super.onActivityResult(requestCode, resultCode, data)
        if (requestCode == Constant.REQ_PAYMENT) {
            if (resultCode == RESULT_OK) {
                //membalikan data agar otomatis cek saldo
                setResult(RESULT_OK, data)
                finish()
            } else {
                if (data != null) {
                    setResult(RESULT_CANCELED, data)
                    finish()
                }
            }
        }

    }

    override fun onClick(v: View?) {
        if (SystemClock.elapsedRealtime() - mLastClickTime < 1000) {
            return
        }
    }

    private fun injectDependency() {
        activityComponent.inject(this)
        if (menuListPresenter != null) {
            menuListPresenter.view = this
            menuListPresenter.start()
        }
    }

    override fun onSuccessGetMenu(menuDashboardList: MutableList<MenuDashboard>?) {
        TODO("Not yet implemented")
    }

    override fun onSuccessUpdate(idMenu: Int) {
        mMenu?.map { item ->
            if (item.id == idMenu)
                item.isNew = false
            else item
        }

        val isTrue = mMenu?.any { it.isNew }
        if (isTrue == false) {
            menuListPresenter.updateKategori(mIdKategori)
        }
    }

    override fun onClickMenuDashboar(idMenu: Int, flagNew: Int) {
        menuListPresenter.updateIsNewFalse(idMenu, flagNew)
    }

    private fun restoreIntentData(savedInstanceState: Bundle?){
        try {
            savedInstanceState?.let {
                val gson = Gson()
                val collectionType =
                    object : TypeToken<List<MenuDashboard>>() {}.type
                val jsonString = it.getString(Constant.MENU)

                mTitle = it.getString(Constant.TAG_TITLE)
                mIdKategori = it.getInt(Constant.KATEGORI_ID)
                mMenu = gson.fromJson<List<MenuDashboard>>(
                    jsonString,
                    collectionType
                )
            }
        } catch (e: Exception) {
            finish()
            return
        }
        setupView()
    }

    override fun onSaveInstanceState(outState: Bundle) {
        super.onSaveInstanceState(outState)
        outState.putString(Constant.TAG_TITLE, mTitle)
        outState.putInt(Constant.KATEGORI_ID, mIdKategori)
        outState.putString(Constant.MENU, Gson().toJson(mMenu))
    }

    override fun onDestroy() {
        super.onDestroy()
    }
}