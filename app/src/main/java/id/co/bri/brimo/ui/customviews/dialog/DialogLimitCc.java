package id.co.bri.brimo.ui.customviews.dialog;

import android.app.Dialog;
import android.graphics.drawable.ColorDrawable;
import android.os.Bundle;
import android.view.Window;
import android.widget.Button;
import android.widget.LinearLayout;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.fragment.app.DialogFragment;
import id.co.bri.brimo.R;

public class DialogLimitCc extends DialogFragment {

    TextView tvTitle;
    TextView tvDesc;
    TextView tvPhone;
    LinearLayout llPhone;

    private Dialog alertDialog;
    private Button btnOk;
    private OnClick onClickOK;
    String sTitle, sDesc, sPhone;

    public DialogLimitCc(OnClick onClickOK, String sTitle, String sDesc,String phone) {
        this.onClickOK = onClickOK;
        this.sTitle = sTitle;
        this.sDesc = sDesc;
        this.sPhone = phone;
    }

    @NonNull
    @Override
    public Dialog onCreateDialog(@Nullable Bundle savedInstanceState) {
        alertDialog = new Dialog(getActivity());
        alertDialog.setCanceledOnTouchOutside(false);
        alertDialog.getWindow().requestFeature(Window.FEATURE_NO_TITLE);
        alertDialog.getWindow().setBackgroundDrawable(new ColorDrawable(0));
        alertDialog.setContentView(R.layout.dialog_limit_cc);
        alertDialog.setOnKeyListener((dialogInterface, i, keyEvent) -> false);

        initView();
        alertDialog.show();

        btnOk.setOnClickListener(v -> {
            dismiss();
        });

        llPhone.setOnClickListener(v -> {
            onClickOK.clickContact();
            dismiss();
        });

        tvPhone.setText(sPhone);

        return alertDialog;
    }

    public void initView() {
        btnOk = alertDialog.findViewById(R.id.btnSubmit);
        tvTitle = alertDialog.findViewById(R.id.tv_title_notice);
        tvDesc = alertDialog.findViewById(R.id.tv_desc_notice);
        llPhone = alertDialog.findViewById(R.id.ll_phone_cs);
        tvPhone = alertDialog.findViewById(R.id.tvphone_number);
        tvTitle.setText(sTitle);
        tvDesc.setText(sDesc);
    }

    public interface OnClick {
        void clickContact();
    }
}
