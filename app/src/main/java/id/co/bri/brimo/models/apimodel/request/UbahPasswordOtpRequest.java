package id.co.bri.brimo.models.apimodel.request;

import com.google.gson.annotations.Expose;
import com.google.gson.annotations.SerializedName;

public class UbahPasswordOtpRequest {

    @SerializedName("reference_number")
    @Expose
    String referenceNumber;

    @SerializedName("new_password")
    @Expose
    String newPassKunci;

    public UbahPasswordOtpRequest(String reference_number,String new_password){
        this.referenceNumber = reference_number;
        this.newPassKunci = new_password;
    }

}
