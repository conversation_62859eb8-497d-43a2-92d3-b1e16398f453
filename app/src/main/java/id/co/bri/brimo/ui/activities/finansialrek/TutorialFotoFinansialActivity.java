package id.co.bri.brimo.ui.activities.finansialrek;

import android.app.Activity;
import android.content.Intent;
import android.os.Bundle;
import androidx.annotation.Nullable;
import java.util.Objects;
import id.co.bri.brimo.R;
import id.co.bri.brimo.databinding.ActivityTutorialFotoFinansialBinding;
import id.co.bri.brimo.domain.config.Constant;
import id.co.bri.brimo.domain.helpers.GeneralHelper;
import id.co.bri.brimo.ui.activities.base.BaseActivity;

public class TutorialFotoFinansialActivity extends BaseActivity {

    private ActivityTutorialFotoFinansialBinding binding;

    protected static String sRefnum;

    public static void launchIntent(Activity caller, String referenceNumber) {
        Intent intent = new Intent(caller, TutorialFotoFinansialActivity.class);
        sRefnum = referenceNumber;
        caller.startActivityForResult(intent, Constant.REQ_FINANSIAL);
    }

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        binding = ActivityTutorialFotoFinansialBinding.inflate(getLayoutInflater());
        setContentView(binding.getRoot());

        GeneralHelper.setToolbar(this, binding.toolbar.toolbar, GeneralHelper.getString(R.string.toolbar_aktivasi_rekening_finansial));

        binding.btnSubmit.setOnClickListener(v -> KameraKtpFinansialActivity.launchIntent(this, sRefnum));
    }

    @Override
    protected void onActivityResult(int requestCode, int resultCode, @Nullable Intent data) {
        super.onActivityResult(requestCode, resultCode, data);
        if (requestCode == Constant.REQ_FINANSIAL) {
            if (resultCode == RESULT_OK && data != null) {
                setResult(RESULT_OK, data);
                finish();
            } else {
                if (data != null) {
                    if (data.hasExtra(Constant.TAG_ERROR_MESSAGE_VALIDATE))
                        GeneralHelper.showSnackBar(Objects.requireNonNull(this).findViewById(R.id.content), data.getStringExtra(Constant.TAG_ERROR_MESSAGE_VALIDATE));
                    else if (data.hasExtra(Constant.TAG_START_NAME))
                        sRefnum = data.getStringExtra(Constant.TAG_START_NAME);
                    else {
                        setResult(RESULT_CANCELED, data);
                        finish();
                    }
                } else
                    setResult(RESULT_CANCELED);

            }
        }
    }

    @Override
    protected void onDestroy() {
        super.onDestroy();
        binding = null;
    }
}