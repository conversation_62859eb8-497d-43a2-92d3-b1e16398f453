package id.co.bri.brimo.ui.customviews.dialog;

import android.app.Dialog;
import android.graphics.drawable.ColorDrawable;
import android.os.Bundle;
import android.text.Html;
import android.view.Window;
import android.widget.Button;
import android.widget.ImageView;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.fragment.app.DialogFragment;
import id.co.bri.brimo.R;
import id.co.bri.brimo.domain.helpers.bubbleShowCaseView.BubbleShowCaseSequence;

public class DialogWithHtml extends DialogFragment {
    private Dialog alertDialog;
    private Button btnTutorial;
    private TextView txtTitle, txtDetail;
//    private ImageView ivClose;
    private BubbleShowCaseSequence bubbleShowCaseSequence;
    private String mTitle;
    private int mDetail;

    public DialogWithHtml(String title, int detail) {
        this.mTitle = title;
        this.mDetail = detail;
    }

    @NonNull
    @Override
    public Dialog onCreateDialog(@Nullable Bundle savedInstanceState) {
        alertDialog = new Dialog(getActivity());
        alertDialog.setCanceledOnTouchOutside(false);
        alertDialog.getWindow().requestFeature(Window.FEATURE_NO_TITLE);
        alertDialog.getWindow().setBackgroundDrawable(new ColorDrawable(0));
        alertDialog.setContentView(R.layout.dialog_with_html);
        alertDialog.setOnKeyListener((dialogInterface, i, keyEvent) -> false);
        alertDialog.show();
        init();
        txtTitle.setText(mTitle);
        txtDetail.setText(Html.fromHtml(getResources().getString(mDetail)));
        btnTutorial.setOnClickListener(view -> {
            dismiss();
            bubbleShowCaseSequence.show();
        });
//        ivClose.setOnClickListener(view -> dismiss());
        return alertDialog;
    }

    public void settext(String title, String detail) {
        this.txtTitle.setText(title);
        this.txtDetail.setText(detail);
    }

    public void sendBubbleShowSequences(BubbleShowCaseSequence bubbleShowCaseSequence) {
        this.bubbleShowCaseSequence = bubbleShowCaseSequence;
    }

    private void init() {
        btnTutorial = alertDialog.findViewById(R.id.btn_jelajah_brimo);
//        ivClose = alertDialog.findViewById(R.id.iv_close_tutorial);
        txtTitle = alertDialog.findViewById(R.id.txt_title);
        txtDetail = alertDialog.findViewById(R.id.txt_detail);
    }
}
