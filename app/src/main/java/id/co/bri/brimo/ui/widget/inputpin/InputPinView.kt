package id.co.bri.brimo.ui.widget.inputpin

import android.annotation.SuppressLint
import android.content.Context
import android.util.AttributeSet
import android.view.LayoutInflater
import android.view.ViewGroup
import android.widget.LinearLayout
import androidx.annotation.DrawableRes
import androidx.appcompat.widget.AppCompatImageView
import androidx.core.content.res.ResourcesCompat
import androidx.core.view.setMargins
import androidx.recyclerview.widget.GridLayoutManager
import androidx.recyclerview.widget.RecyclerView
import id.co.bri.brimo.R
import id.co.bri.brimo.databinding.ViewInputPinBinding
import id.co.bri.brimo.domain.extension.gone
import id.co.bri.brimo.domain.extension.visible
import java.util.Stack

class InputPinView @JvmOverloads constructor(
    context: Context,
    attrs: AttributeSet? = null,
    defStyleAttr: Int = 0
): LinearLayout(context,attrs,defStyleAttr) {

    private val binding = ViewInputPinBinding.inflate(
        LayoutInflater.from(context),
        this,
        true
    )

    private val inputStack: Stack<String> = Stack()
    private val stringBuilder: StringBuilder = StringBuilder()

    private var onCompleteListener: InputPinViewListener? = null
    private var isViewFocused: Boolean = false

    companion object {
        private const val MAX_LENGTH_PIN = 6
    }

    private val pinAdapter = PinViewAdapter()

    init {

        with(binding.rvHolderPin){
            adapter = pinAdapter
            layoutManager = GridLayoutManager(context, 6)
        }

        val typedArray = context.obtainStyledAttributes(attrs, R.styleable.InputPinView)
        val titleHeader = typedArray.getString(R.styleable.InputPinView_header_label)
        binding.tvHeader.text = titleHeader ?: resources.getString(R.string.change_pin_debit_credit_card_input_pin_header)
        typedArray.recycle()
    }

    private fun getChildView(): AppCompatImageView {
        val childLayoutParams = LayoutParams(
            LayoutParams.MATCH_PARENT,
            resources.getDimensionPixelSize(R.dimen.size_42dp),
            1.0f,
        )

        childLayoutParams.setMargins(resources.getDimensionPixelSize(R.dimen.size_4dp))

        val pinEditText = AppCompatImageView(context).apply {
            background = ResourcesCompat.getDrawable(resources, R.drawable.bg_pin_input_unfocused, null)
        }

        pinEditText.layoutParams = childLayoutParams

        return pinEditText
    }

    fun setInputNewPinLabel(charSequence: CharSequence) {
        binding.tvHeader.text = charSequence
    }

    fun setOnCompleteListener(onCompleteListener: InputPinViewListener) {
        this.onCompleteListener = onCompleteListener
    }

    fun addPin(number: String) {
        if (inputStack.size < MAX_LENGTH_PIN) {
            inputStack.add(number)
            notifyAdapter()
            if (inputStack.size == MAX_LENGTH_PIN) {
                for (s in inputStack) {
                    stringBuilder.append(s)
                }
                val fullPin = stringBuilder.toString()
                stringBuilder.setLength(0)
                onCompleteListener?.onPinInputCompleted(fullPin)
            }
        }
    }

    fun deletePin() {
        if (inputStack.size > 0) {
            inputStack.pop()
            notifyAdapter()
        } else {
            onCompleteListener?.onPinDeletedAll()
        }
    }

    fun deleteAllPin() {
        inputStack.clear()
        notifyAdapter()
    }

    fun setFocused(isFocused: Boolean) {
        isViewFocused = isFocused
        if (isFocused) {
            binding.root.background = ResourcesCompat.getDrawable(resources, R.drawable.background_input_pin_view_focused, null)
        } else {
            binding.root.background = ResourcesCompat.getDrawable(resources, R.drawable.background_input_pin_view_unfocused, null)
        }
        resetIcon()
        notifyAdapter()
    }

    private fun resetIcon() {
        with(binding) {
            tvErrorMsg.gone()
            ivIconHeader.gone()
        }
    }

    @SuppressLint("NotifyDataSetChanged")
    private fun notifyAdapter() {
        pinAdapter.notifyDataSetChanged()
    }

    inner class PinViewAdapter:RecyclerView.Adapter<PinViewHolder>() {

        private var isError: Boolean = false

        @SuppressLint("NotifyDataSetChanged")
        fun setIsError(isError: Boolean) {
            this.isError = isError
            notifyDataSetChanged()
        }

        override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): PinViewHolder {
            val views = getChildView()
            return PinViewHolder(views)
        }

        override fun getItemCount(): Int = 6

        override fun onBindViewHolder(holder: PinViewHolder, position: Int) {

            fun setImageBackground(@DrawableRes idDrawable: Int){
                holder.imageView.background = ResourcesCompat.getDrawable(
                    resources,
                    idDrawable,
                    null
                )
            }

            if (inputStack.size <= position) {
                setImageBackground(
                    if (inputStack.size == position && isViewFocused)
                        R.drawable.bg_pin_input_focused
                    else
                        R.drawable.bg_pin_input_unfocused
                )
            } else {
                setImageBackground(R.drawable.bg_pin_input_filled)
            }
        }
    }

    fun showLoading() {
        binding.pbar.visible()
    }

    fun hideLoading() {
        if (binding.pbar.visibility == VISIBLE) {
            binding.pbar.gone()
        }
    }

    fun showError(message: String) {
        setHeaderIcon(R.drawable.ic_circle_error)
        with(binding.tvErrorMsg){
            visible()
            text = message
        }
    }

    fun showError() {
        setHeaderIcon(R.drawable.ic_circle_error)
    }

    fun setSuccess() {
        setHeaderIcon(R.drawable.ic_circle_success)
        if (binding.tvErrorMsg.visibility == VISIBLE) {
            binding.tvErrorMsg.gone()
        }
    }

    private fun setHeaderIcon(@DrawableRes idDrawable: Int) {
        with(binding.ivIconHeader){
            setImageDrawable(ResourcesCompat.getDrawable(
                resources, idDrawable, null))
            visible()
        }
        hideLoading()
    }

    inner class PinViewHolder(
        val imageView: AppCompatImageView
    ): RecyclerView.ViewHolder(imageView)

    interface InputPinViewListener {
        fun onPinInputCompleted(pin: String)

        fun onPinDeletedAll()
    }
}