package id.co.bri.brimo.models.apimodel.request;

import com.google.gson.annotations.Expose;
import com.google.gson.annotations.SerializedName;

import java.util.List;

public class EditFastMenuPinRequest {

    @SerializedName("list_menu")
    @Expose
    private String listMenu;

    @SerializedName("pin")
    @Expose
    private String pin;

    public EditFastMenuPinRequest(String listMenu,String pin){
        this.listMenu= listMenu;
        this.pin = pin;
    }

    public String getListMenu() {
        return listMenu;
    }

    public void setListMenu(String listMenu) {
        this.listMenu = listMenu;
    }

    public String getPin() {
        return pin;
    }

    public void setPin(String pin) {
        this.pin = pin;
    }
}
