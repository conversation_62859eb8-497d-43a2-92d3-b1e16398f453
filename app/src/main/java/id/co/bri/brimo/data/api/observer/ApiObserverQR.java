package id.co.bri.brimo.data.api.observer;

import android.util.Log;

import id.co.bri.brimo.R;
import id.co.bri.brimo.domain.config.Constant;
import id.co.bri.brimo.domain.converter.MapperHelper;
import id.co.bri.brimo.domain.helpers.GeneralHelper;
import id.co.bri.brimo.domain.helpers.ValidationHelper;
import id.co.bri.brimo.models.apimodel.response.RestResponse;

import java.net.SocketTimeoutException;

import androidx.databinding.Observable;
import io.reactivex.observers.DisposableObserver;
import retrofit2.HttpException;

public abstract class ApiObserverQR extends DisposableObserver implements Observable {

    private static final String TAG = "ApiObserverQR";
    protected RestResponse restResponse;
    protected String sequenceNumber;
    protected String stringResponse;
    protected String responseId;

    protected ApiObserverQR(String sequenceNumber) {
        this.sequenceNumber = sequenceNumber;
    }

    @Override
    public void onNext(Object o) {

        if (o instanceof String) {
            try {
                stringResponse = String.valueOf(o);

                //get ID Integrity
                responseId = MapperHelper.getIdResponse(stringResponse);

                //Convert String to RestResponse model
                if (!responseId.isEmpty()) {

                    try {
                        restResponse = MapperHelper.stringToRestResponse(stringResponse, sequenceNumber);

                        if (restResponse == null) {
                            onFailureHttp("response not valid");
                            return;
                        }

                        if (GeneralHelper.isContains(R.array.response_code_success_qr, restResponse.getCode())) {
                            onApiCallSuccess(restResponse);
                        } else {
                            onApiCallError(restResponse.getRestResponse());
                        }

                    } catch (Exception e) {
                        if (!GeneralHelper.isProd()) {
                            Log.e(TAG, "onNext: ", e);
                        }
                        onError(e.getCause());
                    }
                } else {
                    if (!GeneralHelper.isProd()) {
                        Log.e(TAG, "onNext: ID response not found");
                    }
                    onFailureHttp("onNext: ID response not found");
                }
            } catch (Exception e) {
                if (!GeneralHelper.isProd()) {
                    Log.e(TAG, "onNext: ", e);
                }
                onError(e.getCause());
            }
        } else {
            onApiCallError(new RestResponse("12", "Unidentified Object"));
        }

    }

    @Override
    public void onError(Throwable e) {
        if (e instanceof SocketTimeoutException) {
            onFailureHttp(Constant.KONEKSI_TERPUTUS);
        } else if (e instanceof HttpException) {
            onFailureHttp(Constant.SERVER_UNDER_MAINTENANCE);
        } else {
            onFailureHttp(Constant.KONEKSI_TERPUTUS);
        }
    }

    protected abstract void onFailureHttp(String type);

    protected abstract void onApiCallSuccess(RestResponse response);

    protected abstract void onApiCallError(RestResponse restResponse);

    @Override
    public void addOnPropertyChangedCallback(OnPropertyChangedCallback callback) {

    }

    @Override
    public void removeOnPropertyChangedCallback(OnPropertyChangedCallback callback) {

    }

    @Override
    public void onComplete() {

    }
}
