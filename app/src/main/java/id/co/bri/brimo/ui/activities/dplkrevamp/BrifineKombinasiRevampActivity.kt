package id.co.bri.brimo.ui.activities.dplkrevamp

import android.app.Activity
import android.content.Intent
import android.graphics.Color
import android.os.Build
import android.os.Bundle
import android.view.View
import android.view.View.OnDragListener
import android.widget.SeekBar
import android.widget.SeekBar.OnSeekBarChangeListener
import android.widget.Toast
import androidx.annotation.RequiresApi
import com.github.mikephil.charting.animation.Easing
import com.github.mikephil.charting.components.Legend
import com.github.mikephil.charting.data.Entry
import com.github.mikephil.charting.data.PieData
import com.github.mikephil.charting.data.PieDataSet
import com.github.mikephil.charting.data.PieEntry
import com.github.mikephil.charting.highlight.Highlight
import com.github.mikephil.charting.listener.OnChartValueSelectedListener
import id.co.bri.brimo.R
import id.co.bri.brimo.contract.IPresenter.dplkrevamp.IListDplkOptionRevampPresenter
import id.co.bri.brimo.contract.IView.dplkrevamp.IListPilihBrifineRevampView
import id.co.bri.brimo.databinding.ActivityBrifineKombinasiRevampBinding
import id.co.bri.brimo.domain.config.Constant
import id.co.bri.brimo.domain.helpers.GeneralHelper
import id.co.bri.brimo.domain.helpers.MyFormatter
import id.co.bri.brimo.models.apimodel.request.dplk.BrifineKombinasiRequest
import id.co.bri.brimo.models.apimodel.request.dplkrevamp.Combination
import id.co.bri.brimo.models.apimodel.request.dplkrevamp.InquiryDplkRegisRequest
import id.co.bri.brimo.models.apimodel.request.dplkrevamp.SimulasiDplkRequest
import id.co.bri.brimo.models.apimodel.response.dplkrevamp.DetailPerformanceTabDplkResponse
import id.co.bri.brimo.models.apimodel.response.dplkrevamp.FormPilihBrifineResponse
import id.co.bri.brimo.models.apimodel.response.dplkrevamp.ProductFtuDplkResponse
import id.co.bri.brimo.models.apimodel.response.dplkrevamp.SimulasiDplkResponse
import id.co.bri.brimo.ui.activities.base.BaseActivity
import javax.inject.Inject

class BrifineKombinasiRevampActivity : BaseActivity(), OnChartValueSelectedListener,
    View.OnClickListener, IListPilihBrifineRevampView {
    lateinit var binding: ActivityBrifineKombinasiRevampBinding

    private val titleBrifine = ""
    private val kodeProduct = ""
    private val mFristTime = false

    var combination = false
    var kombinasiRequest: BrifineKombinasiRequest? = null


    @Inject
    lateinit var presenter: IListDplkOptionRevampPresenter<IListPilihBrifineRevampView>

    companion object {

        private var mCode: String = ""
        private var mProductName: String = ""
        private var mIsSimulation: Boolean = false
        private var mIsCombination: Boolean = false
        private var mIsRegistered: Boolean = false
        var x = 0
        var y = 0
        var z = 0
        var value = 100
        var sum = 0

        fun launchIntent(
            caller: Activity,
            code: String,
            moneyMarket: Int,
            fixIncome: Int,
            stock: Int,
            isRegistered: Boolean

        ) {
            val i = Intent(caller, BrifineKombinasiRevampActivity::class.java)
            mCode = code
            mIsSimulation = false
            mIsRegistered = isRegistered
            x = moneyMarket
            y = fixIncome
            z = stock
            caller.startActivityForResult(i, Constant.REQ_PAYMENT)
        }

        fun launchIntent(
            caller: Activity,
            code: String,
            productName: String,
            isSimulation: Boolean,
            isCombination: Boolean,
            moneyMarket: Int,
            fixIncome: Int,
            stock: Int
        ) {
            val i = Intent(caller, BrifineKombinasiRevampActivity::class.java)
            mCode = code
            mProductName = productName
            mIsSimulation = isSimulation
            mIsCombination = isCombination
            x = moneyMarket
            y = fixIncome
            z = stock
            caller.startActivityForResult(i, Constant.REQ_PAYMENT)
        }
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        binding = ActivityBrifineKombinasiRevampBinding.inflate(layoutInflater)
        setContentView(binding.root)
        injectDependecy()
        setupView()

    }

    fun injectDependecy() {
        activityComponent.inject(this)
        presenter.view = this
        presenter.setUrlSimulasiDplk(GeneralHelper.getString(R.string.url_simulasi_dplk))
        presenter.setUrlInquiryDplk(GeneralHelper.getString(R.string.url_dplk_inquiry_open_account))

        presenter.start()
    }

    fun setupView() {
        GeneralHelper.setToolbarRevamp(
            this,
            binding.toolbar.toolbar,
            GeneralHelper.getString(R.string.txt_title_toolbar_brifine_kombinasi)
        )
        binding.btnSubmit.setOnClickListener(this)
        binding.seekBar1.progress = 40
        binding.seekBar2.progress = 30
        binding.seekBar3.progress = 30

        x = binding.seekBar1.progress
        y = binding.seekBar2.progress
        z = binding.seekBar3.progress

        binding.seekBar1.setOnSeekBarChangeListener(object : OnSeekBarChangeListener {
            @RequiresApi(api = Build.VERSION_CODES.O)
            override fun onProgressChanged(seekBar: SeekBar, progress: Int, fromUser: Boolean) {
                binding.tvPasar.setText(binding.seekBar1.getProgress().toString() + "%")
                var limit = -1
                if (binding.seekBar2.getProgress() != 0 && binding.seekBar3.getProgress() == 0 || binding.seekBar2.getProgress() == 0 && binding.seekBar3.getProgress() != 0) {
                    limit = 0
                    binding.seekBar1.setMin(1)
                } else if (binding.seekBar2.getProgress() > 0 && binding.seekBar3.getProgress() > 0) {
                    limit = 0
                    binding.seekBar1.setMin(0)
                }
                var limitUp = -1
                if (value != 0) {
                    limitUp =
                        value - (binding.seekBar3.getProgress() + binding.seekBar2.getProgress())
                }
                if (seekBar.progress >= limitUp) {
                    seekBar.progress = limitUp
                } else if (seekBar.progress <= limit) {
                    seekBar.progress = limit
                }

            }

            override fun onStartTrackingTouch(seekBar: SeekBar) {
                x = seekBar.progress
            }

            override fun onStopTrackingTouch(seekBar: SeekBar) {
                validationButton()
                setData(
                    binding.seekBar1.getProgress(),
                    binding.seekBar2.getProgress(),
                    binding.seekBar3.getProgress(),
                    sum
                )

            }
        })

        binding.seekBar2.setOnSeekBarChangeListener(object : OnSeekBarChangeListener {
            @RequiresApi(api = Build.VERSION_CODES.O)
            override fun onProgressChanged(seekBar: SeekBar, progress: Int, fromUser: Boolean) {
                binding.tvPendapatan.setText(binding.seekBar2.getProgress().toString() + "%")
                var limit = -1
                if (binding.seekBar1.getProgress() != 0 && binding.seekBar3.getProgress() == 0 || binding.seekBar1.getProgress() == 0 && binding.seekBar3.getProgress() != 0) {
                    limit = 0
                    binding.seekBar2.setMin(1)
                } else if (binding.seekBar1.getProgress() > 0 && binding.seekBar3.getProgress() > 0) {
                    limit = 0
                    binding.seekBar2.setMin(0)
                }
                var limitUp = -1
                if (value != 0) {
                    limitUp =
                        value - (binding.seekBar3.getProgress() + binding.seekBar1.getProgress())
                }
                if (seekBar.progress >= limitUp) {
                    seekBar.progress = limitUp
                } else if (seekBar.progress <= limit) {
                    seekBar.progress = limit
                }
            }

            override fun onStartTrackingTouch(seekBar: SeekBar) {
                y = seekBar.progress
            }

            override fun onStopTrackingTouch(seekBar: SeekBar) {

                validationButton()
                setData(
                    binding.seekBar1.getProgress(),
                    binding.seekBar2.getProgress(),
                    binding.seekBar3.getProgress(),
                    sum
                )
            }
        })

        binding.seekBar3.setOnSeekBarChangeListener(object : OnSeekBarChangeListener {
            @RequiresApi(api = Build.VERSION_CODES.O)
            override fun onProgressChanged(seekBar: SeekBar, progress: Int, fromUser: Boolean) {
                binding.tvSaham.setText(binding.seekBar3.getProgress().toString() + "%")
                var limit = -1
                if (binding.seekBar1.getProgress() != 0 && binding.seekBar2.getProgress() == 0 || binding.seekBar1.getProgress() == 0 && binding.seekBar2.getProgress() != 0) {
                    limit = 0
                    binding.seekBar3.setMin(1)

                } else if (binding.seekBar1.getProgress() > 0 && binding.seekBar2.getProgress() > 0) {
                    limit = 0
                    binding.seekBar3.setMin(0)
                }
                var limitUp = -1
                if (value != 0) {
                    limitUp =
                        value - (binding.seekBar2.getProgress() + binding.seekBar1.getProgress())
                }
                if (seekBar.progress >= limitUp) {
                    seekBar.progress = limitUp
                } else if (seekBar.progress <= limit) {
                    seekBar.progress = limit
                }
            }

            override fun onStartTrackingTouch(seekBar: SeekBar) {
                z = seekBar.progress
            }

            override fun onStopTrackingTouch(seekBar: SeekBar) {
                validationButton()
                setData(
                    binding.seekBar1.getProgress(),
                    binding.seekBar2.getProgress(),
                    binding.seekBar3.getProgress(),
                    sum
                )

            }
        })

        binding.tvPasar.setText(binding.seekBar1.getProgress().toString() + "%")
        binding.tvPendapatan.setText(binding.seekBar2.getProgress().toString() + "%")
        binding.tvSaham.setText(binding.seekBar3.getProgress().toString() + "%")
        binding.chart.setUsePercentValues(false)
        binding.chart.getDescription().setEnabled(false)

        binding.chart.setDragDecelerationFrictionCoef(0.95f)

        binding.chart.setDrawHoleEnabled(false)

        binding.chart.setTransparentCircleColor(Color.WHITE)
        binding.chart.setTransparentCircleAlpha(0)
        binding.chart.setTransparentCircleRadius(0f)

        binding.chart.setDrawCenterText(false)

        binding.chart.setRotationAngle(0f)
        // enable rotation of the chart by touch
        binding.chart.setRotationEnabled(false)
        binding.chart.setHighlightPerTapEnabled(false)

        // add a selection listener
        binding.chart.setOnChartValueSelectedListener(this)
        binding.chart.setTouchEnabled(true)

        binding.chart.animateY(1400, Easing.EaseInOutQuad)
        binding.chart.setOnDragListener(OnDragListener { v, event ->
            Toast.makeText(this@BrifineKombinasiRevampActivity, "" + event.x, Toast.LENGTH_SHORT)
                .show()
            false
        })

        val l: Legend = binding.chart.legend
        l.isEnabled = false


        // entry label styling
        binding.chart.setDrawEntryLabels(false)

        validationButton()
        setData(
            binding.seekBar1.getProgress(),
            binding.seekBar2.getProgress(),
            binding.seekBar3.getProgress(),
            sum
        )

        binding.btnSubmit.setOnClickListener {
            if (mIsSimulation) {
                presenter.getDataSimulasiDplk(
                    SimulasiDplkRequest(
                        "0", "0", "0", "0", "0",
                        mIsCombination,
                        binding.seekBar1.progress.toString(),
                        binding.seekBar2.progress.toString(),
                        binding.seekBar3.progress.toString()
                    )
                )
            } else {
                presenter.getDataInquiryDplk(
                    InquiryDplkRegisRequest(
                        mCode,
                        Combination(
                            binding.seekBar1.getProgress(),
                            binding.seekBar2.getProgress(),
                            binding.seekBar3.getProgress()
                        )
                    )
                )
            }
        }
    }

    private fun setData(a: Int, b: Int, c: Int, d: Int) {
        val entries = ArrayList<PieEntry>()
        if (a != 0) {
            entries.add(PieEntry(a.toFloat()))
        }
        if (b != 0) {
            entries.add(PieEntry(b.toFloat()))
        }
        if (c != 0) {
            entries.add(PieEntry(c.toFloat()))
        }
        if (d != 0) {
            if (value - d != 0 && value - d > 0) {
                entries.add(PieEntry((value - d).toFloat()))
            }
        }
        val dataSet = PieDataSet(entries, "")
        dataSet.setDrawIcons(false)
        dataSet.sliceSpace = 1.5f

        // add a lot of colors
        val colors = ArrayList<Int>()
        if (a != 0) {
            colors.add(GeneralHelper.getColor(R.color.semanticYellow90))
        }
        if (b != 0) {
            colors.add(GeneralHelper.getColor(R.color.successColor))
        }
        if (c != 0) {
            colors.add(GeneralHelper.getColor(R.color.highlightColor))
        }
        if (d != 0) {
            if (value - d != 0) {
                colors.add(GeneralHelper.getColor(R.color.accent2Color))
            }
        }
        dataSet.colors = colors
        val data = PieData(dataSet)
        data.setValueFormatter(MyFormatter(0))
        data.setValueTextSize(14f)
        data.setValueTextColor(Color.WHITE)
        binding.chart.setData(data)

        binding.ivAsset.setImageResource(R.drawable.ic_circle_yellow)
        binding.ivAsset2.setImageResource(R.drawable.ic_circle_green)
        binding.ivAsset3.setImageResource(R.drawable.circle)
        // undo all highlights
        binding.chart.highlightValues(null)
        binding.chart.invalidate()
    }

    override fun onValueSelected(e: Entry?, h: Highlight?) {

    }

    override fun onNothingSelected() {
    }

    override fun onClick(p0: View?) {

    }

    override fun onSuccessGetSimulasiDplk(response: SimulasiDplkResponse) {
        SimulasiBrifineDplkActivity.lunchIntent(
            this@BrifineKombinasiRevampActivity,
            response,
            mProductName,
            mCode,
            mIsCombination,
            binding.seekBar1.progress.toString(),
            binding.seekBar2.progress.toString(),
            binding.seekBar3.progress.toString(),
            mIsRegistered
        )
    }

    override fun onSuccessInquiryDplkRegis(response: FormPilihBrifineResponse) {
        FormPilihBrifineRevampActivity.launchIntent(this, response, "", mCode)
    }

    override fun onSuccesProductFtuDplk(response: ProductFtuDplkResponse) {
    }

    override fun onSuccesDetailProductFtuDplk(
        response: ProductFtuDplkResponse,
        responseSimulasi: SimulasiDplkResponse,
        responseGraphic: DetailPerformanceTabDplkResponse
    ) {
    }

    private fun validationButton() {
        sum =
            binding.seekBar1.getProgress() + binding.seekBar2.getProgress() + binding.seekBar3.getProgress()
        if (sum == 100) {
            binding.rlInformasiWarning.visibility = View.GONE
            binding.btnSubmit.setEnabled(true)
            binding.btnSubmit.setTextColor(GeneralHelper.getColor(R.color.whiteColor))

        } else {
            binding.rlInformasiWarning.visibility = View.VISIBLE
            binding.btnSubmit.setEnabled(false)
            binding.btnSubmit.setTextColor(GeneralHelper.getColor(R.color.neutral_light60))

        }
    }

    override fun onActivityResult(requestCode: Int, resultCode: Int, data: Intent?) {
        super.onActivityResult(requestCode, resultCode, data)
        if (requestCode == Constant.REQ_BUKA_REKENING) {
            if (resultCode == RESULT_OK) {
                this.setResult(RESULT_OK, data)
                finish()
            } else if (resultCode == RESULT_CANCELED && data != null) {
                this.setResult(RESULT_CANCELED, data)
                finish()
            } else if (resultCode == RESULT_FIRST_USER && data != null) {
                this.setResult(RESULT_FIRST_USER, data)
                finish()
            }
        }

        if (requestCode == Constant.REQ_PAYMENT) {
            if (resultCode == RESULT_OK) {
                this.setResult(RESULT_OK, data)
                finish()
            } else if (resultCode == RESULT_CANCELED && data != null) {
                this.setResult(RESULT_CANCELED, data)
                finish()
            } else if (resultCode == RESULT_FIRST_USER && data != null) {
                this.setResult(RESULT_FIRST_USER, data)
                finish()
            }
        }

    }
}