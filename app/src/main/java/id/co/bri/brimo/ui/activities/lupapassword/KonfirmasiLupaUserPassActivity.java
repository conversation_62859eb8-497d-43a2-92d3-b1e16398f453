package id.co.bri.brimo.ui.activities.lupapassword;

import android.app.Activity;
import android.content.Intent;
import android.os.Bundle;

import androidx.annotation.Nullable;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import java.util.ArrayList;
import java.util.List;

import javax.inject.Inject;

import id.co.bri.brimo.R;
import id.co.bri.brimo.adapters.HeaderTransaksiAdapter;
import id.co.bri.brimo.contract.IPresenter.lupapassword.IKonfirmasiLupaUserPassPresenter;
import id.co.bri.brimo.contract.IView.lupapassword.IKonfirmasiLupaUserPassView;
import id.co.bri.brimo.databinding.ActivityKonfirmasiLupaUserPassBinding;
import id.co.bri.brimo.domain.config.Constant;
import id.co.bri.brimo.domain.helpers.GeneralHelper;
import id.co.bri.brimo.models.apimodel.PreSumRequest;
import id.co.bri.brimo.models.apimodel.response.DataView;
import id.co.bri.brimo.models.apimodel.response.DetailAkunResponse;
import id.co.bri.brimo.models.apimodel.response.MagicLupaPassResponse;
import id.co.bri.brimo.models.apimodel.response.forgetuserpass.OtpNoHpRes;
import id.co.bri.brimo.models.apimodel.response.pengelolaankartu.OtpReissueResponse;
import id.co.bri.brimo.ui.activities.base.BaseActivity;
import id.co.bri.brimo.ui.activities.lupausername.VerifikasiOtpNoHpActivity;
import id.co.bri.brimo.ui.activities.lupausername.WaitingLupaUsernameActivity;
import id.co.bri.brimo.ui.activities.pengelolaankartu.VerifikasiOtpReissueActivity;
import id.co.bri.brimo.ui.customviews.dialog.DialogExitCustom;

public class KonfirmasiLupaUserPassActivity extends BaseActivity implements
        IKonfirmasiLupaUserPassView,
        HeaderTransaksiAdapter.clickCopyListener,
        DialogExitCustom.DialogDefaultListener {

    @Inject
    IKonfirmasiLupaUserPassPresenter<IKonfirmasiLupaUserPassView> presenter;

    private ActivityKonfirmasiLupaUserPassBinding binding;

    private final List<DataView> dataViewList = new ArrayList<>();

    private static String sUsername;
    private static String sTglLahir;
    private static String sNomorKtp;
    private static String sNamaIbu;
    private static String sNomorRek;
    private static DetailAkunResponse mAkunResponse;

    private static boolean isForgetUsername = false;
    private static boolean isReissue = false;

    public static void launchIntentForgetPassword(Activity caller, DetailAkunResponse akunResponse, String username, String tglLahir, boolean isuser) {
        Intent intent = new Intent(caller, KonfirmasiLupaUserPassActivity.class);
        sUsername = username;
        sTglLahir = tglLahir;
        mAkunResponse = akunResponse;
        isForgetUsername = isuser;
        isReissue = false;

        caller.startActivityForResult(intent, Constant.REQ_FORGET);
    }

    public static void launchIntentForgetUsername(Activity caller, DetailAkunResponse akunResponse, String nomorKtp, String namaIbu, String nomorRek, boolean isuser) {
        Intent intent = new Intent(caller, KonfirmasiLupaUserPassActivity.class);
        mAkunResponse = akunResponse;
        sNomorKtp = nomorKtp;
        sNamaIbu = namaIbu;
        sNomorRek = nomorRek;
        isForgetUsername = isuser;
        isReissue = false;

        caller.startActivityForResult(intent, Constant.REQ_FORGET);
    }

    public static void launchIntentReissue(Activity caller, DetailAkunResponse akunResponse, boolean isreissue) {
        Intent intent = new Intent(caller, KonfirmasiLupaUserPassActivity.class);
        mAkunResponse = akunResponse;
        isForgetUsername = false;
        isReissue = isreissue;

        caller.startActivityForResult(intent, Constant.REQ_REISSUE);
    }

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        binding = ActivityKonfirmasiLupaUserPassBinding.inflate(getLayoutInflater());
        setContentView(binding.getRoot());

        GeneralHelper.setToolbar(this, binding.toolbar.toolbar, GeneralHelper.getString(R.string.str_konfirmasi));
        injectDependency();
        setupViews();
    }

    private void injectDependency() {
        getActivityComponent().inject(this);
        if (presenter != null) {
            presenter.setView(this);
            presenter.start();
            presenter.setUrlReissue(GeneralHelper.getString(R.string.url_reissue_pin_send_otp));
            if (isForgetUsername)
                presenter.setUrlOtpForgetUserPass(GeneralHelper.getString(R.string.url_forget_username_send_otp_v2));
            else
                presenter.setUrlOtpForgetUserPass(GeneralHelper.getString(R.string.url_forget_password_send_otp_v2));
        }
    }

    private void setupViews() {
        if (isReissue)
            setDataReissue();
        else if (isForgetUsername)
            setDataUsername();
        else
            setDataPassword();

        binding.recyclerview.setHasFixedSize(true);
        binding.recyclerview.setLayoutManager(new LinearLayoutManager(getApplicationContext(), RecyclerView.VERTICAL, false));
        HeaderTransaksiAdapter headerTransaksiAdapter = new HeaderTransaksiAdapter(dataViewList, this);
        headerTransaksiAdapter.setAdapterClickCopyListener(this);
        binding.recyclerview.setAdapter(headerTransaksiAdapter);

        binding.btnLanjut.setOnClickListener(v -> {
            if (isReissue)
                presenter.sendOTPReissue();
            else
                presenter.sendOtpForgetUserPass(new PreSumRequest(mAkunResponse.getRefnum()));
        });
    }

    private void setDataUsername() {
        if (sNomorKtp != null)
            dataViewList.add(new DataView(GeneralHelper.getString(R.string.nomor_ktp), sNomorKtp, ""));
        if (sNamaIbu != null)
            dataViewList.add(new DataView(GeneralHelper.getString(R.string.nama_ibu_kandung), sNamaIbu, ""));
        if (sNomorRek != null)
            dataViewList.add(new DataView(GeneralHelper.getString(R.string.nomor_rekening), sNomorRek, ""));
        if (mAkunResponse != null) {
            if (mAkunResponse.getEmail() != null)
                dataViewList.add(new DataView(GeneralHelper.getString(R.string.email), mAkunResponse.getEmail(), ""));
            if (mAkunResponse.getPhone() != null)
                dataViewList.add(new DataView(GeneralHelper.getString(R.string.nomor_handphone), mAkunResponse.getPhone(), ""));
        }
    }

    private void setDataPassword() {
        if (sUsername != null)
            dataViewList.add(new DataView(GeneralHelper.getString(R.string.username), sUsername, ""));
        if (mAkunResponse != null) {
            if (mAkunResponse.getEmail() != null)
                dataViewList.add(new DataView(GeneralHelper.getString(R.string.email), mAkunResponse.getEmail(), ""));
            if (mAkunResponse.getPhone() != null)
                dataViewList.add(new DataView(GeneralHelper.getString(R.string.nomor_handphone), mAkunResponse.getPhone(), ""));
        }
        if (sTglLahir != null)
            dataViewList.add(new DataView(GeneralHelper.getString(R.string.tanggal_lahir), sTglLahir, ""));
    }

    private void setDataReissue() {
        dataViewList.add(new DataView(GeneralHelper.getString(R.string.nomor_ktp), mAkunResponse.getNik(), ""));
        dataViewList.add(new DataView(GeneralHelper.getString(R.string.email), mAkunResponse.getEmail(), ""));
        dataViewList.add(new DataView(GeneralHelper.getString(R.string.nomor_handphone), mAkunResponse.getPhone(), ""));
        dataViewList.add(new DataView(GeneralHelper.getString(R.string.nomor_kartu), mAkunResponse.getCardNumberMasking(), ""));
        dataViewList.add(new DataView(GeneralHelper.getString(R.string.nomor_rekening), mAkunResponse.getAccountNumberMasking(), ""));
        dataViewList.add(new DataView(GeneralHelper.getString(R.string.biaya), mAkunResponse.getFee(), ""));
    }

    @Override
    public void onClickCopy(String message, int position) {
        // do nothing
    }

    @Override
    public void onClickYes() {
        // do nothing
    }

    @Override
    public void onSuccessOtpReissue(OtpReissueResponse otpReissueResponse) {
        VerifikasiOtpReissueActivity.launchIntent(this, otpReissueResponse);
    }

    @Override
    public void onSuccessOtpForget(OtpNoHpRes otpResponse) {
        VerifikasiOtpNoHpActivity.launchIntent(this, otpResponse, isForgetUsername);
    }

    @Override
    public void onSuccessMagicLink(MagicLupaPassResponse magicResponse) {
        if (isForgetUsername)
            WaitingLupaUsernameActivity.launchIntent(this, true, true, magicResponse);
        else
            WaitingLupaPasswordActivity.launchIntent(this, true, true, magicResponse);
    }

    @Override
    protected void onActivityResult(int requestCode, int resultCode, @Nullable Intent data) {
        super.onActivityResult(requestCode, resultCode, data);

        if (requestCode == Constant.REQ_FORGET && resultCode == RESULT_OK && data != null) {
            this.setResult(RESULT_OK, data);
            this.finish();
        } else if (requestCode == Constant.REQ_REISSUE && resultCode == RESULT_OK && data != null) {
            this.setResult(RESULT_OK, data);
            this.finish();
        } else if (resultCode == RESULT_CANCELED && data != null) {
            this.setResult(RESULT_CANCELED, data);
            this.finish();
        } else {
            this.setResult(RESULT_CANCELED);
            this.finish();
        }
    }

    @Override
    protected void onDestroy() {
        if (presenter != null)
            presenter.stop();
        binding = null;
        super.onDestroy();
    }
}