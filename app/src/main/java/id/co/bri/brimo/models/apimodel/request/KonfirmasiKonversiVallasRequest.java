package id.co.bri.brimo.models.apimodel.request;

import com.google.gson.annotations.Expose;
import com.google.gson.annotations.SerializedName;

/**
 * Created by user on 11/06/2021
 */
public class KonfirmasiKonversiVallasRequest {

    @SerializedName("reference_number")
    @Expose
    private String reference_number;
    @SerializedName("account_number")
    @Expose
    private String account_number;
    @SerializedName("debit_amount")
    @Expose
    private String debit_amount;
    @SerializedName("credit_amount")
    @Expose
    private String credit_amount;
    @SerializedName("debit_currency")
    @Expose
    private String debit_currency;

    public KonfirmasiKonversiVallasRequest( String reference_number,String account_number,String debit_amount,String credit_amount,String debit_currency) {
        this.reference_number = reference_number;
        this.account_number = account_number;
        this.debit_amount = debit_amount;
        this.credit_amount = credit_amount;
        this.debit_currency = debit_currency;
    }

    public String getReference_number() {
        return reference_number;
    }

    public void setReference_number(String reference_number) {
        this.reference_number = reference_number;
    }

    public String getAccount_number() {
        return account_number;
    }

    public void setAccount_number(String account_number) {
        this.account_number = account_number;
    }

    public String getDebit_amount() {
        return debit_amount;
    }

    public void setDebit_amount(String debit_amount) {
        this.debit_amount = debit_amount;
    }

    public String getCredit_amount() {
        return credit_amount;
    }

    public void setCredit_amount(String credit_amount) {
        this.credit_amount = credit_amount;
    }

    public String getDebit_currency() {
        return debit_currency;
    }

    public void setDebit_currency(String debit_currency) {
        this.debit_currency = debit_currency;
    }
}
