package id.co.bri.brimo.ui.fragments.sbnrevamp

import android.content.Context
import android.net.Uri
import android.opengl.Visibility
import android.os.Bundle
import android.util.Log
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.fragment.app.Fragment
import com.google.android.material.bottomsheet.BottomSheetDialogFragment
import id.co.bri.brimo.databinding.FragmentBottomDialogCustomBinding
import id.co.bri.brimo.domain.helpers.GeneralHelper
import id.co.bri.brimo.models.apimodel.response.sbnrevamp.NotFoundSbnModel


class BottomFragmentSbnKemenkeu(private val mContext : Context, private val response: NotFoundSbnModel,private var mListener: OnFragmentInteractionListener) : BottomSheetDialogFragment() {

    var _binding: FragmentBottomDialogCustomBinding? = null
    val binding get() = _binding!!


    fun BottomFragmentSbnKemenkeu() {

    }
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)

    }

    override fun onCreateView(inflater: LayoutInflater, container: ViewGroup?,
                              savedInstanceState: Bundle?): View? {
        _binding = FragmentBottomDialogCustomBinding.inflate(inflater, container, false)
        return binding.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        binding.tvTitle.text = response.title
        binding.tvDesc.text = response.desc
        binding.tvTitle.visibility = View.VISIBLE
        binding.tvDesc.visibility = View.VISIBLE
        GeneralHelper.loadIconTransaction(context, response.iconPath, response.icon, binding.ivImage, GeneralHelper.getImageId(context, "bri"))
        binding.btnClose.text = response.buttonTextMain
        binding.btnClose.setOnClickListener(View.OnClickListener {
            if (mListener != null) {
                mListener?.onFragmentInteraction()
            }
            dismiss()
        })

    }

    interface OnFragmentInteractionListener {
        fun onFragmentInteraction()
    }
}