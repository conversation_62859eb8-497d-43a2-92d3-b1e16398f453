package id.co.bri.brimo.ui.customviews.dialog

import android.annotation.SuppressLint
import android.app.Dialog
import android.content.DialogInterface
import android.graphics.drawable.ColorDrawable
import android.os.Bundle
import android.view.*
import android.widget.Button
import android.widget.TextView
import androidx.fragment.app.DialogFragment
import butterknife.ButterKnife
import id.co.bri.brimo.R
import id.co.bri.brimo.ui.fragments.FragmentDialogNoImageRevamp

class DialogWorkingHours : DialogFragment() , View.OnClickListener {

    private var alertDialog: Dialog? = null
    private var btnYes: Button? = null
    private var title: TextView? = null
    private var desc: TextView? = null

    companion object {
        var mTitle : String? = null
        var mDesc : String? = null
        var mButton : String? = null
        var dialogDefaulListener: DialogDefaultListener? = null

        @JvmStatic
        fun newInstance(clickListener : DialogDefaultListener, title :String, desc: String, button : String ) =
                DialogWorkingHours().apply {
                    dialogDefaulListener = clickListener
                    mTitle = title
                    mDesc = desc
                    mButton = button

                }
    }

    interface DialogDefaultListener {
        fun onClickDialogWorkingHourse()
    }
    override fun onCreateDialog(savedInstanceState: Bundle?): Dialog {
        alertDialog = Dialog(requireActivity(),R.style.DialogTheme)
        alertDialog!!.setCanceledOnTouchOutside(false)
        alertDialog!!.window!!.requestFeature(Window.FEATURE_NO_TITLE)
        alertDialog!!.window!!.setBackgroundDrawable(ColorDrawable(0))
        alertDialog!!.setContentView(R.layout.dialog_working_hourse)
        alertDialog!!.setOnKeyListener(DialogInterface.OnKeyListener { dialogInterface: DialogInterface?, i: Int, keyEvent: KeyEvent? -> false })
        alertDialog!!.show()

        initView()

        return alertDialog as Dialog
    }

    private fun initView() {
        btnYes = alertDialog!!.findViewById(R.id.btnClose)
        title = alertDialog!!.findViewById(R.id.tv_title)
        desc = alertDialog!!.findViewById(R.id.tv_desc)

        title!!.text = mTitle
        desc!!.text = mDesc
        btnYes!!.text = mButton

        btnYes!!.setOnClickListener(this)
    }

    override fun onClick(p0: View?) {
        dialogDefaulListener!!.onClickDialogWorkingHourse()
    }
}