package id.co.bri.brimo.ui.fragments.rdn;

import android.os.Bundle;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;

import androidx.fragment.app.DialogFragment;

import com.google.android.material.bottomsheet.BottomSheetDialogFragment;

import id.co.bri.brimo.R;
import id.co.bri.brimo.databinding.FragmentBottomRdnConsentBinding;

public class RdnConsentBottomFragment extends BottomSheetDialogFragment {

    private FragmentBottomRdnConsentBinding binding;

    private OnFragmentInteractionListener mListener;

    private String title;
    private String desc;

    public RdnConsentBottomFragment(String title, String desc, OnFragmentInteractionListener onClick) {
        this.title = title;
        this.desc = desc;
        this.mListener = onClick;
        // Required empty public constructor
    }

    public interface OnFragmentInteractionListener {
        void onClickConsent();
    }

    @Override
    public void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setStyle(DialogFragment.STYLE_NORMAL, R.style.CustomBottomSheetDialogTheme);
    }

    @Override
    public View onCreateView(LayoutInflater inflater, ViewGroup container,
                             Bundle savedInstanceState) {
        binding = FragmentBottomRdnConsentBinding.inflate(inflater, container, false);
        binding.tvTitle.setText(title);
        binding.tvDesc.setText(desc);

        binding.btnClose.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View view) {
                dismiss();
            }
        });

        binding.btnSubmit.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View view) {
                mListener.onClickConsent();
            }
        });

        return binding.getRoot();
    }
}