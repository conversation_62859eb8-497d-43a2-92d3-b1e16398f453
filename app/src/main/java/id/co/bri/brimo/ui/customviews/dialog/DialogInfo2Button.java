package id.co.bri.brimo.ui.customviews.dialog;

import android.app.Dialog;
import android.content.Context;
import android.graphics.drawable.ColorDrawable;
import android.os.Bundle;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.fragment.app.DialogFragment;

import android.view.Window;
import android.widget.Button;
import android.widget.ImageView;
import android.widget.TextView;

import id.co.bri.brimo.R;
import id.co.bri.brimo.domain.helpers.GeneralHelper;

public class DialogInfo2Button extends DialogFragment {

    protected Dialog alertDialog;
    protected ImageView ivImage;
    protected TextView tvTitle;
    protected TextView tvDesc;
    protected Button btnOk;
    protected Button btnCancel;

    protected Context mContext;
    protected String image;
    protected String title;
    protected String desc;
    protected String textBtnOk;
    protected String textBtnCancel;

    protected OnCallbackInfo onCallbackInfo;

    public DialogInfo2Button(Context mContext, String image, String title, String desc, String textBtnOk, String textBtnCancel, OnCallbackInfo onCallbackInfo) {
        this.mContext = mContext;
        this.image = image;
        this.title = title;
        this.desc = desc;
        this.textBtnOk = textBtnOk;
        this.textBtnCancel = textBtnCancel;
        this.onCallbackInfo = onCallbackInfo;
    }

    @NonNull
    @Override
    public Dialog onCreateDialog(@Nullable Bundle savedInstanceState) {
        alertDialog = new Dialog(getActivity());
        alertDialog.setCanceledOnTouchOutside(true);
        alertDialog.getWindow().requestFeature(Window.FEATURE_NO_TITLE);
        alertDialog.getWindow().setBackgroundDrawable(new ColorDrawable(0));
        alertDialog.setContentView(R.layout.fragment_dialog_info2_button);
        alertDialog.setOnKeyListener((dialogInterface, i, keyEvent) -> false);
        alertDialog.show();

        initView();

        ivImage.setImageResource(GeneralHelper.getImageId(mContext, image));
        tvTitle.setText(title);
        tvDesc.setText(desc);
        btnOk.setText(textBtnOk);
        btnCancel.setText(textBtnCancel);

        btnOk.setOnClickListener(view -> {
            onCallbackInfo.onCallInfo();
            dismiss();
        });

        btnCancel.setOnClickListener(v -> {
            dismiss();
        });

        return alertDialog;
    }

    public interface OnCallbackInfo {
        void onCallInfo();
    }

    private void initView() {
        ivImage = alertDialog.findViewById(R.id.iv_image);
        tvTitle = alertDialog.findViewById(R.id.tv_title);
        tvDesc = alertDialog.findViewById(R.id.tv_desc);
        btnOk = alertDialog.findViewById(R.id.btn_ok);
        btnCancel = alertDialog.findViewById(R.id.btn_cancel);
    }
}