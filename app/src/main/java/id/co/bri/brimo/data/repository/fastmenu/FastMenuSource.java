package id.co.bri.brimo.data.repository.fastmenu;


import java.util.Date;
import java.util.List;

import id.co.bri.brimo.models.daomodel.FastMenu;
import io.reactivex.Completable;
import io.reactivex.Maybe;
import io.reactivex.Observable;
import io.reactivex.Single;
import io.reactivex.disposables.Disposable;

/**
 * Created by user on 16/03/2021
 */
public interface FastMenuSource {

    Observable<List<FastMenu>> getFastMenu();

    Completable saveFastMenu(List<FastMenu> catgeories);

    Completable deleteAll();

    Completable deleteById(String id);

    Single<Integer> updateFastMenu(int id,String kode, String menuName, String gambarMenu, String menu, String tag, boolean flagNew);

}
