package id.co.bri.brimo.ui.activities.britamarencanarevamp

import android.app.Activity
import android.content.Intent
import android.os.Bundle
import android.os.SystemClock
import android.text.Editable
import android.view.View
import androidx.core.content.ContextCompat
import id.co.bri.brimo.R
import id.co.bri.brimo.contract.IPresenter.britamarencanarevamp.IInquiryPencairanRencanaPresenter
import id.co.bri.brimo.contract.IView.britamarencanarevamp.IInquiryPencairanRencanaView
import id.co.bri.brimo.databinding.ActivityInquiryPencairanRencanaBinding
import id.co.bri.brimo.domain.config.Constant
import id.co.bri.brimo.domain.helpers.GeneralHelper
import id.co.bri.brimo.domain.helpers.textwatcher.AmountFormatWatcher
import id.co.bri.brimo.models.ParameterKonfirmasiModel
import id.co.bri.brimo.models.apimodel.request.dashboardrencanarevamp.KonfirmasiPencairanRencanaRequest
import id.co.bri.brimo.models.apimodel.response.GeneralConfirmationResponse
import id.co.bri.brimo.models.apimodel.response.dashboardrecana.InquiryPencairanResponse
import id.co.bri.brimo.ui.activities.KonfirmasiGeneralRevampActivity
import id.co.bri.brimo.ui.activities.base.BaseActivity
import java.math.BigInteger
import javax.inject.Inject

class InquiryPencairanRencanaActivity : BaseActivity(), View.OnClickListener, IInquiryPencairanRencanaView {

    private val binding by lazy {ActivityInquiryPencairanRencanaBinding.inflate(layoutInflater)}

    private var nominalStrClr: String? = "0"

    @Inject
    lateinit var presenter: IInquiryPencairanRencanaPresenter<IInquiryPencairanRencanaView>

    companion object {
        var mResponse: InquiryPencairanResponse? = null

        fun launcIntent(caller: Activity, inquiryResponse: InquiryPencairanResponse) {
            val intent = Intent(caller, InquiryPencairanRencanaActivity::class.java)
            caller.startActivityForResult(intent, Constant.REQ_PAYMENT)
            this.mResponse = inquiryResponse
        }
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContentView(binding.root)
        injectDependecy()
        setupView()
    }

    private fun setupView() {
        binding.btnLanjutkan.setOnClickListener(this@InquiryPencairanRencanaActivity)
        binding.apply {
            GeneralHelper.setToolbarRevamp(
                this@InquiryPencairanRencanaActivity,
                tbInquiryPencairanRencana.toolbar,
                getString(R.string.pencairan_txt)
            )

            GeneralHelper.loadImageUrl(
                this@InquiryPencairanRencanaActivity,
                mResponse?.bancassAccountDataView?.iconPath,
                icRencana,
                R.drawable.ic_detail_rencana,
                0
            )

            tvTujuanRencana.text = mResponse?.bancassAccountDataView?.title
            tvSaldoRencana.text = mResponse?.bancassAccountDataView?.subtitle

            tilNominal.helperText = getString(R.string.maksimal_pencairan_txt, mResponse?.rencanaMaxWithdrawString)

            tvInitialName.text = GeneralHelper.formatInitialName(mResponse?.sourceAccountDataView?.title)
            tvNamaUser.text = mResponse?.sourceAccountDataView?.title
            tvNamaBank.text = mResponse?.sourceAccountDataView?.subtitle
            tvUserRek.text = GeneralHelper.formatCard(mResponse?.sourceAccountDataView?.description)
        }

        binding.etNominal.addTextChangedListener(activityTextListener)
        binding.etNominal.addTextChangedListener(
            AmountFormatWatcher(
                binding.etNominal,
                null,
                false,
            )
        )
    }

    private fun injectDependecy() {
        activityComponent.inject(this)
        presenter.view = this
        presenter.setUrlKonfirmasi(getString(R.string.url_konfirmasi_pencairan_rencana))
        presenter.start()
    }

    override fun afterText(editable: Editable?) {
        nominalStrClr = if (binding.etNominal.text.toString().isEmpty()) "0"
        else binding.etNominal.text.toString().replace(".", "")
        validasiPencairan()
    }
    private fun validasiPencairan() {
        val substringSaldo =
            mResponse?.bancassAccountDataView?.subtitle?.replace(Regex("[^0-9]"), "")
                ?.toInt()

        val convertValue = BigInteger.valueOf(nominalStrClr.orEmpty().toLong())
        if (convertValue < BigInteger.valueOf(mResponse?.minimumTransaction?.toLong() ?: 0)) {
            binding.apply {
                tilNominal.background =
                    getDrawable(R.drawable.background_cardview_stroked_transparent_red)
                tilNominal.setHelperTextColor(
                    ContextCompat.getColorStateList(
                        this@InquiryPencairanRencanaActivity,
                        R.color.semanticRed80
                    )
                )
                tilNominal.helperText = getString(
                    R.string.minimal_pencairan_txt,
                    mResponse?.rencanaMinWithdrawString
                )
                btnLanjutkan.isEnabled = false
            }
        } else if (convertValue > BigInteger.valueOf(mResponse?.rencanaMaxWithdraw?.toLong() ?: 0)) {
            binding.apply {
                tilNominal.background =
                    getDrawable(R.drawable.background_cardview_stroked_transparent_red)
                tilNominal.setHelperTextColor(
                    ContextCompat.getColorStateList(
                        this@InquiryPencairanRencanaActivity,
                        R.color.semanticRed80
                    )
                )
                tilNominal.helperText = getString(
                    R.string.maksimal_pencairan_txt,
                    mResponse?.rencanaMaxWithdrawString
                )
                btnLanjutkan.isEnabled = false
            }
        } else if ((substringSaldo?.minus(convertValue.toInt()) ?: 0) < 1000000) {
            binding.apply {
                tilNominal.background =
                    getDrawable(R.drawable.background_cardview_stroked_transparent_red)
                tilNominal.setHelperTextColor(
                    ContextCompat.getColorStateList(
                        this@InquiryPencairanRencanaActivity,
                        R.color.semanticRed80
                    )
                )
                tilNominal.helperText = getString(
                    R.string.sisa_saldo_setelah_pencairan_txt,
                    "Rp1.000.000"
                )
                btnLanjutkan.isEnabled = false
            }
        } else {
            binding.apply {
                tilNominal.background =
                    getDrawable(R.drawable.background_cardview_stroked_transparent)
                tilNominal.setHelperTextColor(
                    ContextCompat.getColorStateList(
                        this@InquiryPencairanRencanaActivity,
                        R.color.neutralLight80
                    )
                )
                tilNominal.helperText = getString(
                    R.string.maksimal_pencairan_txt,
                    mResponse?.rencanaMaxWithdrawString
                )
                btnLanjutkan.isEnabled = true
                btnLanjutkan.setTextColor(getColor(R.color.neutral_light10))
            }
        }
    }

    private fun setParameterKonfirmasi(): ParameterKonfirmasiModel? {
        val parameterKonfirmasiModel = ParameterKonfirmasiModel()
        parameterKonfirmasiModel.stringLabelTujuan =
            GeneralHelper.getString(R.string.nomor_tujuan)
        parameterKonfirmasiModel.stringButtonSubmit =
            GeneralHelper.getString(R.string.str_konfirmasi)
        parameterKonfirmasiModel.defaultIcon = R.drawable.bri
        return parameterKonfirmasiModel
    }

    override fun onClick(p0: View?) {
        if (SystemClock.elapsedRealtime() - mLastClickTime < 1000) {
            return
        }

        mLastClickTime = SystemClock.elapsedRealtime()
        when(p0?.id) {
            binding.btnLanjutkan.id -> {
                presenter.getDataKonfirmasi(KonfirmasiPencairanRencanaRequest(
                    mResponse?.referenceNumber.orEmpty(),
                    nominalStrClr.orEmpty()
                ))
            }
        }
    }

    override fun onGetDataKonfirmasiSukses(generalConfirmationResponse: GeneralConfirmationResponse) {
        KonfirmasiGeneralRevampActivity.launchIntent(
            this@InquiryPencairanRencanaActivity,
            generalConfirmationResponse,
            GeneralHelper.getString(R.string.url_payment_pencairan_rencana),
            GeneralHelper.getString(R.string.str_konfirmasi),
            setParameterKonfirmasi(),
            isFromFastMenu,
            false,
            false
        )
    }

    override fun onActivityResult(requestCode: Int, resultCode: Int, data: Intent?) {
        super.onActivityResult(requestCode, resultCode, data)
        if (requestCode == Constant.REQ_PAYMENT) {
            if (resultCode == RESULT_OK) {
                this.setResult(RESULT_OK, data)
            } else if (resultCode == RESULT_CANCELED && data != null) {
                this.setResult(RESULT_CANCELED, data)
            } else {
                this.setResult(RESULT_CANCELED)
            }

            finish()
        }
    }
}