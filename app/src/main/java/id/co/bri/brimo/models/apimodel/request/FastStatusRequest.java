package id.co.bri.brimo.models.apimodel.request;

import com.google.gson.annotations.Expose;
import com.google.gson.annotations.SerializedName;

public class FastStatusRequest extends FastMenuRequest {

    @SerializedName("reference_number")
    @Expose
    private String refNum;

    public FastStatusRequest(FastMenuRequest request, String refNum) {
        super(request.getUsername(), request.getTokenKey());
        this.refNum = refNum;
    }

    public String getRefNum() {
        return refNum;
    }

    public void setRefNum(String refNum) {
        this.refNum = refNum;
    }
}
