package id.co.bri.brimo.models.apimodel.request;

import com.google.gson.annotations.SerializedName;

public class InquiryBrizziTopUpRequest {

    @SerializedName("card_number")
    private String cardNumber;

    @SerializedName("random_string")
    private String randomString;

    public InquiryBrizziTopUpRequest(String cardNumber, String rendomString) {
        this.setCardNumber(cardNumber);
        this.setRandomString(rendomString);
    }

    public String getCardNumber() {
        return cardNumber;
    }

    public void setCardNumber(String cardNumber) {
        this.cardNumber = cardNumber;
    }

    public String getRandomString() {
        return randomString;
    }

    public void setRandomString(String randomString) {
        this.randomString = randomString;
    }
}
