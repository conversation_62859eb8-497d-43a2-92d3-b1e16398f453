package id.co.bri.brimo.domain.helpers.textwatcher;

import android.text.Editable;
import android.text.TextWatcher;
import android.widget.EditText;

import java.lang.ref.WeakReference;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.NumberFormat;
import java.util.Locale;


public class AmountDecimalFormatWatcher implements TextWatcher {
    private final WeakReference<EditText> editTextWeakReference;
    private final Locale locale;
    OnAmountChangeInter amountListener;

    public AmountDecimalFormatWatcher(EditText editText, OnAmountChangeInter listener, Locale locale) {
        this.editTextWeakReference = new WeakReference<>(editText);
        this.amountListener = listener;
        this.locale = locale != null ? locale : Locale.getDefault();
    }


    @Override
    public void beforeTextChanged(CharSequence s, int start, int count, int after) {
        //donothing
    }

    @Override
    public void onTextChanged(CharSequence s, int start, int before, int count) {
        //donothing
    }

    @Override
    public void afterTextChanged(Editable editable) {
        EditText editText = editTextWeakReference.get();
        if (editText == null) return;
        editText.removeTextChangedListener(this);
        String rawValue = editText.toString();
        BigDecimal parsed = parseToBigDecimal(editable.toString(), locale);
        NumberFormat nf = NumberFormat.getNumberInstance(locale);
        nf.setMaximumFractionDigits(2);
        nf.setMinimumFractionDigits(2);
        String formatted = nf.format(parsed);

        editText.setText(formatted);
        editText.setSelection(formatted.length());
        if (amountListener != null) {
            amountListener.onAmountChangeF(formatted, rawValue);
        }
        editText.addTextChangedListener(this);
    }

    private BigDecimal parseToBigDecimal(String value, Locale locale) {
        BigDecimal bigDecimal;
        String replaceable = String.format("[%s,.\\s]", NumberFormat.getCurrencyInstance(locale).getCurrency());

        String cleanString = value.replaceAll(replaceable, "");

        if (value.length() > 0){
            bigDecimal = new BigDecimal(cleanString).setScale(
                    2, RoundingMode.FLOOR).divide(new BigDecimal(100), RoundingMode.FLOOR
            );
        }else{
            bigDecimal = new BigDecimal("0.00").setScale(
                    0, RoundingMode.FLOOR);
        }
        return bigDecimal;
    }
    public interface OnAmountChangeInter {
        void onAmountChangeF(String amount, String rawValue);

        void setAmountListener();
    }
}
