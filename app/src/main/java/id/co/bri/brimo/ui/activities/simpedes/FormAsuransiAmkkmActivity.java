package id.co.bri.brimo.ui.activities.simpedes;

import android.app.Activity;
import android.content.Intent;
import android.os.Bundle;
import android.text.Editable;
import android.text.TextWatcher;
import android.view.View;
import android.widget.EditText;

import androidx.annotation.Nullable;

import java.util.ArrayList;
import java.util.List;

import javax.inject.Inject;

import id.co.bri.brimo.R;
import id.co.bri.brimo.contract.IPresenter.simpedes.IFormAsuransiAmkkmPresenter;
import id.co.bri.brimo.contract.IView.simpedes.IFormAsuransiAmkkmView;
import id.co.bri.brimo.databinding.ActivityFormAsuransiAmkkmBinding;
import id.co.bri.brimo.domain.config.Constant;
import id.co.bri.brimo.domain.helpers.GeneralHelper;
import id.co.bri.brimo.domain.helpers.ValidationHelper;
import id.co.bri.brimo.models.ParameterModel;
import id.co.bri.brimo.models.apimodel.response.InquiryAmkkmResponse;
import id.co.bri.brimo.models.apimodel.response.JenisAsuransiAmkkmResponse;
import id.co.bri.brimo.models.optionmodel.OptionGeneralModel;
import id.co.bri.brimo.ui.activities.base.BaseActivity;
import id.co.bri.brimo.ui.fragments.PilihGeneralNoLogoFragment;
import id.co.bri.brimo.ui.fragments.PilihanKodePosFragment;

public class FormAsuransiAmkkmActivity extends BaseActivity implements View.OnClickListener, IFormAsuransiAmkkmView, PilihanKodePosFragment.onSetData {

    private ActivityFormAsuransiAmkkmBinding binding;

    protected PilihGeneralNoLogoFragment hubunganFragment;
    protected String sHubungan;
    protected String iHubungan;
    private String sAccount;
    private static List<JenisAsuransiAmkkmResponse.Heir> heirs;
    private JenisAsuransiAmkkmResponse.Product product;
    private static final String ACC_NUMBER = "acc_number";
    private static final String JENISAMKKM = "jenis_amkkm";
    @Inject
    IFormAsuransiAmkkmPresenter<IFormAsuransiAmkkmView> presenter;

    protected static void launchActivity(Activity caller) {
        Intent i = new Intent(caller, FormAsuransiAmkkmActivity.class);
        caller.startActivity(i);
    }

    protected static void launchActivity(Activity caller, String sAccounts, JenisAsuransiAmkkmResponse.Product product, List<JenisAsuransiAmkkmResponse.Heir> heir) {
        heirs = heir;
        Intent i = new Intent(caller, FormAsuransiAmkkmActivity.class);
        i.putExtra(ACC_NUMBER, sAccounts);
        i.putExtra(JENISAMKKM, product);
        caller.startActivityForResult(i, ProductListAmkkmActivity.REQUESTCODE);
    }

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        binding = ActivityFormAsuransiAmkkmBinding.inflate(getLayoutInflater());
        setContentView(binding.getRoot());

        injectDependency();
        setupLayout();
    }

    private void injectDependency() {
        getActivityComponent().inject(this);
        if (presenter != null) {
            presenter.setView(this);
            presenter.start();
            presenter.setUrlFormAsuransiAmkkm(GeneralHelper.getString(R.string.url_s3f_open_inquiry_asuransi_amkkm));
        }
    }

    private void setupLayout() {

        if (getIntent().getStringExtra(ACC_NUMBER) != null) {
            sAccount = getIntent().getStringExtra(ACC_NUMBER);
            product = getIntent().getParcelableExtra(JENISAMKKM);
            if (Boolean.TRUE.equals(product.getAddressFlag()))
                binding.lyAlamatResiko.setVisibility(View.VISIBLE);
//
            GeneralHelper.setToolbar(this, binding.tbAsuransi.toolbar, product.getShortName());
            hubunganFragment = new PilihGeneralNoLogoFragment(fetchOptionListHubungan(heirs), (PilihGeneralNoLogoFragment.SelectPilihanGeneral) (position, optionModel) -> {
                sHubungan = heirs.get(position).getValue();
                iHubungan = heirs.get(position).getCode();
                binding.etHubungan.setText(heirs.get(position).getValue());
                checkText();
            });
            binding.etHubungan.setOnClickListener(this);
            binding.btnLanjut.setOnClickListener(this);
            checkTextChangeListener(binding.etNoHandphone);
            checkTextChangeListener(binding.etNamaAhliWaris);
            checkTextChangeListener(binding.etAlamat);
        }

        hubunganFragment = new PilihGeneralNoLogoFragment(fetchOptionListHubungan(heirs), (position, optionModel) -> {
            sHubungan = heirs.get(position).getValue();
            iHubungan = heirs.get(position).getCode();
            binding.etHubungan.setText(heirs.get(position).getValue());
            checkText();
        });
        binding.etHubungan.setOnClickListener(this);
        binding.btnLanjut.setOnClickListener(this);
        binding.etPostCode.setOnClickListener(this);
        checkTextChangeListener(binding.etNoHandphone);
        checkTextChangeListener(binding.etNamaAhliWaris);
    }

    private void checkTextChangeListener(EditText checkEt) {
        checkEt.addTextChangedListener(new TextWatcher() {
            @Override
            public void beforeTextChanged(CharSequence charSequence, int i, int i1, int i2) {
                checkText();
            }

            @Override
            public void onTextChanged(CharSequence charSequence, int i, int i1, int i2) {
                checkText();
            }

            @Override
            public void afterTextChanged(Editable editable) {
                checkText();
            }
        });
    }

    private void checkText() {
        if (Boolean.TRUE.equals(product.getAddressFlag())) {
            checkButton(ValidationHelper.isAlphabetic(binding.etNamaAhliWaris.getText().toString()) &&
                    ValidationHelper.validatePhoneNumber(binding.etNoHandphone.getText().toString()) &&
                    !binding.etHubungan.getText().toString().isEmpty() && !binding.etPostCode.getText().toString().isEmpty() && !binding.etAlamat.getText().toString().isEmpty());
        } else {
            checkButton(ValidationHelper.isAlphabetic(binding.etNamaAhliWaris.getText().toString()) &&
                    ValidationHelper.validatePhoneNumber(binding.etNoHandphone.getText().toString()) &&
                    !binding.etHubungan.getText().toString().isEmpty());
        }

    }

    private List<OptionGeneralModel> fetchOptionListHubungan(List<JenisAsuransiAmkkmResponse.Heir> hubunganList) {
        List<OptionGeneralModel> list = new ArrayList<>();

        if (hubunganList != null) {
            for (JenisAsuransiAmkkmResponse.Heir pointHubungan : hubunganList) {
                list.add(new OptionGeneralModel(0, pointHubungan.getValue(), "", ""));
            }
        }
        return list;
    }

    @Override
    public void onClick(View view) {
        switch (view.getId()) {
            case R.id.btn_lanjut:
                presenter.getInsurance();
                break;
            case R.id.et_hubungan:
                if (hubunganFragment != null) {
                    hubunganFragment.show(getSupportFragmentManager(), "");
                    hubunganFragment.setCancelable(true);
                }
                break;
            case R.id.et_post_code:
                PilihanKodePosFragment pilihanKodePosFragment = new PilihanKodePosFragment(this, this);
                pilihanKodePosFragment.show(getSupportFragmentManager(), "");
                pilihanKodePosFragment.setCancelable(false);
                break;
            default:
                break;
        }
    }

    private void checkButton(Boolean status) {
        if (Boolean.TRUE.equals(status)) {
            binding.btnLanjut.setEnabled(true);
            binding.btnLanjut.setAlpha(1);
        } else {
            binding.btnLanjut.setEnabled(false);
            binding.btnLanjut.setAlpha(0.3f);
        }
    }

    @Override
    public void onGetInquiryAmkkm(InquiryAmkkmResponse inquiryAmkkmResponse) {
        ParameterModel parameterModel = new ParameterModel();
        parameterModel.setStringLabelTujuan("Nomor Tujuan");
        parameterModel.setStringLabelNominal("Nominal Pembayaran");
        parameterModel.setStringButtonSubmit("Bayar");
        parameterModel.setStringLabelMinimum("Minimum");
        parameterModel.setDefaultIcon(R.drawable.ic_bpjs_form);
        String urlPayment = GeneralHelper.getString(R.string.url_s3f_open_konfirmasi_asuransi_amkkm);
        InquiryAsuransiAmkkmActivity.launchIntent(this, inquiryAmkkmResponse, urlPayment, "Pembelian", parameterModel, false, sAccount, product.getAddressFlag());

    }

    @Override
    public String getAccountNumber() {
        return sAccount;
    }

    @Override
    public String getProductId() {
        return product.getId();
    }

    @Override
    public String getHeirName() {
        return binding.etNamaAhliWaris.getText().toString();
    }

    @Override
    public String getHeirPhone() {
        return "0" + binding.etNoHandphone.getText().toString();
    }

    @Override
    public String heirConnection() {
        return "" + iHubungan;
    }

    @Override
    public String riskPostCode() {
        return "" + binding.etPostCode.getText().toString();
    }

    @Override
    public String riskAddress() {
        return "" + binding.etAlamat.getText().toString();
    }

    @Override
    public void onException12(String msg) {
        showSnackbarErrorMessage(msg, -2, this, false);
    }

    @Override
    public void onException(String message) {
        if (GeneralHelper.isContains(Constant.LIST_TYPE_GAGAL, message))
            GeneralHelper.showBottomDialog(this, message);
        else
            showSnackbarErrorMessage(message, -2, this, false);
    }

    @Override
    protected void onActivityResult(int requestCode, int resultCode, @Nullable Intent data) {
        super.onActivityResult(requestCode, resultCode, data);

        if (requestCode == ProductListAmkkmActivity.REQUESTCODE && resultCode == Activity.RESULT_OK) {
            Intent i = new Intent();
            setResult(Activity.RESULT_OK, i);
            finish();
        } else if (requestCode == ProductListAmkkmActivity.REQUESTCODE && resultCode == Activity.RESULT_CANCELED) {
            if (data != null) {
                Intent i = new Intent();
                i.putExtra(Constant.TAG_ERROR_MESSAGE, data.getStringExtra(Constant.TAG_ERROR_MESSAGE));
                setResult(Activity.RESULT_CANCELED, i);
                this.finish();
            } else {
                finish();
            }
        }
    }

    @Override
    public void onBackPressed() {
        Intent i = new Intent();
        setResult(RESULT_FIRST_USER, i);
        finish();
        super.onBackPressed();
    }

    @Override
    public void setKodePos(String id, String kodePos, String kelurahan, String kecamatan, String kota, String provinsi) {
        binding.etPostCode.setText(kodePos);
        binding.etKabupaten.setText(kota);
    }

    @Override
    protected void onDestroy() {
        super.onDestroy();
        binding = null;
    }
}