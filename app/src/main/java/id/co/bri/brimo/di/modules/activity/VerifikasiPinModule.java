package id.co.bri.brimo.di.modules.activity;

import android.content.Intent;

import androidx.recyclerview.widget.GridLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import id.co.bri.brimo.adapters.PinNumberAdapter;
import id.co.bri.brimo.adapters.pinadapter.OtpInputAdapter;
import id.co.bri.brimo.contract.IPresenter.IVerifikasiPinPresenter;
import id.co.bri.brimo.contract.IView.IVerifikasiPinView;
import id.co.bri.brimo.data.api.ApiSource;
import id.co.bri.brimo.data.preference.BRImoPrefSource;
import id.co.bri.brimo.data.repository.anggaran.AnggaranPfmSource;
import id.co.bri.brimo.data.repository.category.CategoryPfmSource;
import id.co.bri.brimo.data.repository.transaksi.TransaksiPfmSource;
import id.co.bri.brimo.di.scopes.PerForm;
import id.co.bri.brimo.domain.helpers.SizeHelper;
import id.co.bri.brimo.domain.helpers.rx.SchedulerProvider;
import id.co.bri.brimo.presenters.VerifikasiPresenter;
import id.co.bri.brimo.ui.activities.SplashScreenActivity;
import id.co.bri.brimo.ui.activities.VerifikasiPinActivity;
import id.co.bri.brimo.ui.customviews.GridSpacingItemDecoration;
import id.co.bri.brimo.ui.customviews.pin.InsertPinNumbers;

import javax.inject.Named;

import dagger.Module;
import dagger.Provides;
import io.reactivex.disposables.CompositeDisposable;


@Module
public class VerifikasiPinModule {

    private VerifikasiPinActivity activity;

    public VerifikasiPinModule(VerifikasiPinActivity activity) {
        this.activity = activity;
    }

    @Provides
    @PerForm
    public VerifikasiPinActivity provideVerifikasiActivity() {
        return activity;
    }

    @Provides
    @PerForm
    public IVerifikasiPinPresenter<IVerifikasiPinView> provideVerifikasiPresenter(BRImoPrefSource brImoPrefSource,
                                                                                  CompositeDisposable compositeDisposable,
                                                                                  SchedulerProvider schedulerProvider,
                                                                                  ApiSource apiSource,
                                                                                  CategoryPfmSource categoryPfmSource,
                                                                                  TransaksiPfmSource transaksiPfmSource,
                                                                                  AnggaranPfmSource anggaranPfmSource
    ) {
        return new VerifikasiPresenter(schedulerProvider, compositeDisposable, brImoPrefSource, apiSource, categoryPfmSource, transaksiPfmSource, anggaranPfmSource);
    }


    @Provides
    @PerForm
    public Intent provideVerifikasiIntent() {
        return new Intent(activity, SplashScreenActivity.class);
    }

    @Named("Pinotp")
    @Provides
    @PerForm
    public GridLayoutManager provideOtpLayoutManager() {
        return new GridLayoutManager(activity, 6);
    }

    @Named("Pinpad")
    @Provides
    @PerForm
    public GridLayoutManager providePadLayoutManager() {
        return new GridLayoutManager(activity, 3);
    }

    @Provides
    @PerForm
    public RecyclerView.ItemDecoration provideItemDecoration() {
        return new GridSpacingItemDecoration(3, SizeHelper.dpToPx(activity, 25), true);
    }

    @Provides
    @PerForm
    public OtpInputAdapter provideOtpAdapter() {
        return new OtpInputAdapter(activity);
    }

    @Provides
    @PerForm
    public PinNumberAdapter providePadAdapter() {
        return new PinNumberAdapter(InsertPinNumbers.Companion.getPinNumberList(activity));
    }
}