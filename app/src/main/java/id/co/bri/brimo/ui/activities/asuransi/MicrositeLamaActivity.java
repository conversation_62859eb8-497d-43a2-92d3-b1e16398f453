package id.co.bri.brimo.ui.activities.asuransi;

import android.annotation.SuppressLint;
import android.app.Activity;
import android.content.ClipData;
import android.content.ClipboardManager;
import android.content.Context;
import android.content.Intent;
import android.graphics.Bitmap;
import android.os.Build;
import android.os.Bundle;
import android.os.CountDownTimer;
import android.view.View;
import android.webkit.CookieManager;
import android.webkit.JavascriptInterface;
import android.webkit.PermissionRequest;
import android.webkit.WebChromeClient;
import android.webkit.WebResourceRequest;
import android.webkit.WebSettings;
import android.webkit.WebStorage;
import android.webkit.WebView;
import android.webkit.WebViewClient;

import androidx.annotation.Nullable;
import androidx.fragment.app.FragmentTransaction;

import javax.inject.Inject;

import id.co.bri.brimo.R;
import id.co.bri.brimo.contract.IPresenter.asuransi.IInquiryMicrositeBrivaPresenter;
import id.co.bri.brimo.contract.IView.asuransi.IInquiryMicrositeBrivaView;
import id.co.bri.brimo.databinding.ActivityMicrositeLamaBinding;
import id.co.bri.brimo.domain.config.Constant;
import id.co.bri.brimo.domain.helpers.GeneralHelper;
import id.co.bri.brimo.models.ParameterModel;
import id.co.bri.brimo.models.apimodel.response.GeneralInquiryResponse;
import id.co.bri.brimo.ui.activities.base.BaseActivity;
import id.co.bri.brimo.ui.customviews.dialog.DialogExitCustom;

public class MicrositeLamaActivity extends BaseActivity implements DialogExitCustom.DialogDefaultListener, IInquiryMicrositeBrivaView {

    private static final String TAG = "url_microsite";
    private static final int SECOND = 1000;
    private static String sJourneyType = "";
    DialogExitCustom dialogExitCustom;
    boolean isFinished = false;
    @Inject
    IInquiryMicrositeBrivaPresenter<IInquiryMicrositeBrivaView> presenter;
    private ActivityMicrositeLamaBinding binding;
    private String msg = "";

    public static void launchIntent(Activity caller, String url) {
        Intent i = new Intent(caller, MicrositeLamaActivity.class);

        i.putExtra(TAG, url);
        caller.startActivityForResult(i, Constant.REQ_PAYMENT);
    }

    public static void launchIntent(Activity caller, String url, String journeyType) {
        Intent i = new Intent(caller, MicrositeLamaActivity.class);
        sJourneyType = journeyType;
        i.putExtra(TAG, url);
        caller.startActivityForResult(i, Constant.REQ_PAYMENT);
    }

    @Override
    protected void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        binding = ActivityMicrositeLamaBinding.inflate(getLayoutInflater());
        setContentView(binding.getRoot());

        injectDependency();
        setupLayout();
    }

    private void injectDependency() {
        getActivityComponent().inject(this);
        if (presenter != null) {
            presenter.setView(this);
            presenter.setUrlLama(GeneralHelper.getString(R.string.url_inquiry_brilife));
            presenter.start();
        }
    }

    private void setupLayout() {
        switch (sJourneyType) {
            case Constant.TRX_TYPE_SIGNAL:
                GeneralHelper.setToolbar(this, binding.toolbar.toolbar, "");
                break;
            default:
                GeneralHelper.setToolbar(this, binding.toolbar.toolbar, GeneralHelper.getString(R.string.asuransi));
                break;
        }


        setTextTimer(30);

        initWebView();

    }

    //set time about 5 second to load microsite
    private void setTextTimer(Integer timer) {
        switch (sJourneyType) {
            case Constant.TRX_TYPE_SIGNAL:
                binding.webView.setVisibility(View.VISIBLE);
                break;
        }
        int countDown = SECOND * timer;
        // Do nothing
        new CountDownTimer(countDown, SECOND) {
            public void onTick(long millisUntilFinished) {
                if (isFinished)
                    onFinish();
            }

            public void onFinish() {
                if (isFinished) {
                    if (binding != null) {
                        binding.lyLoading.setVisibility(View.GONE);
                        binding.webView.setVisibility(View.VISIBLE);
                    }
                } else {
                    Intent i = new Intent();
                    i.putExtra(Constant.TAG_ERROR_MESSAGE, "Halaman gagal dimuat, silahkan coba lagi");
                    setResult(Activity.RESULT_CANCELED, i);
                    finish();
                }
            }
        }.start();
    }

    @SuppressLint("SetJavaScriptEnabled")
    private void initWebView() {
        WebSettings webSettings = binding.webView.getSettings();
        webSettings.setJavaScriptEnabled(true);
        webSettings.setDefaultTextEncodingName("utf-8");
        webSettings.setDomStorageEnabled(true);
        webSettings.setDatabaseEnabled(true);
//        webSettings.setAppCacheEnabled(true);
        webSettings.setAllowFileAccess(false);
        webSettings.setAllowFileAccessFromFileURLs(false);
        webSettings.setAllowUniversalAccessFromFileURLs(false);
        webSettings.setAllowContentAccess(false);
        binding.webView.setWebChromeClient(new WebChromeClient() {
            // Need to accept permissions to use the camera
            @Override
            public void onPermissionRequest(final PermissionRequest request) {
                if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.LOLLIPOP) {
                    request.grant(request.getResources());
                }
            }
        });
        binding.webView.getSettings().setMediaPlaybackRequiresUserGesture(false);

        if (18 < Build.VERSION.SDK_INT) {
            //18 = JellyBean MR2, KITKAT=19
            binding.webView.getSettings().setCacheMode(WebSettings.LOAD_NO_CACHE);
        }

        binding.webView.getSettings().setLoadWithOverviewMode(true);
        binding.webView.getSettings().setUseWideViewPort(true);
        binding.webView.requestFocus();
        binding.webView.setVerticalScrollBarEnabled(true);

        WebAppInterface webAppInterface = new WebAppInterface(this);
        binding.webView.addJavascriptInterface(webAppInterface, getString(R.string.jsbridge));
        binding.webView.setWebViewClient(new WebViewClient() {
            @Override
            public boolean shouldOverrideUrlLoading(
                    WebView view, WebResourceRequest request) {
                isFinished = false;
                binding.webView.loadUrl(request.getUrl().toString());
                return true;
            }

            @Override
            public void onPageStarted(WebView view, String url, Bitmap favicon) {
                super.onPageStarted(view, url, favicon);
            }

            @Override
            public void onPageFinished(WebView view, String url) {
                isFinished = true;
                super.onPageFinished(view, url);
            }

        });
        binding.webView.loadUrl(getIntent().getStringExtra(TAG));
        clearWebView();
    }

    @Override
    public void onClickYes() {
        clearWebView();
        if (msg != null) {
            Intent returnIntent = new Intent();
            returnIntent.putExtra(Constant.TAG_ERROR_MESSAGE, msg);
            this.setResult(RESULT_CANCELED, returnIntent);
            this.finish();
        } else {
            this.finish();
        }
    }

    private void clearWebView() {
        binding.webView.clearCache(true);
        binding.webView.clearFormData();
        binding.webView.clearHistory();
        binding.webView.clearSslPreferences();
        binding.webView.clearMatches();
        CookieManager.getInstance().removeAllCookies(null);
        CookieManager.getInstance().flush();
        WebStorage.getInstance().deleteAllData();
    }

    @Override
    public void onSuccess(GeneralInquiryResponse response) {
        ParameterModel parameterModel = new ParameterModel();

        parameterModel.setStringLabelTujuan("Nomor Tujuan");
        parameterModel.setStringLabelNominal("Nominal Pembayaran");
        parameterModel.setStringButtonSubmit("Bayar");
        parameterModel.setStringLabelMinimum("Pembayaran");
        InquiryMicrositeBrivaActivity.launchIntent(this, response, GeneralHelper.getString(R.string.url_konfirmasi_brilife), GeneralHelper.getString(R.string.url_payment_brilife), GeneralHelper.getString(R.string.briva_close_title_bar), parameterModel, false);
    }

    @Override
    public void onInitiateResourceSuccess(String username, String tokenKey) {

    }

    @Override
    public void onSuccessRevoke() {

    }

    @Override
    public void onGoingRevoke() {

    }

    @Override
    public void onOrderIdNull(String message) {

    }

    private void callDialog(String msg) {
        this.msg = msg;
        switch (sJourneyType) {
            case Constant.TRX_TYPE_SIGNAL:
                onClickYes();
                break;
            default:
                dialogExitCustom = new DialogExitCustom(this, GeneralHelper.getString(R.string.dialog_title_exit_microsite), GeneralHelper.getString(R.string.dialog_desc_exit_microsite));
                FragmentTransaction ft = this.getSupportFragmentManager().beginTransaction();
                ft.add(dialogExitCustom, null);
                ft.commitAllowingStateLoss();
                break;
        }
    }

    @Override
    public void onBackPressed() {
        this.msg = null;
        callDialog(null);
    }

    @Override
    protected void onDestroy() {
        binding.webView.destroy();
        clearWebView();
        super.onDestroy();
    }

    @Override
    protected void onActivityResult(int requestCode, int resultCode, @Nullable Intent data) {
        super.onActivityResult(requestCode, resultCode, data);
        if (requestCode == Constant.REQ_PAYMENT && data != null) {
            if (resultCode == RESULT_OK) {
                this.setResult(RESULT_OK, data);
                this.finish();
            } else {
                if (resultCode == Activity.RESULT_CANCELED) {
                    onException(data.getStringExtra(Constant.TAG_ERROR_MESSAGE));
                }
            }
        } else if (requestCode == Constant.REQ_FORGET_PIN) {
            if (resultCode == RESULT_OK) {
                this.setResult(RESULT_OK);
                this.finish();
            }
        }
    }

    public class WebAppInterface {
        Context context;

        private WebAppInterface(Context context) {
            this.context = context;
        }

        @JavascriptInterface
        public void sendBrivaCode(String orderId) {
            presenter.getInquiryLama(orderId);
        }

        @JavascriptInterface
        public void copyToClipboard(String text) {
            ClipboardManager clipboard = (ClipboardManager) getSystemService(Context.CLIPBOARD_SERVICE);
            ClipData clip = ClipData.newPlainText("Briva", text);
            clipboard.setPrimaryClip(clip);
        }

        @JavascriptInterface
        public void onExceptionInterface(String msg) {
            callDialog(msg);
        }
    }
}