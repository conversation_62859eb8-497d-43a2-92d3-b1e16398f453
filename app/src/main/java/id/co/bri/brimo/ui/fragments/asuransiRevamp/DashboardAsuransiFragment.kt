package id.co.bri.brimo.ui.fragments.asuransiRevamp

import android.content.Context
import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.fragment.app.Fragment
import androidx.recyclerview.widget.LinearLayoutManager
import id.co.bri.brimo.adapters.asuransi.DashboardAsuransiAdapter
import id.co.bri.brimo.databinding.FragmentDashboardAsuransiBinding
import id.co.bri.brimo.models.apimodel.response.asuransi.DashboardAsuransiResponse
import id.co.bri.brimo.ui.fragments.BaseFragment

class DashboardAsuransiFragment : BaseFragment(), DashboardAsuransiAdapter.ItemClickListener {

    private var _binding: FragmentDashboardAsuransiBinding? = null
    private val binding get() = _binding!!
    private var dashboardAsuransiAdapter: DashboardAsuransiAdapter? = null

    private lateinit var dataList: List<DashboardAsuransiResponse.ListProduct>
    private var mPosition: Int = 0
    private var onListener: DialogDefaultListener? = null

    companion object {
        fun newInstance(
            dataList: ArrayList<DashboardAsuransiResponse.ListProduct>,
            position: Int
        ): DashboardAsuransiFragment {
            val fragment = DashboardAsuransiFragment()
            val bundle = Bundle().apply {
                putParcelableArrayList("dataList", dataList)
                putInt("position", position)
            }
            fragment.arguments = bundle
            return fragment
        }
    }

    override fun onAttach(context: Context) {
        super.onAttach(context)
        if (context is DialogDefaultListener) {
            onListener = context
        }
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        arguments?.let {
            dataList = it.getParcelableArrayList("dataList") ?: emptyList()
            mPosition = it.getInt("position")
        }
    }

    override fun onCreateView(
        inflater: LayoutInflater, container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        _binding = FragmentDashboardAsuransiBinding.inflate(inflater, container, false)
        return binding.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        dashboardAsuransiAdapter = DashboardAsuransiAdapter(
            requireContext(),
            dataList[mPosition].insurances,
            this
        )
        binding.rvAsuransi.apply {
            adapter = dashboardAsuransiAdapter
            layoutManager = LinearLayoutManager(requireContext())
        }
    }

    override fun onDestroyView() {
        _binding = null
        super.onDestroyView()
    }

    interface DialogDefaultListener {
        fun onType(
            position: Int,
            productCode: String,
            redirectType: Int,
            companyName: String,
            patnerId: String,
            typeId: String
        )
    }

    override fun onItemClick(
        position: Int,
        productCode: String,
        redirectType: Int,
        companyName: String,
        patnerId: String,
        typeId: String
    ) {
        onListener?.onType(position, productCode, redirectType, companyName, patnerId, typeId)
    }
}
