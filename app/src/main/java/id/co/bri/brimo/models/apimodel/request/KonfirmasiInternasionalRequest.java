package id.co.bri.brimo.models.apimodel.request;

import com.google.gson.annotations.Expose;
import com.google.gson.annotations.SerializedName;

public class KonfirmasiInternasionalRequest {
    @SerializedName("reference_number")
    @Expose
    private String referenceNumber;
    @SerializedName("account_number")
    @Expose
    private String accountNumber;
    @SerializedName("save_as")
    @Expose
    private String note;

    public KonfirmasiInternasionalRequest(String referenceNumber, String accountNumber, String note) {
        this.referenceNumber = referenceNumber;
        this.accountNumber = accountNumber;
        this.note = note;
    }

    public String getReferenceNumber() {
        return referenceNumber;
    }

    public void setReferenceNumber(String referenceNumber) {
        this.referenceNumber = referenceNumber;
    }

    public String getAccountNumber() {
        return accountNumber;
    }

    public void setAccountNumber(String accountNumber) {
        this.accountNumber = accountNumber;
    }

    public String getNote() {
        return note;
    }

    public void setNote(String note) {
        this.note = note;
    }
}
