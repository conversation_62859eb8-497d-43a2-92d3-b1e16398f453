package id.co.bri.brimo.ui.activities.tartun;

import android.app.Activity;
import android.content.ClipData;
import android.content.ClipboardManager;
import android.content.Context;
import android.content.Intent;
import android.content.IntentFilter;
import android.graphics.Bitmap;
import android.os.AsyncTask;
import android.os.Bundle;
import android.os.CountDownTimer;
import android.util.Log;
import android.view.View;
import android.webkit.WebSettings;
import android.widget.Toast;

import androidx.annotation.Nullable;
import androidx.fragment.app.FragmentTransaction;
import androidx.localbroadcastmanager.content.LocalBroadcastManager;

import com.google.gson.Gson;

import javax.inject.Inject;

import id.co.bri.brimo.R;
import id.co.bri.brimo.contract.IPresenter.tariktunai.IRecieptTarikPresenter;
import id.co.bri.brimo.contract.IView.tariktunai.IRecieptTarikView;
import id.co.bri.brimo.databinding.ActivityRecipeTarikBinding;
import id.co.bri.brimo.domain.config.Constant;
import id.co.bri.brimo.domain.helpers.GeneralHelper;
import id.co.bri.brimo.domain.helpers.image.ImageHelper;
import id.co.bri.brimo.models.NotifikasiModel;
import id.co.bri.brimo.models.apimodel.response.PaymentTarikResponse;
import id.co.bri.brimo.models.apimodel.response.ReceiptResponse;
import id.co.bri.brimo.ui.activities.DashboardIBActivity;
import id.co.bri.brimo.ui.activities.ReceiptInboxActivity;
import id.co.bri.brimo.ui.activities.base.BaseActivity;
import id.co.bri.brimo.ui.customviews.dialog.DialogExitCustom;

public class ReceiptTarikActivity extends BaseActivity implements View.OnClickListener, IRecieptTarikView, DialogExitCustom.DialogDefaultListener {

    private ActivityRecipeTarikBinding binding;

    private int SECOND = 1000;

    private ImageHelper imageHelper = new ImageHelper(this);
    private static PaymentTarikResponse paymentTarikResponse;
    GenerateBarCode barCode;
    private Bitmap barCodeBitMap;
    private String tokenString;
    protected boolean isFinish = false;
    private static String mCardlessType = null;

    @Inject
    IRecieptTarikPresenter<IRecieptTarikView> tarikPresenter;

    private CountDownTimer countDownTimer;

    public static void launchIntent(Activity caller, PaymentTarikResponse pendingResponse) {
        Intent intent = new Intent(caller, ReceiptTarikActivity.class);
        mCardlessType = pendingResponse.getCardlessType();
        paymentTarikResponse = pendingResponse;
        caller.startActivityForResult(intent, Constant.REQ_PAYMENT);
        caller.setResult(RESULT_OK);
        caller.finish();
    }


    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        binding = ActivityRecipeTarikBinding.inflate(getLayoutInflater());
        setContentView(binding.getRoot());

        injectDependency();
        binding.btnSubmit.setOnClickListener(this);
        binding.lihatPetunjuk.setOnClickListener(this);
        binding.batalPenarikan.setOnClickListener(this);
        binding.btnCopy.setOnClickListener(this);

        if (paymentTarikResponse != null) {
            tokenString = paymentTarikResponse.getOtp();
            binding.textOTP.setText(tokenString);

            binding.noSumberDana.setText(paymentTarikResponse.getAccountNumber());
            String jalur = paymentTarikResponse.getMerchantName();

            if (paymentTarikResponse.getCardlessType() != null) {
                if (paymentTarikResponse.getCardlessType().equalsIgnoreCase(Constant.TARTUNMERCHANT)) {
                    binding.imgBarCode.setVisibility(View.VISIBLE);
                } else if (paymentTarikResponse.getCardlessType().equalsIgnoreCase(Constant.TARTUNBRILINK)) {
                    binding.imgBarCode.setVisibility(View.VISIBLE);
                } else {
                    binding.imgBarCode.setVisibility(View.GONE);
                    jalur = GeneralHelper.getString(R.string.txt_atm_caps);
                }
            }

            if (paymentTarikResponse.getInformation() != null) {
                GeneralHelper.setWebViewStandart(binding.wvInfoTartun, "", paymentTarikResponse.getInformation());
                WebSettings webSetting = binding.wvInfoTartun.getSettings();
                webSetting.setJavaScriptEnabled(true);
            }

            binding.tvJalurTarikTunai.setText(jalur);
            barCode = new GenerateBarCode();
            barCode.execute(tokenString);
            if (paymentTarikResponse.getExpireTime() != null) {
                if (paymentTarikResponse.getExpireTime().floatValue() > 0)
                    setTextTimer(paymentTarikResponse.getExpireTime());
                else if (paymentTarikResponse.getExpireTime().floatValue() == 0)
                    setTextTimer(0);
                else if (paymentTarikResponse.getExpireTime().floatValue() < 0)
                    DashboardIBActivity.launchIntent(this);
            } else {
                setTextTimer(0);
            }
            if (paymentTarikResponse.getTutorial() == null) {
                binding.lihatPetunjuk.setVisibility(View.INVISIBLE);
            }
        }
        LocalBroadcastManager.getInstance(this).registerReceiver(updates_receiver, new IntentFilter(Constant.TAG_NOTIF));
        GeneralHelper.setToolbar(this, binding.tbReceiptTarik.toolbar, GeneralHelper.getString(R.string.toolbar_tarik_tunai));
    }

    /**
     * Parse data notif foreground
     *
     * @param intent
     */
    @Override
    protected void parseDataNotifForeground(Intent intent) {
        if (intent != null) {

            try {
                // Get data from notifikasi
                Bundle extras = intent.getExtras();

                if (extras != null) {
                    try {
                        String notifikasiString = extras.getString(Constant.TAG_NOTIF);
                        Gson gson = new Gson();
                        NotifikasiModel notifikasiModel = gson.fromJson(notifikasiString, NotifikasiModel.class);

                        if (notifikasiModel != null) {
                            if (notifikasiModel.getType().equalsIgnoreCase(Constant.CARDLESS_BLAST_TYPE)) {
                                String refNumber = notifikasiModel.getRequestRefnum();
                                tarikPresenter.getInboxDetail(refNumber);
                            }

                        }
                    } catch (Exception e) {
                        //donothing
                    }

                }
            } catch (Exception e) {
                //donothing
            }
        }
    }

    @Override
    public void onSuccessGetInboxDetail(ReceiptResponse receiptResponse) {
        ReceiptInboxActivity.launchIntent(this, receiptResponse.getPendingResponses(), true);
    }

    class GenerateBarCode extends AsyncTask<String, Void, Bitmap> {

        @Override
        protected Bitmap doInBackground(String... strings) {
            try {
                barCodeBitMap = imageHelper.generateBarCode(getApplicationContext(), strings[0], 500, 70);
            } catch (Exception e) {
                if (!GeneralHelper.isProd()) {
//                    Log.e(TAG, "doInBackground: ", e);
                }
            }
            return barCodeBitMap;
        }

        @Override
        protected void onPostExecute(Bitmap result) {
            binding.imgBarCode.setImageBitmap(result);
        }
    }

    private void injectDependency() {
        getActivityComponent().inject(this);
        if (tarikPresenter != null) {
            tarikPresenter.setView(this);
            if (paymentTarikResponse != null) {
                if (paymentTarikResponse.getCardlessType() != null) {
                    if (paymentTarikResponse.getCardlessType().equalsIgnoreCase(Constant.TARTUNMERCHANT)) {
                        tarikPresenter.setFormUrl(GeneralHelper.getString(R.string.url_batal_penarikan_tunai_merchant));
                    } else if (paymentTarikResponse.getCardlessType().equalsIgnoreCase(Constant.TARTUNBRILINK)) {
                        tarikPresenter.setFormUrl(GeneralHelper.getString(R.string.url_batal_penarikan_tunai_merchant));
                    } else {
                        tarikPresenter.setFormUrl(GeneralHelper.getString(R.string.url_batal_penarikan_tunai_atm));
                    }
                } else {
                    tarikPresenter.setFormUrl(GeneralHelper.getString(R.string.url_batal_penarikan_tunai_atm));
                }
            }
            tarikPresenter.setUrlDetailInbox(GeneralHelper.getString(R.string.url_activity_detail));
            tarikPresenter.start();
        }
    }

    @Override
    protected void onResume() {
        super.onResume();
        if (tarikPresenter != null) {
            tarikPresenter.setView(this);
            tarikPresenter.setUrlDetailInbox(GeneralHelper.getString(R.string.url_activity_detail));
            tarikPresenter.start();
        }
    }

    private void setTextTimer(int timer) {
        isFinish = true;
        int countDown = SECOND * timer;
        //final int warningTime = Math.round(countDown * 0.2f);
        countDownTimer = new CountDownTimer(countDown, SECOND) {
            //boolean isWarning = false;

            public void onTick(long millisUntilFinished) {
//                setDisableClick(true);
                int seconds = (int) millisUntilFinished / SECOND;
                int[] timeFormat = GeneralHelper.getTimeFormat(seconds);

                binding.txtTimer.setText(String.format(getResources().getString(R.string.txt_countdown_tarik), timeFormat[1], timeFormat[2]));
                /*if (!isWarning && millisUntilFinished < warningTime) {
                    txtTimer.setTextColor(getResources().getColor(R.color.blue));
                    isWarning = true;
                }*/
            }

            public void onFinish() {
                cancel();
                deleteToken();
                isFinish = false;
            }

        }.start();
    }

    private void deleteToken() {
        if (isFinish) {
            isFinish = false;
            tarikPresenter.onGetBatalTarik();
        }
    }


    @Override
    public void onClick(View view) {
        int id = view.getId();
        switch (id) {

            case R.id.btnSubmit:
                DashboardIBActivity.launchIntent(this);
                break;
            case R.id.btn_copy:
                String getstring = binding.textOTP.getText().toString();
                ClipboardManager clipboard = (ClipboardManager) getSystemService(Context.CLIPBOARD_SERVICE);
                ClipData clip = ClipData.newPlainText(getstring, getstring);
                clipboard.setPrimaryClip(clip);
                Toast.makeText(this, GeneralHelper.getString(R.string.txt_succeed_copy), Toast.LENGTH_SHORT).show();
                break;
            case R.id.batalPenarikan:
//                AlertDialog.Builder alertDialog = new AlertDialog.Builder(this)
//                        .setTitle("Pembatalan Transaksi")
//                        .setMessage("Apakah Anda akan membatalkan transaksi?")
//                        .setPositiveButton("Ya", (dialogInterface, i) -> {
//                            tarikPresenter.onGetBatalTarik();
//                        })
//                        .setNegativeButton("Tidak", (dialogInterface, i) -> {
//                        })
//                        .setCancelable(false);
//                alertDialog.show();
                DialogExitCustom dialogExitCustom = new DialogExitCustom(this::onClickYes, GeneralHelper.getString(R.string.cancel_trx), GeneralHelper.getString(R.string.cancel_trx_confirm));
                FragmentTransaction ft = this.getSupportFragmentManager().beginTransaction();
                ft.add(dialogExitCustom, null);
                ft.commitAllowingStateLoss();

                break;
            case R.id.lihat_petunjuk:
                PetunjukActivity.launchIntent(this, paymentTarikResponse.getTutorial());
                break;
        }
    }

    @Override
    public void onSuccesGetBatal(String message) {
        FormTarikTunaiActivity.launchIntentReciept(this);
    }

    @Override
    public void onException93(String message) {
        FormTarikTunaiActivity.launchIntentReciept(this, message);
    }

    @Override
    public void onException12(String message) {
        FormTarikTunaiActivity.launchIntentReciept(this, message);

    }

    @Override
    protected void onActivityResult(int requestCode, int resultCode, @Nullable Intent data) {
        super.onActivityResult(requestCode, resultCode, data);
        if (requestCode == Constant.REQ_PAYMENT) {
            if (resultCode == RESULT_OK) {
                this.setResult(RESULT_OK, data);
                this.finish();
            } else {
                this.setResult(RESULT_CANCELED);
                this.setResult(RESULT_CANCELED, data);
            }
        }
    }

    @Override
    public void onClickYes() {
        tarikPresenter.onGetBatalTarik();
    }

    @Override
    protected void onDestroy() {
        if (countDownTimer != null) {
            countDownTimer.cancel();
        }
        tarikPresenter.stop();
        binding = null;
        super.onDestroy();
    }

    @Override
    public void onBackPressed() {
        if (countDownTimer != null) {
            countDownTimer.cancel();
        }
        tarikPresenter.stop();
        super.onBackPressed();
    }

    @Override
    protected void onPause() {
        super.onPause();
        LocalBroadcastManager.getInstance(this).unregisterReceiver(updates_receiver);
    }
}