package id.co.bri.brimo.ui.activities.dplkrevamp

import android.Manifest
import android.annotation.SuppressLint
import android.app.Activity
import android.content.Intent
import android.content.pm.PackageManager
import android.net.Uri
import android.os.Bundle
import android.view.View
import android.view.WindowManager
import android.widget.LinearLayout
import androidx.core.app.ActivityCompat
import androidx.fragment.app.Fragment
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.swiperefreshlayout.widget.SwipeRefreshLayout
import androidx.viewpager.widget.ViewPager
import com.ethanhua.skeleton.SkeletonScreen
import com.google.gson.Gson
import id.co.bri.brimo.R
import id.co.bri.brimo.adapters.CatatanKeuanganAdapter
import id.co.bri.brimo.adapters.baseadapter.ViewPagerAdapter
import id.co.bri.brimo.adapters.dplkrevamp.DplkFaqAdapter
import id.co.bri.brimo.contract.IPresenter.dplkrevamp.IFirstTimeDPLKPresenter
import id.co.bri.brimo.contract.IView.dplkrevamp.IFirstTimeDplkView
import id.co.bri.brimo.databinding.ActivityFirstTimeDplkBinding
import id.co.bri.brimo.domain.config.Constant
import id.co.bri.brimo.domain.extension.gone
import id.co.bri.brimo.domain.extension.visible
import id.co.bri.brimo.domain.helpers.GeneralHelper
import id.co.bri.brimo.domain.helpers.SizeHelper
import id.co.bri.brimo.models.apimodel.request.dplkrevamp.CheckStatusDplkRequest
import id.co.bri.brimo.models.apimodel.request.dplkrevamp.ListDplkRequest
import id.co.bri.brimo.models.apimodel.response.dplkrevamp.ListPilihBrifineResponse
import id.co.bri.brimo.models.apimodel.response.dplkrevamp.OnboardingDplkResponse
import id.co.bri.brimo.models.apimodel.response.dplkrevamp.PaymentFtuDplkResponse
import id.co.bri.brimo.models.apimodel.response.dplkrevamp.PersonalDataDplkResponse
import id.co.bri.brimo.models.apimodel.response.dplkrevamp.StateRegistDplk
import id.co.bri.brimo.ui.activities.base.BaseActivity
import id.co.bri.brimo.ui.fragments.dplkrevamp.BottomPembukaanBrifineFragment
import id.co.bri.brimo.ui.fragments.dplkrevamp.DplkDashboardHeaderRevampFragment
import id.co.bri.brimo.ui.fragments.dplkrevamp.KeuntunganFirstTimeDplkFragment
import id.co.bri.brimo.ui.fragments.dplkrevamp.RincianFirstTimeDplkFragment
import javax.inject.Inject

class FirstTimeDplkActivity : BaseActivity(), IFirstTimeDplkView, ViewPager.OnPageChangeListener,
    DplkFaqAdapter.OnClickItemListner, SwipeRefreshLayout.OnRefreshListener {

    @Inject
    lateinit var presenter: IFirstTimeDPLKPresenter<IFirstTimeDplkView>

    private lateinit var binding: ActivityFirstTimeDplkBinding

    private lateinit var adapterSlider: ViewPagerAdapter
    private lateinit var fragmentAdapter: CatatanKeuanganAdapter
    private lateinit var dplkFaqAdapter: DplkFaqAdapter

    private var keuntunganFragment: KeuntunganFirstTimeDplkFragment =
        KeuntunganFirstTimeDplkFragment(mResponseOnboarding)
    private var rincianFragment: RincianFirstTimeDplkFragment = RincianFirstTimeDplkFragment(
        mResponseOnboarding
    )

    private val TAG_FRAGMENT = "fragment"
    private val AUTO_SCROLL_THRESHOLD_IN_MILLI = 3500

    private var linearLayout: LinearLayout? = null
    private var skeletonScreenDplkFaq: SkeletonScreen? = null

    private var mHeaderResponse: MutableList<OnboardingDplkResponse.HeaderContent> = mutableListOf()
    private var mFaqDplkResponse: MutableList<OnboardingDplkResponse.Faq> = mutableListOf()
    private var titleList: MutableList<String> = mutableListOf()
    private var fragmentList: MutableList<Fragment> = mutableListOf()

    private var isSeeMooreContent = false

    companion object {
        private var mResponseOnboarding: OnboardingDplkResponse = OnboardingDplkResponse()
        private var errorMessage = ""
        private var stateReceipt: StateRegistDplk = StateRegistDplk.NONE
        private var isPending = false

        @JvmStatic
        fun launchIntent(
            caller: Activity,
            response: OnboardingDplkResponse
        ) {
            val intent = Intent(caller, FirstTimeDplkActivity::class.java)
            this.mResponseOnboarding = response
            caller.startActivityForResult(intent, Constant.REQ_PAYMENT)
        }

        @JvmStatic
        fun launchIntent(
            caller: Activity,
            message: String,
            isPending: Boolean = false,
            stateReceipt: StateRegistDplk
        ) {
            val intent = Intent(caller, FirstTimeDplkActivity::class.java)
            errorMessage = message
            this.stateReceipt = stateReceipt
            this.isPending = isPending
            intent.addFlags(Intent.FLAG_ACTIVITY_CLEAR_TOP or Intent.FLAG_ACTIVITY_NEW_TASK)
            caller.startActivity(intent)
        }
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        binding = ActivityFirstTimeDplkBinding.inflate(layoutInflater)
        setContentView(binding.root)
        injectDependecy()
        setupView()
        settingViewPager()
        setupSlider()
        setupAdapter()
        initListener()
    }

    override fun onResume() {
        super.onResume()
        if (errorMessage != "") {
            showSnackbarErrorMessageRevamp(
                errorMessage, ALERT_ERROR, this@FirstTimeDplkActivity, false
            )
        }

        if (stateReceipt == StateRegistDplk.SUCCESS) {
            DashboardDplkRevampActivity.launchIntent(this, false)
            errorMessage = ""
            stateReceipt = StateRegistDplk.NONE
            finish()
        }

        if (isPending) {
            binding.llProgressOpenAccountDplk.visible()
            binding.llViewPager.gone()
            binding.viewFour.gone()
            binding.llContentDetailInfo.gone()
            binding.tvSeeMoreContent.gone()
        }
    }

    @Deprecated("Deprecated in Java")
    override fun onActivityResult(requestCode: Int, resultCode: Int, data: Intent?) {
        super.onActivityResult(requestCode, resultCode, data)
        if (requestCode == Constant.REQ_REGIS) {
            if (data != null) {
                val errorMessage = data.getStringExtra(Constant.RE_TRX_EXPIRED)
                showSnackbarErrorMessageRevamp(
                    errorMessage, ALERT_ERROR, this@FirstTimeDplkActivity, false
                )

                if (data.getBooleanExtra(Constant.TAG_IS_PENDING_REGIST, false)) {
                    presenter.getDashboardDplk()
                }
            }
        }
    }

    private fun injectDependecy() {
        activityComponent.inject(this)
        presenter.view = this
        presenter.setUrlDashboardDplk(GeneralHelper.getString(R.string.url_dashboard_dplk))
        presenter.seturlDplkRegis(GeneralHelper.getString(R.string.url_dplk_onboarding))
        presenter.setUrlPersonalDataRegistration(GeneralHelper.getString(R.string.url_dplk_personal_data))
        presenter.setUrlListBrifine(GeneralHelper.getString(R.string.url_dplk_get_product))
        presenter.setUrlCheckStatusDplk(GeneralHelper.getString(R.string.url_dplk_check_status_dplk))
        presenter.start()
    }

    private fun setupSkeletonView() {
        mFaqDplkResponse.clear()
        mResponseOnboarding.faq.let {
            mFaqDplkResponse.addAll(it)
        }
        dplkFaqAdapter = DplkFaqAdapter(
            this@FirstTimeDplkActivity
        )
    }

    private fun setupView() {
        binding.apply {
            GeneralHelper.setToolbarRevamp(
                this@FirstTimeDplkActivity,
                tbDplk.toolbar,
                GeneralHelper.getString(R.string.txt_detail_brifine)
            )
            swipeRefresh.setOnRefreshListener(this@FirstTimeDplkActivity)
            fragmentList = mutableListOf(rincianFragment, keuntunganFragment)

            if (!mResponseOnboarding.isRegistered && mResponseOnboarding.isPending) {
                llProgressOpenAccountDplk.visible()
                llContentDetailInfo.gone()
                viewFour.gone()
                llViewPager.gone()
                tvSeeMoreContent.gone()
            } else {
                llProgressOpenAccountDplk.gone()
                llViewPager.visible()
                llContentDetailInfo.visible()
                viewFour.visible()
            }

            nestedScrollview.viewTreeObserver.addOnScrollChangedListener {
                val scrollY = binding.nestedScrollview.scrollY
                binding.imgBawah.visibility = if (scrollY == 0) View.GONE else View.VISIBLE
            }
        }
    }


    private fun settingViewPager() {
        binding.apply {
            titleList = mutableListOf(
                GeneralHelper.getString(R.string.txt_rincian),
                GeneralHelper.getString(R.string.txt_keuntungan)
            )

            fragmentAdapter = CatatanKeuanganAdapter(
                supportFragmentManager, this@FirstTimeDplkActivity, fragmentList, titleList
            )

            vpDetail.adapter = fragmentAdapter
            stDetail.setViewPager(vpDetail)
            stDetail.setOnPageChangeListener(this@FirstTimeDplkActivity)
            linearLayout = stDetail.getChildAt(0) as LinearLayout
            GeneralHelper.changeTabsFontBoldRevamp(
                this@FirstTimeDplkActivity, linearLayout, 0
            )
        }
    }

    private fun setupSlider() {
        binding.apply {
            mHeaderResponse.clear()
            mHeaderResponse.addAll(mResponseOnboarding.headerContent)
            adapterSlider = ViewPagerAdapter(supportFragmentManager)

            mHeaderResponse.iterator().forEach {
                adapterSlider.addFragment(
                    addFragment(it),
                    ""
                )
            }

            with(binding.vpFirstSbn) {
                adapter = adapterSlider
                startAutoScroll()
                interval = AUTO_SCROLL_THRESHOLD_IN_MILLI.toLong()
                setAutoScrollDurationFactor(6.0)
                setCycle(true)
                isStopScrollWhenTouch = true
                dotsIndicator.setViewPager(this)
            }
        }
    }

    private fun initListener() {
        binding.apply {
            btnRegistrasi.setOnClickListener {
                presenter.getPersonalDataRegistration()
            }

            clSimulasiSectionOnboardDplk.setOnClickListener {
                presenter.getDataListBrifine(ListDplkRequest(true))
            }

            tvSeeMoreContent.setOnClickListener {
                isSeeMooreContent = !isSeeMooreContent
                if (isSeeMooreContent) {
                    val layoutParams = llContentDetailInfo.layoutParams
                    layoutParams.height = WindowManager.LayoutParams.WRAP_CONTENT
                    llContentDetailInfo.layoutParams = layoutParams
                    tvSeeMoreContent.text = GeneralHelper.getString(R.string.close)
                } else {
                    val layoutParams = llContentDetailInfo.layoutParams
                    layoutParams.height = SizeHelper.dpToPx(this@FirstTimeDplkActivity, 300)
                    llContentDetailInfo.layoutParams = layoutParams
                    tvSeeMoreContent.text = GeneralHelper.getString(R.string.see_more)
                }
            }
            btnCheckStatusDplk.setOnClickListener {
                val requestBody =
                    CheckStatusDplkRequest(registrationId = mResponseOnboarding.pendingRegistrationId)
                presenter.getCheckStatusDplk(requestBody)
            }
            tvHeadphone.setOnClickListener {
                makePhoneCall(Constant.CALL_CONTACT_BRI)
            }

            tvPhoneNumber.setOnClickListener {
                makePhoneCall(Constant.CALL_CONTACT_DPLK)
            }

            tvEmail.setOnClickListener {
                val email = Constant.InvestasiConfig.EMAIL_ASSET_DPLK
                val intent = Intent(Intent.ACTION_SENDTO).apply {
                    data = Uri.parse(email)
                }
                startActivity(intent)
            }

            imgBawah.setOnClickListener {
                binding.nestedScrollview.smoothScrollTo(binding.nestedScrollview.bottom, 0)
            }
        }
    }

    private fun addFragment(it: OnboardingDplkResponse.HeaderContent): Fragment {
        val bundle = Bundle()
        bundle.putString(TAG_FRAGMENT, Gson().toJson(it))
        val fragment = DplkDashboardHeaderRevampFragment()
        fragment.arguments = bundle
        return fragment
    }

    private fun showBottomFragmentStatus() {
        val bottomInfo = BottomPembukaanBrifineFragment(
            mResponseOnboarding.pendingPopup.title,
            mResponseOnboarding.pendingPopup.description,
            mResponseOnboarding.pendingPopup.buttonString
        )
        bottomInfo.show(supportFragmentManager, "")
    }

    @SuppressLint("NotifyDataSetChanged")
    override fun onSuccessGetDashboardDplk(response: OnboardingDplkResponse) {
        mResponseOnboarding = response
        skeletonScreenDplkFaq?.hide()

        binding.apply {
            swipeRefresh.isRefreshing = false
            mFaqDplkResponse.clear()
            mFaqDplkResponse.addAll(mResponseOnboarding.faq)
            dplkFaqAdapter.differ.submitList(mFaqDplkResponse)
            dplkFaqAdapter.notifyDataSetChanged()
        }
    }

    override fun onSuccessPersonalDataDplk(response: PersonalDataDplkResponse) {
        StepPersonalDataDplkActivity.launchIntent(this, response)
    }

    override fun onSuccesCheckStatus(response: PaymentFtuDplkResponse) {
        if (response.immediatelyFlag) {
            ReceiptRegistDplkActivity.launchIntent(this@FirstTimeDplkActivity, response)
            finish()
        } else {
            showSnackbarErrorMessageRevamp(
                GeneralHelper.getString(R.string.txt_desc_massage_info_snackbar),
                ALERT_ERROR,
                this@FirstTimeDplkActivity,
                false
            )
        }
    }

    override fun onExceptionGeneralError(message: String) {
        showSnackbarErrorMessageRevamp(
            message, ALERT_ERROR, this@FirstTimeDplkActivity, false
        )
    }

    override fun onExceptionTrxExpired(message: String) {
        val i = Intent()
        i.putExtra(Constant.TAG_ERROR_MESSAGE, message)
        setResult(RESULT_CANCELED, i)
        finish()
    }

    override fun onSuccessListBrifine(response: ListPilihBrifineResponse) {
        ListDplkOptionRevampActivity.launchIntent(
            this@FirstTimeDplkActivity, response, isSimulation = true, isRegistered = true
        )
    }

    override fun onPageScrolled(
        position: Int,
        positionOffset: Float,
        positionOffsetPixels: Int
    ) {
        // do nothing
    }

    override fun onPageSelected(position: Int) {
        binding.apply {
            if (position == 1) {
                val layoutParams = llContentDetailInfo.layoutParams
                layoutParams.height = SizeHelper.dpToPx(this@FirstTimeDplkActivity, 600)
                llContentDetailInfo.layoutParams = layoutParams
                tvSeeMoreContent.gone()
            } else {
                tvSeeMoreContent.visible()
                if (isSeeMooreContent) {
                    val layoutParams = llContentDetailInfo.layoutParams
                    layoutParams.height = WindowManager.LayoutParams.WRAP_CONTENT
                    llContentDetailInfo.layoutParams = layoutParams
                    tvSeeMoreContent.text = GeneralHelper.getString(R.string.close)
                } else {
                    val layoutParams = llContentDetailInfo.layoutParams
                    layoutParams.height = SizeHelper.dpToPx(this@FirstTimeDplkActivity, 420)
                    llContentDetailInfo.layoutParams = layoutParams
                    tvSeeMoreContent.text = GeneralHelper.getString(R.string.see_more)
                }
            }
            GeneralHelper.changeTabsFontBoldRevamp(
                this@FirstTimeDplkActivity, linearLayout, position
            )
        }
    }


    override fun onPageScrollStateChanged(state: Int) {
        // do nothing
    }

    override fun onFuncAutomateScroll(isExpand: Boolean) {
        // do nothing
    }

    override fun onRefresh() {
        presenter.setUrlDashboardDplk(GeneralHelper.getString(R.string.url_dashboard_dplk))
        presenter.getDashboardDplk()
    }

    private fun setupAdapter() {
        binding.apply {
            mFaqDplkResponse.addAll(mResponseOnboarding.faq)
            dplkFaqAdapter = DplkFaqAdapter(
                this@FirstTimeDplkActivity
            )
            rvFaq.layoutManager = LinearLayoutManager(this@FirstTimeDplkActivity)
            dplkFaqAdapter.differ.submitList(mFaqDplkResponse)
            rvFaq.adapter = dplkFaqAdapter
        }
    }

    private fun makePhoneCall(phoneNumber: String) {
        val phoneIntent = Intent(Intent.ACTION_CALL, Uri.parse("tel:$phoneNumber"))
        if (ActivityCompat.checkSelfPermission(
                this, Manifest.permission.CALL_PHONE
            ) != PackageManager.PERMISSION_GRANTED
        ) {
            checkPermission()
            return
        }
        startActivity(phoneIntent)
    }

    private fun checkPermission() {
        val phoneCallPermission = Manifest.permission.CALL_PHONE
        if (ActivityCompat.checkSelfPermission(
                this, phoneCallPermission
            ) != PackageManager.PERMISSION_GRANTED
        ) {
            ActivityCompat.requestPermissions(
                this, arrayOf(phoneCallPermission), PERMISSIONS_ALL
            )
        } else {
            // Permission is already granted, make the call
        }
    }
}