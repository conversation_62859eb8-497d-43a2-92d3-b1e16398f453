package id.co.bri.brimo.ui.activities.simpedes;

import android.app.Activity;
import android.content.Intent;
import android.os.Bundle;
import android.os.SystemClock;
import android.util.Log;
import android.view.View;

import androidx.annotation.Nullable;
import androidx.fragment.app.FragmentTransaction;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.google.gson.Gson;

import java.util.List;

import javax.inject.Inject;

import id.co.bri.brimo.R;
import id.co.bri.brimo.adapters.BillingAmountAdapter;
import id.co.bri.brimo.adapters.DetailTransaksiAdapter;
import id.co.bri.brimo.contract.IPresenter.simpedes.IInquirySimpedesOpenPresenter;
import id.co.bri.brimo.contract.IView.simpedes.IInquirySimpedesOpenView;
import id.co.bri.brimo.databinding.ActivityInquirySimpedesOpenBinding;
import id.co.bri.brimo.domain.config.Constant;
import id.co.bri.brimo.domain.helpers.GeneralHelper;
import id.co.bri.brimo.models.AccountModel;
import id.co.bri.brimo.models.ParameterKonfirmasiModel;
import id.co.bri.brimo.models.apimodel.response.InquiryOpenRencanaResponse;
import id.co.bri.brimo.models.apimodel.response.KonfirmasiRencanaResponse;
import id.co.bri.brimo.ui.activities.base.BaseActivity;
import id.co.bri.brimo.ui.activities.britamarencana.KonfirmasiDataRencanaActivity;
import id.co.bri.brimo.ui.customviews.dialog.DialogInformation;
import id.co.bri.brimo.ui.customviews.dialog.DialogInformation.OnActionClick;
import id.co.bri.brimo.ui.fragments.SumberDanaFragment;

public class InquirySimpedesOpenActivity extends BaseActivity implements
        IInquirySimpedesOpenView,
        View.OnClickListener,
        SumberDanaFragment.SelectSumberDanaInterface,
        OnActionClick {

    private ActivityInquirySimpedesOpenBinding binding;

    @Inject
    IInquirySimpedesOpenPresenter<IInquirySimpedesOpenView> presenter;

    protected static final String TAG_RESPONSE = "response";
    protected InquiryOpenRencanaResponse inquiryRes;

    protected BillingAmountAdapter amountAdapter;
    protected DetailTransaksiAdapter detailTransaksiAdapter;
    protected AccountModel model;
    protected List<AccountModel> mListAccountModel;
    protected List<Integer> mListFailed;

    protected boolean isSaldoHold = false;
    protected double saldoNominal;
    protected String mSaldoString;
    protected String mAkunDefault;

    int counter = 0;
    protected String tanggal = "";

    public static void launchIntent(Activity caller, String inquiryResponse) {
        Intent intent = new Intent(caller, InquirySimpedesOpenActivity.class);
        intent.putExtra(TAG_RESPONSE, inquiryResponse);
        caller.startActivityForResult(intent, Constant.REQ_PAYMENT);
    }

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        binding = ActivityInquirySimpedesOpenBinding.inflate(getLayoutInflater());
        setContentView(binding.getRoot());

        if (getIntent().getExtras().getString(TAG_RESPONSE) != null)
            inquiryRes = new Gson().fromJson(getIntent().getExtras().getString(TAG_RESPONSE), InquiryOpenRencanaResponse.class);

        GeneralHelper.setToolbar(this, binding.toolbar.toolbar, GeneralHelper.getString(R.string.toolbar_simpedes));

        injectDependency();
        setView();
        setupAccount();
    }

    private void injectDependency() {
        getActivityComponent().inject(this);
        if (presenter != null) {
            presenter.setView(this);
            presenter.start();
            presenter.setUrlConfirm(GeneralHelper.getString(R.string.url_s3f_open_confirmation));
        }
    }

    private void setView() {
        //Detail target dan bulan
        binding.rvDetailPayment.setHasFixedSize(true);
        binding.rvDetailPayment.setLayoutManager(new LinearLayoutManager(getApplicationContext(), RecyclerView.VERTICAL, false));
        detailTransaksiAdapter = new DetailTransaksiAdapter(inquiryRes.getBillingDetail(), this);
        binding.rvDetailPayment.setAdapter(detailTransaksiAdapter);

        //Total tagihan per bulan
        binding.rvBillingAmount.setHasFixedSize(true);
        binding.rvBillingAmount.setLayoutManager(new LinearLayoutManager(this, RecyclerView.VERTICAL, false));
        amountAdapter = new BillingAmountAdapter(this, inquiryRes.getBillingAmount());
        binding.rvBillingAmount.setAdapter(amountAdapter);

        binding.itemLayoutBackground.setOnClickListener(this);
        binding.btnSubmit.setOnClickListener(this);
    }

    private void setupAccount() {
        //List Account
        model = new AccountModel();
        if (!inquiryRes.getAccountList().isEmpty()) {
            mListAccountModel = inquiryRes.getAccountList();
        }

        //get account default
        if (mListAccountModel != null) {
            for (AccountModel accountModel : mListAccountModel) {
                if (accountModel.getIsDefault() == 1) {
                    model = accountModel;
                    break;
                } else {
                    model = mListAccountModel.get(0);
                }
            }
        }

        if (model.getAcoountString() != null) {
            binding.tvNorek.setText(model.getAcoountString());
        }

        if (model.getName() != null) {
            binding.tvInisial.setText(GeneralHelper.formatInitialName(model.getName()));
        }

        if (model.getAcoount() != null) {
            if (model.getAcoount().equals(mAkunDefault)) {
                binding.tvSaldo.setText(GeneralHelper.formatNominalIDR(model.getCurrency(), mSaldoString));
            } else {
                saldoNominal = 0;
                binding.tvSaldo.setText("-");
            }
        } else {
            binding.tvSaldo.setText("-");
        }

        if (isSaldoHold)
            binding.ivAlertSaldo.setVisibility(View.VISIBLE);
        else binding.ivAlertSaldo.setVisibility(View.GONE);

        validationButton();
    }

    @Override
    public void onSelectSumberDana(AccountModel bankModel) {
        model = bankModel;
        binding.tvInisial.setText(GeneralHelper.formatInitialName(bankModel.getName()));
        binding.tvNorek.setText(model.getAcoountString());

        if (model.getSaldoReponse() != null) {
            binding.tvSaldo.setText(GeneralHelper.formatNominalIDR(model.getCurrency(), model.getSaldoReponse().getBalanceString()));
            saldoNominal = model.getSaldoReponse().getBalance();
            isSaldoHold = model.getSaldoReponse().isOnHold();
        } else {
            binding.tvSaldo.setText(String.format("%s%s", model.getCurrency(), "-"));
            saldoNominal = 0;
            isSaldoHold = false;
        }

        // saldo tertahan
        if (bankModel.getSaldoReponse().isOnHold()) {
            binding.ivAlertSaldo.setVisibility(View.VISIBLE);
            dialogInfoSaldoHold();
        } else binding.ivAlertSaldo.setVisibility(View.GONE);

        validationButton();
    }

    private void dialogInfoSaldoHold() {
        DialogInformation dialog = new DialogInformation(this, "maaf_maaf",
                GeneralHelper.getString(R.string.title_popup_saldo_tertahan),
                GeneralHelper.getString(R.string.desc_popup_saldo_tertahan),
                GeneralHelper.getString(R.string.ok), this, true, false);
        FragmentTransaction ft = getSupportFragmentManager().beginTransaction();
        ft.add(dialog, null);
        ft.commitAllowingStateLoss();
    }

    @Override
    public void onSendFailedList(List<Integer> list) {
        this.mListFailed = list;
    }

    @Override
    public void onClick(View v) {
        if (SystemClock.elapsedRealtime() - mLastClickTime < 1000) {
            return;
        }
        mLastClickTime = SystemClock.elapsedRealtime();
        int id = v.getId();
        switch (id) {
            case R.id.btn_submit:
                presenter.getDataConfirm(inquiryRes.getReferenceNumber(), model.getAcoount());
                break;
            case R.id.item_layout_background:
                counter++;
                if (mListAccountModel == null) {
                    GeneralHelper.showToast(this, GeneralHelper.getString(R.string.you_dont_have_any_accounts_yet));
                } else {
                    SumberDanaFragment fragmentSumberDana = new SumberDanaFragment(mListAccountModel, this, counter, mListFailed);
                    fragmentSumberDana.show(getSupportFragmentManager(), Constant.TAG_PICK_ACCOUNT);
                }
                break;
            default:
                break;
        }
    }

    @Override
    public void onActivityResult(int requestCode, int resultCode, @Nullable Intent data) {
        super.onActivityResult(requestCode, resultCode, data);
        if (requestCode == Constant.REQ_PAYMENT) {
            if (resultCode == RESULT_OK) {
                this.setResult(RESULT_OK, data);
            } else {
                this.setResult(RESULT_CANCELED, data);
            }
            this.finish();
        } else if (requestCode == Constant.REQ_FORGET_PIN && resultCode == RESULT_OK) {
            this.setResult(RESULT_OK);
            this.finish();
        }

    }

    public ParameterKonfirmasiModel setParameterKonfirmasi() {
        ParameterKonfirmasiModel parameterKonfirmasiModel = new ParameterKonfirmasiModel();
        parameterKonfirmasiModel.setDefaultIcon(GeneralHelper.getImageId(this, "ic_simpedes_bisa"));

        return parameterKonfirmasiModel;
    }

    @Override
    public void getDataSuccessConfirm(KonfirmasiRencanaResponse confrimRes) {
        Log.d("kentang 266", "getDataSuccessConfirm: " + new Gson().toJson(confrimRes));
        KonfirmasiDataRencanaActivity.launchIntent(this, false, false, true, confrimRes, setParameterKonfirmasi());
    }

    @Override
    public void onException93(String message) {
        Intent returnIntent = new Intent();

        returnIntent.putExtra(Constant.TAG_ERROR_MESSAGE, message);

        this.setResult(RESULT_CANCELED, returnIntent);
        this.finish();
    }

    @Override
    public void setDefaultSaldo(double defaultSaldo, String stringSaldo, String akunDefault, boolean saldoHold) {
        saldoNominal = defaultSaldo;
        mSaldoString = stringSaldo;
        mAkunDefault = akunDefault;
        isSaldoHold = saldoHold;
    }

    protected void validationButton() {
        if (inquiryRes != null) {
            if (saldoNominal < inquiryRes.getMinimumTransaction() && isSaldoHold) {
                binding.btnSubmit.setAlpha(0.3f);
                binding.btnSubmit.setEnabled(false);
            } else {
                binding.btnSubmit.setAlpha(1);
                binding.btnSubmit.setEnabled(true);
            }
        }
    }

    @Override
    public void onClickAction() {
        // do nothing
    }

    @Override
    protected void onDestroy() {
        super.onDestroy();
        binding = null;
    }
}