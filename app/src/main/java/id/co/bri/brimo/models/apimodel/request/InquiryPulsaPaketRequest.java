package id.co.bri.brimo.models.apimodel.request;

import com.google.gson.annotations.SerializedName;

public class InquiryPulsaPaketRequest {
    @SerializedName("phone_number")
    private String phoneNumber;

    @SerializedName("provider_id")
    private String providerId;

    @SerializedName("item")
    private String amount;

    public InquiryPulsaPaketRequest(String phoneNumber, String providerId, String amount) {
        this.phoneNumber = phoneNumber;
        this.providerId = providerId;
        this.amount = amount;
    }

    public String getPhoneNumber() {
        return phoneNumber;
    }

    public void setPhoneNumber(String phoneNumber) {
        this.phoneNumber = phoneNumber;
    }

    public String getProviderId() {
        return providerId;
    }

    public void setProviderId(String providerId) {
        this.providerId = providerId;
    }

    public String getAmount() {
        return amount;
    }

    public void setAmount(String amount) {
        this.amount = amount;
    }
}
