package id.co.bri.brimo.ui.activities.base

import android.os.Bundle
import android.os.CountDownTimer
import android.view.KeyEvent
import android.view.View
import android.widget.TextView
import androidx.core.content.ContextCompat
import androidx.recyclerview.widget.GridLayoutManager
import id.co.bri.brimo.R
import id.co.bri.brimo.adapters.PinNumberAdapter
import id.co.bri.brimo.adapters.baseadapter.base.BasePinAdapter
import id.co.bri.brimo.adapters.pinadapter.OtpRevampAdapter
import id.co.bri.brimo.databinding.ActivityApplyVccOtpBinding
import id.co.bri.brimo.domain.helpers.GeneralHelper
import id.co.bri.brimo.ui.customviews.pin.InsertPinNumbers
import id.co.bri.brimo.ui.fragments.registrasi.BottomDialogKirimUlangFragment
import id.co.bri.brimo.util.extension.setBackgroundTransparent

abstract class BaseOtpRevampActivity : BaseActivity(),
    PinNumberAdapter.OnPinNumberListener,
    BasePinAdapter.PinAdapterListener,
    BottomDialogKirimUlangFragment.OnBackPressUlangi,
    View.OnKeyListener {

    private var countDownTimer: CountDownTimer? = null
    private val second = 1000

    private var otpMethod = OtpMethod.WA

    private lateinit var otpRevampAdapter: OtpRevampAdapter
    private lateinit var pinNumberAdapter: PinNumberAdapter
    private lateinit var pinOtpLayoutManager: GridLayoutManager
    private lateinit var pinPadLayoutManager: GridLayoutManager
    private lateinit var binding: ActivityApplyVccOtpBinding

    protected abstract fun setContent()
    protected abstract fun setPhoneNumber(): String
    protected abstract fun setInfoUi(tvInfo: TextView)
    protected abstract fun setOtpMethod(): OtpMethod
    protected abstract fun setExpiredInSecond(): Int
    protected abstract fun onCompleteOtp(otpString: String)
    protected abstract fun onResendClick(otpMethod: String)
    protected open fun isWithDialogKirimUlang(): Boolean = true

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        binding = ActivityApplyVccOtpBinding.inflate(layoutInflater)
        setContentView(binding.root)
        setupView()
    }

    override fun onKey(v: View?, keyCode: Int, event: KeyEvent?): Boolean {
        return false
    }

    override fun onPinClicked(pinNumber: Int) {
        otpRevampAdapter.addPin(pinNumber.toString())
    }

    override fun onDeleteClicked() {
        otpRevampAdapter.deletePin()
    }

    override fun notifyChanges() {
        otpRevampAdapter.notifyDataSetChanged()
    }

    override fun onComplete(string: String) {
        onCompleteOtp(string)
    }

    override fun itemResending(method: String) {
        onResendClick(method)
    }

    private fun setupView() {
        GeneralHelper.setToolbarRevamp(this, binding.toolbarRevamp.toolbar, GeneralHelper.getString(R.string.konfirmasi_otp))
        setBackgroundTransparent()

        setContent()

        otpMethod = setOtpMethod()
        setInfoUi(binding.tvDescOtp)

        otpRevampAdapter = OtpRevampAdapter(this, 1)
        pinNumberAdapter = PinNumberAdapter(InsertPinNumbers.getPinNumberList(this))
        pinOtpLayoutManager = GridLayoutManager(this, 6)
        pinPadLayoutManager = GridLayoutManager(this, 3)

        pinNumberAdapter.onPinNumberListener = this
        otpRevampAdapter.setListener(this)
        binding.rvBox.layoutManager = pinOtpLayoutManager
        binding.rvBox.adapter = otpRevampAdapter
        binding.rvInputOtp.layoutManager = pinPadLayoutManager
        binding.rvInputOtp.adapter = pinNumberAdapter

        setTextTimer(setExpiredInSecond())

        binding.tvUlangi.setOnClickListener {
            if (isWithDialogKirimUlang()) bottomSheetResending()
            else onResendClick(getString(R.string.sms))
        }
    }

    private fun bottomSheetResending() {
        val dialogFragment = BottomDialogKirimUlangFragment(this)
        dialogFragment.show(supportFragmentManager, "")
        dialogFragment.isCancelable = true
    }

    fun setTextTimer(timer: Int) {
        val countDown: Int = second * timer
        countDownTimer = object : CountDownTimer(countDown.toLong(), second.toLong()) {
            override fun onTick(millisUntilFinished: Long) {
                val seconds: Int = millisUntilFinished.toInt() / second
                val timeFormat = GeneralHelper.getTimeFormat(seconds)
                binding.tvTimer.text = String.format(
                    resources.getString(R.string.countdown_otp00_00),
                    timeFormat[1], timeFormat[2]
                )

                binding.tvUlangi.setTextColor(ContextCompat.getColor(applicationContext, R.color.neutral_light50))
                binding.tvUlangi.isEnabled = false
            }

            override fun onFinish() {
                binding.tvTimer.text = GeneralHelper.getString(R.string.time00_00)
                binding.tvUlangi.setTextColor(ContextCompat.getColor(applicationContext, R.color.neutral_light10))
                binding.tvUlangi.isEnabled = true
            }
        }.start()
    }

    fun deleteAllPin() {
        otpRevampAdapter.deleteAllPin()
    }

    fun setResendUi(expiredInSecond: Int? = null) {
        countDownTimer?.cancel()
        setTextTimer(expiredInSecond ?: setExpiredInSecond())
    }
}

enum class OtpMethod(val method: String) {
    SMS("SMS"),
    WA("WA")
}