package id.co.bri.brimo.ui.fragments.dashboardLifestyle

import android.Manifest
import android.annotation.SuppressLint
import android.content.Context
import android.content.Intent
import android.net.Uri
import android.os.Build
import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.annotation.RequiresApi
import androidx.browser.customtabs.CustomTabsIntent
import androidx.core.app.ActivityCompat
import androidx.core.content.ContextCompat
import androidx.core.content.FileProvider
import androidx.recyclerview.widget.LinearLayoutManager
import id.co.bri.brimo.R
import id.co.bri.brimo.adapters.dashboardLifestyle.PromoLifestyleAdapter
import id.co.bri.brimo.contract.IPresenter.lifestyle.IPromoLifestylePresenter
import id.co.bri.brimo.contract.IView.lifestyle.IPromoLifestyleView
import id.co.bri.brimo.databinding.FragmentPromoLifestyleBinding
import id.co.bri.brimo.domain.config.Constant
import id.co.bri.brimo.domain.config.LifestyleConfig
import id.co.bri.brimo.domain.config.LifestyleConfig.MenuLifestyleCode
import id.co.bri.brimo.domain.helpers.GeneralHelper
import id.co.bri.brimo.models.apimodel.request.PartnerIdRequest
import id.co.bri.brimo.models.apimodel.response.*
import id.co.bri.brimo.models.apimodel.response.lifestyle.PromoDataView
import id.co.bri.brimo.ui.activities.DetailPromoActivity
import id.co.bri.brimo.ui.activities.base.BaseActivity
import id.co.bri.brimo.ui.activities.lifestyle.WebviewLifestyleActivity
import id.co.bri.brimo.ui.activities.lifestyle.ekspedisi.WebviewEkspedisiActivity
import id.co.bri.brimo.ui.activities.travel.FormBusActivity
import id.co.bri.brimo.ui.activities.travel.WebViewTravelActivity
import id.co.bri.brimo.ui.customviews.dialog.DialogSetDefaultRevamp
import id.co.bri.brimo.ui.fragments.BaseFragment
import java.io.File
import java.io.FileOutputStream
import java.io.IOException
import javax.inject.Inject

class PromoLifestyleFragment(
    private var sellingDataViews: List<PromoDataView>, private var mPosition: Int
) : BaseFragment(), IPromoLifestyleView, PromoLifestyleAdapter.OnClickPromoLifestyle {

    private lateinit var binding: FragmentPromoLifestyleBinding
    private var promoLifestyleAdapter: PromoLifestyleAdapter? = null
    private lateinit var listPromo: List<DetailPromoResponse>

    @Inject
    lateinit var presenter: IPromoLifestylePresenter<IPromoLifestyleView>

    constructor() : this(emptyList(), 0) {

    }

    override fun onCreateView(
        inflater: LayoutInflater, container: ViewGroup?, savedInstanceState: Bundle?
    ): View {
        // Inflate the layout for this fragment
        binding = FragmentPromoLifestyleBinding.inflate(inflater, container, false)
        return binding.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        injectDependency()
        setupView()
    }

    private fun injectDependency() {
        activityComponent.inject(this)
        presenter.view = this
        presenter.setUrlDetailPromo(GeneralHelper.getString(R.string.url_detail_promo))
        presenter.setUrlBusShuttle(GeneralHelper.getString(R.string.url_form_bus))
        presenter.setUrlKai(GeneralHelper.getString(R.string.url_travel_kai_webview))
        presenter.setUrlWebviewTugu(GeneralHelper.getString(R.string.url_webview_new))
        presenter.start()
    }

    @SuppressLint("NotifyDataSetChanged")
    private fun setupView() {
        listPromo = sellingDataViews[mPosition].dataView

        promoLifestyleAdapter = PromoLifestyleAdapter(requireActivity(), listPromo, this)
        binding.rvPromoLifestyle.layoutManager = LinearLayoutManager(
            requireActivity(), LinearLayoutManager.HORIZONTAL, false
        )

        promoLifestyleAdapter?.setItems(listPromo)
        promoLifestyleAdapter?.notifyDataSetChanged()

        binding.rvPromoLifestyle.setHasFixedSize(true)
        binding.rvPromoLifestyle.adapter = promoLifestyleAdapter
    }

    override fun onClickPromoItem(detailPromoResponse: DetailPromoResponse) {
        when (detailPromoResponse.appType) {
            1, 2 -> {
                presenter.getDetailPromoItem(detailPromoResponse)
            }
            3, 4 -> {
                openBannerContent(detailPromoResponse)
            }
            5 -> {
                // To Mini Program.
            }
            else -> {
                presenter.getDetailPromoItem(detailPromoResponse)
            }
        }
    }

    override fun onSuccessGetDetailPromo(
        promoResponse: PromoResponse, detailPromoResponse: DetailPromoResponse
    ) {
        DetailPromoActivity.launchIntentPromoLifestyle(
            requireActivity(),
            promoResponse.promo,
            false,
            false,
            false,
            detailPromoResponse,
            true
        )
    }

    override fun onSuccessGetFormBus(cityFormResponse: CityFormResponse) {
        FormBusActivity.launchIntent(requireActivity(), cityFormResponse)
    }

    override fun onSuccessGetFormKai(
        urlWebViewResponse: UrlWebViewResponse, mTitle: String, appType: Int
    ) {
        if (appType == 3) {
            WebViewTravelActivity.launchIntent(
                requireActivity(),
                urlWebViewResponse.url,
                urlWebViewResponse.sessionId,
                Constant.TravelMenu.TRAVEL_KAI,
                mTitle
            )
        } else if (appType == 4) {
            openInDefaultBrowser(urlWebViewResponse.url)
        }
    }

    override fun onSuccessGetWebviewTugu(
        generalWebviewResponse: GeneralWebviewResponse,
        mTitle: String,
        mCodeMenu: String,
        appType: Int
    ) {
        if (appType == 3) {
            when (mCodeMenu) {
                MenuLifestyleCode.MENU_KERETA_API.menuCode,
                MenuLifestyleCode.MENU_PESAWAT.menuCode -> {
                    WebViewTravelActivity.launchIntent(
                        requireActivity(),
                        generalWebviewResponse.webviewData.url,
                        generalWebviewResponse.sessionId,
                        mCodeMenu,
                        mTitle
                    )
                }

                LifestyleConfig.MenuLifestyleCode.MENU_MOKIRIM.menuCode -> {
                    WebviewEkspedisiActivity.launchIntent(
                        requireActivity(),
                        generalWebviewResponse.webviewData.url,
                        GeneralHelper.getString(R.string.txt_kirim_barang),
                        LifestyleConfig.Lifestyle.KIRIM_BARANG,
                        GeneralHelper.getString(R.string.flag_url_brimo),
                        generalWebviewResponse.sessionId
                    )
                }

                else -> {
                    WebviewLifestyleActivity.launchIntent(
                        requireActivity(),
                        generalWebviewResponse.webviewData.url,
                        generalWebviewResponse.sessionId,
                        mTitle,
                        "",
                        "",
                        mCodeMenu,
                        generalWebviewResponse.webviewData.postData
                    )
                }
            }
        } else if (appType == 4) {
            openInDefaultBrowser(generalWebviewResponse.webviewData.url)
        }
    }

    override fun onExceptionTrxExpired(desc: String?) {
        val returnIntent = Intent()
        returnIntent.putExtra(Constant.TAG_ERROR_MESSAGE, desc)
        requireActivity().setResult(BaseActivity.RESULT_CANCELED, returnIntent)
        requireActivity().finish()
    }

    override fun showIndihomeConfirmation(
        partnerIdRequest: PartnerIdRequest?, titleWebview: String, codeMenu: String, appType: Int
    ) {
        val dialogFragment = DialogSetDefaultRevamp(
            object : DialogSetDefaultRevamp.DialogDefaultListener {
                override fun onClickYesDefault(requestId: Int) {
                    presenter.confirmIndihomeRegistration(
                        PartnerIdRequest(partnerIdRequest?.partnerId),
                        titleWebview,
                        codeMenu,
                        appType
                    )
                }

                override fun onClickNoDefault(requestId: Int) {}
            },
            GeneralHelper.getString(R.string.txt_dialog_title_indihome_registration),
            GeneralHelper.getString(R.string.txt_dialog_description_indihome_registration),
            GeneralHelper.getString(R.string.ya_lanjutkan),
            GeneralHelper.getString(R.string.batal),
            0
        )
        dialogFragment.show(requireActivity().supportFragmentManager, DialogSetDefaultRevamp.TAG)
    }

    private fun openBannerContent(detailPromoResponse: DetailPromoResponse) {
        if (detailPromoResponse.featureCode == MenuLifestyleCode.MENU_KERETA_API.menuCode) {
            presenter.getFormKai(detailPromoResponse.featureCode, detailPromoResponse.appType)
        } else if (detailPromoResponse.featureCode == MenuLifestyleCode.MENU_INDIHOME.menuCode) {
            val permissions: Array<String> = permissionsCheck()
            if (!hasPermissions(requireContext(), *permissions)) {
                ActivityCompat.requestPermissions(requireActivity(), permissions, 1)
            } else {
                presenter.getIndihomeRegistrationData(
                    PartnerIdRequest(detailPromoResponse.partnerId),
                    detailPromoResponse.title,
                    detailPromoResponse.featureCode,
                    detailPromoResponse.appType
                )
            }
        } else {
            presenter.getWebViewTugu(
                PartnerIdRequest(detailPromoResponse.partnerId),
                detailPromoResponse.title,
                detailPromoResponse.featureCode,
                detailPromoResponse.appType
            )
        }
    }

    private fun permissionsCheck(): Array<String> {
        val permission: Array<String> = if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU) {
            permissionsIndiHome33
        } else {
            permissionsIndiHome
        }
        return permission
    }

    private val permissionsIndiHome = arrayOf(
        Manifest.permission.WRITE_EXTERNAL_STORAGE,
        Manifest.permission.READ_EXTERNAL_STORAGE,
        Manifest.permission.CAMERA,
        Manifest.permission.ACCESS_FINE_LOCATION,
        Manifest.permission.ACCESS_COARSE_LOCATION
    )

    @RequiresApi(Build.VERSION_CODES.TIRAMISU)
    private val permissionsIndiHome33 = arrayOf(
        Manifest.permission.READ_MEDIA_IMAGES,
        Manifest.permission.CAMERA,
        Manifest.permission.ACCESS_FINE_LOCATION,
        Manifest.permission.ACCESS_COARSE_LOCATION
    )

    fun openHtmlInChrome(context: Context, htmlString: String) {
        try {
            // Simpan file HTML ke direktori cache
            val htmlFile = File(context.getExternalFilesDir(""), "response.html")
            val fos = FileOutputStream(htmlFile)
            fos.write(htmlString.toByteArray())
            fos.close()

            // Buat URI menggunakan FileProvider
            val htmlUri =
                FileProvider.getUriForFile(context, context.packageName + ".provider", htmlFile)

            // Buat Intent untuk membuka file HTML di Chrome
            val intent = Intent(Intent.ACTION_VIEW)
            intent.setDataAndType(htmlUri, "text/html")
            intent.addFlags(Intent.FLAG_GRANT_READ_URI_PERMISSION)
            intent.setPackage("com.android.chrome") // Spesifik ke Chrome
            context.startActivity(intent)
        } catch (e: IOException) {
            e.printStackTrace()
        }
    }

    fun openCustomTab(mUrl: String) {
        val builder = CustomTabsIntent.Builder()
        builder.setToolbarColor(ContextCompat.getColor(requireContext(), R.color.toolbar_blue))
        val customTabsIntent = builder.build()
        customTabsIntent.intent.setPackage("com.android.chrome")
        customTabsIntent.intent.addFlags(Intent.FLAG_ACTIVITY_CLEAR_TOP)
        customTabsIntent.intent.data = Uri.parse(mUrl)
        startActivityForResult(
            customTabsIntent.intent, Constant.CHROME_CUSTOM_TAB_REQUEST_CODE
        )
    }

    fun openInDefaultBrowser(url: String) {
        val intent = Intent(Intent.ACTION_VIEW, Uri.parse(url))
        intent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK) 
        startActivity(intent)
    }
}