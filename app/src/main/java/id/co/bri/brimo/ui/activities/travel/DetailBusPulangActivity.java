package id.co.bri.brimo.ui.activities.travel;

import android.app.Activity;
import android.content.Intent;
import android.os.Bundle;
import android.view.View;
import android.widget.LinearLayout;

import androidx.annotation.Nullable;
import androidx.fragment.app.Fragment;
import androidx.viewpager.widget.ViewPager;

import java.util.ArrayList;
import java.util.List;

import id.co.bri.brimo.R;
import id.co.bri.brimo.adapters.CatatanKeuanganAdapter;
import id.co.bri.brimo.databinding.ActivityDetailBusBinding;
import id.co.bri.brimo.domain.config.Constant;
import id.co.bri.brimo.domain.helpers.GeneralHelper;
import id.co.bri.brimo.models.apimodel.request.SearchBusRequest;
import id.co.bri.brimo.models.apimodel.response.PickRouteResponse;
import id.co.bri.brimo.models.apimodel.response.SearhBusResponse;
import id.co.bri.brimo.ui.activities.base.BaseActivity;
import id.co.bri.brimo.ui.fragments.travel.DetailFiturFragment;
import id.co.bri.brimo.ui.fragments.travel.DetailRuteFragment;
import id.co.bri.brimo.ui.fragments.travel.DetailTermFragment;

public class DetailBusPulangActivity extends BaseActivity implements View.OnClickListener,
        ViewPager.OnPageChangeListener {

    private ActivityDetailBusBinding binding;

    private CatatanKeuanganAdapter tabAdapter;
    private List<String> titleList;
    private List<Fragment> fragmentList;
    private LinearLayout lyTabs;

    private List<PickRouteResponse> pickRouteResponseList;
    private static PickRouteResponse mPickRouteResponse;
    private static PickRouteResponse mRouteBusPergi;
    private static SearchBusRequest mSearchBusRequest;
    public static boolean mIsPulangPergi;
    public static SearhBusResponse mSearhBusResponse;
    public static boolean mIsAsalCity;
    public static boolean mIsTujuanCity;
    public static String mStringAsal;
    public static String mStringTujuan;

    private DetailFiturFragment detailFiturFragment;
    private DetailRuteFragment detailRuteFragment;
    private DetailTermFragment detailTermFragment;

    public static void launchIntent(Activity caller, PickRouteResponse routeBusPergi, PickRouteResponse pickRouteResponse, SearchBusRequest searchBusRequest, SearhBusResponse searhBusResponse, boolean isAsalCity, boolean isTujuanCity, String stringAsal, String stringTujuan, boolean isPulangPergi) {
        Intent intent = new Intent(caller, DetailBusPulangActivity.class);
        mPickRouteResponse = pickRouteResponse;
        mRouteBusPergi = routeBusPergi;
        mSearchBusRequest = searchBusRequest;
        mSearhBusResponse = searhBusResponse;
        mIsAsalCity = isAsalCity;
        mIsTujuanCity = isTujuanCity;
        mStringAsal = stringAsal;
        mStringTujuan = stringTujuan;
        mIsPulangPergi = isPulangPergi;
        caller.startActivityForResult(intent, Constant.REQ_PAYMENT);
    }

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        binding = ActivityDetailBusBinding.inflate(getLayoutInflater());
        setContentView(binding.getRoot());

        setupView();
    }

    public void setupView() {

        //TO DO tiket pulang pergi
        GeneralHelper.setToolbar(this, binding.toolbar.toolbar, GeneralHelper.getString(R.string.return_vehicle));
        fragmentList = new ArrayList<>();

        if (!mPickRouteResponse.getRouteDetail().getStatus().equalsIgnoreCase("AVAILABLE")) {
            binding.btnSubmit.setEnabled(false);
            binding.btnSubmit.setAlpha((float) 0.3);
        } else {
            binding.btnSubmit.setEnabled(true);
            binding.btnSubmit.setAlpha(1);
        }

        detailFiturFragment = DetailFiturFragment.newInstance(mPickRouteResponse.getRouteDetail());
        detailRuteFragment = DetailRuteFragment.newInstance(mPickRouteResponse.getRouteDetail());
        detailTermFragment = DetailTermFragment.newInstance(mPickRouteResponse.getAdditionalInfo());

        fragmentList.add(detailFiturFragment);
        fragmentList.add(detailRuteFragment);
        fragmentList.add(detailTermFragment);

        titleList = new ArrayList<>();
        titleList.add("Fitur");
        titleList.add("Rute");
        titleList.add("Tiket");

        tabAdapter = new CatatanKeuanganAdapter(getSupportFragmentManager(), this, fragmentList, titleList);
        binding.vpDetailBus.setAdapter(tabAdapter);
        binding.tabDetailBus.setViewPager(binding.vpDetailBus);

        //Add bold effect on selected tab
        binding.tabDetailBus.setOnPageChangeListener(this);
        lyTabs = (LinearLayout) binding.tabDetailBus.getChildAt(0);

        //change default style toolbar font
        GeneralHelper.changeSmartTabsFont(this, lyTabs, 0);

        binding.btnSubmit.setOnClickListener(this);

        //setup price
        String price = mPickRouteResponse.getRouteDetail().getFare().getCurrencyValue().getAmount().toString();
        binding.tvBusPrice.setText(String.format("Rp%s", GeneralHelper.formatNominal(price)));
    }

    @Override
    public void onClick(View view) {
        FormPemesananTiketActivity.launchIntent(this, mRouteBusPergi, mPickRouteResponse, mSearchBusRequest, true);
    }

    @Override
    public void onPageScrolled(int position, float positionOffset, int positionOffsetPixels) {

    }

    @Override
    public void onPageSelected(int position) {
        GeneralHelper.changeSmartTabsFont(this, lyTabs, position);
    }

    @Override
    public void onPageScrollStateChanged(int state) {

    }

    @Override
    protected void onActivityResult(int requestCode, int resultCode, @Nullable Intent data) {
        super.onActivityResult(requestCode, resultCode, data);
        if (requestCode == Constant.REQ_PAYMENT) {
            if (resultCode == RESULT_OK) {
                this.setResult(RESULT_OK, data);
                this.finish();
            } else {
                this.setResult(RESULT_CANCELED, data);
//                this.finish();
            }
        }
    }

    @Override
    protected void onDestroy() {
        super.onDestroy();
        binding = null;
    }
}