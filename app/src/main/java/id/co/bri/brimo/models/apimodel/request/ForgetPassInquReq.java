package id.co.bri.brimo.models.apimodel.request;

import com.google.gson.annotations.Expose;
import com.google.gson.annotations.SerializedName;

public class ForgetPassInquReq {
    @SerializedName("username")
    @Expose
    private String username;
    @SerializedName("born_date")
    @Expose
    private String bornDate;

    @SerializedName("pin")
    @Expose
    private String pin;

    public ForgetPassInquReq(String username, String bornDate, String pin) {
        this.username = username;
        this.bornDate = bornDate;
        this.pin = pin;
    }

    public String getUsername() {
        return username;
    }

    public void setUsername(String username) {
        this.username = username;
    }

    public String getBornDate() {
        return bornDate;
    }

    public void setBornDate(String bornDate) {
        this.bornDate = bornDate;
    }

    public String getPin() {
        return pin;
    }

    public void setPin(String pin) {
        this.pin = pin;
    }
}
