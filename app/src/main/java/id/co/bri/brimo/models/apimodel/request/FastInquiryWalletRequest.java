package id.co.bri.brimo.models.apimodel.request;

import com.google.gson.annotations.SerializedName;

public class FastInquiryWalletRequest extends FastMenuRequest {
    @SerializedName("purchase_number")
    private String billingNumber;

    @SerializedName("ewallet_code")
    private String billingType;

    public FastInquiryWalletRequest(String username, String tokenKey) {
        super(username, tokenKey);
    }

    public FastInquiryWalletRequest(FastMenuRequest request, String billingNumber, String billingType) {
        super(request.getUsername(), request.getTokenKey());
        this.billingNumber = billingNumber;
        this.billingType = billingType;
    }

    public String getBillingNumber() {
        return billingNumber;
    }

    public void setBillingNumber(String billingNumber) {
        this.billingNumber = billingNumber;
    }

    public String getBillingType() {
        return billingType;
    }

    public void setBillingType(String billingType) {
        this.billingType = billingType;
    }

}
