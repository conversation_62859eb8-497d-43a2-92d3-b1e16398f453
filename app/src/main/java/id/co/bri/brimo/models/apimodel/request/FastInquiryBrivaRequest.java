package id.co.bri.brimo.models.apimodel.request;

import com.google.gson.annotations.SerializedName;

public class FastInquiryBrivaRequest extends FastMenuRequest {
    @SerializedName("briva_number")
    private String billingNumber;

    @SerializedName("billing_type")
    private String billingType;

    public FastInquiryBrivaRequest(String username, String tokenKey) {
        super(username, tokenKey);
    }

    public FastInquiryBrivaRequest(String username, String tokenKey, String billingNumber, String billingType) {
        super(username, tokenKey);
        this.billingNumber = billingNumber;
        this.billingType = billingType;
    }

    public FastInquiryBrivaRequest(FastMenuRequest request, String billingNumber, String billingType) {
        super(request.getUsername(),request.getTokenKey());
        this.billingNumber = billingNumber;
        this.billingType = billingType;
    }

    public String getBillingNumber() {
        return billingNumber;
    }

    public void setBillingNumber(String billingNumber) {
        this.billingNumber = billingNumber;
    }

    public String getBillingType() {
        return billingType;
    }

    public void setBillingType(String billingType) {
        this.billingType = billingType;
    }

}
