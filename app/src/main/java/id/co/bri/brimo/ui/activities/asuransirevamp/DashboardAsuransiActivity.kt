package id.co.bri.brimo.ui.activities.asuransirevamp

import android.Manifest
import android.app.Activity
import android.content.Intent
import android.content.pm.PackageManager
import android.graphics.Color
import android.os.Build
import android.os.Bundle
import android.view.View
import android.widget.LinearLayout
import androidx.core.app.ActivityCompat
import androidx.fragment.app.Fragment
import androidx.fragment.app.FragmentManager
import androidx.fragment.app.FragmentStatePagerAdapter
import androidx.viewpager.widget.ViewPager
import com.ethanhua.skeleton.Skeleton
import com.ethanhua.skeleton.SkeletonScreen
import id.co.bri.brimo.R
import id.co.bri.brimo.contract.IPresenter.asuransiRevamp.IDashboardAsuransiPresenter
import id.co.bri.brimo.contract.IView.asuransiRevamp.IDashboardAsuransiView
import id.co.bri.brimo.data.preference.BRImoPrefRepository
import id.co.bri.brimo.databinding.ActivityDashboardAsuransiBinding
import id.co.bri.brimo.domain.config.Constant
import id.co.bri.brimo.domain.helpers.GeneralHelper
import id.co.bri.brimo.domain.helpers.bubbleShowCaseView.BubbleShowCase
import id.co.bri.brimo.domain.helpers.bubbleShowCaseView.BubbleShowCaseBuilder
import id.co.bri.brimo.domain.helpers.bubbleShowCaseView.BubbleShowCaseSequence
import id.co.bri.brimo.models.apimodel.request.asuransirevamp.DetailInsuranceRequest
import id.co.bri.brimo.models.apimodel.response.WebviewBrilifeLamaResponse
import id.co.bri.brimo.models.apimodel.response.WebviewBrilifeResponse
import id.co.bri.brimo.models.apimodel.response.asuransi.DashboardAsuransiResponse
import id.co.bri.brimo.models.apimodel.response.asuransi.DetailAsuransiResponse
import id.co.bri.brimo.models.apimodel.response.asuransi.ListAsuransiResponse
import id.co.bri.brimo.ui.activities.FormAsuransiActivity
import id.co.bri.brimo.ui.activities.asuransi.MicrositeActivity
import id.co.bri.brimo.ui.activities.asuransi.MicrositeLamaActivity
import id.co.bri.brimo.ui.activities.base.BaseActivity
import id.co.bri.brimo.ui.customviews.dialog.DialogExitCustom
import id.co.bri.brimo.ui.fragments.asuransiRevamp.DashboardAsuransiFragment
import javax.inject.Inject

class DashboardAsuransiActivity : BaseActivity(), IDashboardAsuransiView, DashboardAsuransiFragment.DialogDefaultListener, ViewPager.OnPageChangeListener, View.OnClickListener,
    DialogExitCustom.DialogDefaultListener{
    lateinit var binding : ActivityDashboardAsuransiBinding

    var response : DashboardAsuransiResponse? = null

    private var titleList: ArrayList<String> = ArrayList()
    var lyTabs : LinearLayout? = null
    var skeletonScreen: SkeletonScreen? = null
    var skeletonScreen1: SkeletonScreen? = null

    val s = BubbleShowCaseSequence()

    var brImoPrefRepository = BRImoPrefRepository(this)

    var profile: BubbleShowCaseBuilder? = null
    var bayar:BubbleShowCaseBuilder? = null
    var listAsuransi:BubbleShowCaseBuilder? = null
    var flag01 : Boolean = false
    var mProductCode : String? =null
    var partnerId : String? = null
    var mCompanyName : String? = null
    var typeId : String? = null
    var isCLick: Boolean = false


    @Inject
    lateinit var presenter : IDashboardAsuransiPresenter<IDashboardAsuransiView>


    companion object {
        @JvmStatic
        fun launchIntent(caller: Activity, fromFastMenu: Boolean) {
            val intent = Intent(caller, DashboardAsuransiActivity::class.java)
            isFromFastMenu = fromFastMenu
            caller.startActivityForResult(intent, Constant.REQ_PAYMENT)
        }
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        binding = ActivityDashboardAsuransiBinding.inflate(layoutInflater)
        setContentView(binding.root)

        injectDepedency()
        setupView()
    }

    private fun setupView() {
        //toolbar
        GeneralHelper.setToolbarRevamp(this, binding.tbAsuransi.toolbar,GeneralHelper.getString(R.string.asuransi))
        binding.btnPolish.setOnClickListener(this)
        binding.rlBayarAsuransi.setOnClickListener(this)
    }



    private fun injectDepedency() {
        activityComponent.inject(this)
        presenter.view = this
        presenter.start()
        presenter.setUrlDashboard(GeneralHelper.getString(R.string.url_dashboard_asuransi))
        presenter.setUrlList(GeneralHelper.getString(R.string.url_list_asuransi))
        presenter.setUrlInsuranceDetail(GeneralHelper.getString(R.string.url_insurance_asmik_detail))
        presenter.setUrlProduct(GeneralHelper.getString(R.string.url_webview_new))
        presenter.setUrlProdukLama(GeneralHelper.getString(R.string.url_webview_brilife_lama))
        presenter.getDataDashboard()
    }

    override fun onSuccessGetData(dashboardAsuransiResponse: DashboardAsuransiResponse) {
        response = dashboardAsuransiResponse

        for (i in response!!.listProducts!!.indices) {
            titleList.add(response!!.listProducts!![i].insuranceType!!)
        }


        val catatanKeuanganFragmentAdapter = MyPagerAdapter(supportFragmentManager, response!!.listProducts!!,this,titleList)
        binding.vpAsuransi.adapter = catatanKeuanganFragmentAdapter
        binding.tabCatatanKeuangan.setViewPager(binding.vpAsuransi)


        //Add bold effect on selected tab
        binding.tabCatatanKeuangan.setOnPageChangeListener(this)
        lyTabs =  binding.tabCatatanKeuangan.getChildAt(0) as LinearLayout


        //change default style toolbar font
        GeneralHelper.changeTabsFontDashboardAsuransiRevamp(this, lyTabs, 0)

        addBubbleShowCase()
        if (!brImoPrefRepository.asuransiBubble) {
            brImoPrefRepository.saveAsuransiBubble(true)
            s.show()
        }
    }

    override fun onSuccessGetList(response: ListAsuransiResponse) {
        ListAsuransiActivity.launchIntent(this,response,false,false)
    }

    override fun onSuccessDetailInsurance(response: DetailAsuransiResponse.InformationSection) {
        DetailBeliAsuransiActivity.launchIntent(this,false, response,mProductCode!!)
    }

    override fun onSuccess01() {
        ListAsuransiActivity.launchIntent(this,false,true)
    }

    private fun addBubbleShowCase() {
        try {
            bayar = BubbleShowCaseBuilder(this) //Activity instance
                    .title("Kumpulan polis aktif ") //Any title for the bubble view
                    .description(GeneralHelper.getString(R.string.desc_tutorial_1))
                    .backgroundColor(Color.WHITE)
                    .textColor(Color.BLACK)
                    .buttonTitle("Berikutnya")
                    .targetView(binding.rlTilte)
                    .enableLewati(true)
                    .enableViewClose(true)
                    .textViewLewati("Lewati")
                    .arrowPosition(BubbleShowCase.ArrowPosition.TOP)
            profile = BubbleShowCaseBuilder(this) //Activity instance
                    .title("Bayar Asuransi lewat BRImo") //Any title for the bubble view
                    .description(GeneralHelper.getString(R.string.desc_tutorial_2))
                    .backgroundColor(Color.WHITE)
                    .textColor(Color.BLACK)
                    .buttonTitle("Berikutnya")
                    .targetView(binding.cardView)
                    .enableLewati(true)
                    .textViewLewati("Lewati")
                    .arrowPosition(BubbleShowCase.ArrowPosition.TOP)
            listAsuransi = BubbleShowCaseBuilder(this) //Activity instance
                    .title("Banyak produk asuransi, siap lindungi Anda") //Any title for the bubble view
                    .description(GeneralHelper.getString(R.string.desc_tutorial_3))
                    .backgroundColor(Color.WHITE)
                    .textColor(Color.BLACK)
                    .targetView(binding.tabCatatanKeuangan)
                    .buttonTitle("Selesai")
                    .textViewLewati("")
                    .enableLewati(true)
                    .arrowPosition(BubbleShowCase.ArrowPosition.TOP)
            val list: MutableList<BubbleShowCaseBuilder> = java.util.ArrayList()
            list.add(bayar!!)
            list.add(profile!!)
            list.add(listAsuransi!!)
            s.addShowCases(list)
        } catch (e: Exception) {
        }
    }


    class MyPagerAdapter(
            fragmentManager: FragmentManager?,
            private val product1: List<DashboardAsuransiResponse.ListProduct>, private val onListener : DashboardAsuransiFragment.DialogDefaultListener,private val mTitle : List<String>
    ) : FragmentStatePagerAdapter(fragmentManager!!) {

        // Returns total number of pages
        override fun getCount(): Int {
            return product1.size
        }

        // Returns the fragment to display for that page
        override fun getItem(position: Int): Fragment {
            return DashboardAsuransiFragment.newInstance(ArrayList(product1), position)
        }

        override fun getPageTitle(position: Int): CharSequence? {
            return mTitle[position]
        }
    }

    override fun onType(position: Int, productCode: String, redirectType : Int, companyName : String, patnerId : String, type : String) {
        partnerId = patnerId
        mCompanyName = companyName
        mProductCode = productCode
        typeId = type
        if (redirectType == 2 ){
            presenter.getDataDetailInsurance(DetailInsuranceRequest(mProductCode))
        }else{
            val dialogExitCustom = DialogExitCustom(
                { onClickYes() },
                GeneralHelper.getString(R.string.lanjutkan_proses),
                GeneralHelper.getString(R.string.arahan) + companyName + GeneralHelper.getString(R.string.meninggalkan_brimo)
            )
            val ft = this.supportFragmentManager.beginTransaction()
            ft.add(dialogExitCustom, null)
            ft.commitAllowingStateLoss()


        }

    }

    override fun onPageScrolled(position: Int, positionOffset: Float, positionOffsetPixels: Int) {

    }

    override fun onPageSelected(position: Int) {
        GeneralHelper.changeTabsFontDashboardAsuransiRevamp(this, lyTabs, position)
    }

    override fun onPageScrollStateChanged(state: Int) {

    }

    override fun showSkeleton() {
        skeletonScreen1 = Skeleton.bind(binding.tabCatatanKeuangan)
                .shimmer(true)
                .angle(20)
                .duration(1200).color(R.color.white)
                .load(R.layout.skeleton_tab_asuransi)
                .show()
        skeletonScreen = Skeleton.bind(binding.vpAsuransi)
                .shimmer(true)
                .angle(20)
                .duration(1200).color(R.color.white)
                .load(R.layout.skeleton_dashboard_asuransi)
                .show()


    }

    override fun hideSkeleton() {
        skeletonScreen!!.hide()
        skeletonScreen1!!.hide()
        isCLick = true
    }

    override fun onSuccessWebview(webviewBrilifeResponse: WebviewBrilifeResponse?) {
        MicrositeActivity.launchIntent(this, webviewBrilifeResponse!!.webviewData.url, webviewBrilifeResponse.sessionId, partnerId, typeId)
    }

    override fun onSuccessWebviewLama(webviewBrilifeResponse: WebviewBrilifeLamaResponse?) {
        MicrositeLamaActivity.launchIntent(this, webviewBrilifeResponse!!.url)
    }

    override fun onException12(msg: String?) {
        showSnackbarErrorMessageRevamp(msg, ALERT_ERROR, this, false)
    }

    override fun onClick(p0: View?) {
        when(p0!!.id) {
            R.id.btn_polish -> {
                if (brImoPrefRepository.asuransiBubble && isCLick) {
                    presenter.getDataList()
                }
            }
            R.id.rl_bayar_asuransi -> {
                FormAsuransiActivity.launchIntent(this, false)
            }

        }
    }

    override fun onActivityResult(requestCode: Int, resultCode: Int, data: Intent?) {
        super.onActivityResult(requestCode, resultCode, data)

        if (requestCode == Constant.REQ_PAYMENT && data != null) {
            if (resultCode == RESULT_OK) {
                this.setResult(RESULT_OK)
            } else if (resultCode == RESULT_CANCELED && data.hasExtra(Constant.TAG_ERROR_MESSAGE)) {
                this.setResult(RESULT_CANCELED, data)
            } else {
                setResult(RESULT_CANCELED)
            }
        }
    }

    override fun onClickYes() {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU) {
            if (checkSelfPermission(Manifest.permission.CAMERA) == PackageManager.PERMISSION_GRANTED && checkSelfPermission(
                    Manifest.permission.READ_MEDIA_IMAGES
                ) == PackageManager.PERMISSION_GRANTED && checkSelfPermission(
                    Manifest.permission.READ_MEDIA_AUDIO
                ) == PackageManager.PERMISSION_GRANTED && checkSelfPermission(
                    Manifest.permission.READ_MEDIA_VIDEO
                ) == PackageManager.PERMISSION_GRANTED
            ) {
                if (typeId.equals("brilife", ignoreCase = true)) {
                    presenter.getProdukLama(mProductCode)
                } else presenter.getProduct(partnerId!!)
            } else {
                ActivityCompat.requestPermissions(
                    this, arrayOf(
                        Manifest.permission.CAMERA, Manifest.permission.READ_MEDIA_IMAGES,
                        Manifest.permission.READ_MEDIA_AUDIO,
                        Manifest.permission.READ_MEDIA_VIDEO
                    ), 1
                )
            }
        } else {
            if (checkSelfPermission(Manifest.permission.CAMERA) == PackageManager.PERMISSION_GRANTED && checkSelfPermission(
                    Manifest.permission.READ_EXTERNAL_STORAGE
                ) == PackageManager.PERMISSION_GRANTED && checkSelfPermission(
                    Manifest.permission.WRITE_EXTERNAL_STORAGE
                ) == PackageManager.PERMISSION_GRANTED
            ) {
                if (typeId.equals("brilife", ignoreCase = true)) {
                    presenter.getProdukLama(mProductCode)
                } else presenter.getProduct(partnerId!!)
            } else {
                ActivityCompat.requestPermissions(
                    this,
                    arrayOf(
                        Manifest.permission.CAMERA,
                        Manifest.permission.WRITE_EXTERNAL_STORAGE,
                        Manifest.permission.WRITE_EXTERNAL_STORAGE
                    ),
                    1
                )
            }
        }
    }
}