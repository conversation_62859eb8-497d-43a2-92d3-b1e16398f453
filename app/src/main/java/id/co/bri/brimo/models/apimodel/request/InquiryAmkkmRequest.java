package id.co.bri.brimo.models.apimodel.request;

import com.google.gson.annotations.Expose;
import com.google.gson.annotations.SerializedName;

public class InquiryAmkkmRequest {

    @SerializedName("account_number")
    @Expose
    private String accountNumber;

    @SerializedName("product_id")
    @Expose
    private String productId;

    @SerializedName("heir_name")
    @Expose
    private String heirName;

    @SerializedName("heir_phone")
    @Expose
    private String heirPhone;

    @SerializedName("heir_connection")
    @Expose
    private String heirConnection;

    @SerializedName("risk_postcode")
    @Expose
    private String riskPostCode;

    @SerializedName("risk_address")
    @Expose
    private String riskAddress;

    public InquiryAmkkmRequest(String accountNumber, String productId, String heirName, String heirPhone, String heirConnection, String riskPostCode, String riskAddress) {
        this.accountNumber = accountNumber;
        this.productId = productId;
        this.heirName = heirName;
        this.heirPhone = heirPhone;
        this.heirConnection = heirConnection;
        this.riskPostCode = riskPostCode;
        this.riskAddress = riskAddress;
    }

    public String getAccountNumber() {
        return accountNumber;
    }

    public void setAccountNumber(String accountNumber) {
        this.accountNumber = accountNumber;
    }

    public String getProductId() {
        return productId;
    }

    public void setProductId(String productId) {
        this.productId = productId;
    }

    public String getHeirName() {
        return heirName;
    }

    public void setHeirName(String heirName) {
        this.heirName = heirName;
    }

    public String getHeirPhone() {
        return heirPhone;
    }

    public void setHeirPhone(String heirPhone) {
        this.heirPhone = heirPhone;
    }

    public String getHeirConnection() {
        return heirConnection;
    }

    public void setHeirConnection(String heirConnection) {
        this.heirConnection = heirConnection;
    }

    public String getRiskPostCode() {
        return riskPostCode;
    }

    public void setRiskPostCode(String riskPostCode) {
        this.riskPostCode = riskPostCode;
    }

    public String getRiskAddress() {
        return riskAddress;
    }

    public void setRiskAddress(String riskAddress) {
        this.riskAddress = riskAddress;
    }
}
