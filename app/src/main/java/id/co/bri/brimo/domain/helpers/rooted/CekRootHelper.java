package id.co.bri.brimo.domain.helpers.rooted;

import android.app.Activity;
import android.app.ActivityManager;
import android.content.Context;
import android.content.pm.PackageManager;
import android.os.Build;
import android.os.Debug;

import java.io.File;
import java.util.List;

public class CekRootHelper {
    public CekRootHelper() {
        super();
    }



    public static boolean checkRoot2() {
        String string0 = Build.TAGS;
        boolean bool = string0 == null || !string0.contains("test-keys") ? false : true;
        return bool;
    }

    public static boolean checkRoot3() {
        boolean bool = true;
        String[] array_string = new String[]{"/system/app/Superuser.apk", "/system/xbin/daemonsu", "/system/etc/init.d/99SuperSUDaemon",
                "/system/bin/.ext/.su", "/system/etc/.has_su_daemon", "/system/etc/.installed_su_daemon", "/myandroidtools",
                "/dev/com.koushikdutta.superuser.daemon/"};
        int i = array_string.length;
        int i1 = 0;
        while(true) {
            if(i1 >= i) {
                return false;
            }
            else if(!new File(array_string[i1]).exists()) {
                ++i1;
                continue;
            }

            return bool;
        }

    }

    public static boolean detectDebugger() {
        return Debug.isDebuggerConnected();
    }

    public static boolean detect_threadCpuTimeNanos(){
        long start = Debug.threadCpuTimeNanos();

        for(int i=0; i<1000000; ++i)
            continue;

        long stop = Debug.threadCpuTimeNanos();

        if(stop - start < 10000000) {
            return false;
        }
        else {
            return true;
        }
    }


    public static boolean checkProcesses(Activity context) {
        boolean flag = false;
        ActivityManager manager = (ActivityManager) context.getSystemService(Context.ACTIVITY_SERVICE);
        List<ActivityManager.RunningServiceInfo> list = manager.getRunningServices(300);
        if (list != null) {
            String temp;
            for (int i = 0; i < list.size(); ++i) {
                temp = list.get(i).process;
                if (temp.contains("frida")) {
                    context.finishAffinity();
                    flag = true;
                }

                if (temp.contains("myandroidtools")) {
                    context.finishAffinity();
                    flag = true;
                }
            }
        }

        return flag;
    }

    public static boolean isAppInstalled(Context context, String packageName) {
        try {
            context.getPackageManager().getApplicationInfo(packageName, 0);
            return true;
        }
        catch (PackageManager.NameNotFoundException e) {
            return false;
        }
    }

    public static String getForegroundApplication(Context context){
        ActivityManager am=(ActivityManager)context.getSystemService(Context.ACTIVITY_SERVICE);
        ActivityManager.RunningTaskInfo foreground=am.getRunningTasks(1).get(0);
        return foreground.topActivity.getPackageName();
    }


}
