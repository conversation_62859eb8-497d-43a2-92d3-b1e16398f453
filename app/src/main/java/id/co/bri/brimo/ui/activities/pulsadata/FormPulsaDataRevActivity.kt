package id.co.bri.brimo.ui.activities.pulsadata

import android.annotation.SuppressLint
import android.app.Activity
import android.app.SearchManager
import android.content.Intent
import android.os.Build
import android.os.Bundle
import android.util.TypedValue
import android.view.View
import android.view.ViewGroup
import android.widget.EditText
import androidx.annotation.NonNull
import androidx.annotation.RequiresApi
import androidx.appcompat.widget.SearchView
import androidx.core.content.ContextCompat
import androidx.core.content.res.ResourcesCompat
import androidx.fragment.app.FragmentTransaction
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.ethanhua.skeleton.Skeleton
import com.ethanhua.skeleton.SkeletonScreen
import id.co.bri.brimo.R
import id.co.bri.brimo.adapters.HistoryAdapterRevamp
import id.co.bri.brimo.adapters.SavedAdapterRevamp
import id.co.bri.brimo.contract.IPresenter.pulsarevamp.IFormPulsaDataPresenter
import id.co.bri.brimo.contract.IView.pulsarevamp.IFormPulsaDataView
import id.co.bri.brimo.databinding.ActivityFormPulsaDataRevBinding
import id.co.bri.brimo.domain.config.Constant
import id.co.bri.brimo.domain.helpers.GeneralHelper
import id.co.bri.brimo.models.ParameterModel
import id.co.bri.brimo.models.apimodel.response.*
import id.co.bri.brimo.models.apimodel.response.pulsarevamp.FormPulsaDataResponse
import id.co.bri.brimo.models.apimodel.response.pulsarevamp.ProviderItem
import id.co.bri.brimo.models.optionmodel.OptionSearchRevampModel
import id.co.bri.brimo.ui.activities.base.BaseActivity
import id.co.bri.brimo.ui.activities.dompetdigitalrevamp.PilihWalletActivity
import id.co.bri.brimo.ui.activities.transferrevamp.FormEditSavedRevampActivity
import id.co.bri.brimo.ui.customviews.dialog.DialogSetDefaultRevamp
import id.co.bri.brimo.ui.fragments.SearchRevampFragment
import java.util.*
import javax.inject.Inject


open class FormPulsaDataRevActivity : BaseActivity(), IFormPulsaDataView, HistoryAdapterRevamp.ClickItem,
    SavedAdapterRevamp.ClickItem, View.OnClickListener {

    private lateinit var binding : ActivityFormPulsaDataRevBinding
    private lateinit var historyAdapter: HistoryAdapterRevamp
    private lateinit var savedAdapter: SavedAdapterRevamp
    private var historyResponses = ArrayList<HistoryResponse>()
    private var savedResponses = ArrayList<SavedResponse>()

    private lateinit var skeletonScreenHistory: SkeletonScreen
    private lateinit var skeletonScreenSaved: SkeletonScreen
    private var mNominal = ""
    private var kosong = false;
    private var errorMessage: String? = null
    private var purchaseType = ""
    private var dataId = ""
    private var providerItem : MutableList<ProviderItem> = mutableListOf()
    var stringList : String? = null
    var paketModelList: MutableList<DataListRevamp> = mutableListOf()
    var benar : Boolean = false
    var counter = 0
    protected var TAG_PULSA_DATA = "pulsa_data"

    private var posUpdate : Int? = null
    private lateinit var mFormPulsaDataResponse: FormPulsaDataResponse

    @Inject
    lateinit var presenter: IFormPulsaDataPresenter<IFormPulsaDataView>

    companion object {
        private var isFromFastMenu = false
        private var defaultNo = ""
        @JvmStatic
        fun launchIntent(caller: Activity, fromFastMenu: Boolean) {
            val intent = Intent(caller, FormPulsaDataRevActivity::class.java)
            isFromFastMenu = fromFastMenu
            caller.startActivityForResult(intent, Constant.REQ_PAYMENT)
        }
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        binding = ActivityFormPulsaDataRevBinding.inflate(layoutInflater)
        setContentView(binding.root)

        GeneralHelper.setToolbar(this, binding.tbPulsa.toolbar, GeneralHelper.getString(R.string.pulsa_paket_data_pulsa_data_title))

        injectDependency()
        searchFavorit()

        initiateHistoryAdapter()
        initiateSavedAdapter()

        binding.tvLastTrx.text = GeneralHelper.getString(R.string.pulsa_paket_data_isi_pulsa_paket_data_terakhir)
        binding.btnSubmit.setOnClickListener(this)

    }

    override fun onActivityResult(requestCode: Int, resultCode: Int, data: Intent?) {
        super.onActivityResult(requestCode, resultCode, data)
        if (requestCode == Constant.REQ_EDIT_SAVED && data != null) {
            if (resultCode == RESULT_OK) {
                val message = GeneralHelper.getString(R.array.type_option_desc, Constant.EditOption.EDIT)
                showSnackbarErrorMessageRevamp(message, ALERT_CONFIRM, this, false)
                val pos = data.getIntExtra(Constant.TAG_POSITION, 0)
                savedResponses[pos].title = data.getStringExtra(Constant.TAG_TITLE)
                savedAdapter.notifyDataSetChanged()

            }
        }else if (requestCode == Constant.REQ_PAYMENT) {
            if (resultCode == RESULT_OK) {
//membalikan data agar otomatis cek saldo
                setResult(RESULT_OK, data)
                finish()
            } else {
                if (data != null) {
//membalikan data error message agar muncul snackbar di dashboard
                    errorMessage = data.getStringExtra(Constant.TAG_ERROR_MESSAGE)
                    cekErrorMessage()
                    if (isFromFastMenu) {
                        presenter.getDataFormFastMenu()
                    } else {
                        presenter.getDataForm()
                    }
                }
            }
        }

    }

    /*
    * method untuk menampilkan error snackbar dari Intent aktivity inquiry dan konfirmasi
    */
    protected open fun cekErrorMessage() {
        if (errorMessage != null) {
//menampilkan snacknar error
            showSnackbarErrorMessageRevamp(errorMessage, ALERT_ERROR, this, false)

//clear error message
            errorMessage = null
        }
    }

    protected fun injectDependency() {
        activityComponent.inject(this)
        if (presenter != null) {
            initiateUrlPresenter()
            if (isFromFastMenu) {
                presenter.getDataFormFastMenu()
            } else {
                presenter.getDataForm()
            }
        }
    }

    private fun initiateUrlPresenter() {
        presenter.view = this
        if (isFromFastMenu) {
            presenter.setFormUrl(GeneralHelper.getString(R.string.url_form_pulsa_data_fm_revamp))
            presenter.setInquiryUrl(GeneralHelper.getString(R.string.url_inquiry_pulsa_data_fm_revamp))
            presenter.setKonfirmasiUrl(GeneralHelper.getString(R.string.url_confirmation_pulsa_data_fm_revamp))

        } else {
            presenter.setFormUrl(GeneralHelper.getString(R.string.url_form_pulsa_data_revamp))
            presenter.setInquiryUrl(GeneralHelper.getString(R.string.url_inquiry_pulsa_data_revamp))
            presenter.setKonfirmasiUrl(GeneralHelper.getString(R.string.url_confirmation_pulsa_data_revamp))
        }
        presenter.start()
    }

    fun setParameter(): ParameterModel {
        val parameterModel = ParameterModel()
        parameterModel.stringLabelTujuan = GeneralHelper.getString(R.string.nomor_tujuan)
        parameterModel.stringLabelNominal = GeneralHelper.getString(R.string.nominal_pembayaran)
        parameterModel.stringButtonSubmit = GeneralHelper.getString(R.string.beli)
        parameterModel.stringLabelMinimum = GeneralHelper.getString(R.string.pulsa_paket_data_pembayaran)
        parameterModel.defaultIcon = 0

        return parameterModel
    }

    private fun successGetHistory(formPulsaDataResponse: FormPulsaDataResponse) {
        historyResponses.clear()
        historyResponses.addAll(formPulsaDataResponse.history!!)
        historyAdapter.setHistoryResponses(historyResponses)
        historyAdapter.notifyDataSetChanged()

        binding.rvBrivaTerakhir.adapter = historyAdapter
        binding.rvBrivaTerakhir.visibility = View.VISIBLE
        if (historyResponses.size > 0) {
            binding.llNoDataHistory.visibility = View.GONE
            binding.rvBrivaTerakhir.visibility = View.VISIBLE
        } else {
            binding.llNoDataHistory.visibility = View.VISIBLE
            binding.rvBrivaTerakhir.visibility = View.GONE
            binding.tvNoHistory.text =
                GeneralHelper.getString(R.string.pulsa_data_no_history)
        }
    }

    private fun successGetFavorite(formPulsaDataResponse: FormPulsaDataResponse) {
        savedResponses.clear()
        savedResponses.addAll(formPulsaDataResponse.saved!!)
        savedAdapter.setSavedResponses(savedResponses)
        savedAdapter.notifyDataSetChanged()
        binding.rvDaftarBriva.adapter = savedAdapter
        binding.rvDaftarBriva.visibility = View.VISIBLE
        if (savedResponses.size > 0) {
            enableSearchView(binding.searchviewBriva, true)
            binding.llNoDataSaved.visibility = View.GONE
        } else {
            enableSearchView(binding.searchviewBriva, false)
            binding.llNoDataSaved.visibility = View.VISIBLE
        }
    }

    open fun initiateHistoryAdapter() {
        binding.rvBrivaTerakhir.layoutManager =
            LinearLayoutManager(baseContext, RecyclerView.HORIZONTAL, false)
        historyAdapter = HistoryAdapterRevamp(
            baseContext,
            historyResponses,
            this,
            0,
            isFromFastMenu
        )
        skeletonScreenHistory = Skeleton.bind(binding.rvBrivaTerakhir)
            .adapter(historyAdapter)
            .shimmer(true)
            .angle(20)
            .frozen(false)
            .duration(1200)
            .count(5)
            .load(R.layout.item_skeleton_history_pulsa_revamp)
            .show()
    }

    open fun initiateSavedAdapter() {
        binding.rvDaftarBriva.layoutManager =
            LinearLayoutManager(baseContext, RecyclerView.VERTICAL, false)

        if (baseContext != null) savedAdapter = SavedAdapterRevamp(
            baseContext,
            savedResponses,
            this,
            0,
            isFromFastMenu,
            true
        )
        skeletonScreenSaved = Skeleton.bind(binding.rvDaftarBriva)
            .adapter(savedAdapter)
            .shimmer(true)
            .angle(20)
            .frozen(false)
            .duration(1200)
            .count(5)
            .load(R.layout.item_skeleton_saved_transfer)
            .show()
    }

    override fun hideSkeleton(hide : Boolean) {
       /* possible AndroidRuntimeException*/
        runOnUiThread {
            if (hide) {
                if (isFromFastMenu){
                    disableButtonSubmit(true)
                    skeletonScreenHistory.show()
                    skeletonScreenSaved.show()
                }else{
                    disableButtonSubmit(false)
                    skeletonScreenHistory.hide()
                    skeletonScreenSaved.hide()
                }

            } else {
                disableButtonSubmit(true)
                skeletonScreenHistory.show()
                skeletonScreenSaved.show()
            }
        }
    }

    override fun onException12(message: String) {
        val returnIntent = Intent()

        returnIntent.putExtra(Constant.TAG_ERROR_MESSAGE, message)

        this.setResult(RESULT_CANCELED, returnIntent)
        finish()
    }

    override fun onException93(message: String) {
        val returnIntent = Intent()

        returnIntent.putExtra(Constant.TAG_ERROR_MESSAGE, message)

        this.setResult(RESULT_CANCELED, returnIntent)
        finish()
    }


    protected open fun disableButtonSubmit(disable: Boolean) {
        if (disable) {
            binding.btnSubmit.isEnabled = false
            binding.btnSubmit.setTextColor(GeneralHelper.getColor(R.color.neutral_light60))
        } else {
            binding.btnSubmit.isEnabled = true
            binding.btnSubmit.setTextColor(GeneralHelper.getColor(R.color.whiteColor))
        }
    }

    override fun onClickHistoryItem(historyResponse: HistoryResponse) {
        val yourArray: List<String> = historyResponse.value.split("|")
        purchaseType = yourArray[0]
        mNominal = yourArray[1]
        dataId = yourArray[1]
        mFormPulsaDataResponse.phoneNumber = yourArray[2]
        val prefixInput = mFormPulsaDataResponse.phoneNumber.substring(0, 4)
        kosong = true
        providerItem.addAll(mFormPulsaDataResponse.provider)

        for (i in providerItem) {
            if (GeneralHelper.isContains(i.prefix, prefixInput)) {
                if (i.dataList != null){
                    paketModelList= i.dataList
                }
            }

        }
        for (j in paketModelList){

            val id : String = j.id
            if (!GeneralHelper.isContains(id, dataId)) {
                counter++
            }
        }
        onInquiry()
    }

    override fun onClickSavedItem(savedResponse: SavedResponse) {
        val yourArray: List<String> = savedResponse.value.split("|")
        mFormPulsaDataResponse.phoneNumber = yourArray[1]
        kosong = true
        onInquiry()
    }

    override fun onClickUpdateItem(@NonNull savedResponse: SavedResponse, position: Int) {
        posUpdate = position
        if (this != null) {
            val SearchRevampFragment = SearchRevampFragment(fetchList(savedResponse),
                { optionSearchRevampModel: OptionSearchRevampModel ->
                    if (optionSearchRevampModel.codeModel == Constant.EditOption.EDIT.toString()) {
                        FormEditSavedRevampActivity.launchIntent(
                            this,
                            savedResponse,
                            position,
                            0,
                            GeneralHelper.getString(R.string.url_update_pulsa),
                            GeneralHelper.getString(R.string.nomor_tujuan)
                        )
                    } else if (optionSearchRevampModel.codeModel == Constant.EditOption.HAPUS.toString()) {
                        val dialogTitle =
                            GeneralHelper.getString(R.string.pulsa_data_confirmation_dialog_title)
                        val dialogDesc = String.format(
                            GeneralHelper.getString(R.string.pulsa_data_confirmation_dialog_description),
                            savedResponse.getTitle()
                        )
                        val dialogSetDefaultRevamp = DialogSetDefaultRevamp(
                            object : DialogSetDefaultRevamp.DialogDefaultListener {
                                override fun onClickYesDefault(requestId: Int) {
                                    presenter.setUpdateItemTf(
                                        GeneralHelper.getString(R.string.url_delete_pulsa),
                                        savedResponse,
                                        position,
                                        Constant.EditOption.HAPUS
                                    )

                                    skeletonScreenHistory.show()
                                    skeletonScreenSaved.show()
                                    disableButtonSubmit(true)
                                }

                                override fun onClickNoDefault(requestId: Int) {}
                            },
                            dialogTitle,
                            dialogDesc,
                            GeneralHelper.getString(R.string.pulsa_data_confirmation_dialog_delete_positive),
                            GeneralHelper.getString(R.string.pulsa_data_confirmation_dialog_delete_negative),
                            Constant.REQ_EDIT_SAVED
                        )
                        val ft: FragmentTransaction =
                            this.supportFragmentManager.beginTransaction()
                        ft.add(dialogSetDefaultRevamp, null)
                        ft.commitAllowingStateLoss()
                    } else if (optionSearchRevampModel.codeModel == Constant.EditOption.NON_FAV.toString()) {
                        presenter.setUpdateItemTf(
                            GeneralHelper.getString(R.string.url_unfavorite_pulsa),
                            savedResponse,
                            position,
                            Constant.EditOption.NON_FAV
                        )
                        skeletonScreenHistory.show()
                        skeletonScreenSaved.show()
                        disableButtonSubmit(true)
                    } else if (optionSearchRevampModel.codeModel == Constant.EditOption.FAV.toString()) {
                        presenter.setUpdateItemTf(
                            GeneralHelper.getString(R.string.url_favorite_pulsa),
                            savedResponse,
                            position,
                            Constant.EditOption.FAV
                        )
                        skeletonScreenHistory.show()
                        skeletonScreenSaved.show()
                        disableButtonSubmit(true)
                    }
                }, "", GeneralHelper.getString(R.string.pengaturan), false
            )
            SearchRevampFragment.show(supportFragmentManager, "")
        }
    }

    override fun onNoSavedItemSearch(isTrue: Boolean, query: CharSequence?) {
        if (isTrue) {
            val sb = StringBuilder(query!!.length)
            sb.append(query)
            if (query.length != 0) {
                val desc = String.format(
                    GeneralHelper.getString(R.string.pulsa_data_no_favorit_found_desc),
                    "\"" + sb + "\""
                )
                binding.tvNoDataFoundDesc.text = desc
                binding.rvDaftarBriva.visibility = View.GONE
                binding.llNoDataSavedFound.visibility = View.VISIBLE
                binding.llNoDataSaved.visibility = View.GONE
            } else {
                binding.llNoDataSavedFound.visibility = View.GONE
                binding.rvDaftarBriva.visibility = View.VISIBLE
            }
        } else {
            binding.llNoDataSavedFound.visibility = View.GONE
            binding.rvDaftarBriva.visibility = View.VISIBLE
        }
    }

    fun fetchList(savedResponse: SavedResponse): List<OptionSearchRevampModel>? {
        val optionSearchRevampModelArrayList: MutableList<OptionSearchRevampModel> = ArrayList()
        if (this != null) {
            if (java.lang.Boolean.TRUE == savedResponse.favorite) {
                optionSearchRevampModelArrayList.add(
                    OptionSearchRevampModel(
                        "ic_opt_fav_revamp",
                        GeneralHelper.getString(this, R.string.opt_non_fav),
                        "",
                        0,
                        0,
                        Constant.EditOption.NON_FAV.toString(),
                        true
                    )
                )
            } else {
                optionSearchRevampModelArrayList.add(
                    OptionSearchRevampModel(
                        "ic_opt_fav_revamp",
                        GeneralHelper.getString(this, R.string.opt_fav),
                        "",
                        0,
                        0,
                        Constant.EditOption.FAV.toString(),
                        true
                    )
                )
            }
            optionSearchRevampModelArrayList.add(
                OptionSearchRevampModel(
                    "ic_edit_outline_revamp",
                    GeneralHelper.getString(this, R.string.edit_daftar_transfer),
                    "",
                    0,
                    0,
                    Constant.EditOption.EDIT.toString(),
                    true
                )
            )
            optionSearchRevampModelArrayList.add(
                OptionSearchRevampModel(
                    "ic_opt_hapus_revamp",
                    GeneralHelper.getString(this, R.string.unfavorit_saved),
                    "",
                    0,
                    0,
                    Constant.EditOption.HAPUS.toString(),
                    true
                )
            )
        }
        return optionSearchRevampModelArrayList
    }





    private fun searchFavorit() {
        binding.searchviewBriva.queryHint = GeneralHelper.getString(R.string.pulsa_paket_data_hint_query_search)
        val searchManager = this.getSystemService(SEARCH_SERVICE) as SearchManager
        binding.searchviewBriva.setSearchableInfo(searchManager.getSearchableInfo(this.componentName))

        binding.searchviewBriva.maxWidth = Int.MAX_VALUE

        val editText: EditText = binding.searchviewBriva.findViewById(androidx.appcompat.R.id.search_src_text)
        editText.setHintTextColor(ContextCompat.getColor(this, R.color.neutral_light60))
        editText.setTypeface(ResourcesCompat.getFont(this, R.font.bri_digital_text_medium))

        editText.setTextSize(TypedValue.COMPLEX_UNIT_SP, 14f)
        binding.searchviewBriva.setOnQueryTextFocusChangeListener { _, b ->
            if (b) {
                binding.searchviewBriva.setBackgroundResource(
                    GeneralHelper.getImageId(
                        this,
                        "bg_blue_border"
                    )
                )
            } else {
                binding.searchviewBriva.setBackgroundResource(
                    GeneralHelper.getImageId(
                        this,
                        "bg_grey_border"
                    )
                )
            }
        }

        binding.searchviewBriva.setOnQueryTextListener(object : SearchView.OnQueryTextListener {
            override fun onQueryTextSubmit(query: String): Boolean {
                Objects.requireNonNull(savedAdapter.filter).filter(query)
                return false
            }

            override fun onQueryTextChange(newText: String): Boolean {
                Objects.requireNonNull(savedAdapter.filter).filter(newText)

                if (newText.isEmpty()) {
                    if (savedResponses.isNotEmpty()) {
                        binding.llNoDataSavedFound.visibility = View.GONE
                        binding.rvDaftarBriva.visibility = View.VISIBLE
                    } else {
                        binding.llNoDataSavedFound.visibility = View.GONE
                        binding.llNoDataSaved.visibility = View.VISIBLE
                    }
                }

                return false
            }
        })
    }

    override fun onSuccessGetData(formPulsaDataResponse: FormPulsaDataResponse?) {
        if (formPulsaDataResponse != null) {
            mFormPulsaDataResponse = formPulsaDataResponse
            defaultNo = formPulsaDataResponse.phoneNumber
            successGetHistory(formPulsaDataResponse)
            successGetFavorite(formPulsaDataResponse)
            checkDataHistorySavedList()
        }
    }

    @RequiresApi(Build.VERSION_CODES.N)
    @SuppressLint("NotifyDataSetChanged")
    override fun onSuccessUpdate(savedResponse: SavedResponse, item: Int, type: Int) {
        val message = GeneralHelper.getString(R.array.type_option_desc, type)
        showSnackbarErrorMessageRevamp(message, ALERT_CONFIRM, this, false)

        if (type == Constant.EditOption.HAPUS){
            mFormPulsaDataResponse.saved.removeAt(posUpdate!!)
            savedResponses.addAll(mFormPulsaDataResponse.saved)
            savedAdapter.setSavedResponses(savedResponses)
            savedAdapter.notifyDataSetChanged()
            binding.rvDaftarBriva.adapter = savedAdapter
            binding.rvDaftarBriva.visibility = View.VISIBLE
            if (savedResponses.size > 0) {
                binding.llNoDataSaved.visibility = View.GONE
            } else {
                binding.llNoDataSaved.visibility = View.VISIBLE
            }
        }else if (type == Constant.EditOption.NON_FAV || type == Constant.EditOption.FAV){
            if (savedResponse.favorite){
                mFormPulsaDataResponse.saved[posUpdate!!].favorite = false
            }else {
                mFormPulsaDataResponse.saved[posUpdate!!].favorite = true
            }

            mFormPulsaDataResponse.saved.toMutableList()
            mFormPulsaDataResponse.saved.sortWith(compareBy<SavedResponse> {it.favorite}.reversed().thenBy {it.title})
        }
        successGetFavorite(mFormPulsaDataResponse)
        hideSkeleton(true)
    }

    override fun onBackPressed() {
        presenter.stop()
        super.onBackPressed()
    }

    override fun onClick(p0: View?) {
        when(p0!!.id) {
            R.id.btnSubmit -> {
                mFormPulsaDataResponse.phoneNumber = defaultNo
                kosong = false
                onInquiry()
            }
        }
    }

    fun onInquiry() {
        if (isFromFastMenu){
            if (kosong){
                InquiryPulsaRevampRevActivity.launchIntent(this,true,GeneralHelper.getString(R.string.url_confirmation_pulsa_data_fm_revamp), GeneralHelper.getString(R.string.url_payment_pulsa_data_fm_revamp), mFormPulsaDataResponse,setParameter(),
                    mFormPulsaDataResponse.phoneNumber, mNominal, savedResponses, dataId, purchaseType)
            }else{
                InquiryPulsaRevampRevActivity.launchIntent(this,true,GeneralHelper.getString(R.string.url_confirmation_pulsa_data_fm_revamp), GeneralHelper.getString(R.string.url_payment_pulsa_data_fm_revamp), mFormPulsaDataResponse,setParameter(),
                    mFormPulsaDataResponse.phoneNumber, "",savedResponses, "", "")
            }
        }else{
            if (kosong){
                InquiryPulsaRevampRevActivity.launchIntent(this,false,GeneralHelper.getString(R.string.url_confirmation_pulsa_data_revamp), GeneralHelper.getString(R.string.url_payment_pulsa_data_revamp), mFormPulsaDataResponse,setParameter(),
                    mFormPulsaDataResponse.phoneNumber, mNominal, savedResponses, dataId, purchaseType)
            }else{
                InquiryPulsaRevampRevActivity.launchIntent(this,false,GeneralHelper.getString(R.string.url_confirmation_pulsa_data_revamp), GeneralHelper.getString(R.string.url_payment_pulsa_data_revamp), mFormPulsaDataResponse,setParameter(),
                    mFormPulsaDataResponse.phoneNumber, "",savedResponses, "", "")
            }
        }
    }

    override fun onExceptionGetDataForm(message: String) {
        GeneralHelper.showDialogGagalBack(this, message)
    }


    override fun onException(message: String?) {
        hideSkeleton(true)
        try {
            if (GeneralHelper.isContains(Constant.LIST_TYPE_GAGAL, message)) {
                GeneralHelper.showBottomDialog(this, message)
            } else {
                GeneralHelper.showSnackBarRevamp(
                    this.findViewById(R.id.content),
                    message
                )
            }
        } catch (e: Exception) {
// donothing
        }
    }

    open fun enableSearchView(view: View, enabled: Boolean) {
        view.isEnabled = enabled
        if (view is ViewGroup) {
            val viewGroup = view
            for (i in 0 until viewGroup.childCount) {
                val child = viewGroup.getChildAt(i)
                enableSearchView(child, enabled)
            }
        }
    }

    fun checkDataHistorySavedList() {
        if (historyResponses.isEmpty() && savedResponses.isEmpty() && !isFromFastMenu) {
            binding.btnSubmit.performClick()
        }
    }

}