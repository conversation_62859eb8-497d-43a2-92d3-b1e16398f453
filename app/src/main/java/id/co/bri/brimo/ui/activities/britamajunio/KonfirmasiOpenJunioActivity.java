package id.co.bri.brimo.ui.activities.britamajunio;

import android.app.Activity;
import android.content.Intent;
import android.os.Build;
import android.os.Bundle;
import android.view.View;
import android.view.Window;
import android.view.WindowManager;

import androidx.annotation.Nullable;
import androidx.fragment.app.FragmentTransaction;
import androidx.recyclerview.widget.LinearLayoutManager;

import javax.inject.Inject;

import id.co.bri.brimo.R;
import id.co.bri.brimo.adapters.esbn.DetailProdukBoldSbnAdapter;
import id.co.bri.brimo.contract.IPresenter.britamajunio.IConfirmationJunioPresenter;
import id.co.bri.brimo.contract.IView.britamajunio.IConfirmationJunioView;
import id.co.bri.brimo.databinding.ActivityConfirmationOpenJunioBinding;
import id.co.bri.brimo.domain.config.Constant;
import id.co.bri.brimo.domain.helpers.GeneralHelper;
import id.co.bri.brimo.models.apimodel.request.junio.PaymentJunioRequest;
import id.co.bri.brimo.models.apimodel.response.DetailListType;
import id.co.bri.brimo.models.apimodel.response.PendingResponse;
import id.co.bri.brimo.models.apimodel.response.junio.ConfirmationJuniResponse;
import id.co.bri.brimo.ui.activities.GeneralSyaratActivity;
import id.co.bri.brimo.ui.activities.LupaPinActivity;
import id.co.bri.brimo.ui.activities.LupaPinFastActivity;
import id.co.bri.brimo.ui.activities.base.BaseActivity;
import id.co.bri.brimo.ui.customviews.dialog.DialogExitCustom;
import id.co.bri.brimo.ui.fragments.PinFragment;

public class KonfirmasiOpenJunioActivity extends BaseActivity implements View.OnClickListener, PinFragment.SendPin, IConfirmationJunioView,DialogExitCustom.DialogDefaultListener {

    private ActivityConfirmationOpenJunioBinding binding;

    private static ConfirmationJuniResponse response;
    public DetailListType sourceAccountDataViews;
    protected boolean isSyarat = false;

    public static void launchIntent(Activity caller, ConfirmationJuniResponse data){
        Intent intent = new Intent(caller, KonfirmasiOpenJunioActivity.class);
        response = data;
        caller.startActivityForResult(intent, Constant.REQ_PAYMENT);
    }

    @Inject
    IConfirmationJunioPresenter<IConfirmationJunioView> presenter;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        binding = ActivityConfirmationOpenJunioBinding.inflate(getLayoutInflater());
        setContentView(binding.getRoot());

        injectDependency();

        GeneralHelper.setToolbar(this, binding.toolbar.toolbar, GeneralHelper.getString(R.string.toolbar_confirmation));

        if (Build.VERSION.SDK_INT >= 21) {
            Window window = getWindow();
            window.addFlags(WindowManager.LayoutParams.FLAG_DRAWS_SYSTEM_BAR_BACKGROUNDS);
            window.clearFlags(WindowManager.LayoutParams.FLAG_TRANSLUCENT_STATUS);
            window.setStatusBarColor(getResources().getColor(R.color.toolbar_blue));
        }


        if (GeneralHelper.isProd())
            getWindow().setFlags(WindowManager.LayoutParams.FLAG_SECURE,
                    WindowManager.LayoutParams.FLAG_SECURE);

        setView();

        binding.ivCheckBox.setOnClickListener(this);
        binding.tvSyarat.setOnClickListener(this);
        binding.btnSubmit.setOnClickListener(this);
    }

    private void injectDependency() {
        getActivityComponent().inject(this);
        if (presenter != null) {
            presenter.setView(this);
            presenter.start();
            presenter.setaurl(GeneralHelper.getString(R.string.url_payment_junio));
        }
    }

    private void setView(){
        sourceAccountDataViews = response.getSourceAccountDataView();

        //Detail Rekening Asal
        binding.tvNama.setText(sourceAccountDataViews.getTitle());
        binding.tvNorek.setText(sourceAccountDataViews.getSubtitle());

        binding.rvBillingInfo.setLayoutManager(new LinearLayoutManager(this));
        DetailProdukBoldSbnAdapter detailAdapter1 = new DetailProdukBoldSbnAdapter(this, response.getSourceAccountDetailView());
        binding.rvBillingInfo.setAdapter(detailAdapter1);

        binding.rvBillingAmountDetail.setLayoutManager(new LinearLayoutManager(this));
        DetailProdukBoldSbnAdapter detailAdapter2 = new DetailProdukBoldSbnAdapter(this, response.getAutoDebetDetailView());
        binding.rvBillingAmountDetail.setAdapter(detailAdapter2);

        //Set Image Circle
        if (sourceAccountDataViews.getListType().equals("image")) {
            binding.llLogo2.setVisibility(View.VISIBLE);
            binding.rlSumberdana.setVisibility(View.GONE);
            //load icon transaction
            GeneralHelper.loadIconTransaction(
                    this,
                    sourceAccountDataViews.getIconPath(),
                    sourceAccountDataViews.getIconName(),
                    binding.ivIcon2,
                    R.drawable.bri);
        } else {
            binding.llLogo2.setVisibility(View.GONE);
            binding.rlSumberdana.setVisibility(View.VISIBLE);
            //Set Initial
            String title = sourceAccountDataViews.getTitle();
            binding.tvInisial.setText(GeneralHelper.formatInitialName(title));
        }

        setvalidasiButton();
    }

    @Override
    public void onClick(View v) {
        switch (v.getId()){
            case R.id.tv_syarat:
                GeneralSyaratActivity.launchIntent(this, response.getTnc());
                break;
            case R.id.btnSubmit:
                PinFragment pinFragment = new PinFragment(this, this);
                pinFragment.show();
                break;
        }
    }

    protected void setvalidasiButton() {
        if (isSyarat) {
            binding.btnSubmit.setAlpha(1);
            binding.btnSubmit.setEnabled(true);
        } else {
            binding.btnSubmit.setAlpha(0.3f);
            binding.btnSubmit.setEnabled(false);
        }
    }

    @Override
    public void onSendPinComplete(String pin) {
        PaymentJunioRequest data = new PaymentJunioRequest(pin, response.getReferenceNumber());
        presenter.getPayment(data);
    }

    @Override
    public void onLupaPin() {
        //TO DO routing
        if (isFromFastMenu) LupaPinFastActivity.launchIntent(this);
        else LupaPinActivity.launchIntent(this);
    }
    @Override
    public void onSuccessPayment(PendingResponse paymentResponse) {
        ReceiptOpenJunioActivity.launchIntent(this, paymentResponse);
    }

    /**
     * Mapping RC Back ketika RC 93, transaksi sudah expired
     * @param message
     */
    @Override
    public void onException93(String message) {
        Intent returnIntent = new Intent();

        returnIntent.putExtra(Constant.TAG_ERROR_MESSAGE, message);

        this.setResult(RESULT_CANCELED, returnIntent);
        this.finish();
    }

    /**
     * Mapping RC error 01, RC error yang memiliki data yg di pop up (bottomSheet)
     * @param message
     */
    @Override
    public void onException01(String message) {
        GeneralHelper.showDialogGagalBackDescBerubah(this, Constant.TRANSAKSI_GAGAL, message);
        setResult(RESULT_OK);
    }

    @Override
    public void onException12(String message) {
        showSnackbarErrorMessage(message,ALERT_ERROR,this,false);
    }

    @Override
    public void onBackPressed() {
        DialogExitCustom dialogExitCustom = new DialogExitCustom(this::onClickYes, "Batalkan Pembukaan", "Semua informasi yang telah dimasukkan tidak akan tersimpan dan kamu harus mengulang pendaftaran dari awal. Yakin untuk membatalkan?");
        FragmentTransaction ft = this.getSupportFragmentManager().beginTransaction();
        ft.add(dialogExitCustom, null);
        ft.commitAllowingStateLoss();
    }

    @Override
    public void onClickYes() {
        Intent returnIntent = new Intent();
        this.setResult(RESULT_CANCELED, returnIntent);
        this.finish();
    }

    @Override
    protected void onActivityResult(int requestCode, int resultCode, @Nullable Intent data) {
        super.onActivityResult(requestCode, resultCode, data);
        if (requestCode == Constant.REQ_PAYMENT) {
            if (resultCode == RESULT_OK) {
                this.setResult(RESULT_OK, data);
                this.finish();
            } else {
                this.setResult(RESULT_CANCELED, data);
                this.finish();
            }
        } else if (requestCode == Constant.REQ_FORGET_PIN) {
            if (resultCode == RESULT_OK) {
                this.setResult(RESULT_OK);
                this.finish();
            }
        } else
        if (requestCode == Constant.REQ_PETTUNJUK1 && data != null) {
            if (resultCode == RESULT_OK) {
                isSyarat = Boolean.parseBoolean(data.getStringExtra("checkbox"));

                if (isSyarat) {
                    binding.ivCheckBox.setBackgroundResource(R.drawable.checkbox_on);
                } else {
                    binding.ivCheckBox.setBackgroundResource(R.drawable.checkbox_off);
                }
                setvalidasiButton();
            }
        }
    }

    @Override
    protected void onResume() {
        super.onResume();
    }

    @Override
    protected void onDestroy() {
        super.onDestroy();
        binding = null;
    }
}