package id.co.bri.brimo.models.apimodel.request;

import com.google.gson.annotations.SerializedName;

/**
 * Created by user on 04/03/2021
 */
public class InquiryBPJSRequest {

    @SerializedName("code_bpjs_ks_type")
    private String bpjsCode;

    @SerializedName("payment_number")
    private String bpjsNumber;

    public InquiryBPJSRequest(String bpjsNumber, String bpjsCode) {
        this.bpjsCode = bpjsCode;
        this.bpjsNumber = bpjsNumber;
    }

    public String getBpjsCode() {
        return bpjsCode;
    }

    public void setBpjsCode(String bpjsCode) {
        this.bpjsCode = bpjsCode;
    }

    public String getBpjsNumber() {
        return bpjsNumber;
    }

    public void setBpjsNumber(String bpjsNumber) {
        this.bpjsNumber = bpjsNumber;
    }
}
