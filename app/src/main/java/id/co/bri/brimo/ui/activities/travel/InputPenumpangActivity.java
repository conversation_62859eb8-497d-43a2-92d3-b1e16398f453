package id.co.bri.brimo.ui.activities.travel;

import android.app.Activity;
import android.content.Intent;
import android.os.Bundle;
import android.text.Editable;
import android.text.TextWatcher;
import android.view.View;
import android.widget.EditText;

import java.util.ArrayList;
import java.util.List;

import id.co.bri.brimo.R;
import id.co.bri.brimo.databinding.ActivityInputPenumpangBinding;
import id.co.bri.brimo.domain.config.Constant;
import id.co.bri.brimo.domain.helpers.GeneralHelper;
import id.co.bri.brimo.models.PenumpangModel;
import id.co.bri.brimo.models.optionmodel.OptionGeneralModel;
import id.co.bri.brimo.ui.activities.base.BaseActivity;
import id.co.bri.brimo.ui.fragments.JenisDonasiFragment;
import id.co.bri.brimo.ui.fragments.PilihanGeneralFragment;

public class InputPenumpangActivity extends BaseActivity implements View.OnClickListener,
        PilihanGeneralFragment.SelectPilihanGeneral,
        JenisDonasiFragment.SelectJenisDonasi {

    private ActivityInputPenumpangBinding binding;

    private static int mPosition;
    private static PenumpangModel mPenumpangModel;
    private OptionGeneralModel idTypeModel;
    private static boolean mIsRequiredId = false;

    public static void launchIntent(Activity caller, int position, PenumpangModel penumpangModel, boolean isRequiredId) {
        Intent intent = new Intent(caller, InputPenumpangActivity.class);
        mPosition = position;
        mPenumpangModel = penumpangModel;
        mIsRequiredId = isRequiredId;
        caller.startActivityForResult(intent, Constant.REQ_KATEGORI);
    }

    private OptionGeneralModel currentOptionModel;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        binding = ActivityInputPenumpangBinding.inflate(getLayoutInflater());
        setContentView(binding.getRoot());

        GeneralHelper.setToolbar(this, binding.toolbar.toolbar, GeneralHelper.getString(R.string.pessanger_detail));
        setupView();
        if (mPenumpangModel != null) {
            binding.etNama.setText(mPenumpangModel.getNamaLengkap());
            binding.etTitel.setText(mPenumpangModel.getTitel());
            binding.etIdType.setText(mPenumpangModel.getIdType());
            binding.etIdNumber.setText(mPenumpangModel.getIdNumber());
        }
        binding.btnSubmit.setOnClickListener(this);
        binding.etTitel.setOnClickListener(this);
        binding.etIdType.setOnClickListener(this);

        editTextCheck(binding.etNama);
        editTextCheck(binding.etTitel);
    }

    public void setupView() {
        if (!mIsRequiredId) {
            binding.llIdType.setVisibility(View.GONE);
            binding.llIdNumber.setVisibility(View.GONE);
            binding.viewId.setVisibility(View.GONE);
            binding.viewNumber.setVisibility(View.GONE);
        } else {
            binding.llIdType.setVisibility(View.VISIBLE);
            binding.llIdNumber.setVisibility(View.VISIBLE);
            binding.viewId.setVisibility(View.VISIBLE);
            binding.viewNumber.setVisibility(View.VISIBLE);
            editTextCheck(binding.etIdType);
            editTextCheck(binding.etIdNumber);
        }
    }

    @Override
    public void onClick(View view) {
        switch (view.getId()) {
            case R.id.btnSubmit:
                Intent returnIntent = new Intent();
                returnIntent.putExtra("nama", binding.etNama.getText().toString());
                returnIntent.putExtra("titel", binding.etTitel.getText().toString());
                returnIntent.putExtra("id_type", binding.etIdType.getText().toString());
                returnIntent.putExtra("id_number", binding.etIdNumber.getText().toString());
                returnIntent.putExtra("posisi", String.valueOf(mPosition));
                this.setResult(RESULT_OK, returnIntent);
                this.finish();
                break;
            case R.id.et_titel:
                PilihanGeneralFragment fragmentPilihan = new PilihanGeneralFragment(fetchOptionList(), this);
                fragmentPilihan.show(getSupportFragmentManager(), "");
                break;
            case R.id.et_id_type:
                JenisDonasiFragment fragment = new JenisDonasiFragment(fetchIdTypeList(), this);
                fragment.show(getSupportFragmentManager(), "");
                break;
        }
    }

    private List<OptionGeneralModel> fetchOptionList() {
        List<OptionGeneralModel> list = new ArrayList<OptionGeneralModel>();

        list.add(new OptionGeneralModel(0, ("Tuan"), "", ""));
        list.add(new OptionGeneralModel(0, ("Nyonya"), "", ""));
        list.add(new OptionGeneralModel(0, ("Nona"), "", ""));

        return list;
    }

    private List<OptionGeneralModel> fetchIdTypeList() {
        List<OptionGeneralModel> list = new ArrayList<OptionGeneralModel>();

        list.add(new OptionGeneralModel(0, ("KTP"), "", ""));
        list.add(new OptionGeneralModel(0, ("SIM"), "", ""));

        return list;
    }

    @Override
    public void onClick(int position, OptionGeneralModel optionModel) {
        currentOptionModel = optionModel;
        binding.etTitel.setText(currentOptionModel.getOptionName());
    }

    public void editTextCheck(EditText editText) {
        editText.addTextChangedListener(new TextWatcher() {
            @Override
            public void beforeTextChanged(CharSequence charSequence, int i, int i1, int i2) {
                validateButton();
            }

            @Override
            public void onTextChanged(CharSequence charSequence, int i, int i1, int i2) {
                validateButton();
            }

            @Override
            public void afterTextChanged(Editable editable) {
                validateButton();
            }
        });
    }

    public void validateButton() {
        if (binding.etNama.getText().toString().length() == 0) {
            setButton(false);
        } else if (binding.etTitel.getText().toString().length() == 0) {
            setButton(false);
        } else if (binding.etIdType.getText().toString().length() == 0 && mIsRequiredId) {
            setButton(false);
        } else if (binding.etIdNumber.getText().toString().length() <= 13 && mIsRequiredId) {
            setButton(false);
        } else {
            setButton(true);
        }
    }

    public void setButton(boolean enable) {
        if (enable) {
            binding.btnSubmit.setEnabled(true);
            binding.btnSubmit.setAlpha(1);
        } else {
            binding.btnSubmit.setEnabled(false);
            binding.btnSubmit.setAlpha((float) 0.3);
        }
    }

    @Override
    public void onClickJenisDonasi(int position, OptionGeneralModel optionModel) {
        idTypeModel = optionModel;
        binding.etIdType.setText(idTypeModel.getOptionName());
    }

    @Override
    protected void onDestroy() {
        super.onDestroy();
        binding = null;
    }
}