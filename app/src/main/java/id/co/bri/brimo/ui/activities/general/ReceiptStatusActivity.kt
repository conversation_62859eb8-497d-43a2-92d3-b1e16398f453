package id.co.bri.brimo.ui.activities.general

import android.Manifest
import android.annotation.SuppressLint
import android.app.Activity
import android.content.Intent
import android.content.pm.PackageManager
import android.net.Uri
import android.os.Bundle
import android.text.SpannableString
import android.text.style.UnderlineSpan
import android.widget.TextView
import androidx.core.app.ActivityCompat
import androidx.core.view.isVisible
import androidx.recyclerview.widget.LinearLayoutManager
import id.co.bri.brimo.R
import id.co.bri.brimo.adapters.TotalTransaksiRevInfoAdapter
import id.co.bri.brimo.adapters.general.DetailTransaksiInfoAdapter
import id.co.bri.brimo.adapters.general.ReceiptStatusProgressAdapter
import id.co.bri.brimo.contract.IPresenter.general.IReceiptStatusPresenter
import id.co.bri.brimo.contract.IView.general.IReceiptStatusView
import id.co.bri.brimo.databinding.ActivityReceiptStatusBinding
import id.co.bri.brimo.domain.config.Constant
import id.co.bri.brimo.domain.helpers.GeneralHelper
import id.co.bri.brimo.models.apimodel.request.ReceiptStatusRequest
import id.co.bri.brimo.models.apimodel.request.dplkrevamp.DetailReceiptKlaimDplkRequest
import id.co.bri.brimo.models.apimodel.request.rdn.RdnReceiptWithdrawRequest
import id.co.bri.brimo.models.apimodel.response.DataView
import id.co.bri.brimo.models.apimodel.response.ReceiptRevampResponse
import id.co.bri.brimo.models.apimodel.response.rdnrevamp.RdnContactUs
import id.co.bri.brimo.ui.activities.base.BaseActivity
import id.co.bri.brimo.ui.activities.dplkrevamp.DetailBrifineDplkRevampActivity.Companion.IS_FROM_RECEIPT
import id.co.bri.brimo.ui.activities.rdnrevamp.dashboard.DashboardRdnRevampActivity
import id.co.bri.brimo.ui.activities.receipt.ReceiptRevampActivity
import id.co.bri.brimo.ui.fragments.bottomsheet.OpenBottomSheetGeneralFragment
import id.co.bri.brimo.ui.fragments.general.StatusOpenBottomFragment
import id.co.bri.brimo.util.extension.gone
import id.co.bri.brimo.util.extension.visible
import javax.inject.Inject

class ReceiptStatusActivity : BaseActivity(), IReceiptStatusView,
    DetailTransaksiInfoAdapter.OnCallback {

    private var _binding: ActivityReceiptStatusBinding? = null
    private val binding get() = _binding!!
    private lateinit var request: ReceiptStatusRequest
    private lateinit var mAdapterStep: ReceiptStatusProgressAdapter
    private lateinit var mAdapterHeaderTransaksi: DetailTransaksiInfoAdapter
    private lateinit var mAdapterDetailTransaksi: DetailTransaksiInfoAdapter
    private lateinit var mAdapterDetailBiaya: DetailTransaksiInfoAdapter
    private lateinit var mAdapterTotalBiaya: TotalTransaksiRevInfoAdapter
    private var headerString =
        "<head><meta name='viewport' content='width=device-width, initial-scale=1.0, maximum-scale=1.0, minimum-scale=1.0, user-scalable=no'></head>"

    @Inject
    lateinit var presenter: IReceiptStatusPresenter<IReceiptStatusView>

    companion object {
        private var mFundCode: String = ""
        private var mTrxType: String = ""
        private var mResponse: ReceiptRevampResponse = ReceiptRevampResponse()
        private var mFromDashboardEmas = false
        private var isFromConfirmation = false

        @JvmStatic
        fun launchIntent(
            caller: Activity,
            refNum: String,
            response: ReceiptRevampResponse,
            fromDashboardEmas: Boolean,
            trxType: String,
            isFromConfirmation: Boolean = false
        ) {
            val intent = Intent(caller, ReceiptStatusActivity::class.java)
            mFundCode = refNum
            mResponse = response
            mFromDashboardEmas = fromDashboardEmas
            mTrxType = trxType
            this.isFromConfirmation = isFromConfirmation
            caller.startActivityForResult(intent, Constant.REQ_PAYMENT)
        }
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        _binding = ActivityReceiptStatusBinding.inflate(layoutInflater)
        setContentView(binding.root)
        injectDependency()
        setupView()
        initListener()

        with(binding) {
            setUnderlinedText(tvHeadphone)
            setUnderlinedText(tvPhoneNumber)
            setUnderlinedText(tvEmail)
        }
    }

    override fun onRequestPermissionsResult(
        requestCode: Int,
        permissions: Array<out String>,
        grantResults: IntArray
    ) {
        super.onRequestPermissionsResult(requestCode, permissions, grantResults)
        if (requestCode == PERMISSIONS_ALL) {
            if (grantResults.isNotEmpty() && grantResults[0] == PackageManager.PERMISSION_GRANTED) {
                // Permission granted, make the call
            } else {
                showAlertPermission(getString(R.string.notes_need_permission))
                // Permission denied, inform the user or handle accordingly
            }
        }
    }

    private fun initListener() {
        with(binding) {
            btLihatBuktiTrx.setOnClickListener {
                when (mTrxType) {
                    Constant.TRX_TYPE_KLAIM_DPLK_RECEIPT, Constant.TRX_TYPE_KLAIM_DPLK_DETAIL -> {
                        presenter.getReceiptDetailKlaimDplk(DetailReceiptKlaimDplkRequest(mResponse.trxId))
                    }

                    Constant.TRX_TYPE_WITHDRAW_RDN_RECEIPT -> {
                        val request = RdnReceiptWithdrawRequest(
                            rdnAccount = mResponse.productDetail.rdnAccount,
                            transactionId = mResponse.transactionNumber
                        )
                        presenter.getReceiptWdRdn(request = request)
                    }

                    else -> {

                    }
                }
            }

            tvHeadphone.setOnClickListener {
                makePhoneCall(Constant.CALL_CONTACT_BRI)
            }

            tvPhoneNumber.setOnClickListener {
                makePhoneCall(Constant.CALL_CONTACT_DPLK)
            }

            tvEmail.setOnClickListener {
                val email = "mailto:<EMAIL>"
                val intent = Intent(Intent.ACTION_SENDTO).apply {
                    data = Uri.parse(email)
                }
                startActivity(intent)
            }
        }
    }

    private fun injectDependency() {
        activityComponent.inject(this)
        presenter.view = this
        presenter.setUrlReceiptDetailKlaimDplk(getString(R.string.url_receipt_detail_klaim_dplk))
        presenter.setUrlReceiptWdRdn(getString(R.string.url_withdraw_recipt))
        request = ReceiptStatusRequest(mFundCode)

        presenter.start()
    }

    private fun setupView() {
        setupData(mResponse)
    }

    private fun setupData(response: ReceiptRevampResponse) {
        if (response.transactionSuccess != null) {
            if (response.transactionSuccess) {
                val hasProcessOrFailed = mResponse.trackingDataView.any {
                    it.statusEnum in listOf(DataView.Status.PROCESS, DataView.Status.FAILED)
                }
                binding.layoutButton.isVisible = !hasProcessOrFailed
                binding.rlBantuan.isVisible = hasProcessOrFailed
            } else {
                binding.layoutButton.gone()
            }
        } else {
            val hasProcessOrFailed = mResponse.trackingDataView.any {
                it.statusEnum in listOf(DataView.Status.PROCESS, DataView.Status.FAILED)
            }
            binding.layoutButton.isVisible = !hasProcessOrFailed
            binding.rlBantuan.isVisible = hasProcessOrFailed
        }

        if (mTrxType.equals(Constant.TRX_TYPE_KLAIM_DPLK_DETAIL)) {
            GeneralHelper.setToolbar(
                this@ReceiptStatusActivity, binding.tbStatusAmbilFisik.toolbar,
                getString(
                    R.string.txt_status_klaim
                )
            )
            binding.tvTitle.setText(
                getString(
                    R.string.txt_status_klaim
                )
            )
            binding.tvTitleDetail.setText(getString(R.string.txt_detail_klaim))
        } else if (mTrxType.equals(Constant.TRX_TYPE_KLAIM_DPLK_RECEIPT)) {
            GeneralHelper.setToolbar(
                this@ReceiptStatusActivity, binding.tbStatusAmbilFisik.toolbar,
                getString(
                    R.string.txt_status_klaim
                )
            )
            binding.tvTitle.setText(
                getString(
                    R.string.txt_status_klaim
                )
            )
            binding.tvTitleDetail.setText(getString(R.string.txt_detail_klaim))

            StatusOpenBottomFragment.showDialogStatus(
                fragmentManager = supportFragmentManager,
                imgPath = response.paymentDrawer.iconPath,
                imgName = "",
                titleTxt = response.paymentDrawer.title,
                subTitleTxt = response.paymentDrawer.description,
                btnFirstFunction = {

                },
                btnSecondFunction = {

                },
                isClickableOutside = true,
                firstBtnTxt = GeneralHelper.getString(R.string.check_status),
                withBgSecondBtn = false
            )
        } else if (mTrxType.equals(Constant.TRX_TYPE_WITHDRAW_RDN_RECEIPT)) {
            GeneralHelper.setToolbar(
                this@ReceiptStatusActivity, binding.tbStatusAmbilFisik.toolbar,
                response.type
            )

            with(binding) {
                layoutButton.isVisible = response.transactionSuccess
                tvTitle.text = response.subtitle
                tvDetailProduct.text = response.productDetail.rdnAccount
                tvTitleDetailProduct.text = getString(R.string.txt_detail_rdn)
                tvTitleDetail.text = getString(R.string.txt_detail_withdraw)
                ivPanah.gone()
                ivProdukWithoutBackground.gone()
                rlBantuan.visible()
                llInitial.visible()
            }

            setDataContactUs(response.rdnContactUs)

            if (response.paymentDrawer != null) {
                StatusOpenBottomFragment.showDialogStatus(
                    fragmentManager = supportFragmentManager,
                    imgPath = response.paymentDrawer.iconPath,
                    imgName = "",
                    titleTxt = response.paymentDrawer.title,
                    subTitleTxt = response.paymentDrawer.description,
                    btnFirstFunction = {

                    },
                    btnSecondFunction = {

                    },
                    isClickableOutside = true,
                    firstBtnTxt = GeneralHelper.getString(R.string.check_status),
                    withBgSecondBtn = false
                )
            }
        }
        binding.tvHeader.text = response.title
        binding.tvDesc.text = response.description


        mAdapterStep = response.trackingDataView.let { ReceiptStatusProgressAdapter(it, this) }

        binding.rvTracking.layoutManager = LinearLayoutManager(
            this,
            LinearLayoutManager.VERTICAL, false
        )
        binding.rvTracking.adapter = mAdapterStep


        if (response.referenceDataView != null) {
            binding.lineBottomHeader.isVisible = true
            binding.rvHeaderDataView.isVisible = true

            mAdapterHeaderTransaksi =
                response.referenceDataView.let { DetailTransaksiInfoAdapter(it, this, this) }
            binding.rvHeaderDataView.layoutManager = LinearLayoutManager(
                this,
                LinearLayoutManager.VERTICAL, false
            )
            binding.rvHeaderDataView.adapter = mAdapterHeaderTransaksi
        } else {
            binding.lineBottomHeader.isVisible = false
            binding.rvHeaderDataView.isVisible = false
        }

        mAdapterDetailTransaksi =
            response.detailDataView.let { DetailTransaksiInfoAdapter(it, this, this) }
        binding.rvDetailTransaksi.layoutManager = LinearLayoutManager(
            this,
            LinearLayoutManager.VERTICAL, false
        )
        binding.rvDetailTransaksi.adapter = mAdapterDetailTransaksi


        mAdapterDetailBiaya =
            response.amountDataView.let { DetailTransaksiInfoAdapter(it, this, this) }
        binding.rvBiayaCetak.layoutManager = LinearLayoutManager(
            this,
            LinearLayoutManager.VERTICAL, false
        )
        binding.rvBiayaCetak.adapter = mAdapterDetailBiaya


        mAdapterTotalBiaya = response.totalDataView.let {
            TotalTransaksiRevInfoAdapter(it, this) {
                OpenBottomSheetGeneralFragment.showDialogInformationWithOutImage(
                    fragmentManager = supportFragmentManager,
                    imgPath = "",
                    imgName = "",
                    titleTxt = it.title,
                    subTitleTxt = it.description,
                    btnFirstFunction = {},
                    isClickableOutside = false,
                    firstBtnTxt = getString(R.string.kembali)
                )
            }
        }
        binding.rvTotalPayment.layoutManager = LinearLayoutManager(
            this,
            LinearLayoutManager.VERTICAL, false
        )
        binding.rvTotalPayment.adapter = mAdapterTotalBiaya

        binding.llRiwayat.isVisible = mResponse.productDetail != null
        if (mResponse.productDetail != null) {
            GeneralHelper.loadImageUrl(
                this@ReceiptStatusActivity,
                mResponse.productDetail.iconPath,
                binding.ivProduk,
                R.drawable.bri,
                0
            )
            GeneralHelper.loadImageUrl(
                this@ReceiptStatusActivity,
                mResponse.productDetail.iconPath,
                binding.ivProdukWithoutBackground,
                R.drawable.bri,
                0
            )
            binding.tvTitleProduct.text = mResponse.productDetail.name
            if (mTrxType != Constant.TRX_TYPE_WITHDRAW_RDN_RECEIPT) {
                binding.tvDetailProduct.text = mResponse.productDetail.type
            }
        }


        binding.llContainerDetailProduct.setOnClickListener {
            when (mTrxType) {
                Constant.TRX_TYPE_KLAIM_DPLK_RECEIPT -> presenter.getReceiptDetailKlaimDplk(
                    DetailReceiptKlaimDplkRequest(mResponse.referenceNumber)
                )

            }
            presenter.getReceiptDetail(ReceiptStatusRequest(mResponse.productDetail.fundCode))
        }
        hideProgress()
    }

    override fun onSuccessGetReceiptStatus(response: ReceiptRevampResponse) {
        mResponse = response
        setupData(mResponse)
    }

    override fun onSuccessGetPaymentRevamp(receiptRevampResponse: ReceiptRevampResponse?) {
        ReceiptRevampActivity.launchIntentTrackingEmas(false, false,true,this,receiptRevampResponse)
    }


    override fun onActivityResult(requestCode: Int, resultCode: Int, data: Intent?) {
        super.onActivityResult(requestCode, resultCode, data)
        if (requestCode == Constant.REQ_PAYMENT) {
            if (resultCode == RESULT_OK) {
                setResult(RESULT_OK)
                finish()
            }
        }
    }

    private fun makePhoneCall(phoneNumber: String) {
        val phoneIntent = Intent(Intent.ACTION_CALL, Uri.parse("tel:$phoneNumber"))
        if (ActivityCompat.checkSelfPermission(
                this,
                Manifest.permission.CALL_PHONE
            ) != PackageManager.PERMISSION_GRANTED
        ) {
            checkPermission()
            return
        }
        startActivity(phoneIntent)
    }

    private fun checkPermission() {
        val phoneCallPermission = Manifest.permission.CALL_PHONE
        if (ActivityCompat.checkSelfPermission(
                this,
                phoneCallPermission
            ) != PackageManager.PERMISSION_GRANTED
        ) {
            ActivityCompat.requestPermissions(
                this,
                arrayOf(phoneCallPermission),
                PERMISSIONS_ALL
            )
        } else {
            // Permission is already granted, make the call
        }
    }

    private fun setUnderlinedText(textView: TextView) {
        val spannable = SpannableString(textView.text)
        spannable.setSpan(UnderlineSpan(), 0, spannable.length, 0)
        textView.text = spannable
    }

    override fun onBackPressed() {
        super.onBackPressed()
        _binding = null

        val intent = Intent()
        when (mTrxType) {
            Constant.TRX_TYPE_KLAIM_DPLK_RECEIPT -> {
                intent.putExtra(IS_FROM_RECEIPT, true)
            }
            Constant.TRX_TYPE_WITHDRAW_RDN_RECEIPT -> {
                if (isFromConfirmation) {
                    DashboardRdnRevampActivity.launchIntentFromReceiptStatus(this@ReceiptStatusActivity)
                } else {
                    setResult(RESULT_CANCELED, intent)
                    finish()
                }
                return
            }
        }

        setResult(RESULT_OK, intent)
        finish()
    }

    override fun onResume() {
        super.onResume()

    }

    override fun onItemClick(item: DataView) {
        OpenBottomSheetGeneralFragment.showDialogInformationWithNoImage(
            supportFragmentManager,
            "",
            "",
            item.unitInfo.title,
            item.unitInfo.description,
            {},
            true,
            GeneralHelper.getString(R.string.btn_tutup)
        )
    }

    @SuppressLint("SetTextI18n")
    private fun setDataContactUs(data: RdnContactUs) {
        with(binding) {
            tvKantor.text = data.detail?.companyName ?: ""
            tvAlamat.text = (data.detail?.address ?: "") + "\n" + (data.detail?.subAddress ?: "")
            tvHeadphone.text = data.detail?.phoneNumber1 ?: ""
            tvPhoneNumber.text = data.detail?.phoneNumber2 ?: ""
            tvEmail.text = data.detail?.email ?: ""
            ivBantuan.setImageResource(R.drawable.ic_brids)

            tvHeadphone.setOnClickListener { makePhoneCall(Constant.CALL_CONTACT_BRIDS_1) }
            tvPhoneNumber.setOnClickListener { makePhoneCall(Constant.CALL_CONTACT_BRIDS_2) }
            tvEmail.setOnClickListener {
                val email = "mailto:${data.detail?.email}"
                val intent = Intent(Intent.ACTION_SENDTO)
                intent.data = Uri.parse(email)
                startActivity(intent)
            }
        }
    }
}