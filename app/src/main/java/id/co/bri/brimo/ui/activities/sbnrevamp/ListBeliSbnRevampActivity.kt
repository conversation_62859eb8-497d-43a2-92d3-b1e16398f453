package id.co.bri.brimo.ui.activities.sbnrevamp

import android.app.Activity
import android.content.Intent
import android.os.Bundle
import android.os.SystemClock
import android.view.View
import androidx.recyclerview.widget.LinearLayoutManager
import id.co.bri.brimo.R
import id.co.bri.brimo.adapters.sbnrevamp.SbnBeliAdapter
import id.co.bri.brimo.contract.IPresenter.sbnrevamp.IBeliSbnRevampPresenter
import id.co.bri.brimo.contract.IView.sbnrevamp.IBeliSbnRevampView
import id.co.bri.brimo.databinding.ActivityListBeliSbnRevampBinding
import id.co.bri.brimo.domain.config.Constant
import id.co.bri.brimo.domain.helpers.GeneralHelper
import id.co.bri.brimo.models.apimodel.request.esbn.RegisKemenkeuRequest
import id.co.bri.brimo.models.apimodel.request.sbnrevamp.SbnSimulasiRequest
import id.co.bri.brimo.models.apimodel.response.esbn.*
import id.co.bri.brimo.models.apimodel.response.esbn.regisESBN.EsbnProductBriefResponse
import id.co.bri.brimo.models.apimodel.response.rdn.RdnOnBoardingResponse
import id.co.bri.brimo.models.apimodel.response.rdn.RdnOnCheckpointResponse
import id.co.bri.brimo.models.apimodel.response.sbnrevamp.NotFoundSbnModel
import id.co.bri.brimo.models.apimodel.response.sbnrevamp.SbnBeliResponse
import id.co.bri.brimo.models.apimodel.response.sbnrevamp.SbnSimulasiResponse
import id.co.bri.brimo.ui.activities.base.BaseActivity
import id.co.bri.brimo.ui.activities.rdn.CekStatusRdnActivity
import id.co.bri.brimo.ui.activities.rdn.DashboardRDNActivity
import id.co.bri.brimo.ui.activities.rdn.DescriptionRdnProductActivity
import id.co.bri.brimo.ui.activities.rdnrevamp.dashboard.DashboardRdnRevampActivity
import id.co.bri.brimo.ui.activities.sbn.DetailBeliSbnActivity
import id.co.bri.brimo.ui.activities.sbn.TentangSbnActivity
import id.co.bri.brimo.ui.activities.sbn.regisSbn.EsbnProductBriefRegisActivity
import id.co.bri.brimo.ui.fragments.bottomsheet.OpenBottomSheetGeneralFragment
import id.co.bri.brimo.ui.fragments.esbn.BottomFragmentRegKemenkeu
import id.co.bri.brimo.ui.fragments.rdn.CustomBottomDialogFragment
import id.co.bri.brimo.ui.fragments.sbnrevamp.BottomFragmentSbnKemenkeu
import javax.inject.Inject

class ListBeliSbnRevampActivity : BaseActivity(), IBeliSbnRevampView, View.OnClickListener, SbnBeliAdapter.onItemClickListener,BottomFragmentSbnKemenkeu.OnFragmentInteractionListener,BottomFragmentRegKemenkeu.BottomDialogDefaultListener {

    private lateinit var binding: ActivityListBeliSbnRevampBinding
    private var adapter : SbnBeliAdapter? = null

    private var mProductListSbnResponse: MutableList<SbnBeliResponse.SbnListProduct.SbnItemProduct>? = mutableListOf()


    @Inject
    lateinit var presenter : IBeliSbnRevampPresenter<IBeliSbnRevampView>

    companion object {

        private var mResponse: SbnBeliResponse?=null
        var mAccount : String = ""
        var mSid: String = ""
        var mSre: String = ""
        @JvmStatic
        fun launchIntent(caller: Activity, response: SbnBeliResponse,account :String, sid : String, sre : String) {
            val intent = Intent(caller, ListBeliSbnRevampActivity::class.java)
            caller.startActivityForResult(intent, Constant.REQ_PAYMENT)
            mResponse = response
            mAccount = account
            mSid = sid
            mSre = sre
        }
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        binding = ActivityListBeliSbnRevampBinding.inflate(layoutInflater)
        setContentView(binding.root)
        injecetDepedency()
        setupView()
    }

    private fun injecetDepedency() {
        activityComponent.inject(this)
        presenter.view = this
        presenter.setUrlSimulasiSbn(GeneralHelper.getString(R.string.url_simulasi_sbn))
        presenter.setUrlTentangSbn(GeneralHelper.getString(R.string.url_tentang_sbn))
        presenter.setUrlGetDetail(GeneralHelper.getString(R.string.url_get_detail_offer))
        presenter.setUrlValidateUser(GeneralHelper.getString(R.string.url_sbn_validate_user))
        presenter.setUrlKemenkeu(GeneralHelper.getString(R.string.url_kemenkeu))
        presenter.setUrlRegistrasiSbn(GeneralHelper.getString(R.string.url_product_brief_esbn))
        presenter.setUrlProductsBrief(GeneralHelper.getString(R.string.url_rdn_onboarding_new))
        presenter.start()
    }

    private fun setupView() {
        GeneralHelper.setToolbarRevamp(this,binding.tbBeliSbn.toolbar, GeneralHelper.getString(R.string.tb_beli_sbn))
            if(mResponse?.sbnListProduct?.berlangsung?.size == 0){
                binding.lyEmpty.visibility = View.VISIBLE
                binding.rvListSbn.visibility = View.GONE
            }else {
                binding.lyEmpty.visibility = View.GONE
                binding.rvListSbn.visibility = View.VISIBLE
                binding.btnBerlangsung.setBackgroundResource(GeneralHelper.getImageId(this, "background_blue_sbn"))
                binding.btnBerlangsung.setTextColor(GeneralHelper.getColor(R.color.primaryBlue80))
            }
        adapter = SbnBeliAdapter(this,this@ListBeliSbnRevampActivity)
        binding.apply {
            rvListSbn.layoutManager = LinearLayoutManager(
                    this@ListBeliSbnRevampActivity,
                    LinearLayoutManager.VERTICAL,
                    false
            )
            rvListSbn.adapter = adapter
            mProductListSbnResponse?.clear()
            mResponse?.sbnListProduct?.berlangsung?.let { mProductListSbnResponse?.addAll(it) }
            mProductListSbnResponse?.let { adapter?.setItems(it) }
            btnBerlangsung.setOnClickListener(this@ListBeliSbnRevampActivity)
            btnAkanDatang.setOnClickListener(this@ListBeliSbnRevampActivity)
            btnBerakhir.setOnClickListener(this@ListBeliSbnRevampActivity)
            rlTentangSbn.setOnClickListener(this@ListBeliSbnRevampActivity)
        }

    }

    override fun onSuccessSimulasiSbn(response: SbnSimulasiResponse, idSeri : Int) {
        SbnSimulasiRevampActivity.launchIntent(this,response,idSeri, mAccount, mSid, mSre)
    }

    override fun onSuccesGetAboutSbn(response: DataTentangSbnResponse) {
        TentangSbnActivity.launchIntent(this, response)
    }

    override fun onSuccessGetDetail(beliSbnResponse: BeliSbnResponse?) {
        DetailBeliSbnActivity.launchIntent(this, beliSbnResponse,"0")
    }

    override fun onSuccessGetValidateUser(idSeri: Int) {
        presenter.getDetailOffer(idSeri)
    }

    override fun onException12(message: String?) {
        showSnackbarErrorMessageRevamp(
                message,
                ALERT_ERROR,
                this@ListBeliSbnRevampActivity,
                false
        )
    }

    override fun onException93(message: String?) {
        val i = Intent()
        i.putExtra(Constant.TAG_ERROR_MESSAGE, message)
        setResult(RESULT_CANCELED, i)
        finish()
    }

    override fun onExceptionR5(response: NotFoundSbnModel?) {
        val customBottomDialogFragment = CustomBottomDialogFragment(this, response!!.title, response.desc, getString(R.string.ok), response.icon, response.iconPath, false)
        customBottomDialogFragment.show(this.supportFragmentManager, "")
    }

    override fun onExceptionR4(response: NotFoundSbnModel?) {
        presenter.getSbnRegisData()
    }

    override fun onExceptionRegisKemenkeu(response: NotFoundSbnModel?) {
        val bottomFragmentSbnKemenkeu = BottomFragmentSbnKemenkeu(this, response!!,this)
        bottomFragmentSbnKemenkeu.show(this.supportFragmentManager, "")
    }

    override fun onSuccessGetRegisSbn(response: EsbnProductBriefResponse) {
        EsbnProductBriefRegisActivity.launchIntent(this, response, false)
    }

    override fun onSuccessGetRegisRdnS1(response: DashboardDataSbnResponse) {
        val btnTitle = GeneralHelper.getString(R.string.buka_rdn_txt)
        showBottomSheetRegistrasi(
                response,
                btnTitle,
                action = {presenter.getOnProductBrief()}
        )
    }


    override fun onSuccessGetRegisRdnS2(response: DashboardDataSbnResponse) {
        val btnTitle = GeneralHelper.getString(R.string.string_button_01_sbn_to_rdn)
        showBottomSheetRegistrasi(
                response,
                btnTitle,
                action = { DashboardRdnRevampActivity.launchIntent(this)}
        )
    }

    private fun showBottomSheetRegistrasi(response: DashboardDataSbnResponse, btnTitle: String, action: () -> Unit = {}) {
        OpenBottomSheetGeneralFragment.showDialogInformationWithAction(
                fragmentManager = supportFragmentManager,
                imgPath = response.dialogData.imageUrl,
                imgName = "",
                titleTxt = response.dialogData.title,
                subTitleTxt = response.dialogData.description,
                btnFirstFunction = { action() },
                btnThirdFunction = {},
                isClickableOutside = true,
                firstBtnTxt = btnTitle,
                thirdBtnTxt = getString(R.string.nanti_saja)
        )
    }

    override fun onSuccessGetSbnException(response: EsbnExceptionResponse) {
        OpenBottomSheetGeneralFragment.showDialogInformation(
                fragmentManager = supportFragmentManager,
                imgPath = response.imagePath,
                imgName = response.imageName,
                titleTxt = response.title,
                subTitleTxt = response.description,
                btnFirstFunction = {},
                isClickableOutside = false,
                firstBtnTxt = GeneralHelper.getString(R.string.ok)
        )
    }

    override fun onCheckPointRegisSbn(response: RdnOnCheckpointResponse) {
        CekStatusRdnActivity.launchIntent(this, response)
    }

    override fun onSuccessProductRdn(response: RdnOnBoardingResponse) {
        DescriptionRdnProductActivity.rdnOnBoardingResponse = response
        DescriptionRdnProductActivity.launchIntent(this)
    }

    override fun onSuccessKemenkeu(response: DashboardDataSbnResponse) {
        val customBottomDialogFragment = CustomBottomDialogFragment(this, response.dialogData.title, response.dialogData.description, GeneralHelper.getString(R.string.ok), "", response.dialogData.imageUrl, false)
        customBottomDialogFragment.show(supportFragmentManager, customBottomDialogFragment.tag)
    }

    override fun onException02(response: EsbnExceptionResponse) {
        val customBottomDialogFragment = CustomBottomDialogFragment(this, response.title, response.description, GeneralHelper.getString(R.string.baiklah), response.imageName, response.imagePath, false)
        customBottomDialogFragment.show(supportFragmentManager, customBottomDialogFragment.tag)
    }

    override fun onClick(p0: View?) {
       when(p0?.id){
           R.id.btn_akan_datang->{
               binding.btnAkanDatang.setBackgroundResource(GeneralHelper.getImageId(this, "background_blue_sbn"))
               binding.btnAkanDatang.setTextColor(GeneralHelper.getColor(R.color.primaryBlue80))
               binding.btnBerlangsung.setBackgroundResource(GeneralHelper.getImageId(this,"background_btn_sbn"))
               binding.btnBerakhir.setBackgroundResource(GeneralHelper.getImageId(this,"background_btn_sbn"))
               binding.btnBerlangsung.setTextColor(GeneralHelper.getColor(R.color.neutral_dark40))
               binding.btnBerakhir.setTextColor(GeneralHelper.getColor(R.color.neutral_dark40))
               if(mResponse?.sbnListProduct?.akanDatang?.size == 0){
                   binding.lyEmpty.visibility = View.VISIBLE
                   binding.rvListSbn.visibility = View.GONE
               }else{
                   binding.lyEmpty.visibility = View.GONE
                   binding.rvListSbn.visibility = View.VISIBLE
                   mProductListSbnResponse?.clear()
                   mResponse?.sbnListProduct?.akanDatang?.let { mProductListSbnResponse?.addAll(it) }
                   mProductListSbnResponse?.let { adapter?.setItems(it) }
               }

           }

           R.id.btn_berlangsung->{
               binding.btnBerlangsung.setBackgroundResource(GeneralHelper.getImageId(this, "background_blue_sbn"))
               binding.btnBerlangsung.setTextColor(GeneralHelper.getColor(R.color.primaryBlue80))
               binding.btnAkanDatang.setBackgroundResource(GeneralHelper.getImageId(this,"background_btn_sbn"))
               binding.btnBerakhir.setBackgroundResource(GeneralHelper.getImageId(this,"background_btn_sbn"))
               binding.btnAkanDatang.setTextColor(GeneralHelper.getColor(R.color.neutral_dark40))
               binding.btnBerakhir.setTextColor(GeneralHelper.getColor(R.color.neutral_dark40))
               if(mResponse?.sbnListProduct?.berlangsung?.size == 0){
                   binding.lyEmpty.visibility = View.VISIBLE
                   binding.rvListSbn.visibility = View.GONE
               }else{
                   binding.lyEmpty.visibility = View.GONE
                   binding.rvListSbn.visibility = View.VISIBLE
               mProductListSbnResponse?.clear()
               mResponse?.sbnListProduct?.berlangsung?.let { mProductListSbnResponse?.addAll(it) }
               mProductListSbnResponse?.let { adapter?.setItems(it) }
               }
           }

           R.id.btn_berakhir->{
               binding.btnBerakhir.setBackgroundResource(GeneralHelper.getImageId(this, "background_blue_sbn"))
               binding.btnBerakhir.setTextColor(GeneralHelper.getColor(R.color.primaryBlue80))
               binding.btnBerlangsung.setBackgroundResource(GeneralHelper.getImageId(this,"background_btn_sbn"))
               binding.btnAkanDatang.setBackgroundResource(GeneralHelper.getImageId(this,"background_btn_sbn"))
               binding.btnAkanDatang.setTextColor(GeneralHelper.getColor(R.color.neutral_dark40))
               binding.btnBerlangsung.setTextColor(GeneralHelper.getColor(R.color.neutral_dark40))
               if(mResponse?.sbnListProduct?.berakhir?.size == 0){
                   binding.lyEmpty.visibility = View.VISIBLE
                   binding.rvListSbn.visibility = View.GONE
               }else{
                   binding.lyEmpty.visibility = View.GONE
                   binding.rvListSbn.visibility = View.VISIBLE
               mProductListSbnResponse?.clear()
               mResponse?.sbnListProduct?.berakhir?.let { mProductListSbnResponse?.addAll(it) }
               mProductListSbnResponse?.let { adapter?.setItems(it) }
               }
           }
           R.id.rl_tentang_sbn ->{
               presenter.getAboutSbn()
           }
       }
    }

    override fun onItemClick(seri : String, productId : Int) {
        presenter.getDataSimulasiSbn(SbnSimulasiRequest(seri,"0"), productId )
    }

    override fun onFuncClickItem(sbnProductId: Int) {
        if (SystemClock.elapsedRealtime() - mLastClickTime < 1000) {
            return
        }
        mLastClickTime = SystemClock.elapsedRealtime()
        presenter.getValidateUser(sbnProductId)
    }

    override fun onFragmentInteraction() {
        val rdnSidBottomFragment = BottomFragmentRegKemenkeu(this, GeneralHelper.getString(R.string.batal))
        rdnSidBottomFragment.show(supportFragmentManager, "")
    }

    override fun onClickOk(account: String?, sre: String?, sid: String?) {
        presenter.getKemenkeuData(RegisKemenkeuRequest(mAccount, mSid, mSre))
    }

    override fun onCLickNanti() {
    }

    override fun onActivityResult(requestCode: Int, resultCode: Int, data: Intent?) {
        super.onActivityResult(requestCode, resultCode, data)
        if (requestCode == Constant.REQ_PAYMENT && data != null) {
            if (resultCode == RESULT_OK) {
                setResult(RESULT_OK, data)
                finish()
            }else {
                setResult(RESULT_CANCELED, data)
            }
        }
    }
}