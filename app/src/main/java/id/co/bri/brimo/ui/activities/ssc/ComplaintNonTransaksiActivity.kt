package id.co.bri.brimo.ui.activities.ssc

import android.app.Activity
import android.content.Intent
import android.os.Bundle
import android.text.Editable
import android.text.Html
import android.view.View
import com.google.gson.Gson
import id.co.bri.brimo.R
import id.co.bri.brimo.adapters.HeaderTransaksiAdapter
import id.co.bri.brimo.databinding.ActivityComplaintNonTransaksiBinding
import id.co.bri.brimo.domain.config.Constant
import id.co.bri.brimo.domain.helpers.GeneralHelper
import id.co.bri.brimo.domain.helpers.ValidationHelper
import id.co.bri.brimo.models.BankModel
import id.co.bri.brimo.models.DurasiModel
import id.co.bri.brimo.models.apimodel.request.ssc.CreateTicketNtAtmReq
import id.co.bri.brimo.models.apimodel.request.ssc.CreateTicketNtConcenReq
import id.co.bri.brimo.models.apimodel.request.ssc.CreateTicketNtFakeReq
import id.co.bri.brimo.models.apimodel.request.ssc.CreateTicketNtUkerReq
import id.co.bri.brimo.models.apimodel.response.ssc.ComplaintInformasiResponse
import id.co.bri.brimo.models.apimodel.response.ssc.ComplaintTicketCreateRes
import id.co.bri.brimo.ui.activities.GeneralSyaratActivity

class ComplaintNonTransaksiActivity : BaseComplaintActivity(),
    View.OnClickListener {

    private val binding: ActivityComplaintNonTransaksiBinding by lazy {
        ActivityComplaintNonTransaksiBinding.inflate(layoutInflater)
    }

    private var featureId = ""
    private var sCodeChannel = ""
    private var isChecked = false

    companion object {
        private const val TAG_FEATUREID = "feature_id"
        private const val TAG_TOOLBAR = "feature_name"
        private const val TAG_RESPONSE = "response"

        @JvmStatic
        fun launchIntent(
            caller: Activity,
            sToolbar: String,
            sFeatureId: String,
            response: ComplaintInformasiResponse

        ) {
            val intent = Intent(caller, ComplaintNonTransaksiActivity::class.java)
            intent.putExtra(TAG_FEATUREID, sFeatureId)
            intent.putExtra(TAG_TOOLBAR, sToolbar)
            intent.putExtra(TAG_RESPONSE, Gson().toJson(response))
            caller.startActivityForResult(intent, Constant.REQ_NON_PAYMENT)
        }

    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContentView(binding.root)

        intentExtra()
        setToolbar()
        setupViews()
    }

    override fun injectDependency() {
        super.injectDependency()
        presenter.setUrlCreateTicket(GeneralHelper.getString(R.string.url_ss_complaint_ticket_create_non_mutation))
    }

    override fun intentExtra() {
        if (intent.hasExtra(TAG_FEATUREID))
            featureId = intent.getStringExtra(TAG_FEATUREID).toString()

        if (intent.hasExtra(TAG_TOOLBAR))
            sToolbar = intent.getStringExtra(TAG_TOOLBAR).toString()

        if (intent.hasExtra(TAG_RESPONSE))
            informationRes = Gson().fromJson(
                intent.getStringExtra(TAG_RESPONSE),
                ComplaintInformasiResponse::class.java
            )
    }

    override fun setToolbar() {
        GeneralHelper.setToolbar(this, binding.toolbar.toolbar, sToolbar)
    }

    @Suppress("DEPRECATION")
    override fun setupViews() {
        showViews()
        onClickListener()

        GeneralHelper.setToolbar(this, binding.toolbar.toolbar, sToolbar)

        binding.layoutNoteTerm.tvDescSyarat.text =
            Html.fromHtml(GeneralHelper.getString(R.string.syarat_ssc_nontrx))
    }

    override fun recyclerTrxInfo(headerTransaksiAdapter: HeaderTransaksiAdapter) {
        // do nothing
    }

    override fun notFoundTrxInfo() {
        // do nothing
    }

    override fun showViews() {
        when (featureId) {
            Constant.ComplaintMenu.COMPLAINT_NTCONCEN -> showNtConcen()
            Constant.ComplaintMenu.COMPLAINT_NTUKER -> showNtUker()
            Constant.ComplaintMenu.COMPLAINT_NTFAKE -> showNtFake()
            Constant.ComplaintMenu.COMPLAINT_NTATM,
            Constant.ComplaintMenu.COMPLAINT_NTATMOFF -> showNtAtmOff()
        }
    }

    private fun showNtConcen() {
        binding.layoutOfficerNameBri.visibility = View.VISIBLE
        binding.layoutInteractionDate.visibility = View.VISIBLE
        binding.layoutInteractionTime.visibility = View.VISIBLE
        binding.layoutPhoneUsed.visibility = View.VISIBLE
    }

    private fun showNtUker() {
        binding.layoutOfficerNameBri.visibility = View.VISIBLE
        binding.layoutInteractionDate.visibility = View.VISIBLE
        binding.layoutInteractionTime.visibility = View.VISIBLE
        binding.layoutCustomerDomicilli.visibility = View.VISIBLE
        binding.layoutOfficeBri.visibility = View.VISIBLE
    }

    private fun showNtFake() {
        binding.layoutSelectChannel.visibility = View.VISIBLE
        binding.layoutDetailChannel.visibility = View.VISIBLE
    }

    private fun showNtAtmOff() {
        binding.layoutNumberTidMachine.visibility = View.VISIBLE
        binding.layoutLocationAddressAtm.visibility = View.VISIBLE
    }

    private fun onClickListener() {
        binding.etOfficerNameBri.addTextChangedListener(activityTextListener)
        binding.etPhoneUsed.addTextChangedListener(activityTextListener)
        binding.etNumberTidMachine.addTextChangedListener(activityTextListener)
        binding.etLocationAddressAtm.addTextChangedListener(activityTextListener)
        binding.etDetailChannel.addTextChangedListener(activityTextListener)
        binding.etCustomerDomicilli.addTextChangedListener(activityTextListener)
        binding.etOfficeBri.addTextChangedListener(activityTextListener)
        binding.layoutNoteTerm.etPermasalahan.addTextChangedListener(activityTextListener)

        binding.layoutNoteTerm.layoutSyarat.setOnClickListener(this)
        binding.etInteractionDate.setOnClickListener(this)
        binding.etInteractionTime.setOnClickListener(this)
        binding.etOfficeBri.setOnClickListener(this)
        binding.etSelectChannel.setOnClickListener(this)
        binding.btnSubmit.setOnClickListener(this)
    }

    override fun changeText(charSequence: CharSequence?, i: Int, i1: Int, i2: Int) {
        validationButton()
    }

    override fun afterText(editable: Editable?) {
        binding.layoutNoteTerm.tvLength.text =
            binding.layoutNoteTerm.etPermasalahan.length().toString() + "/200"
    }

    override fun onClick(v: View) {
        when (v.id) {
            binding.layoutNoteTerm.layoutSyarat.id -> GeneralSyaratActivity.launchIntentNoArrow(
                this,
                informationRes?.term,
                true
            )
            binding.etInteractionDate.id -> setCalendar()
            binding.etInteractionTime.id -> setTimePicker(
                GeneralHelper.getString(R.string.waktu_interaksi),
                GeneralHelper.getString(R.string.desc_perkiraan_waktu_interaksi)
            )
            binding.etSelectChannel.id
            -> bottomSheetNoSearchData(informationRes!!.listChannel)
            binding.btnSubmit.id -> buttonClickSendReq()
        }
    }

    override fun buttonClickSendReq() {
        when (featureId) {
            Constant.ComplaintMenu.COMPLAINT_NTCONCEN -> requestNtConcen()
            Constant.ComplaintMenu.COMPLAINT_NTUKER -> requestNtUker()
            Constant.ComplaintMenu.COMPLAINT_NTFAKE -> requestNtFake()
            Constant.ComplaintMenu.COMPLAINT_NTATM,
            Constant.ComplaintMenu.COMPLAINT_NTATMOFF -> requestNtAtm()
        }
    }

    private fun requestNtConcen() {
        presenter.sendCreateTicket(
            CreateTicketNtConcenReq(
                featureId,
                binding.etOfficerNameBri.text.toString(),
                sDate,
                binding.etInteractionTime.text.toString(),
                binding.etPhoneUsed.text.toString(),
                binding.layoutNoteTerm.etPermasalahan.text.toString()
            )
        )
    }

    private fun requestNtUker() {
        presenter.sendCreateTicket(
            CreateTicketNtUkerReq(
                featureId,
                binding.etOfficerNameBri.text.toString(),
                sDate,
                binding.etInteractionTime.text.toString(),
                binding.etCustomerDomicilli.text.toString(),
                binding.etOfficeBri.text.toString(),
                binding.layoutNoteTerm.etPermasalahan.text.toString()
            )
        )
    }

    private fun requestNtFake() {
        presenter.sendCreateTicket(
            CreateTicketNtFakeReq(
                featureId,
                sCodeChannel,
                binding.etDetailChannel.text.toString(),
                binding.layoutNoteTerm.etPermasalahan.text.toString()
            )
        )
    }

    private fun requestNtAtm() {
        presenter.sendCreateTicket(
            CreateTicketNtAtmReq(
                featureId,
                binding.etNumberTidMachine.text.toString(),
                binding.etLocationAddressAtm.text.toString(),
                binding.layoutNoteTerm.etPermasalahan.text.toString()
            )
        )
    }

    override fun onCallbackTimePicker(time: String) {
        binding.etInteractionTime.setText(time)
        validationButton()
    }

    override fun validationButton() {
        when (featureId) {
            Constant.ComplaintMenu.COMPLAINT_NTCONCEN -> validateConcen()
            Constant.ComplaintMenu.COMPLAINT_NTUKER -> validateUker()
            Constant.ComplaintMenu.COMPLAINT_NTFAKE -> validateSoceng()
            Constant.ComplaintMenu.COMPLAINT_NTATM,
            Constant.ComplaintMenu.COMPLAINT_NTATMOFF -> validateAtm()
        }
    }

    private fun validateConcen() {
        enableButton(false)
        if (binding.etOfficerNameBri.text!!.length < 3)
            return
        if (binding.etInteractionDate.text!!.toString().isEmpty())
            return
        if (binding.etInteractionTime.text!!.toString().isEmpty())
            return
        if (!ValidationHelper.validatePhoneNumber(binding.etPhoneUsed.text!!.toString()))
            return
        if (binding.layoutNoteTerm.etPermasalahan.text!!.isEmpty())
            return
        if (!isChecked)
            return
        enableButton(true)
    }

    private fun validateUker() {
        enableButton(false)
        if (binding.etOfficerNameBri.text!!.isEmpty())
            return
        if (binding.etInteractionDate.text!!.isEmpty())
            return
        if (binding.etInteractionTime.text!!.isEmpty())
            return
        if (binding.etCustomerDomicilli.text!!.isEmpty())
            return
        if (binding.etOfficeBri.text!!.isEmpty())
            return
        if (binding.layoutNoteTerm.etPermasalahan.text!!.isEmpty())
            return
        if (!isChecked)
            return
        enableButton(true)
    }

    private fun validateSoceng() {
        enableButton(false)
        if (binding.etSelectChannel.text!!.isEmpty())
            return
        if (binding.etDetailChannel.text!!.isEmpty())
            return
        if (binding.layoutNoteTerm.etPermasalahan.text!!.isEmpty())
            return
        if (!isChecked)
            return
        enableButton(true)
    }

    private fun validateAtm() {
        enableButton(false)
        if (binding.etNumberTidMachine.text!!.isEmpty())
            return
        if (binding.etLocationAddressAtm.text!!.isEmpty())
            return
        if (binding.layoutNoteTerm.etPermasalahan.text!!.isEmpty())
            return
        if (!isChecked)
            return
        enableButton(true)
    }

    override fun enableButton(isEnable: Boolean) {
        if (isEnable) {
            binding.btnSubmit.alpha = 1f
            binding.btnSubmit.isEnabled = true
        } else {
            binding.btnSubmit.alpha = 0.3f
            binding.btnSubmit.isEnabled = false
        }
    }

    override fun onSuccessCreateTicket(response: ComplaintTicketCreateRes) {
        LaporanTransaksiActivity.launchIntent(this, sToolbar, response, Constant.CIAType.TYPE_COMPLAINT_IN_APPS_GENERAL)
    }

    override fun onException93(desc: String) {
        val returnIntent = Intent()
        returnIntent.putExtra("error_exception93", desc)
        setResult(RESULT_CANCELED, returnIntent)
        finish()
    }

    private fun setDuration(mDurasiModel: DurasiModel) {
        val dateString = mDurasiModel.startDateStringddMMyyyySlash
        binding.etInteractionDate.setText(dateString)
        sDate = mDurasiModel.startDateString
    }

    override fun onSelectBank(bankModel: BankModel) {
        binding.etSelectChannel.setText(bankModel.nama)
        sCodeChannel = bankModel.kode
    }

    @Deprecated("Deprecated in Java")
    @Suppress("DEPRECATION")
    override fun onActivityResult(requestCode: Int, resultCode: Int, data: Intent?) {
        super.onActivityResult(requestCode, resultCode, data)

        if (resultCode == RESULT_OK && requestCode == Constant.REQ_CALENDAR && data != null) {
            val durasiModel = DurasiModel(
                data.getIntExtra(Constant.START_DAY, 0),
                data.getIntExtra(Constant.START_MONTH, 0),
                data.getIntExtra(Constant.START_YEAR, 0)
            )
            setDuration(durasiModel)
            validationButton()
        }

        if (resultCode == RESULT_OK && requestCode == Constant.REQ_PETTUNJUK1 && data != null) {
            isChecked = java.lang.Boolean.parseBoolean(data.getStringExtra("checkbox"))
            if (isChecked) {
                binding.layoutNoteTerm.cbSyarat.setBackgroundResource(R.drawable.checkbox_on)
            } else {
                binding.layoutNoteTerm.cbSyarat.setBackgroundResource(R.drawable.checkbox_off)
            }
            validationButton()
        }

        if (resultCode == RESULT_CANCELED && requestCode == Constant.REQ_NON_PAYMENT) {
            setResult(RESULT_CANCELED)
            finish()
        }
    }
}