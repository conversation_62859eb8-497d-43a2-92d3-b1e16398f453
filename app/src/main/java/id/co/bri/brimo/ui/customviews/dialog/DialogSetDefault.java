package id.co.bri.brimo.ui.customviews.dialog;

import android.app.Dialog;
import android.graphics.drawable.ColorDrawable;
import android.os.Bundle;
import android.view.View;
import android.view.Window;
import android.widget.Button;
import android.widget.ImageView;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.fragment.app.DialogFragment;

import id.co.bri.brimo.R;

public class DialogSetDefault extends DialogFragment {
    private int requestId;
    private Dialog alertDialog;
    private Button btnYes, btnNo;
    private TextView title, desc;
    private String sTitle, sDesc;
    private String sYes, sNo;
    private DialogDefaultListener dialogDefaulListener;

    public DialogSetDefault() {
        // do nothing
    }

    public DialogSetDefault(DialogDefaultListener clickListener, String sTitle, String sDesc, int requestId) {
        this.dialogDefaulListener = clickListener;
        this.sTitle = sTitle;
        this.sDesc = sDesc;
        this.requestId = requestId;
    }

    public DialogSetDefault(DialogDefaultListener clickListener, String sTitle, String sDesc, String btnYes, String btnNo, int requestId) {
        this.dialogDefaulListener = clickListener;
        this.sTitle = sTitle;
        this.sDesc = sDesc;
        this.sYes = btnYes;
        this.sNo = btnNo;
        this.requestId = requestId;
    }

    @NonNull
    @Override
    public Dialog onCreateDialog(@Nullable Bundle savedInstanceState) {
        alertDialog = new Dialog(getActivity());
        alertDialog.setCanceledOnTouchOutside(false);
        alertDialog.getWindow().requestFeature(Window.FEATURE_NO_TITLE);
        alertDialog.getWindow().setBackgroundDrawable(new ColorDrawable(0));
        alertDialog.setContentView(R.layout.dialog_set_default);
        alertDialog.setOnKeyListener((dialogInterface, i, keyEvent) -> false);
        alertDialog.show();

        initView();

        btnNo.setOnClickListener(view -> {
            if (dialogDefaulListener != null) {
                dialogDefaulListener.onClickNoDefault(requestId);
            }
            dismiss();
        });

        btnYes.setOnClickListener(view -> {
            if (dialogDefaulListener != null) {
                dialogDefaulListener.onClickYesDefault(requestId);
            }
            dismiss();
        });

        return alertDialog;
    }

    private void initView() {

        btnYes = alertDialog.findViewById(R.id.btn_yes);
        btnNo = alertDialog.findViewById(R.id.btn_no);
        title = alertDialog.findViewById(R.id.tv_title_default);
        desc = alertDialog.findViewById(R.id.tv_desc_default);

        if (sYes != null && sNo != null) {
            btnYes.setText(sYes);
            btnNo.setText(sNo);
        }

        title.setText(sTitle);
        desc.setText(sDesc);
    }

    public interface DialogDefaultListener {
        void onClickYesDefault(int requestId);

        void onClickNoDefault(int requestId);
    }

}
