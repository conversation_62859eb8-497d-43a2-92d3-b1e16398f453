package id.co.bri.brimo.ui.activities.travel;

import android.app.Activity;
import android.content.Intent;
import android.os.Bundle;
import android.text.Editable;
import android.text.TextWatcher;
import android.util.Patterns;
import android.view.View;
import android.widget.EditText;

import java.util.ArrayList;
import java.util.List;

import id.co.bri.brimo.R;
import id.co.bri.brimo.databinding.ActivityDetailPemesanBinding;
import id.co.bri.brimo.domain.config.Constant;
import id.co.bri.brimo.domain.helpers.GeneralHelper;
import id.co.bri.brimo.models.PemesanTiketModel;
import id.co.bri.brimo.models.optionmodel.OptionGeneralModel;
import id.co.bri.brimo.ui.activities.base.BaseActivity;
import id.co.bri.brimo.ui.fragments.JenisDonasiFragment;
import id.co.bri.brimo.ui.fragments.PilihanGeneralFragment;

public class DetailPemesanActivity extends BaseActivity implements View.OnClickListener,
        PilihanGeneralFragment.SelectPilihanGeneral,
        JenisDonasiFragment.SelectJenisDonasi {

    private ActivityDetailPemesanBinding binding;

    private static PemesanTiketModel mPemesanTiketModel;
    private OptionGeneralModel currentOptionModel;
    private OptionGeneralModel idTypeModel;
    private static boolean mIsRequiredId = false;

    public static void launchIntent(Activity caller, PemesanTiketModel pemesanTiketModel, boolean isRequiredId) {
        Intent intent = new Intent(caller, DetailPemesanActivity.class);
        mPemesanTiketModel = pemesanTiketModel;
        mIsRequiredId = isRequiredId;
        caller.startActivityForResult(intent, Constant.REQ_NON_PAYMENT);
    }

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        binding = ActivityDetailPemesanBinding.inflate(getLayoutInflater());
        setContentView(binding.getRoot());

        GeneralHelper.setToolbar(this, binding.toolbar.toolbar, "Detail Pemesan");
        setupView();
        binding.btnSubmit.setOnClickListener(this);
        binding.etTitel.setOnClickListener(this);
        binding.etIdType.setOnClickListener(this);
        if (mPemesanTiketModel != null) {
            binding.etNama.setText(mPemesanTiketModel.getNamaLengkap());
            binding.etTitel.setText(mPemesanTiketModel.getTitel());
            binding.etEmail.setText(mPemesanTiketModel.getEmail());
            binding.etNohp.setText(mPemesanTiketModel.getNoHandphone());
            binding.etIdType.setText(mPemesanTiketModel.getIdType());
            binding.etIdNumber.setText(mPemesanTiketModel.getIdNumber());
        }
        editTextCheck(binding.etEmail);
        editTextCheck(binding.etNama);
        editTextCheck(binding.etNohp);
        editTextCheck(binding.etTitel);
    }

    public void setupView() {
        if (!mIsRequiredId) {
            binding.llIdType.setVisibility(View.GONE);
            binding.llIdNumber.setVisibility(View.GONE);
            binding.viewId.setVisibility(View.GONE);
            binding.viewNumber.setVisibility(View.GONE);
        } else {
            binding.llIdType.setVisibility(View.VISIBLE);
            binding.llIdNumber.setVisibility(View.VISIBLE);
            binding.viewId.setVisibility(View.VISIBLE);
            binding.viewNumber.setVisibility(View.VISIBLE);
            editTextCheck(binding.etIdType);
            editTextCheck(binding.etIdNumber);
        }
    }

    @Override
    public void onClick(View view) {
        switch (view.getId()) {
            case R.id.btnSubmit:
                Intent returnIntent = new Intent();
                returnIntent.putExtra("nama", binding.etNama.getText().toString());
                returnIntent.putExtra("titel", binding.etTitel.getText().toString());
                returnIntent.putExtra("no_hp", binding.etNohp.getText().toString());
                returnIntent.putExtra("email", binding.etEmail.getText().toString());
                returnIntent.putExtra("id_type", binding.etIdType.getText().toString());
                returnIntent.putExtra("id_number", binding.etIdNumber.getText().toString());
                this.setResult(RESULT_OK, returnIntent);
                this.finish();
                break;
            case R.id.et_titel:
                PilihanGeneralFragment fragmentPilihan = new PilihanGeneralFragment(fetchOptionList(), this);
                fragmentPilihan.show(getSupportFragmentManager(), "");
                break;
            case R.id.et_id_type:
                JenisDonasiFragment fragment = new JenisDonasiFragment(fetchIdTypeList(), this);
                fragment.show(getSupportFragmentManager(), "");
                break;
        }

    }

    @Override
    public void onClick(int position, OptionGeneralModel optionModel) {
        currentOptionModel = optionModel;
        binding.etTitel.setText(currentOptionModel.getOptionName());
    }

    private List<OptionGeneralModel> fetchOptionList() {
        List<OptionGeneralModel> list = new ArrayList<OptionGeneralModel>();

        list.add(new OptionGeneralModel(0, ("Tuan"), "", ""));
        list.add(new OptionGeneralModel(0, ("Nyonya"), "", ""));
        list.add(new OptionGeneralModel(0, ("Nona"), "", ""));

        return list;
    }

    private List<OptionGeneralModel> fetchIdTypeList() {
        List<OptionGeneralModel> list = new ArrayList<OptionGeneralModel>();

        list.add(new OptionGeneralModel(0, ("KTP"), "", ""));
        list.add(new OptionGeneralModel(0, ("SIM"), "", ""));

        return list;
    }

    public void editTextCheck(EditText editText) {
        editText.addTextChangedListener(new TextWatcher() {
            @Override
            public void beforeTextChanged(CharSequence charSequence, int i, int i1, int i2) {
                validateButton();
            }

            @Override
            public void onTextChanged(CharSequence charSequence, int i, int i1, int i2) {
                validateButton();
            }

            @Override
            public void afterTextChanged(Editable editable) {
                validateButton();
            }
        });
    }

    public void validateButton() {
        if (binding.etNama.getText().toString().length() == 0) {
            setButton(false);
        } else if (binding.etNohp.getText().toString().length() <= 8) {
            setButton(false);
        } else if (binding.etTitel.getText().toString().length() == 0) {
            setButton(false);
        } else if (binding.etEmail.getText().toString().length() <= 10) {
            setButton(false);
        } else if (!Patterns.EMAIL_ADDRESS.matcher(binding.etEmail.getText().toString()).matches()) {
            setButton(false);
        } else if (binding.etIdType.getText().toString().length() == 0 && mIsRequiredId) {
            setButton(false);
        } else if (binding.etIdNumber.getText().toString().length() <= 13 && mIsRequiredId) {
            setButton(false);
        } else {
            setButton(true);
        }
    }

    public void setButton(boolean enable) {
        if (enable) {
            binding.btnSubmit.setEnabled(true);
            binding.btnSubmit.setAlpha(1);
        } else {
            binding.btnSubmit.setEnabled(false);
            binding.btnSubmit.setAlpha((float) 0.3);
        }
    }

    @Override
    public void onClickJenisDonasi(int position, OptionGeneralModel optionModel) {
        idTypeModel = optionModel;
        binding.etIdType.setText(idTypeModel.getOptionName());
    }

    @Override
    protected void onDestroy() {
        super.onDestroy();
        binding = null;
    }
}