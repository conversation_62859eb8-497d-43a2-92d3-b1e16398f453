package id.co.bri.brimo.ui.activities.britamarencana;

import android.app.Activity;
import android.content.Intent;
import android.os.Bundle;
import android.view.View;
import androidx.annotation.Nullable;
import javax.inject.Inject;
import id.co.bri.brimo.R;
import id.co.bri.brimo.contract.IPresenter.britamarencana.IPencairanRencanaPresenter;
import id.co.bri.brimo.contract.IView.britamarencana.IPencairanRencanaView;
import id.co.bri.brimo.databinding.ActivityPencairanRencanaBinding;
import id.co.bri.brimo.domain.config.Constant;
import id.co.bri.brimo.domain.helpers.GeneralHelper;
import id.co.bri.brimo.domain.helpers.textwatcher.AmountFormatWatcher;
import id.co.bri.brimo.models.ParameterKonfirmasiModel;
import id.co.bri.brimo.models.apimodel.request.KonfirmasiPencairanRequest;
import id.co.bri.brimo.models.apimodel.response.FormPencairanResponse;
import id.co.bri.brimo.models.apimodel.response.GeneralConfirmationResponse;
import id.co.bri.brimo.ui.activities.KonfirmasiGeneralActivity;
import id.co.bri.brimo.ui.activities.base.BaseActivity;

public class PencairanRencanaActivity extends BaseActivity implements View.OnClickListener, AmountFormatWatcher.onAmountChange, IPencairanRencanaView {

    private ActivityPencairanRencanaBinding binding;

    protected Long nominalPencairan;
    protected Double maksimal;
    protected boolean isValid = false;
    protected static FormPencairanResponse mFormPencairanResponse;

    public static void launchIntent(Activity caller, FormPencairanResponse formPencairanResponse) {
        Intent intent = new Intent(caller, PencairanRencanaActivity.class);
        mFormPencairanResponse = formPencairanResponse;
        caller.startActivity(intent);
    }

    @Inject
    IPencairanRencanaPresenter<IPencairanRencanaView> presenter;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        binding = ActivityPencairanRencanaBinding.inflate(getLayoutInflater());
        setContentView(binding.getRoot());

        binding.btnSubmit.setOnClickListener(this);
        GeneralHelper.setToolbar(this, binding.toolbar.toolbar, "Pencairan Sebagian");
        binding.edNominal.addTextChangedListener(new AmountFormatWatcher(binding.edNominal, this, false));
        setupView();
        injectDependency();
    }

    private void injectDependency() {
        getActivityComponent().inject(this);
        if (presenter != null) {
            presenter.setView(this);
            presenter.start();
            presenter.setUrl(GeneralHelper.getString(R.string.url_konfirmasi_pencairan));
        }
    }


    public void setupView() {
        maksimal = Double.valueOf(mFormPencairanResponse.getMaximumTransaction());
//        tvSaldo.setText(String.format("Rp%s", GeneralHelper.formatNominal(maksimal)));
        setTvMinTrx();
        binding.tvSaldo.setText(mFormPencairanResponse.getSourceAccountDataView().getSubtitle());
        binding.tvNorek.setText(mFormPencairanResponse.getSourceAccountDataView().getTitle());
        binding.tvInisial.setText(GeneralHelper.formatInitialName(mFormPencairanResponse.getSourceAccountDataView().getTitle()));
    }

    public void setTvMinTrx() {
        binding.tvError.setVisibility(View.VISIBLE);
        binding.tvError.setText(String.format("Minimum Pencairan %s", mFormPencairanResponse.getMinimumTransactionString()));
        binding.tvError.setTextColor(getResources().getColor(R.color.colorErrorMinTrx));
        buttonEnable(false);
    }

    public void setTvErrorMax() {
        binding.tvError.setText(GeneralHelper.getString(R.string.jumlah_pencairan));
        binding.tvError.setTextColor(getResources().getColor(R.color.red_alert));
        binding.tvError.setVisibility(View.VISIBLE);
        buttonEnable(false);
    }

    public void setTvError() {
        binding.tvError.setText(GeneralHelper.getString(R.string.sisa_kurang_dari_sejuta));
        binding.tvError.setTextColor(getResources().getColor(R.color.red_alert));
        binding.tvError.setVisibility(View.VISIBLE);
        buttonEnable(false);
    }

    @Override
    public void onClick(View view) {
        KonfirmasiPencairanRequest konfirmasiPencairanRequest = new KonfirmasiPencairanRequest(nominalPencairan.toString(), mFormPencairanResponse.getReferenceNumber());
        presenter.getInquiryPencairan(konfirmasiPencairanRequest);
    }

    @Override
    public void onAmountChange(String amount) {
        if (binding.edNominal.getText().toString().isEmpty()) {
            setTvMinTrx();
        }
        nominalPencairan = Long.parseLong(binding.edNominal.getText().toString().replace(".", "").replace("Rp", "").replace("-", ""));
        if (nominalPencairan == 0) buttonEnable(false);
        else if (nominalPencairan <= maksimal) {
            if ((mFormPencairanResponse.getBalance() - nominalPencairan) < 1000000) {
                setTvError();
            } else if (nominalPencairan < mFormPencairanResponse.getMinimumTransaction()) {
                setTvMinTrx();
            } else {
                binding.tvError.setVisibility(View.GONE);
                buttonEnable(true);
            }
//            tvError.setVisibility(View.GONE);
//            buttonEnable(true);
        } else setTvErrorMax();
    }

    @Override
    public void setAmountListener() {

    }

    public void buttonEnable(boolean enabled) {
        if (enabled) {
            binding.btnSubmit.setAlpha(1);
            binding.btnSubmit.setEnabled(true);
        } else {
            binding.btnSubmit.setEnabled(false);
            binding.btnSubmit.setAlpha((float) 0.3);
        }
    }

    public ParameterKonfirmasiModel setParameterKonfirmasi() {
        ParameterKonfirmasiModel parameterKonfirmasiModel = new ParameterKonfirmasiModel();

        parameterKonfirmasiModel.setStringLabelTujuan("Nomor Tujuan");
        parameterKonfirmasiModel.setStringButtonSubmit("Cairkan");

        return parameterKonfirmasiModel;
    }


    @Override
    public void onSuccessGetInquiryPencairan(GeneralConfirmationResponse generalConfirmationResponse) {
        KonfirmasiGeneralActivity.launchIntent(this, generalConfirmationResponse, GeneralHelper.getString(R.string.url_pencairan_payment), "Konfirmasi", setParameterKonfirmasi(), false, false,false);
    }

    @Override
    protected void onActivityResult(int requestCode, int resultCode, @Nullable Intent data) {
        super.onActivityResult(requestCode, resultCode, data);
        if (requestCode == Constant.REQ_PAYMENT) {
            if (resultCode == RESULT_OK) {
                this.setResult(RESULT_OK, data);
            } else {
                this.setResult(RESULT_CANCELED, data);
            }
            this.finish();
        }
    }

    @Override
    protected void onDestroy() {
        super.onDestroy();
        binding = null;
    }
}