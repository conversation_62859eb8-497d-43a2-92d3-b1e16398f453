package id.co.bri.brimo.ui.fragments.penutupanrencana

import android.content.DialogInterface
import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.FrameLayout
import com.google.android.material.bottomsheet.BottomSheetBehavior
import com.google.android.material.bottomsheet.BottomSheetDialogFragment
import id.co.bri.brimo.R

class FragmentBottomSheetCloseRencana(
    private var customViedId: View,
    private var cancelable: Boolean = false,
    private var onTouchOutsideCancelable: Boolean = false,
    var onCollapse: () -> Unit
) : BottomSheetDialogFragment() {

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setStyle(STYLE_NORMAL, R.style.CustomBottomSheetDialogTheme)
    }

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View = customViedId.rootView

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)

        val bottomSheet = dialog?.findViewById(R.id.design_bottom_sheet) as FrameLayout
        val behavior = BottomSheetBehavior.from(bottomSheet)
        dialog?.apply {
            setOnShowListener { behavior.state = BottomSheetBehavior.STATE_EXPANDED }
            setCancelable(cancelable)
            setCanceledOnTouchOutside(onTouchOutsideCancelable)
        }
    }

    override fun onDismiss(dialog: DialogInterface) {
        super.onDismiss(dialog)
        onCollapse()
    }
}