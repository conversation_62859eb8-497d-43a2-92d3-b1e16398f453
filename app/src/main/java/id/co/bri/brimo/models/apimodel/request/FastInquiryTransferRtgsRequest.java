package id.co.bri.brimo.models.apimodel.request;

import com.google.gson.annotations.Expose;
import com.google.gson.annotations.SerializedName;

public class FastInquiryTransferRtgsRequest extends FastMenuRequest {
    @SerializedName("bank_code")
    @Expose
    private String bankCode;
    @SerializedName("bank_code_rtgs")
    @Expose
    private String bankCodeRtgs;
    @SerializedName("account")
    @Expose
    private String account;

    public FastInquiryTransferRtgsRequest(FastMenuRequest request, String bankCode, String account) {
        super(request.getUsername(), request.getTokenKey());
        this.bankCode = bankCode;
        this.account = account;
    }

    public String getBankCode() {
        return bankCode;
    }

    public void setBankCode(String bankCode) {
        this.bankCode = bankCode;
    }


    public String getAccount() {
        return account;
    }

    public void setAccount(String account) {
        this.account = account;
    }
}

