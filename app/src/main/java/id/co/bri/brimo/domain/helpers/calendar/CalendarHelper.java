package id.co.bri.brimo.domain.helpers.calendar;


import android.annotation.SuppressLint;
import android.os.Build;
import android.util.Log;

import androidx.annotation.RequiresApi;

import org.jetbrains.annotations.NotNull;

import id.co.bri.brimo.domain.config.Constant;
import id.co.bri.brimo.domain.helpers.GeneralHelper;
import id.co.bri.brimo.models.DurasiModel;
import id.co.bri.brimo.models.MonthModel;
import kotlin.jvm.internal.Intrinsics;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.YearMonth;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.List;
import java.util.Locale;
import java.util.Objects;
import java.util.TimeZone;


public class CalendarHelper {

    private static final String TAG = "CalendarHelper";
    static long now = System.currentTimeMillis();
    static String DATE_FORMAT_DETAIL = "EEE MMM dd hh:mm:ss 'GMT'Z yyyy";
    static String DATE_FORMAT_RECEIPT = "yyyyMMdd-hhmmss";
    public static String[] dayBahasa = new String[]{
            "Senin", "Selasa", "Rabu", "Kamis", "Jumat", "Sabtu", "Minggu"
    };
    static String DATE_FORMAT_EDIT_PFM = "EEEE, dd MMMM yyyy";
    static String DATE_FORMAT_TO_BACKEND = "yyyy-MM-dd";

    static String DATE_FORMAT_MONTH_YEAR = "MMM yyyy";

    static String DATE_TIME_FORMAT_TRAVEL = "dd MMM yyyy, HH:mm";

    static String MONTH_YEAR_TIME_FORMAT_TRAVEL = "dd MM, HH:mm";

    public static class TypeFilterPeriode {
        public static final String MONTH_MONTHLY = "MONTH_MONTHLY";
        public static final String YEAR_MONTHLY = "YEAR_MONTHLY";
        public static final String START_DATE = "START_DATE";
        public static final String END_DATE = "END_DATE";
    }

    public static int getDaysInMonth(int year, int month) {

        switch (month - 1) {
            case Calendar.JANUARY:
            case Calendar.MARCH:
            case Calendar.MAY:
            case Calendar.JULY:
            case Calendar.AUGUST:
            case Calendar.OCTOBER:
            case Calendar.DECEMBER:
                return 31;

            case Calendar.APRIL:
            case Calendar.JUNE:
            case Calendar.SEPTEMBER:
            case Calendar.NOVEMBER:
                return 30;

            // 2
            case Calendar.FEBRUARY:
                return ((year % 4 == 0 && year % 100 != 0) || (year % 400 == 0)) ? 29 : 28;

            default:
                throw new IllegalArgumentException("Invalid Month");
        }
    }

    // get Durasi HARI INI
    public static DurasiModel getDurasiHariIni() {
        DurasiModel durasi = new DurasiModel();
        Calendar calendarToday = Calendar.getInstance();

        // hari ini
        durasi.setStartDay(calendarToday.get(Calendar.DAY_OF_MONTH));
        durasi.setStartMonth(calendarToday.get(Calendar.MONTH) + 1);
        durasi.setStartYear(calendarToday.get(Calendar.YEAR));

        //last day hari ini
        durasi.setEndDay(calendarToday.get(Calendar.DAY_OF_MONTH));
        durasi.setEndMonth(calendarToday.get(Calendar.MONTH) + 1);
        durasi.setEndYear(calendarToday.get(Calendar.YEAR));

        return durasi;
    }

    // get Durasi HARI INI
    public static DurasiModel getStartHariIni() {
        DurasiModel durasi = new DurasiModel();
        Calendar calendarToday = Calendar.getInstance();

        // hari ini
        durasi.setStartDay(calendarToday.get(Calendar.DAY_OF_MONTH));
        durasi.setStartMonth(calendarToday.get(Calendar.MONTH) + 1);
        durasi.setStartYear(calendarToday.get(Calendar.YEAR));

        //last day hari ini
        durasi.setEndDay(0);
        durasi.setEndMonth(0);
        durasi.setEndYear(0);

        return durasi;
    }

    // get Durasi KEMARIN
    public static DurasiModel getDurasiKemarin() {
        DurasiModel durasi = new DurasiModel();

        Calendar cal = Calendar.getInstance();
        Calendar calendarToday = Calendar.getInstance();

        cal.add(Calendar.DATE, -1);

        // kemarin
        durasi.setStartDay(cal.get(Calendar.DAY_OF_MONTH));
        durasi.setStartMonth(cal.get(Calendar.MONTH) + 1);
        durasi.setStartYear(cal.get(Calendar.YEAR));

        //last day hari ini
        durasi.setEndDay(cal.get(Calendar.DAY_OF_MONTH));
        durasi.setEndMonth(cal.get(Calendar.MONTH) + 1);
        durasi.setEndYear(cal.get(Calendar.YEAR));

        return durasi;
    }

    // get Durasi MINGGU INI
    public static DurasiModel getDurasiMingguIni() {
        DurasiModel durasi = new DurasiModel();

        Calendar c1 = Calendar.getInstance();
        Calendar calendarToday = Calendar.getInstance();

        //first day of week
        if (calendarToday.get(Calendar.DAY_OF_WEEK) == Calendar.SUNDAY) {
            c1.add(Calendar.DATE, -6);
        } else {
            c1.set(Calendar.DAY_OF_WEEK, Calendar.MONDAY);
        }

        durasi.setStartDay(c1.get(Calendar.DAY_OF_MONTH));
        durasi.setStartMonth(c1.get(Calendar.MONTH) + 1);
        durasi.setStartYear(c1.get(Calendar.YEAR));

        //last day of week
        //c1.set(Calendar.DAY_OF_WEEK, Calendar.SATURDAY);

        durasi.setEndDay(calendarToday.get(Calendar.DAY_OF_MONTH));
        durasi.setEndMonth(calendarToday.get(Calendar.MONTH) + 1);
        durasi.setEndYear(calendarToday.get(Calendar.YEAR));

        return durasi;
    }

    // get Durasi BULAN INI
    public static DurasiModel getDurasiBulanIni() {
        DurasiModel durasi = new DurasiModel();

        Calendar c2 = Calendar.getInstance();
        Calendar calendarToday = Calendar.getInstance();
        //first day of week
        c2.set(Calendar.DAY_OF_MONTH, 1);

        durasi.setStartDay(c2.get(Calendar.DAY_OF_MONTH));
        durasi.setStartMonth(c2.get(Calendar.MONTH) + 1);
        durasi.setStartYear(c2.get(Calendar.YEAR));

        //last day of week
        //c2.set(Calendar.DAY_OF_MONTH, c2.getActualMaximum(Calendar.DAY_OF_MONTH));

        durasi.setEndDay(calendarToday.get(Calendar.DAY_OF_MONTH));
        durasi.setEndMonth(calendarToday.get(Calendar.MONTH) + 1);
        durasi.setEndYear(calendarToday.get(Calendar.YEAR));

        return durasi;
    }

    // get Durasi TAHUN INI
    public static DurasiModel getDurasiTahunIni() {
        DurasiModel durasi = new DurasiModel();

        Calendar c3 = Calendar.getInstance();
        Calendar calendarToday = Calendar.getInstance();
        //first day of week
        c3.set(Calendar.DAY_OF_YEAR, 1);

        durasi.setStartDay(c3.get(Calendar.DAY_OF_MONTH));
        durasi.setStartMonth(c3.get(Calendar.MONTH) + 1);
        durasi.setStartYear(c3.get(Calendar.YEAR));

        //last day of week
        //c3.set(Calendar.DAY_OF_MONTH, c3.getActualMaximum(Calendar.DAY_OF_YEAR));

        durasi.setEndDay(calendarToday.get(Calendar.DAY_OF_MONTH));
        durasi.setEndMonth(calendarToday.get(Calendar.MONTH) + 1);
        durasi.setEndYear(calendarToday.get(Calendar.YEAR));

        return durasi;
    }

    public static String formatNum(int key) {
        String formatted = key + "";
        if (formatted.length() != 2) {
            formatted = "0" + key;
        }
        return formatted;
    }


    public static int getWeekCount(int yyyy, int month) {
        int maxWeeknumber = 0;
        Calendar calendarToday = Calendar.getInstance();

        calendarToday.set(Calendar.YEAR, yyyy);
        calendarToday.set(Calendar.DAY_OF_MONTH, 1);
        calendarToday.set(Calendar.MONTH, month - 1);
        maxWeeknumber = calendarToday.getActualMaximum(Calendar.WEEK_OF_MONTH);
        return maxWeeknumber;
    }

    public static int getFirstWeekDay(int yyyy, int month) {
        // [1 : Sunday] ~ [7 : Saturday]
        Calendar calendarToday = Calendar.getInstance();
        calendarToday.set(yyyy, month - 1, 1);
        int firstDayOfWeek = calendarToday.get(Calendar.DAY_OF_WEEK);
        return firstDayOfWeek;
    }

    public static String getFormattedForCal(String date) {
        String result = date;
        if (date.length() != 2)
            return "0" + date;
        else
            return result;
    }

    public static long calculatePeriod(String beginDate, String endDate) {
        long result = 0;

        try {
            SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
            Date formattedBegin = sdf.parse(beginDate);
            Date formattedEnd = sdf.parse(endDate);

            long diff = formattedEnd.getTime() - formattedBegin.getTime();
            result = diff / (24 * 60 * 60 * 1000);

        } catch (ParseException e) {

        }
        return result;
    }

    public static Calendar stringToCalendar(String calendarString) {
        SimpleDateFormat format = new SimpleDateFormat(Constant.DB_DATE_FORMAT);
        Calendar calendarToday = Calendar.getInstance();

        try {
            Date date = format.parse(calendarString);
            calendarToday.setTime(date);
        } catch (Exception e) {

            return null;
        }
        return calendarToday;
    }

    /*
     *
     *  Fungsi fungsi per Date
     *
     *
     */
    public static String dateToPfmDate(Date date) {
        @SuppressLint("SimpleDateFormat") SimpleDateFormat format = new SimpleDateFormat(Constant.PFM_DATE_FORMAT);
        try {
            String dateTime = format.format(date);
            return dateTime;
        } catch (Exception e) {

            return null;
        }
    }

    public static String stringToDateddMMMyyyy(String stringDate) {
        @SuppressLint("SimpleDateFormat") SimpleDateFormat format = new SimpleDateFormat(Constant.PFM_DATE_FORMAT);
        try {
            String dateTime = format.format(stringDate);
            return dateTime;
        } catch (Exception e) {

            return null;
        }
    }

    public static String stringToPfmDate(String stringDate) {
        @SuppressLint("SimpleDateFormat") SimpleDateFormat oldFormat = new SimpleDateFormat(Constant.DB_DATE_FORMAT);
        String dateResult = "";
        try {
            Date dateOld = oldFormat.parse(stringDate);
            dateResult = dateToPfmDate(dateOld);
        } catch (Exception e) {

        }

        return dateResult;


    }

    public static String stringDateyyyyMMdd(Date date) {
        @SuppressLint("SimpleDateFormat") SimpleDateFormat format = new SimpleDateFormat(Constant.DB_DATE_FORMAT);
        try {
            String dateTime = format.format(date);
            return dateTime;
        } catch (Exception e) {

            return null;
        }
    }

    public static String stringDateddMMyyyy(String stringDate) {
        @SuppressLint("SimpleDateFormat") SimpleDateFormat oldFormat = new SimpleDateFormat(Constant.DATE_FORMAT);
        String dateResult = "";
        try {
            Date dateOld = oldFormat.parse(stringDate);
            dateResult = stringDateyyyyMMdd(dateOld);
        } catch (Exception e) {

        }

        return dateResult;
    }

    public static String stringDateFormatddMMyyyy(String stringDate) {
        @SuppressLint("SimpleDateFormat") SimpleDateFormat oldFormat = new SimpleDateFormat(Constant.DATE_FORMAT);
        String dateResult = "";
        try {
            Date dateOld = oldFormat.parse(stringDate);
            dateResult = dateFormat(dateOld);
        } catch (Exception e) {

        }

        return dateResult;
    }

    public static String stringDateFormatddMMMyyyy(String stringDate) {
        @SuppressLint("SimpleDateFormat") SimpleDateFormat oldFormat = new SimpleDateFormat(Constant.DATE_FORMAT);
        String dateResult = "";
        try {
            Date dateOld = oldFormat.parse(stringDate);
            dateResult = dateToPfmDate(dateOld);
        } catch (Exception e) {

        }

        return dateResult;
    }

    public static String stringDateFormatddMMyyyySlash(String stringDate) {
        SimpleDateFormat oldFormat = new SimpleDateFormat(Constant.DATE_FORMAT_SLASH);
        String dateResult = "";
        try {
            Date dateOld = oldFormat.parse(stringDate);
            dateResult = dateFormatSlash(dateOld);
        } catch (Exception e) {

        }

        return dateResult;
    }

    public static String stringDateFormatSpaceToSlash(String stringDate) {
        SimpleDateFormat oldFormat = new SimpleDateFormat(Constant.DATE_FORMAT);
        String dateResult = "";
        try {
            Date dateOld = oldFormat.parse(stringDate);
            dateResult = dateFormatSlash(dateOld);
        } catch (Exception e) {

        }

        return dateResult;
    }

    public static String dateFormatSlash(Date date) {
        SimpleDateFormat format = new SimpleDateFormat(Constant.DATE_FORMAT_SLASH);
        try {
            String dateTime = format.format(date);
            return dateTime;
        } catch (Exception e) {

            return null;
        }
    }

    public static String dateFormatDash(Date date) {
        SimpleDateFormat format = new SimpleDateFormat(Constant.DATE_FORMAT_DASH);
        try {
            String dateTime = format.format(date);
            return dateTime;
        } catch (Exception e) {

            return null;
        }
    }

    public static String stringDateFormatddMMyyyyDash(String stringDate) {
        SimpleDateFormat oldFormat = new SimpleDateFormat(Constant.DATE_FORMAT);
        String dateResult = "";
        try {
            Date dateOld = oldFormat.parse(stringDate);
            dateResult = dateFormatDash(dateOld);
        } catch (Exception e) {

        }

        return dateResult;
    }

    public static String stringDateFormatddMMyyToddMMyyyyDash(String stringDate) {
        SimpleDateFormat outputDateFormat = new SimpleDateFormat(Constant.DATE_FORMATDDMMYY, Locale.getDefault());
        String dateResult = "";
        try {
            Date dateOld = outputDateFormat.parse(validateDateBorn(stringDate));
            dateResult = dateFormatDash(dateOld);
        } catch (Exception e) {

        }

        return dateResult;
    }

    public static String dateFormat(Date date) {
        SimpleDateFormat format = new SimpleDateFormat(Constant.DATE_FORMAT);
        try {
            String dateTime = format.format(date);
            return dateTime;
        } catch (Exception e) {

            return null;
        }
    }

    public static Date stringToDate(String stringDate) {
        SimpleDateFormat oldFormat = new SimpleDateFormat(Constant.DB_DATE_FORMAT);
        Date dateResult = null;
        try {
            dateResult = oldFormat.parse(stringDate);
        } catch (Exception e) {

        }

        return dateResult;

    }

    public static Date stringToDateMutation(String stringDate) {
        SimpleDateFormat oldFormat = new SimpleDateFormat(Constant.DB_DATE_FORMAT);
        Date dateResult = null;
        try {
            dateResult = oldFormat.parse(stringDate);
        } catch (Exception e) {

        }

        return dateResult;
    }

    public static String getDate() {
        String result = "";
        Calendar calendar = Calendar.getInstance(TimeZone.getDefault());
        int thisDate = calendar.get(Calendar.DATE);
        try {
            result = String.valueOf(thisDate);
        } catch (Exception e) {

        }

        return result;
    }

    public static String getCurrentMonth() {
        String result = "";
        Calendar calendar = Calendar.getInstance(TimeZone.getDefault());
        int thisMonth = calendar.get(Calendar.MONTH) + 1;
        try {
            result = String.valueOf(thisMonth);
        } catch (Exception e) {

        }

        return result;
    }

    public static String getAfterMonth() {
        String result = "";
        Calendar calendar = Calendar.getInstance(TimeZone.getDefault());
        int thisMonth = calendar.get(Calendar.MONTH) + 2;
        try {
            result = String.valueOf(thisMonth);
        } catch (Exception e) {

        }

        return result;
    }

    public static String getLastMonth() {
        String result = "";
        Calendar calendar = Calendar.getInstance(TimeZone.getDefault());
        int thisMonth = calendar.get(Calendar.MONTH);
        try {
            result = String.valueOf(thisMonth);
        } catch (Exception e) {

        }

        return result;
    }

    public static String getLast3Month() {
        String result = "";
        Calendar calendar = Calendar.getInstance(TimeZone.getDefault());
        int thisMonth = calendar.get(Calendar.MONTH) - 1;
        try {
            result = String.valueOf(thisMonth);
        } catch (Exception e) {

        }

        return result;
    }

    public static String getLast4month() {
        String result = "";
        Calendar calendar = Calendar.getInstance(TimeZone.getDefault());
        int thisMonth = calendar.get(Calendar.MONTH) - 2;
        try {
            result = String.valueOf(thisMonth);
        } catch (Exception e) {

        }

        return result;
    }

    public static String getCurrentYear() {
        String result = "";
        Calendar calendar = Calendar.getInstance(TimeZone.getDefault());
        int thisMonth = calendar.get(Calendar.YEAR);
        try {
            result = String.valueOf(thisMonth);
        } catch (Exception e) {

        }

        return result;
    }

    public static Date getCurrentDate() {
        Date result = null;
        Calendar cal = Calendar.getInstance();

        result = cal.getTime();

        return result;
    }

    public static String getCurrentDateString() {
        String formattedDate = "";
        Date c = Calendar.getInstance().getTime();

        SimpleDateFormat df = new SimpleDateFormat(Constant.DB_DATE_FORMAT, Locale.getDefault());
        formattedDate = df.format(c);

        return formattedDate;
    }

    public static String getCurrentTimeReceipt() {
        String formattedDate = "";
        Date c = Calendar.getInstance().getTime();

        SimpleDateFormat df = new SimpleDateFormat(DATE_FORMAT_RECEIPT, Locale.getDefault());
        formattedDate = df.format(c);

        return formattedDate;
    }

    public static String convertMont(String oldMonth) {
        String month = "";
        int bulan = Integer.parseInt(oldMonth);

        switch (oldMonth) {
            case "01":
                month = "Januari";
                break;
            case "02":
                month = "Februari";
                break;
            case "03":
                month = "Maret";
                break;
            case "04":
                month = "April";
                break;
            case "05":
                month = "Mei";
                break;
            case "06":
                month = "Juni";
                break;
            case "07":
                month = "Juli";
                break;
            case "08":
                month = "Agustus";
                break;
            case "09":
                month = "September";
                break;
            case "10":
                month = "Oktober";
                break;
            case "11":
                month = "November";
                break;
            case "12":
                month = "Desember";
                break;
        }
        return month;
    }

    public static String convertMonthInt(String oldMonth) {
        String month = "";

        switch (oldMonth) {
            case "Januari":
                month = "1";
                break;
            case "Februari":
                month = "2";
                break;
            case "Maret":
                month = "3";
                break;
            case "April":
                month = "4";
                break;
            case "Mei":
                month = "5";
                break;
            case "Juni":
                month = "6";
                break;
            case "Juli":
                month = "7";
                break;
            case "Agustus":
                month = "8";
                break;
            case "September":
                month = "9";
                break;
            case "Oktober":
                month = "10";
                break;
            case "November":
                month = "11";
                break;
            case "Desember":
                month = "12";
                break;
        }
        return month;
    }

    //konversi bulan ke tahun+bulan
    public static String konversiBulan(int month) {
        String konversi;
        int tahun = month / 12;
        int sisaBulan = month % 12;
        if (tahun == 0) konversi = String.format("(%d Bulan)", sisaBulan);
        else if (sisaBulan == 0) konversi = String.format("(%d Tahun)", tahun);
        else konversi = String.format("(%d Tahun %d Bulan)", tahun, sisaBulan);
        return konversi;
    }

    public static boolean isToday(String date) {
        boolean isToday = false;
        SimpleDateFormat sdf = new SimpleDateFormat(Constant.DB_DATE_FORMAT);
        Date strDate = null;
        try {
            strDate = sdf.parse(date);
        } catch (ParseException e) {
            Log.e(TAG, "isToday: ", e);
            return isToday;
        }
        isToday = System.currentTimeMillis() == strDate.getTime();

        return isToday;
    }

    public static String getCurrentTime() {

        SimpleDateFormat sdf = new SimpleDateFormat("HH:mm:ss");
        String currentTime = sdf.format(new Date());

        return currentTime;
    }

    public static String getDateNowFormatRange() {
        SimpleDateFormat formatter = new SimpleDateFormat("yyyyMMdd");
        Date date = new Date(System.currentTimeMillis());
        String sDate = formatter.format(date);

        return sDate;
    }

    public static String getDateNow() {
        SimpleDateFormat formatter = new SimpleDateFormat("yyyy-MM-dd");
        Date date = new Date(System.currentTimeMillis());
        String sDate = formatter.format(date);

        return sDate;
    }

    public static String getFullDateNow() {
        String maxDate = getDateNow();
        SimpleDateFormat now = new SimpleDateFormat("dd MMM yyyy");
        Calendar cal = Calendar.getInstance();
        try {
            cal.setTime(now.parse(maxDate));
        } catch (ParseException e) {
            if (!GeneralHelper.isProd()) {
                Log.e(TAG, "getFullDateNow: ", e);
            }
        }
        String currentDate = now.format(cal.getTime());
        return currentDate;
    }

    public static String getFullDateNow(Locale locale) {
        String maxDate = getDateNow();
        SimpleDateFormat now = new SimpleDateFormat("dd MMM yyyy", locale);
        Calendar cal = Calendar.getInstance();
        try {
            cal.setTime(now.parse(maxDate));
        } catch (ParseException e) {
            if (!GeneralHelper.isProd()) {
                Log.e(TAG, "getFullDateNow: ", e);
            }
        }
        String currentDate = now.format(cal.getTime());
        return currentDate;
    }

    public static String getNameOfCurrentMonthYear() {
        String maxDate = getCurrentMonth();
        SimpleDateFormat monthDate = new SimpleDateFormat("MMMM yyyy");
        Calendar cal = Calendar.getInstance();
        try {
            cal.setTime(monthDate.parse(maxDate));
        } catch (ParseException e) {
            if (!GeneralHelper.isProd()) {
                Log.e(TAG, "getFullDateNow: ", e);
            }
        }
        String monthName = monthDate.format(cal.getTime());
        return monthName;
    }

    public static String getNameOfCurrentMonth() {
        String maxDate = getCurrentMonth();
        SimpleDateFormat monthDate = new SimpleDateFormat("MMMM");
        Calendar cal = Calendar.getInstance();
        try {
            cal.setTime(monthDate.parse(maxDate));
        } catch (ParseException e) {
            if (!GeneralHelper.isProd()) {
                Log.e(TAG, "getFullDateNow: ", e);
            }
        }
        String monthName = monthDate.format(cal.getTime());
        return monthName;
    }

    public static String convertToddMMyy(Date date) {
        SimpleDateFormat format = new SimpleDateFormat(Constant.DATE_FORMAT_YEAR);
        try {
            String dateTime = format.format(date);
            return dateTime;
        } catch (Exception e) {

            return null;
        }
    }

    public static String dateFormatyyyyMMdd(String stringDate) {
        SimpleDateFormat oldFormat = new SimpleDateFormat("yyyyMMdd");
        String dateResult = "";
        try {
            Date dateOld = oldFormat.parse(stringDate);
            dateResult = convertToddMMyy(dateOld);
        } catch (Exception e) {
            if (!GeneralHelper.isProd()) {
                Log.e(TAG, "getFullDateNow: ", e);
            }
        }

        return dateResult;
    }

    public static String convertToNewFormat(String stringDate) {
        SimpleDateFormat oldFormat = new SimpleDateFormat("yyyyMMdd");
        String dateResult = "";
        try {
            Date dateOld = oldFormat.parse(stringDate);
            dateResult = stringDateyyyyMMdd(dateOld);
        } catch (Exception e) {
            if (!GeneralHelper.isProd()) {
                Log.e(TAG, "getFullDateNow: ", e);
            }
        }

        return dateResult;
    }

    public static String convertToStringFormat(String stringDate) {
        SimpleDateFormat oldFormat = new SimpleDateFormat("dd MMMM yyyy");
        String dateResult = "";
        try {
            Date dateOld = oldFormat.parse(stringDate);
            dateResult = stringDateyyyyMMdd(dateOld);
        } catch (Exception e) {
            if (!GeneralHelper.isProd()) {
                Log.e(TAG, "getFullDateNow: ", e);
            }
        }

        return dateResult;
    }

    public static String parseDateToyyyyMMdd(String dateInput) {
        // Define input and output date formats
        SimpleDateFormat inputFormat = new SimpleDateFormat(DATE_FORMAT_EDIT_PFM, new Locale("id", "ID")); // Indonesian Locale
        SimpleDateFormat outputFormat = new SimpleDateFormat(DATE_FORMAT_TO_BACKEND, Locale.US);

        try {
            // Parse the input date string
            Date date = inputFormat.parse(dateInput);

            // Format the parsed date to the desired output format
            return outputFormat.format(date);
        } catch (ParseException e) {
            if (!GeneralHelper.isProd()) {
                Log.e(TAG, "parseDateToyyyyMMdd: ", e);
            }
            return null; // Handle parsing errors here
        }
    }

    public static String parseDateToddMMyyyy(String dateInput) {
        Date date;
        String str = null;
        try {
            String inputDate = "dd MMMM yyyy";
            String outputDate = "EEEE, dd MMM yyyy";
            SimpleDateFormat inputFormat = new SimpleDateFormat(inputDate);
            SimpleDateFormat outputFormat = new SimpleDateFormat(outputDate);

            date = inputFormat.parse(dateInput);
            str = outputFormat.format(date);
        } catch (ParseException e) {
            if (!GeneralHelper.isProd()) {
                Log.e(TAG, "parseDateToddMMyyyy: ", e);
            }
        }
        return str;
    }

    @SuppressLint("SimpleDateFormat")
    public static String convertLongToTime(long time) {
        Date date = new Date(time);
        TimeZone utc = TimeZone.getTimeZone("UTC");
        SimpleDateFormat format = new SimpleDateFormat("HH:mm");
        format.setTimeZone(utc);
        return format.format(date);
    }

    @SuppressLint("SimpleDateFormat")
    public static String convertLongToDate(Long time) {
        Date date = new Date(time);
        TimeZone utc = TimeZone.getTimeZone("UTC");
        SimpleDateFormat format = new SimpleDateFormat("dd MMMM yyyy");
        format.setTimeZone(utc);
        return format.format(date);
    }

    @SuppressLint("SimpleDateFormat")
    public static String stringDateTimeToddMMyyyy(String dateTime) {
        Date date;
        String stringDate = null;
        try {
            String inputDate = "yyyy-MM-dd'T'HH:mm:ss'Z'";
            String outputDate = "dd MMM yyyy";
            SimpleDateFormat inputFormat = new SimpleDateFormat(inputDate);
            SimpleDateFormat outputFormat = new SimpleDateFormat(outputDate);

            date = inputFormat.parse(dateTime);
            stringDate = outputFormat.format(date);
        } catch (ParseException e) {
            if (!GeneralHelper.isProd()) {
                Log.e(TAG, "parseDateToddMMyyyy: ", e);
            }
        }

        return stringDate;
    }

    @SuppressLint("SimpleDateFormat")
    public static boolean compareDate(String dateNow, String dateAfter) {
        SimpleDateFormat formatter = new SimpleDateFormat("dd MMM yyyy");

        boolean dateCompare = false;

        //compare if dateNow before dateAfter return true
        try {
            if (Objects.requireNonNull(formatter.parse(dateNow)).before(formatter.parse(dateAfter))) {
                dateCompare = true;
            } else if (Objects.requireNonNull(formatter.parse(dateNow)).after(formatter.parse(dateAfter))) {
                dateCompare = true;
            }
        } catch (ParseException e) {
            Log.e(TAG, "compareDate: ", e);
        }

        return dateCompare;
    }


    public static String autoFillDateBirth(String autoFillDate) {
        SimpleDateFormat inputDateFormat = new SimpleDateFormat("ddMMyy", Locale.getDefault());
        String tglLahir = null;
        try {
            Date inputDate = inputDateFormat.parse(validateDateBorn(autoFillDate));
            SimpleDateFormat outputDateFormat = new SimpleDateFormat(Constant.DATE_FORMAT_DASH, Locale.getDefault());
            String bornDate = outputDateFormat.format(Objects.requireNonNull(inputDate));


            if (isAgeGreaterThan17(bornDate)) {
                tglLahir = bornDate.substring(0, 2) + " " +
                        CalendarHelper.convertMont(bornDate.substring(3, 5)) + " " +
                        bornDate.substring(6);
            } else tglLahir = "";
        } catch (ParseException e) {
            if (!GeneralHelper.isProd()) {
                Log.e(TAG, "autoFillDateBirth: ", e);
            }
        }

        return tglLahir;
    }

    /**
     * Get the list of months from the current month to the previous 12 months
     *
     * @param year  The year to get the months from
     * @param month The current month
     * @param day   The current day
     * @return
     */
    public static boolean isValidDate(int year, int month, int day) {
        Calendar calendar = Calendar.getInstance();
        calendar.set(Calendar.YEAR, year);
        calendar.set(Calendar.MONTH, month - 1); // Calendar months are 0-based
        int maxDay = calendar.getActualMaximum(Calendar.DAY_OF_MONTH);
        return day >= 1 && day <= maxDay;
    }

    public static String validateDateBorn(String autoFillDate) {
        String autoDate;

        int year = Integer.parseInt(autoFillDate.substring(4));
        if (year >= 0 && year <= 21) { // Assuming year 0-21 corresponds to 2000-2021
            year += 2000;
        } else if (year >= 22 && year <= 99) { // Assuming year 22-99 corresponds to 1922-1999
            year += 1900;
        }

        // Checking if it's a leap year
        boolean isLeapYear = (year % 4 == 0) && (year % 100 != 0 || year % 400 == 0);
        // Extracting day and month from the provided date string
        int day = Integer.parseInt(autoFillDate.substring(0, 2));
        int month = Integer.parseInt(autoFillDate.substring(2, 4));
        String dayString;

        if (day >= 41 && day <= 71) {
            int adjustedDay = day - 40;
            dayString = (adjustedDay < 10) ? "0" + adjustedDay : String.valueOf(adjustedDay);
        } else dayString = String.valueOf(day);

        // Validating day and month
        if (Integer.parseInt(dayString) < 1 || month < 1 || month > 12) {
            return ""; // Invalid date, return empty string
        }

        // Validating day according to the month
        int maxDaysInMonth;
        switch (month) {
            case 2: // February
                maxDaysInMonth = isLeapYear ? 29 : 28;
                break;
            case 4: // April
            case 6: // June
            case 9: // September
            case 11: // November
                maxDaysInMonth = 30;
                break;
            default:
                maxDaysInMonth = 31;
                break;
        }

        if (Integer.parseInt(dayString) > maxDaysInMonth) {
            return ""; // Invalid date, return empty string
        }

        // Proceeding with other conditions
        if (!isLeapYear && month == 2) {
            autoDate = "";
        } else if (day >= 41 && day <= 71) {
            autoDate = dayString + autoFillDate.substring(2);
        } else if (!autoFillDate.equals("000000")) {
            autoDate = autoFillDate;
        } else {
            autoDate = "";
        }
        return autoDate;
    }

    public static boolean isAgeGreaterThan17(String borndate) {
        SimpleDateFormat dateFormat = new SimpleDateFormat(Constant.DATE_FORMAT_DASH, Locale.getDefault());
        Calendar calendar = Calendar.getInstance();
        int currentYear = calendar.get(Calendar.YEAR);
        int currentMonth = calendar.get(Calendar.MONTH);
        int currentDay = calendar.get(Calendar.DAY_OF_MONTH);

        int minimumBirthYear = currentYear - 17;
        calendar.set(Calendar.YEAR, minimumBirthYear);
        calendar.add(Calendar.YEAR, -17);

        Date birthDate;
        try {
            birthDate = dateFormat.parse(borndate);
        } catch (ParseException e) {
            if (!GeneralHelper.isProd()) {
                Log.e(TAG, "isAgeGreaterThan17: ", e);
            }
            return false;
        }

        if (birthDate != null) {
            calendar.setTime(birthDate);
        }

        int birthYear = calendar.get(Calendar.YEAR);
        int birthMonth = calendar.get(Calendar.MONTH);
        int birthDay = calendar.get(Calendar.DAY_OF_MONTH);

        return birthYear < minimumBirthYear ||
                (birthYear == minimumBirthYear && birthMonth < currentMonth) ||
                (birthYear == minimumBirthYear && birthMonth == currentMonth && birthDay <= currentDay);

    }

    @SuppressLint("SimpleDateFormat")
    public static String getLastDateOfMonth() {
        Date today = new Date();

        Calendar calendar = Calendar.getInstance();
        calendar.setTime(today);

        calendar.add(Calendar.MONTH, 1);
        calendar.set(Calendar.DAY_OF_MONTH, 1);
        calendar.add(Calendar.DATE, -1);

        Date lastDayOfMonth = calendar.getTime();

        SimpleDateFormat sdf = new SimpleDateFormat("dd");
        return sdf.format(lastDayOfMonth);
    }

    @SuppressLint("SimpleDateFormat")
    public static String getCurrentDateOnly() {
        String maxDate = getDateNow();
        SimpleDateFormat now = new SimpleDateFormat("dd");
        Calendar cal = Calendar.getInstance();
        try {
            cal.setTime(now.parse(maxDate));
        } catch (ParseException e) {
            if (!GeneralHelper.isProd()) {
                Log.e(TAG, "getFullDateNow: ", e);
            }
        }
        String currentDate = now.format(cal.getTime());
        return currentDate;
    }

    public static String getDateNowFormat(String format) {
        SimpleDateFormat formatter = new SimpleDateFormat(format);
        Date date = new Date(System.currentTimeMillis());
        String sDate = formatter.format(date);

        return sDate;
    }

    public static String changeFormatCalendar(String originalFormat, String targetFormat, String inputString) {
        // Input string with the original date format
        String outputDateString = "";
        // Define the original date format
        @SuppressLint("SimpleDateFormat") SimpleDateFormat originalFormatDate = new SimpleDateFormat(originalFormat);

        // Define the target date format
        @SuppressLint("SimpleDateFormat") SimpleDateFormat targetFormatDate = new SimpleDateFormat(targetFormat);

        try {
            // Parse the input string to a Date object
            Date date = originalFormatDate.parse(inputString);
            // Format the Date object to the target date format
            assert date != null;
            outputDateString = targetFormatDate.format(date);
        } catch (ParseException e) {

        }
        return outputDateString;
    }

    public static String getYearMonthNow() {
        SimpleDateFormat formatter = new SimpleDateFormat("yyyy-MM");
        Date date = new Date(System.currentTimeMillis());
        String sDate = formatter.format(date);

        return sDate;
    }

    @SuppressLint("SimpleDateFormat")
    public static String setMonthYear(String dateInput) throws ParseException {
        SimpleDateFormat inputFormat = new SimpleDateFormat("MM/yyyy");
        SimpleDateFormat outputFormat = new SimpleDateFormat(DATE_FORMAT_MONTH_YEAR);
        Date date = inputFormat.parse(dateInput);

        return outputFormat.format(Objects.requireNonNull(date));
    }

    @RequiresApi(api = Build.VERSION_CODES.N)
    @SuppressLint("SimpleDateFormat")
    public static String setDateTimeTravel(String dateInput) throws ParseException {
        SimpleDateFormat inputFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        SimpleDateFormat outputFormat;
        if (android.os.Build.VERSION.SDK_INT >= android.os.Build.VERSION_CODES.N) {
            outputFormat = new SimpleDateFormat(DATE_TIME_FORMAT_TRAVEL);
        } else {
            outputFormat = new SimpleDateFormat(MONTH_YEAR_TIME_FORMAT_TRAVEL);
        }
        Date date = inputFormat.parse(dateInput);

        return outputFormat.format(Objects.requireNonNull(date));
    }

    public static String parseDateToddMMyyyyFromyyyyMMdd(String dateInput) {
        Date date;
        String str = null;
        try {
            String inputDate = DATE_FORMAT_TO_BACKEND;
            String outputDate = "dd MMMM yyyy";
            SimpleDateFormat inputFormat = new SimpleDateFormat(inputDate);
            SimpleDateFormat outputFormat = new SimpleDateFormat(outputDate);

            date = inputFormat.parse(dateInput);
            str = outputFormat.format(date);
        } catch (ParseException e) {
            if (!GeneralHelper.isProd()) {
                Log.e(TAG, "parseDateToddMMyyyy: ", e);
            }
        }
        return str;
    }

}