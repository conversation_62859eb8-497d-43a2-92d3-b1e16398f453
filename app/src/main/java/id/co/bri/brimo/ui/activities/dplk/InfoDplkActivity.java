package id.co.bri.brimo.ui.activities.dplk;

import android.app.Activity;
import android.content.Intent;
import android.graphics.Color;
import android.os.Bundle;
import android.util.Log;
import android.view.View;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;
import androidx.swiperefreshlayout.widget.SwipeRefreshLayout;

import com.ethanhua.skeleton.Skeleton;
import com.ethanhua.skeleton.SkeletonScreen;

import java.util.ArrayList;
import java.util.Collections;
import java.util.Comparator;
import java.util.List;

import javax.inject.Inject;

import id.co.bri.brimo.R;
import id.co.bri.brimo.adapters.ListInfoDplkAdapter;
import id.co.bri.brimo.contract.IPresenter.dplk.IInfoDplkPresenter;
import id.co.bri.brimo.contract.IView.dplk.IInfoDplkView;
import id.co.bri.brimo.databinding.ActivityInfoDplkBinding;
import id.co.bri.brimo.domain.config.Constant;
import id.co.bri.brimo.domain.helpers.GeneralHelper;
import id.co.bri.brimo.domain.helpers.bubbleShowCaseView.BubbleShowCase;
import id.co.bri.brimo.domain.helpers.bubbleShowCaseView.BubbleShowCaseBuilder;
import id.co.bri.brimo.domain.helpers.bubbleShowCaseView.BubbleShowCaseListener;
import id.co.bri.brimo.models.ParameterModel;
import id.co.bri.brimo.models.apimodel.request.AccountDplkRequest;
import id.co.bri.brimo.models.apimodel.response.GeneralInquiryResponse;
import id.co.bri.brimo.models.apimodel.response.InfoDplkResponse;
import id.co.bri.brimo.models.apimodel.response.ListRekeningDplkResponse;
import id.co.bri.brimo.models.apimodel.response.dplk.DplkBoardingResponse;
import id.co.bri.brimo.models.apimodel.response.esbn.EsbnExceptionResponse;
import id.co.bri.brimo.ui.activities.FormDplkActivity;
import id.co.bri.brimo.ui.activities.InquiryGeneralCloseActivity;
import id.co.bri.brimo.ui.activities.InquiryGeneralOpenActivity;
import id.co.bri.brimo.ui.activities.InquiryPlnTokenActivity;
import id.co.bri.brimo.ui.activities.base.BaseActivity;
import id.co.bri.brimo.ui.activities.dplk.OnboardingDplkActivity;
import id.co.bri.brimo.ui.fragments.rdn.CustomBottomDialogFragment;

public class InfoDplkActivity extends BaseActivity implements IInfoDplkView, View.OnClickListener, ListInfoDplkAdapter.OnCLickTopup, SwipeRefreshLayout.OnRefreshListener, CustomBottomDialogFragment.DialogDefaultListener, ListInfoDplkAdapter.OnBubbleCaseShow {

    private ActivityInfoDplkBinding binding;

    @Inject
    IInfoDplkPresenter<IInfoDplkView> presenter;

    private static final String TAG = "InfoDplkActivity";
    private static String errorMessage = null;

    private double totalSaldo;
    int accSize = 0;
    int notfoundI = 0;
    protected boolean isLoading = false;
    protected ListInfoDplkAdapter listInfoDplkAdapter;
    private SkeletonScreen skeletonScreen;
    private SkeletonScreen skeletonTopUp;
    private SkeletonScreen skeletonOpen;
    private List<InfoDplkResponse.Dplk> infoDplkList = new ArrayList<>();
    protected BubbleShowCaseBuilder lihatProgress;
    protected static Boolean mIsFromOpen = false;
    protected static Boolean mIsFromTopUp = false;

    public static void LaunchIntent(Activity caller) {
        Intent intent = new Intent(caller, InfoDplkActivity.class);
        mIsFromTopUp = false;
        mIsFromOpen = false;
        caller.startActivityForResult(intent, Constant.REQ_PAYMENT);
    }

    public static void LaunchIntent(Activity caller, Boolean isFromOpen, Boolean isFromTopup) {
        Intent intent = new Intent(caller, InfoDplkActivity.class);
        mIsFromOpen = isFromOpen;
        mIsFromTopUp = isFromTopup;
        caller.startActivityForResult(intent, Constant.REQ_PAYMENT);
    }

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        binding = ActivityInfoDplkBinding.inflate(getLayoutInflater());
        setContentView(binding.getRoot());

        GeneralHelper.setToolbar(this, binding.toolbar.toolbar, GeneralHelper.getString(R.string.dplk_title_bar));
        if (mIsFromTopUp) {
            FormDplkActivity.launchIntent(this, false, true);
        }

        injectDependency();
        initiateList();

        if (mIsFromOpen) {
            presenter.getOnboarding();
        }
        binding.pbSaldo.setVisibility(View.VISIBLE);
        binding.lyOpen.setOnClickListener(this);
        binding.lyTopup.setOnClickListener(this);
        binding.swipeRefresh.setOnRefreshListener(this);
    }

    private void injectDependency() {
        getActivityComponent().inject(this);
        if (presenter != null) {
            presenter.setView(this);
            presenter.setUrlDplk(GeneralHelper.getString(R.string.url_info_dplk));
            presenter.setUrlAccount(GeneralHelper.getString(R.string.url_rekening_dplk));
            presenter.setInquiryUrl(GeneralHelper.getString(R.string.url_inquiry_dplk));
            presenter.setUrlOnboarding(GeneralHelper.getString(R.string.url_dplk_onboarding));
            presenter.getAccountRekening();
            presenter.start();
        }
    }

    private void initiateList() {
        binding.rvInfoDplk.setLayoutManager(new LinearLayoutManager(this, RecyclerView.VERTICAL, false));
        binding.rvInfoDplk.smoothScrollToPosition(0);
        binding.rvInfoDplk.setHasFixedSize(true);

        listInfoDplkAdapter = new ListInfoDplkAdapter(this, infoDplkList, null, this, this);
        binding.rvInfoDplk.setAdapter(listInfoDplkAdapter);
        listInfoDplkAdapter.notifyDataSetChanged();

        skeletonScreen = Skeleton.bind(binding.rvInfoDplk)
                .adapter(listInfoDplkAdapter)
                .shimmer(true)
                .angle(20)
                .frozen(false)
                .duration(1200)
                .load(R.layout.item_skeleton_info_dplk)
                .show();

        skeletonTopUp = Skeleton.bind(binding.lyTopup)
                .shimmer(true)
                .angle(20)
                .duration(1200)
                .load(R.layout.item_skeleton_single2)
                .show();

        skeletonOpen = Skeleton.bind(binding.lyOpen)
                .shimmer(true)
                .angle(20)
                .duration(1200)
                .load(R.layout.item_skeleton_single2)
                .show();
    }

    @Override
    public void onClick(View view) {
        switch (view.getId()) {
            case R.id.ly_topup:
                FormDplkActivity.launchIntent(this, false, false);
                break;
            case R.id.ly_open:
                //open DPLK onboarding
               // presenter.getOnboarding();
//                ListPilihBrifineRevampActivity.launchIntent(this);

                break;
        }
    }

    @Override
    public void SuccessGetAccountRek(List<ListRekeningDplkResponse.Account> accounts) {
        accSize = accounts.size();
        for (int i = 0; i < accSize; i++) {
            AccountDplkRequest accountDplkRequest = new AccountDplkRequest(accounts.get(i).getAccount());
            presenter.setInfoDplk(accountDplkRequest);
        }
    }

    @Override
    public void onSuccessGetListRekening(ListRekeningDplkResponse rekeningDplkResponse) {
        binding.tvTopup.setText(rekeningDplkResponse.getProduct().get(0).getTitle());
        binding.tvOpen.setText(rekeningDplkResponse.getProduct().get(1).getTitle());
        binding.lyTopup.setEnabled(rekeningDplkResponse.getProduct().get(0).getEnable());
        binding.lyOpen.setEnabled(rekeningDplkResponse.getProduct().get(1).getEnable());
        skeletonTopUp.hide();
        skeletonOpen.hide();
    }

    @Override
    public void onException12(String message) {
        Intent intentReturn = new Intent();
        intentReturn.putExtra(Constant.TAG_ERROR_MESSAGE, message);
        setResult(RESULT_CANCELED, intentReturn);
        finish();
    }

    @Override
    public void SuccessGetAccountDplk(InfoDplkResponse accountDplkList) {
        if (accountDplkList != null) {
            skeletonScreen.hide();
            binding.linearSaldo.setVisibility(View.VISIBLE);
            binding.linearDot.setVisibility(View.GONE);
            infoDplkList.addAll(accountDplkList.getDplkList());

            Collections.sort(infoDplkList, new Comparator<InfoDplkResponse.Dplk>() {
                @Override
                public int compare(InfoDplkResponse.Dplk lhs, InfoDplkResponse.Dplk rhs) {
                    return lhs.getPopUp().compareTo(rhs.getPopUp());
                }
            });
            Collections.reverse(infoDplkList);

            for (InfoDplkResponse.Dplk account : accountDplkList.getDplkList()) {
                if (account.getTotalBalance() != null)
                    totalSaldo = totalSaldo + account.getTotalBalance();
            }

            binding.tvTotalSaldoDplk.setText(GeneralHelper.formatNominal(totalSaldo));
            enableData();
        }
    }

    @Override
    public void showProgress() {
        isLoading = true;
        binding.swipeRefresh.setEnabled(false);
        binding.swipeRefresh.setRefreshing(false);
    }

    @Override
    public void notFoundDplk() {
        notfoundI = notfoundI + 1;
        if (notfoundI == accSize) {
            disableData();
        }
    }

    @Override
    public void onException01(ListRekeningDplkResponse rekeningDplkResponse) {
        disableData();
        binding.tvTopup.setText(rekeningDplkResponse.getProduct().get(0).getTitle());
        binding.tvOpen.setText(rekeningDplkResponse.getProduct().get(1).getTitle());
        binding.lyTopup.setEnabled(rekeningDplkResponse.getProduct().get(0).getEnable());
        binding.lyTopup.setEnabled(rekeningDplkResponse.getProduct().get(1).getEnable());
        skeletonTopUp.hide();
        skeletonOpen.hide();
    }

    @Override
    public void enableData() {
        isLoading = false;
        binding.swipeRefresh.setEnabled(true);
        binding.swipeRefresh.setRefreshing(false);
        binding.rvInfoDplk.setVisibility(View.VISIBLE);
        binding.rlNotFound.setVisibility(View.GONE);
    }

    @Override
    public void disableData() {
        isLoading = false;
        binding.swipeRefresh.setEnabled(false);
        binding.swipeRefresh.setRefreshing(false);
        binding.rvInfoDplk.setVisibility(View.GONE);
        binding.rlNotFound.setVisibility(View.VISIBLE);
        binding.linearDot.setVisibility(View.GONE);
        binding.linearSaldo.setVisibility(View.VISIBLE);
    }

    @Override
    public void onSuccessGetInquiry(GeneralInquiryResponse dplkInquiryResponse) {
        if (dplkInquiryResponse.getOpenPayment().equals(true)) {
            if (dplkInquiryResponse.getIsBilling().equals(true)) {
                InquiryGeneralCloseActivity.launchIntent(
                        this,
                        dplkInquiryResponse,
                        GeneralHelper.getString(R.string.url_confirmation_dplk),
                        GeneralHelper.getString(R.string.url_payment_dplk),
                        GeneralHelper.getString(R.string.dplk_topup_title_bar),
                        setParameter(), isFromFastMenu);
            } else {
                if (dplkInquiryResponse.getAmountOption() == null) {
                    InquiryGeneralOpenActivity.launchIntent(
                            this,
                            dplkInquiryResponse,
                            GeneralHelper.getString(R.string.url_confirmation_dplk),
                            GeneralHelper.getString(R.string.url_payment_dplk),
                            GeneralHelper.getString(R.string.dplk_topup_title_bar),
                            setParameter(), isFromFastMenu);
                } else {
                    InquiryPlnTokenActivity.launchIntent(
                            this,
                            dplkInquiryResponse,
                            GeneralHelper.getString(R.string.dplk_topup_title_bar),
                            GeneralHelper.getString(R.string.url_confirmation_dplk),
                            GeneralHelper.getString(R.string.url_payment_dplk),
                            setParameter(), isFromFastMenu);
                }
            }
        } else {
            InquiryGeneralCloseActivity.launchIntent(
                    this, dplkInquiryResponse,
                    GeneralHelper.getString(R.string.url_confirmation_dplk),
                    GeneralHelper.getString(R.string.url_payment_dplk),
                    GeneralHelper.getString(R.string.dplk_topup_title_bar),
                    setParameter(), isFromFastMenu);
        }
    }

    @Override
    public void onProgress() {
        GeneralHelper.showDialog(this);
    }

    @Override
    public void onHide() {
        GeneralHelper.dismissDialog();
    }

    @Override
    public void onException02(EsbnExceptionResponse response) {
        CustomBottomDialogFragment customBottomDialogFragment = new CustomBottomDialogFragment(
                this, response.getTitle(),
                response.getDescription(),
                GeneralHelper.getString(R.string.baiklah),
                response.getImageName(), response.getImagePath(), false,false, this);
        customBottomDialogFragment.show(getSupportFragmentManager(), customBottomDialogFragment.getTag());
    }

    @Override
    public void onSuccessOnBoarding(DplkBoardingResponse response) {
        OnboardingDplkActivity.launchIntent(this, response);
    }

    @Override
    protected void onActivityResult(int requestCode, int resultCode, @Nullable Intent data) {
        super.onActivityResult(requestCode, resultCode, data);
        if (requestCode == Constant.REQ_PAYMENT) {
            Log.d(TAG, "onActivityResult: masuk sini");
            if (resultCode == RESULT_OK) {
                //membalikan data agar otomatis cek saldo
                setResult(RESULT_OK, data);
                skeletonOpen.show();
                skeletonTopUp.show();
                listInfoDplkAdapter.setAttached(false);
                infoDplkList.clear();
                totalSaldo = 0;
                notfoundI = 0;
                binding.rvInfoDplk.setVisibility(View.VISIBLE);
                skeletonScreen.show();
                binding.linearSaldo.setVisibility(View.INVISIBLE);
                binding.pbSaldo.setVisibility(View.VISIBLE);
                binding.linearDot.setVisibility(View.VISIBLE);
                presenter.getAccountRekening();
            } else if (resultCode == RESULT_FIRST_USER) {
                finish();
            } else {

                if (data != null) {
                    errorMessage = data.getStringExtra(Constant.TAG_ERROR_MESSAGE);
                    if (errorMessage != null) {
                        onException(errorMessage);
                        errorMessage = null;
                    }
                }
            }

        }
    }


    @Override
    public void onTopup(String accountNumber) {
        presenter.getDataInquiry(accountNumber);
    }

    private ParameterModel setParameter() {
        ParameterModel parameterModel = new ParameterModel();

        parameterModel.setStringLabelTujuan(GeneralHelper.getString(R.string.destination_number_hint));
        parameterModel.setStringLabelNominal(GeneralHelper.getString(R.string.nominal_top_up));
        parameterModel.setStringButtonSubmit(GeneralHelper.getString(R.string.top_up));
        parameterModel.setStringLabelMinimum(GeneralHelper.getString(R.string.top_up));
        parameterModel.setDefaultIcon(getDefaultIconResource());

        return parameterModel;
    }

    private int getDefaultIconResource() {
        return R.drawable.ic_dplk;
    }

    @Override
    public void onRefresh() {
        if (!isLoading) {
            listInfoDplkAdapter.setAttached(false);
            infoDplkList.clear();
            totalSaldo = 0;
            notfoundI = 0;
            binding.rvInfoDplk.setVisibility(View.VISIBLE);
            skeletonScreen.show();
            binding.linearSaldo.setVisibility(View.INVISIBLE);
            binding.pbSaldo.setVisibility(View.VISIBLE);
            binding.linearDot.setVisibility(View.VISIBLE);
            presenter.getAccountRekening();

        }
    }

    @Override
    public void onClickDialog() {
        finish();
    }

    protected void bubbleShowDplk(View view, InfoDplkResponse.Dplk dplk) {
        try {

            String title = GeneralHelper.getString(R.string.brifine_baru);
            String desc = GeneralHelper.getString(R.string.desc_brifine_baru);

            lihatProgress = new BubbleShowCaseBuilder(this) //Activity instance
                    .title(title) //Any title for the bubble view
                    .description(desc)
                    .arrowPosition(BubbleShowCase.ArrowPosition.TOP)
                    .backgroundColor(Color.WHITE)
                    .textColor(Color.BLACK)
                    .titleTextSize(16) //Title text size in SP (default value 16sp)
                    .descriptionTextSize(14) //Subtitle text size in SP (default value 14sp)
                    .disableCloseAction(true)
                    .targetView(view)
                    .enableViewClose(true)
                    .listener(new BubbleShowCaseListener() {
                        @Override
                        public void onSkipActionClick(@NonNull BubbleShowCase bubbleShowCase) {
                            //Do Nothing
                        }

                        @Override
                        public void onTargetClick(@NonNull BubbleShowCase bubbleShowCase) {
                            // Do nothing
                        }

                        @Override
                        public void onCloseActionImageClick(@NonNull BubbleShowCase bubbleShowCase) {

                        }

                        @Override
                        public void onBackgroundDimClick(@NonNull BubbleShowCase bubbleShowCase) {
                            // Do nothing
                            bubbleShowCase.dismiss();
                        }

                        @Override
                        public void onBubbleClick(@NonNull BubbleShowCase bubbleShowCase) {
                            // Do nothing
                        }
                    });

            lihatProgress.show();
        } catch (Exception e) {
            if (!GeneralHelper.isProd())
                Log.e(TAG, "bubbleShowDplk: ", e);
        }
    }

    @Override
    public void onShow(View view, InfoDplkResponse.Dplk dplk) {
        if (view != null) {
            bubbleShowDplk(view, dplk);
        }
    }

    @Override
    protected void onDestroy() {
        super.onDestroy();
        binding = null;
    }
}