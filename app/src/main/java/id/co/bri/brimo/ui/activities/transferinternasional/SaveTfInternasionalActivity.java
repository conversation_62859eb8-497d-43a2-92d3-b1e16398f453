package id.co.bri.brimo.ui.activities.transferinternasional;

import android.app.Activity;
import android.content.Intent;
import android.os.Bundle;
import android.os.SystemClock;
import android.text.Editable;
import android.view.View;

import androidx.annotation.Nullable;
import androidx.fragment.app.FragmentTransaction;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import java.util.List;

import javax.inject.Inject;

import id.co.bri.brimo.R;
import id.co.bri.brimo.adapters.DetailPengirimAdapter;
import id.co.bri.brimo.contract.IPresenter.remittence.IInquiryInternasionalPresenter;
import id.co.bri.brimo.contract.IView.remittence.IInquiryInternasionalView;
import id.co.bri.brimo.databinding.ActivitySaveTfInternasionalBinding;
import id.co.bri.brimo.domain.config.Constant;
import id.co.bri.brimo.domain.helpers.GeneralHelper;
import id.co.bri.brimo.models.AccountModel;
import id.co.bri.brimo.models.apimodel.request.KonfirmasiInternasionalRequest;
import id.co.bri.brimo.models.apimodel.response.InquiryInternasionalResponse;
import id.co.bri.brimo.models.apimodel.response.KonfirmasiInternasionalResponse;
import id.co.bri.brimo.ui.activities.base.BaseActivity;
import id.co.bri.brimo.ui.customviews.dialog.DialogExitCustom;
import id.co.bri.brimo.ui.fragments.SumberDanaFragment;

public class SaveTfInternasionalActivity extends BaseActivity implements View.OnClickListener, IInquiryInternasionalView,
        SumberDanaFragment.SelectSumberDanaInterface, DialogExitCustom.DialogDefaultListener {

    private ActivitySaveTfInternasionalBinding binding;

    protected Double saldo = 0.0;
    protected int counter = 0;
    protected boolean isLoading, isCheckedSave = false;
    protected String saldoString = "";
    protected String defaultAkun;

    protected List<Integer> mListFailed;
    protected List<AccountModel> mListAccountModel;
    protected AccountModel model;


    @Inject
    IInquiryInternasionalPresenter<IInquiryInternasionalView> presenter;

    DetailPengirimAdapter detailPengirimAdapter, detailPenerimaAdapter;

    private static InquiryInternasionalResponse mInquiryInternasionalResponse;

    public static void launchIntent(Activity caller, InquiryInternasionalResponse inquiryInternasionalResponse) {
        Intent intent = new Intent(caller, SaveTfInternasionalActivity.class);
        mInquiryInternasionalResponse = inquiryInternasionalResponse;
        caller.startActivityForResult(intent, Constant.REQ_PAYMENT);
    }

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        binding = ActivitySaveTfInternasionalBinding.inflate(getLayoutInflater());
        setContentView(binding.getRoot());

        GeneralHelper.setToolbar(this, binding.toolbar.toolbar, getString(R.string.tf_international_toolbar));
        setupSaveName();
        setupView();
        injectDependency();
        binding.btnSubmit.setOnClickListener(this);
        binding.itemLayoutBackground.setOnClickListener(this);
        binding.etSavedName.addTextChangedListener(activityTextListener);
    }

    public void setupView() {

        binding.tvPenerima.setText("Penerima");
        binding.tvPengirim.setText("Pengirim");
        // Detail Amount
        binding.tvValue.setText(mInquiryInternasionalResponse.getAmountTransfer());
        binding.tvAmountTerima.setText(mInquiryInternasionalResponse.getAmountReceiptString());

        binding.rvPengirim.setHasFixedSize(true);
        binding.rvPengirim.setLayoutManager(new LinearLayoutManager(this, RecyclerView.VERTICAL, false));
        detailPengirimAdapter = new DetailPengirimAdapter(mInquiryInternasionalResponse.getSenderSummary());
        binding.rvPengirim.setAdapter(detailPengirimAdapter);

        binding.rvPenerima.setHasFixedSize(true);
        binding.rvPenerima.setLayoutManager(new LinearLayoutManager(this, RecyclerView.VERTICAL, false));
        detailPenerimaAdapter = new DetailPengirimAdapter(mInquiryInternasionalResponse.getBenefSummary());
        binding.rvPenerima.setAdapter(detailPenerimaAdapter);

    }

    private void injectDependency() {
        getActivityComponent().inject(this);
        if (presenter != null) {
            presenter.setView(this);
            presenter.start();
            presenter.setUrl(GeneralHelper.getString(R.string.url_konfirmasi_internasional));
        }
    }

    @Override
    public void onClick(View view) {
        if (SystemClock.elapsedRealtime() - mLastClickTime < 1000) {
            return;
        }
        mLastClickTime = SystemClock.elapsedRealtime();
        int id = view.getId();
        switch (id) {
            case R.id.item_layout_background:
                counter++;
                if (mListAccountModel == null) {
                    GeneralHelper.showToast(this, GeneralHelper.getString(R.string.you_dont_have_any_accounts_yet));
                } else {
                    SumberDanaFragment fragmentSumberDana = new SumberDanaFragment(mListAccountModel, this, counter, mListFailed);
                    fragmentSumberDana.show(getSupportFragmentManager(), Constant.TAG_PICK_ACCOUNT);
                }
                break;
            case R.id.btnSubmit:
                onSubmit();
                break;
        }
    }

    protected void setupSaveName() {
        if (mInquiryInternasionalResponse.getSaved() != null) {
            if (!mInquiryInternasionalResponse.getSaved().equals("")) {
                binding.viewSaved.setVisibility(View.GONE);
            } else {
                binding.viewSaved.setVisibility(View.VISIBLE);
            }
        } else {
            binding.viewSaved.setVisibility(View.VISIBLE);
        }
        binding.checkboxSave.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View view) {
                if (binding.checkboxSave.isChecked()) {
                    isCheckedSave = true;
                    binding.saveContent.setVisibility(View.VISIBLE);
                    if (binding.etSavedName.getText().toString().equals("")) {
                        disableButtonSubmit(true);
                    } else cekSaldo();
                } else {
                    binding.checkboxSave.setChecked(false);
                    isCheckedSave = false;
                    binding.saveContent.setVisibility(View.GONE);
                    cekSaldo();
                }
            }
        });
    }

    @Override
    public void setDefaultSaldo(double saldoDefault, String saldoStringPref, String defaultAcc) {
        // load default saldo dari preference
        saldoString = saldoStringPref;
        saldo = saldoDefault;
        defaultAkun = defaultAcc;


        //set layout
        setupAccount(saldoDefault);
        setupSaveName();
    }


    //setup account view
    protected void setupAccount(double saldoDefault) {
        if (mInquiryInternasionalResponse == null)
            finish();

        //List Account
        model = new AccountModel();
        if (mInquiryInternasionalResponse.getAccountList().size() > 0)
            mListAccountModel = mInquiryInternasionalResponse.getAccountList();

        for (AccountModel accountModel : mListAccountModel) {
            if (accountModel.getIsDefault() == 1) {
                model = accountModel;
                break;
            } else {
                model = mListAccountModel.get(0);
            }
        }

        //jika get minimum tidak null
        if (model.getMinimumBalance() != null) {
            saldo = saldoDefault - model.getMinimumBalance();
        } else {
            saldo = saldoDefault;
        }

        if (isFromFastMenu) {
            binding.tvSaldo.setVisibility(View.GONE);
        } else {
            binding.tvSaldo.setVisibility(View.VISIBLE);

            if (model.getAcoount() != null) {
                if (model.getAcoount().equals(defaultAkun)) {
                    //tvSaldo.setText(String.format("%s%s", model.getCurrency(), saldoString));
                    binding.tvSaldo.setText(GeneralHelper.formatNominalIDR(model.getCurrency(), saldoString));
                } else {
                    binding.tvSaldo.setText("-");
                }
            }

        }

        //jika string rekening tidak kosong
        if (model.getAcoountString() != null) {
            binding.tvNorek.setText(model.getAcoountString());
        } else {
            binding.tvNorek.setText("-");
        }

        //jika nama kosong
        if (model.getName() != null) {
            binding.tvInisial.setText(GeneralHelper.formatInitialName(model.getName()));
        } else {
            binding.tvInisial.setText("I");
        }

        cekSaldo();
    }

    public void cekSaldo() {
        if (mInquiryInternasionalResponse.getSendAmountTotal() < saldo) {
            disableButtonSubmit(false);
        } else disableButtonSubmit(true);
    }

    @Override
    public void onException93(String message) {
        Intent returnIntent = new Intent();

        returnIntent.putExtra(Constant.TAG_ERROR_MESSAGE, message);

        this.setResult(RESULT_CANCELED, returnIntent);
        this.finish();
    }

    @Override
    public int getAmount() {
        return 0;
    }

    @Override
    public void onSubmit() {
        KonfirmasiInternasionalRequest konfirmasiInternasionalRequest = new KonfirmasiInternasionalRequest(mInquiryInternasionalResponse.getReferenceNumber(),
                model.getAcoount(), binding.etSavedName.getText().toString());
        presenter.getKonfirmasiInternational(konfirmasiInternasionalRequest);
    }

    @Override
    public void onSuccessKonfirmasi(KonfirmasiInternasionalResponse konfirmasiInternasionalResponse) {
        KonfirmasiTransferInternasionalActivity.launchIntent(this, konfirmasiInternasionalResponse);
    }


    @Override
    public void onSelectSumberDana(AccountModel bankModel) {
        model = bankModel;

        if (model.getSaldoReponse() != null) {
            binding.tvSaldo.setText(GeneralHelper.formatNominalIDR(model.getCurrency(), model.getSaldoReponse().getBalanceString()));
            saldo = model.getSaldoReponse().getBalance() - model.getMinimumBalance();
        } else {
            binding.tvSaldo.setText(String.format("%s%s", model.getCurrency(), "-"));
            saldo = 0.0;
        }
        binding.tvNorek.setText(model.getAcoountString());
        binding.tvInisial.setText(GeneralHelper.formatInitialName(bankModel.getName()));

        cekSaldo();
    }

    @Override
    public void onSendFailedList(List<Integer> list) {
        this.mListFailed = list;
    }

    @Override
    protected void afterText(Editable editable) {
        //balikan listener field inputan nama
        try {
            String savedText = binding.etSavedName.getText().toString();
            if (savedText.equalsIgnoreCase("")) {
                disableButtonSubmit(true);
            } else if (!savedText.equalsIgnoreCase("") && saldo <= 0) {
                cekSaldo();
            } else {
                disableButtonSubmit(mInquiryInternasionalResponse.getSendAmountTotal() >= saldo);
            }
        } catch (Exception e) {
            if (!GeneralHelper.isProd()) {
//                Log.e(TAG, "afterText: ", e);
            }
        }
    }

    protected void disableButtonSubmit(boolean disable) {
        if (disable) {
            binding.btnSubmit.setEnabled(false);
            binding.btnSubmit.setAlpha((float) 0.3);
        } else {
            binding.btnSubmit.setEnabled(true);
            binding.btnSubmit.setAlpha(1);
        }
    }

    @Override
    public void onBackPressed() {
        DialogExitCustom dialogExitCustom = new DialogExitCustom(this::onClickYes, GeneralHelper.getString(R.string.title_dialog_exit_konfirmasi), GeneralHelper.getString(R.string.content_dialog_exit_konfirmasi));
        FragmentTransaction ft = this.getSupportFragmentManager().beginTransaction();
        ft.add(dialogExitCustom, null);
        ft.commitAllowingStateLoss();
    }

    @Override
    protected void onActivityResult(int requestCode, int resultCode, @Nullable Intent data) {
        super.onActivityResult(requestCode, resultCode, data);
        if (requestCode == Constant.REQ_PAYMENT) {
            if (resultCode == RESULT_OK) {
                this.setResult(RESULT_OK, data);
                this.finish();
            } else {
                this.setResult(RESULT_CANCELED, data);
                this.finish();
            }
        } else if (requestCode == Constant.REQ_FORGET_PIN) {
            if (resultCode == RESULT_OK) {
                this.setResult(RESULT_OK);
                this.finish();
            }
        }
    }

    @Override
    public void onClickYes() {
        super.onBackPressed();
    }


}