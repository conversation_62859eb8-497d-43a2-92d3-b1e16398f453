package id.co.bri.brimo.ui.activities.deposito;

import android.app.Activity;
import android.content.Intent;
import android.os.Bundle;
import android.os.SystemClock;
import android.view.View;
import androidx.annotation.Nullable;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;
import java.util.ArrayList;
import java.util.List;
import javax.inject.Inject;
import id.co.bri.brimo.R;
import id.co.bri.brimo.adapters.DetailDepositoAdapter;
import id.co.bri.brimo.contract.IPresenter.deposito.IDetailDepositoPresenter;
import id.co.bri.brimo.contract.IView.deposito.IDetailDepositoView;
import id.co.bri.brimo.databinding.ActivityDetailDepositoBinding;
import id.co.bri.brimo.domain.config.Constant;
import id.co.bri.brimo.domain.helpers.GeneralHelper;
import id.co.bri.brimo.models.DataViewDeposito;
import id.co.bri.brimo.models.apimodel.response.ConfirmationPenutupanDepositoResponse;
import id.co.bri.brimo.models.apimodel.response.DetailDepositoResponse;
import id.co.bri.brimo.models.apimodel.response.InquiryPenutupanDepositoResponse;
import id.co.bri.brimo.models.apimodel.response.RenewalDepositoResponse;
import id.co.bri.brimo.ui.activities.base.BaseActivity;
import id.co.bri.brimo.ui.activities.simpedes.ProductListAmkkmActivity;
import id.co.bri.brimo.ui.fragments.BottomFragmentWithHtml;
import id.co.bri.brimo.ui.fragments.deposito.CairkanDepositoFragment;

public class DetailDepositoActivity extends BaseActivity implements View.OnClickListener, CairkanDepositoFragment.OnFragmentInteractionListener, DetailDepositoAdapter.OnClickListener, IDetailDepositoView {

    private ActivityDetailDepositoBinding binding;

    protected static DetailDepositoResponse mDetail;
    protected static String[] str1;
    protected String contentTitlehtml;
    protected  String contentDeschtml;

    InquiryPenutupanDepositoResponse inquiryPenutupanDepositoResponse;

    @Inject
    IDetailDepositoPresenter<IDetailDepositoView> presenter;

    DetailDepositoAdapter dataTransaksiAdapter;

    List<DataViewDeposito> listAdapter = new ArrayList<>();

    BottomFragmentWithHtml bottomFragmentNoImage;

    public static void launchIntent(Activity caller, DetailDepositoResponse detailDepositoResponse) {
        Intent intent = new Intent(caller, DetailDepositoActivity.class);
        mDetail = detailDepositoResponse;
        caller.startActivityForResult(intent, Constant.REQ_PAYMENT);
    }

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        binding = ActivityDetailDepositoBinding.inflate(getLayoutInflater());
        setContentView(binding.getRoot());

        GeneralHelper.setToolbar(this, binding.tbDeposito.toolbar, "Info Deposito");

        injectDependency();

        listAdapter = mDetail.getDetail();

        //formatString();
        binding.txtMataUang.setText(mDetail.getCurrency());
        binding.tvSaldo.setText(mDetail.getBalanceString());

        binding.rvDetailDeposito.setHasFixedSize(true);
        binding.rvDetailDeposito.setLayoutManager(new LinearLayoutManager(getApplicationContext(), RecyclerView.VERTICAL, false));
        dataTransaksiAdapter = new DetailDepositoAdapter(listAdapter, DetailDepositoActivity.this,this);
        binding.rvDetailDeposito.setAdapter(dataTransaksiAdapter);

        binding.btnSubmit.setOnClickListener(this);
    }

    private void injectDependency() {
        getActivityComponent().inject(this);

        if (presenter != null) {
            presenter.setView(this);
            presenter.setUrlRenewalDeposito(GeneralHelper.getString(R.string.url_deposito_renewal_form));
            presenter.setUrlInquiryPenutupanDeposito(GeneralHelper.getString(R.string.url_deposito_close_inquiry));
            presenter.setUrlConfirmationPenutupanDeposito(GeneralHelper.getString(R.string.url_deposito_close_confirmation));
            presenter.start();
        }
    }

    @Override
    public void onClick(View v) {
        switch (v.getId()){
            case R.id.btnSubmit:
                if (SystemClock.elapsedRealtime() - mLastClickTime < 1000) {
                    return;
                }
                    presenter.getInquiryPenutupanDeposito(mDetail.getAccount());
                break;
        }
    }

    private void callFragment() {
        CairkanDepositoFragment cairkanDepositoFragment = new CairkanDepositoFragment("Yakin ingin cairkan deposito?",inquiryPenutupanDepositoResponse.getWarnNotes(),
                ""+inquiryPenutupanDepositoResponse.getYear(),
                ""+inquiryPenutupanDepositoResponse.getMonth(),
                ""+inquiryPenutupanDepositoResponse.getDay(),
                this);
        cairkanDepositoFragment.show(getSupportFragmentManager(),"");
    }

    @Override
    public void onClickDeposito() {
        presenter.getConfirmationPenutupanDeposito(inquiryPenutupanDepositoResponse.getReferenceNumber());
    }

    @Override
    public void onCLickPenalti() {
        PenaltiPenutupanDepositoActivity.launchIntent(this,inquiryPenutupanDepositoResponse.getPenaltyContent());
    }


    @Override
    public void onClickItem() {
        presenter.getDataRenewalDeposito(mDetail.getAccount());
    }

    @Override
    public void getDataRenewalDeposito(RenewalDepositoResponse response) {
        UbahPerpanjanganDepositoActivity.launchIntent(this,response,mDetail.getAccount());
    }

    @Override
    public void getDataInquiryPenutupanDeposito(InquiryPenutupanDepositoResponse response) {
        this.inquiryPenutupanDepositoResponse = response;
        callFragment();
    }

    @Override
    public void getDataConfirmationPenutupanDeposito(ConfirmationPenutupanDepositoResponse response) {
        InquiryPenutupanDepositoActivity.launchIntent(this,response);
    }

    @Override
    protected void onActivityResult(int requestCode, int resultCode, @Nullable Intent data) {
        super.onActivityResult(requestCode, resultCode, data);
        if (requestCode == Constant.REQ_PAYMENT && resultCode == RESULT_OK) {
            setResult(RESULT_OK, data);
            this.finish();
        } else if (requestCode == ProductListAmkkmActivity.REQUESTCODE && resultCode == Activity.RESULT_CANCELED
                && data != null && data.getStringExtra(Constant.TAG_ERROR_MESSAGE) != null) {
            onException(data.getStringExtra(Constant.TAG_ERROR_MESSAGE));
        }
    }

    @Override
    public void onClickItemInfo(DataViewDeposito dataViewDeposito) {
        bottomFragmentNoImage = new BottomFragmentWithHtml(dataViewDeposito, "Tutup");
        bottomFragmentNoImage.show(getSupportFragmentManager(), "");
    }

    @Override
    protected void onDestroy() {
        super.onDestroy();
        binding = null;
    }
}
