package id.co.bri.brimo.ui.activities.sbn;

import android.app.Activity;
import android.content.Intent;
import android.os.Bundle;

import androidx.recyclerview.widget.LinearLayoutManager;

import com.google.gson.Gson;

import id.co.bri.brimo.adapters.esbn.CaraBayarAdapter;
import id.co.bri.brimo.adapters.esbn.PembayaranUkerSbnAdapter;
import id.co.bri.brimo.databinding.ActivityTutorialPembayaranSbnBinding;
import id.co.bri.brimo.domain.config.Constant;
import id.co.bri.brimo.domain.helpers.GeneralHelper;
import id.co.bri.brimo.models.apimodel.response.esbn.DataProcedureSbnResponse;
import id.co.bri.brimo.ui.activities.base.BaseActivity;

public class TutorialPembayaranSbnActivity extends BaseActivity {

    private ActivityTutorialPembayaranSbnBinding binding;

    public static String TAG_RESPONSE = "response";
    private DataProcedureSbnResponse dataProcedureSbnResponse;

    public static void launchIntent(Activity caller, DataProcedureSbnResponse procedureSbn) {
        Intent intent = new Intent(caller, TutorialPembayaranSbnActivity.class);
        intent.putExtra(TAG_RESPONSE, new Gson().toJson(procedureSbn));
        caller.startActivityForResult(intent, Constant.REQ_PAYMENT);
    }

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        binding = ActivityTutorialPembayaranSbnBinding.inflate(getLayoutInflater());
        setContentView(binding.getRoot());

        setupView();
    }

    private void setupView() {
        GeneralHelper.setToolbar(this, binding.tbTutorialBayar.toolbar, "Cara Bayar");

        if (getIntent().getStringExtra(TAG_RESPONSE) != null) {
            dataProcedureSbnResponse = new Gson().fromJson(getIntent().getStringExtra(TAG_RESPONSE), DataProcedureSbnResponse.class);

            binding.tvHead.setText(dataProcedureSbnResponse.getTitle().getTitleHead());
            binding.tvDesc.setText(dataProcedureSbnResponse.getTitle().getTitleDetail());

            binding.rcCaraBayar.setHasFixedSize(true);
            binding.rcCaraBayar.setLayoutManager(new LinearLayoutManager(this));
            CaraBayarAdapter adapter = new CaraBayarAdapter(this, dataProcedureSbnResponse.getStep().getStepList());
            binding.rcCaraBayar.setAdapter(adapter);

            binding.rcCaraBayarUker.setHasFixedSize(true);
            binding.rcCaraBayarUker.setLayoutManager(new LinearLayoutManager(this));
            PembayaranUkerSbnAdapter adapter2 = new PembayaranUkerSbnAdapter(dataProcedureSbnResponse.getTitle().getTitleList(), this);
            binding.rcCaraBayarUker.setAdapter(adapter2);
        }

    }

    @Override
    protected void onDestroy() {
        super.onDestroy();
        binding = null;
    }
}