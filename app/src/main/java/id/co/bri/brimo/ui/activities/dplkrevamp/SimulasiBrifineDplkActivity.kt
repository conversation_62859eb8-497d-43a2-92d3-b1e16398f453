package id.co.bri.brimo.ui.activities.dplkrevamp

import android.app.Activity
import android.content.Intent
import android.graphics.Color
import android.os.Bundle
import android.text.Editable
import android.view.View
import android.view.WindowManager
import android.view.inputmethod.InputMethodManager
import android.webkit.WebView
import android.webkit.WebViewClient
import android.widget.Toast
import androidx.core.content.ContextCompat
import androidx.recyclerview.widget.LinearLayoutManager
import com.ethanhua.skeleton.Skeleton
import com.ethanhua.skeleton.SkeletonScreen
import com.github.mikephil.charting.animation.Easing
import com.github.mikephil.charting.components.Legend
import com.github.mikephil.charting.data.Entry
import com.github.mikephil.charting.highlight.Highlight
import com.github.mikephil.charting.listener.OnChartValueSelectedListener
import id.co.bri.brimo.R
import id.co.bri.brimo.adapters.dplkrevamp.SimulasiDplkEstimasiInvestasiAdapter
import id.co.bri.brimo.adapters.dplkrevamp.SimulasiDplkTotalEstimasiInvestasiAdapter
import id.co.bri.brimo.contract.IPresenter.dplkrevamp.ISimulasiDplkRevampPresenter
import id.co.bri.brimo.contract.IView.dplkrevamp.ISimulasiDplkRevampView
import id.co.bri.brimo.databinding.ActivitySimulasiBrifineDplkBinding
import id.co.bri.brimo.domain.config.Constant
import id.co.bri.brimo.domain.extension.gone
import id.co.bri.brimo.domain.extension.visible
import id.co.bri.brimo.domain.helpers.GeneralHelper
import id.co.bri.brimo.domain.helpers.PieChartHelper
import id.co.bri.brimo.domain.helpers.textwatcher.AmountFormatWatcher
import id.co.bri.brimo.models.apimodel.request.dplkrevamp.Combination
import id.co.bri.brimo.models.apimodel.request.dplkrevamp.InquiryDplkRegisRequest
import id.co.bri.brimo.models.apimodel.request.dplkrevamp.SimulasiDplkRequest
import id.co.bri.brimo.models.apimodel.response.dplk.DplkBoardingResponse
import id.co.bri.brimo.models.apimodel.response.dplkrevamp.FormPilihBrifineResponse
import id.co.bri.brimo.models.apimodel.response.dplkrevamp.SimulasiDplkResponse
import id.co.bri.brimo.ui.activities.base.BaseActivity
import id.co.bri.brimo.ui.activities.dplk.DescriptionDplkProductActivity
import id.co.bri.brimo.ui.fragments.dplkrevamp.BottomPembukaanBrifineFragment
import okhttp3.internal.format
import javax.inject.Inject

class SimulasiBrifineDplkActivity :
    BaseActivity(),
    ISimulasiDplkRevampView,
    OnChartValueSelectedListener {

    private lateinit var binding: ActivitySimulasiBrifineDplkBinding

    private var estimasiInvestasiAdapter: SimulasiDplkEstimasiInvestasiAdapter? = null
    private var totalEstimasiInvestasiAdapter: SimulasiDplkTotalEstimasiInvestasiAdapter? = null
    private var skeletonEstimasiInvestasi: SkeletonScreen? = null

    private var setoranAwalStr: String = ""
    private var usiaSaatIniStr: String = ""
    private var usiaPensiunStr: String = ""
    private var setoranRutinStr: String = ""
    private var kenaikanIuranStr: String = ""

    var x = 0
    var y = 0
    var z = 0
    var value = 100
    var sum = 0

    private var isCheckboxChecked: Boolean = false

    @Inject
    lateinit var presenter: ISimulasiDplkRevampPresenter<ISimulasiDplkRevampView>

    companion object {

        private lateinit var mResponseSimulasi: SimulasiDplkResponse
        private var productName: String = ""
        private var productCode: String = ""
        private var isCombination: Boolean = false
        private var moneyMarket: String = ""
        private var fixIncome: String = ""
        private var stock: String = ""
        private var isRegistered = false

        @JvmStatic
        fun lunchIntent(
            caller: Activity,
            response: SimulasiDplkResponse,
            productName: String,
            productCode: String,
            isCombination: Boolean,
            moneyMarket: String,
            fixIncome: String,
            stock: String,
            isRegistered: Boolean,
        ) {
            val intent = Intent(caller, SimulasiBrifineDplkActivity::class.java)
            mResponseSimulasi = response
            this.productName = productName
            this.productCode = productCode
            this.isCombination = isCombination
            this.moneyMarket = moneyMarket
            this.fixIncome = fixIncome
            this.stock = stock
            this.isRegistered = isRegistered
            caller.startActivityForResult(intent, Constant.REQ_PAYMENT)
        }
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        binding = ActivitySimulasiBrifineDplkBinding.inflate(layoutInflater)
        setContentView(binding.root)
        injectDependecy()
        setupView()
    }

    private fun setupSkeletonView() {
        binding.llHasilSimulasi.visibility = View.VISIBLE
        binding.wvInformation.visibility = View.GONE

        skeletonEstimasiInvestasi = Skeleton.bind(binding.llListHasilSimulasi)
            .shimmer(true)
            .angle(20)
            .duration(500)
            .load(R.layout.skeleton_estimasi_investasi)
            .show()

        binding.nestedScrollview.post {
            binding.nestedScrollview.fling(0)
            binding.nestedScrollview.smoothScrollTo(0, binding.nestedScrollview.bottom)
        }
    }

    private fun injectDependecy() {
        activityComponent.inject(this)
        presenter.view = this
        presenter.setUrlSimulasiDplk(GeneralHelper.getString(R.string.url_simulasi_dplk))
        presenter.setUrlInquiryDplk(GeneralHelper.getString(R.string.url_dplk_inquiry_open_account))
        presenter.setUrlRegistrationDplk(GeneralHelper.getString(R.string.url_dplk_onboarding))
        presenter.start()
    }

    private fun setupView() {
        binding.apply {
            GeneralHelper.setToolbarRevamp(
                this@SimulasiBrifineDplkActivity,
                tbSimulasiSbn.toolbar,
                GeneralHelper.getString(R.string.txt_title_simulasi_brifine)
            )

            val window = window
            window.addFlags(WindowManager.LayoutParams.FLAG_DRAWS_SYSTEM_BAR_BACKGROUNDS)
            window.clearFlags(WindowManager.LayoutParams.FLAG_TRANSLUCENT_STATUS)
            window.statusBarColor = GeneralHelper.getColor(R.color.primary_blue80)

            tvJenisDplk.text = productName
            tilNominal.helperText = mResponseSimulasi.deposit.minimumDepositString

            etUsiaSaatIni.hint = mResponseSimulasi.deposit.ageNow.toString()
            etUsiaPensiun.hint = mResponseSimulasi.deposit.ageRetirement.toString()

            tvUsiaSaatIni.text = format(
                GeneralHelper.getString(R.string.txt_min_tahun),
                mResponseSimulasi.deposit.ageNowString
            )
            tvUsiaPensiun.text = format(
                GeneralHelper.getString(R.string.txt_min_tahun),
                mResponseSimulasi.deposit.ageRetirementString
            )
            if (!isRegistered) {
                btnBukaBrifine.text = GeneralHelper.getString(R.string.btn_regis_revamp_txt)
            }
            tilSetoranRutin.helperText = mResponseSimulasi.autopayment.minimumDepositString
            etKenaikIuran.setText(mResponseSimulasi.autopayment.subscriptionRaise.toString())

            tvSyarat.text = mResponseSimulasi.autopayment.checklistWordingAutopayment
            tvMinimalPercentage.text = mResponseSimulasi.autopayment.percentageString

            setoranAwalStr = etNominal.text.toString()
            usiaSaatIniStr = (etUsiaSaatIni.text ?: "0").toString()
            usiaPensiunStr = (etUsiaPensiun.text ?: "0").toString()
            setoranRutinStr = etSetoranRutin.text.toString()
            kenaikanIuranStr = etKenaikIuran.text.toString()

            etNominal.addTextChangedListener(activityTextListener)
            etNominal.addTextChangedListener(AmountFormatWatcher(binding.etNominal, null, false))

            etUsiaSaatIni.addTextChangedListener(activityTextListener)
            etUsiaSaatIni.addTextChangedListener(
                AmountFormatWatcher(
                    binding.etUsiaSaatIni,
                    null,
                    false
                )
            )

            etUsiaPensiun.addTextChangedListener(activityTextListener)
            etUsiaPensiun.addTextChangedListener(
                AmountFormatWatcher(
                    binding.etUsiaPensiun,
                    null,
                    false
                )
            )

            etSetoranRutin.addTextChangedListener(activityTextListener)
            etSetoranRutin.addTextChangedListener(
                AmountFormatWatcher(
                    binding.etSetoranRutin,
                    null,
                    false
                )
            )

            etKenaikIuran.addTextChangedListener(activityTextListener)
            etKenaikIuran.addTextChangedListener(
                AmountFormatWatcher(
                    binding.etKenaikIuran,
                    null,
                    false
                )
            )

            binding.btnSubmit.isEnabled = false
            binding.btnSubmit.setTextColor(GeneralHelper.getColor(R.color.neutral_light60))

            requestFocus()

            cbSyarat.setOnClickListener {
                setupCheckBox()
            }

            if (isCombination) {
                llAlokasiChart.visibility = View.VISIBLE
                binding.tvDescPasar.text = moneyMarket
                binding.tvDescPendapatan.text = fixIncome
                binding.tvDescSaham.text = stock
                if (moneyMarket.toInt() == 0) {
                    binding.llPasarUang.gone()
                } else if (fixIncome.toInt() == 0) {
                    binding.llPendapatan.gone()
                } else if (stock.toInt() == 0) {
                    binding.llSaham.gone()
                } else {
                    binding.llPasarUang.visible()
                    binding.llPendapatan.visible()
                    binding.llSaham.visible()
                }
                setupLabel()
            } else {
                llAlokasiChart.visibility = View.GONE
            }

            cvJenisDplk.setOnClickListener {
                finish()
            }

            btnBukaBrifine.setOnClickListener {
                handleRoutingButtonSimulasiDplk()
            }

            btnSubmit.setOnClickListener {
                setupSkeletonView()
                if (isCombination) {
                    presenter.getDataSimulasiDplk(
                        SimulasiDplkRequest(
                            setoranAwalStr,
                            etKenaikIuran.text.toString(),
                            setoranRutinStr,
                            usiaSaatIniStr,
                            usiaPensiunStr,
                            isCombination,
                            moneyMarket,
                            fixIncome,
                            stock
                        )
                    )
                } else {
                    presenter.getDataSimulasiDplk(
                        SimulasiDplkRequest(
                            setoranAwalStr,
                            etKenaikIuran.text.toString(),
                            setoranRutinStr,
                            usiaSaatIniStr,
                            usiaPensiunStr,
                            isCombination,
                            "0",
                            "0",
                            "0"
                        )
                    )
                }
            }
        }

        binding.tvAturKombinasi.setOnClickListener {
            finish()
        }
    }

    private fun setupLabel() {
        x = moneyMarket.toInt()
        y = stock.toInt()
        z = fixIncome.toInt()

        binding.chart.setUsePercentValues(false)
        binding.chart.description.isEnabled = false

        binding.chart.dragDecelerationFrictionCoef = 0.95f

        binding.chart.isDrawHoleEnabled = false

        binding.chart.setTransparentCircleColor(Color.WHITE)
        binding.chart.setTransparentCircleAlpha(0)
        binding.chart.transparentCircleRadius = 0f

        binding.chart.setDrawCenterText(false)

        binding.chart.rotationAngle = 0f
        binding.chart.isRotationEnabled = false
        binding.chart.isHighlightPerTapEnabled = false

        binding.chart.setOnChartValueSelectedListener(this)
        binding.chart.setTouchEnabled(true)

        binding.chart.animateY(1400, Easing.EaseInOutQuad)
        // chart.spin(2000, 0, 360);

        // chart.spin(2000, 0, 360);
        binding.chart.setOnDragListener { _, event ->
            Toast.makeText(this, "" + event.x, Toast.LENGTH_SHORT).show()
            false
        }

        val l: Legend = binding.chart.legend
        l.isEnabled = false


        binding.chart.setDrawEntryLabels(false)
        PieChartHelper.setPieChart(
            x,
            y,
            z,
            sum,
            GeneralHelper.getString(R.string.txt_pasar_uang),
            GeneralHelper.getString(R.string.txt_pendapatan_tetap),
            GeneralHelper.getString(R.string.txt_saham),
            GeneralHelper.getString(R.string.txt_kosong),
            R.color.semanticYellow90,
            R.color.successColor,
            R.color.highlightColor,
            R.color.accent2Color,
            binding.chart
        )
    }

    private fun setupCheckBox() {
        if (!isCheckboxChecked) {
            isCheckboxChecked = true
            binding.etSetoranRutin.setText(setoranAwalStr)
        } else {
            isCheckboxChecked = false
            binding.etSetoranRutin.setText(GeneralHelper.getString(R.string.zero))
        }
    }

    private fun requestFocus() {
        //nominal
        binding.etNominal.requestFocus()
        binding.etNominal.isEnabled = true

        val imNominal = getSystemService(INPUT_METHOD_SERVICE) as InputMethodManager
        binding.etNominal.requestFocus()
        imNominal.showSoftInput(binding.etNominal, 0)

        //usia saat ini
        binding.etUsiaSaatIni.requestFocus()
        binding.etUsiaSaatIni.isEnabled = true

        val imUsiaSaatIni = getSystemService(INPUT_METHOD_SERVICE) as InputMethodManager
        binding.etUsiaSaatIni.requestFocus()
        imUsiaSaatIni.showSoftInput(binding.etUsiaSaatIni, 0)

        //usia pensiun
        binding.etUsiaPensiun.requestFocus()
        binding.etUsiaPensiun.isEnabled = true

        val imPensiun = getSystemService(INPUT_METHOD_SERVICE) as InputMethodManager
        binding.etUsiaPensiun.requestFocus()
        imPensiun.showSoftInput(binding.etUsiaPensiun, 0)

        //setoran rutin
        binding.etSetoranRutin.requestFocus()
        binding.etSetoranRutin.isEnabled = true

        val imSetoranRutin = getSystemService(INPUT_METHOD_SERVICE) as InputMethodManager
        binding.etSetoranRutin.requestFocus()
        imSetoranRutin.showSoftInput(binding.etSetoranRutin, 0)

        binding.etNominal.requestFocus()        //usia saat ini
        binding.etUsiaSaatIni.requestFocus()
        binding.etUsiaSaatIni.isEnabled = true

        binding.etNominal.requestFocus()
    }

    private fun checkButton() {
        checkSetoranAwal(setoranAwalStr.toDouble())
        checkUsianSaatIni(usiaSaatIniStr.toInt(), usiaPensiunStr.toInt())
        checkUsiaPensiun(usiaPensiunStr.toInt())
        checkSetoranRutin(setoranRutinStr.toDouble())
        checkKenaikanIuran(kenaikanIuranStr.toInt())

        if (
            checkSetoranAwal(setoranAwalStr.toDouble()) &&
            checkUsianSaatIni(usiaSaatIniStr.toInt(), usiaPensiunStr.toInt()) &&
            checkUsiaPensiun(usiaPensiunStr.toInt()) &&
            checkSetoranRutin(setoranRutinStr.toDouble())
            && checkKenaikanIuran(kenaikanIuranStr.toInt())
        ) {
            binding.btnSubmit.isEnabled = true
            binding.btnSubmit.setTextColor(GeneralHelper.getColor(R.color.whiteColor))
        } else {
            binding.btnSubmit.isEnabled = false
            binding.btnSubmit.setTextColor(GeneralHelper.getColor(R.color.neutral_light60))

        }
    }

    private fun checkSetoranAwal(setoranAwalValue: Double): Boolean {
        val isChecked: Boolean =
            if (setoranAwalValue == 0.0) {
                binding.tilNominal.helperText = mResponseSimulasi.deposit.minimumDepositString
                binding.tilNominal.setHelperTextColor(
                    ContextCompat.getColorStateList(
                        this,
                        R.color.neutral_light80
                    )
                )
                false
            } else if (setoranAwalValue < mResponseSimulasi.deposit.minimumDeposit) {
                binding.tilNominal.helperText = mResponseSimulasi.deposit.minimumDepositString
                binding.tilNominal.setHelperTextColor(
                    ContextCompat.getColorStateList(
                        this@SimulasiBrifineDplkActivity,
                        R.color.semanticRed80
                    )
                )
                false
            } else if (setoranAwalValue > mResponseSimulasi.deposit.maximumDeposit) {
                binding.tilNominal.helperText = mResponseSimulasi.deposit.maximumDepositString
                binding.tilNominal.setHelperTextColor(
                    ContextCompat.getColorStateList(
                        this@SimulasiBrifineDplkActivity,
                        R.color.semanticRed80
                    )
                )
                false
            } else {
                binding.tilNominal.helperText = mResponseSimulasi.deposit.minimumDepositString
                binding.tilNominal.setHelperTextColor(
                    ContextCompat.getColorStateList(
                        this@SimulasiBrifineDplkActivity,
                        R.color.neutral_light80
                    )
                )
                true
            }

        return isChecked
    }

    private fun checkUsianSaatIni(usiaSaatIniStr: Int, usiaPensiunStr: Int): Boolean {
        val isChecked: Boolean =
            if (usiaSaatIniStr == 0) {
                binding.tvUsiaSaatIni.setTextColor(
                    ContextCompat.getColorStateList(
                        this,
                        R.color.neutral_light60
                    )
                )
                false
            } else if (usiaSaatIniStr < mResponseSimulasi.deposit.ageNow) {
                binding.tvUsiaSaatIni.setTextColor(
                    ContextCompat.getColorStateList(
                        this,
                        R.color.semanticRed80
                    )
                )
                binding.tvWarning.gone()
                false
            } else if (usiaSaatIniStr > 99) {
                binding.etUsiaSaatIni.setText(GeneralHelper.getString(R.string.trx99))
                true
            } else if (usiaSaatIniStr > usiaPensiunStr) {
                binding.tvWarning.visible()
                binding.tvUsiaSaatIni.setTextColor(GeneralHelper.getColor(R.color.neutral_light60))
                binding.tvWarning.text = GeneralHelper.getString(R.string.txt_warning_usia)
                false
            } else {
                binding.tvUsiaSaatIni.setTextColor(
                    ContextCompat.getColorStateList(
                        this,
                        R.color.neutralLight60
                    )
                )
                binding.tvWarning.gone()
                true
            }

        return isChecked
    }

    private fun checkUsiaPensiun(usiaPensiunStr: Int): Boolean {
        val isChecked: Boolean =
            if (usiaPensiunStr == 0) {
                binding.tvUsiaPensiun.setTextColor(
                    ContextCompat.getColorStateList(
                        this,
                        R.color.neutral_light60
                    )
                )
                false
            } else if (usiaPensiunStr < mResponseSimulasi.deposit.ageRetirement) {
                binding.tvUsiaPensiun.setTextColor(
                    ContextCompat.getColorStateList(
                        this,
                        R.color.semanticRed80
                    )
                )
                false
            } else if (usiaPensiunStr > 99) {
                binding.etUsiaPensiun.setText("99")
                true
            } else {
                binding.tvUsiaPensiun.setTextColor(
                    ContextCompat.getColorStateList(
                        this,
                        R.color.neutral_light60
                    )
                )
                true
            }

        return isChecked
    }

    private fun checkSetoranRutin(setoranRutinStr: Double): Boolean {
        val isChecked: Boolean =
            if (setoranRutinStr == 0.0) {
                binding.tilSetoranRutin.helperText = mResponseSimulasi.deposit.minimumDepositString
                binding.tilSetoranRutin.setHelperTextColor(
                    ContextCompat.getColorStateList(
                        this,
                        R.color.neutralLight60
                    )
                )
                false
            } else if (setoranRutinStr < mResponseSimulasi.autopayment.minimumDeposit) {
                binding.tilSetoranRutin.helperText = mResponseSimulasi.deposit.minimumDepositString
                binding.tilSetoranRutin.setHelperTextColor(
                    ContextCompat.getColorStateList(
                        this,
                        R.color.semanticRed80
                    )
                )
                false
            } else if (setoranRutinStr > mResponseSimulasi.deposit.maximumDeposit) {
                binding.tilSetoranRutin.helperText = mResponseSimulasi.deposit.maximumDepositString
                binding.tilSetoranRutin.setHelperTextColor(
                    ContextCompat.getColorStateList(
                        this,
                        R.color.semanticRed80
                    )
                )
                false
            } else {
                binding.tilSetoranRutin.helperText = mResponseSimulasi.deposit.minimumDepositString
                binding.tilSetoranRutin.setHelperTextColor(
                    ContextCompat.getColorStateList(
                        this,
                        R.color.neutralLight60
                    )
                )
                true
            }
        return isChecked
    }

    private fun checkKenaikanIuran(kenaikanIuranStr: Int): Boolean {
        val isChecked: Boolean = if (kenaikanIuranStr <= 50) {
            binding.tvMinimalPercentage.setTextColor(
                ContextCompat.getColorStateList(
                    this,
                    R.color.neutralLight60
                )
            )
            true
        } else {
            binding.tvMinimalPercentage.setTextColor(
                ContextCompat.getColorStateList(
                    this,
                    R.color.semanticRed80
                )
            )
            false
        }

        return isChecked
    }

    private fun handleRoutingButtonSimulasiDplk() {
        when {
            !isRegistered -> {
                presenter.getRegistrationDplk()
            }

            mResponseSimulasi.isPending -> {
                showBottomFragmentStatus()
            }

            else -> {
                getDataInquiryDplk()
            }
        }
    }

    private fun showBottomFragmentStatus() {
        val bottomInfo = BottomPembukaanBrifineFragment(
            mResponseSimulasi.pendingAccount.pendingPopup.title,
            mResponseSimulasi.pendingAccount.pendingPopup.description,
            mResponseSimulasi.pendingAccount.pendingPopup.buttonString
        )
        bottomInfo.show(supportFragmentManager, "")
    }

    private fun getDataInquiryDplk() {
        val requestBody: InquiryDplkRegisRequest = if (isCombination)
            InquiryDplkRegisRequest(
                productCode,
                Combination(
                    moneyMarket.toInt(),
                    stock.toInt(),
                    fixIncome.toInt()
                )
            ) else {
            InquiryDplkRegisRequest(productCode, null)
        }
        presenter.getDataInquiryDplk(requestBody)
    }

    override fun changeText(charSequence: CharSequence?, i: Int, i1: Int, i2: Int) {
        super.changeText(charSequence, i, i1, i2)
        setoranAwalStr = if (binding.etNominal.text.toString().isEmpty()) "0"
        else binding.etNominal.text.toString().replace(".", "")

        usiaSaatIniStr = if (binding.etUsiaSaatIni.text.toString().isEmpty()) "0"
        else binding.etUsiaSaatIni.text.toString().replace(".", "")

        usiaPensiunStr = if (binding.etUsiaPensiun.text.toString().isEmpty()) "0"
        else binding.etUsiaPensiun.text.toString().replace(".", "")

        if (binding.etSetoranRutin.text.toString().isEmpty()) {
            setoranRutinStr = "0"
        } else {
            setoranRutinStr = binding.etSetoranRutin.text.toString().replace(".", "")
            binding.cbSyarat.isChecked = false
        }

        kenaikanIuranStr = if (binding.etKenaikIuran.text.toString().isEmpty()) "0"
        else binding.etKenaikIuran.text.toString().replace(".", "")

        checkButton()
    }

    override fun onSuccessGetSimulasiDplk(response: SimulasiDplkResponse) {
        binding.apply {
            mResponseSimulasi = response
            llHasilSimulasi.visibility = View.VISIBLE
            llBukaBrifine.visibility = View.VISIBLE
            binding.wvInformation.visibility = View.VISIBLE

            tvWordingNilaiInvestasi.text = response.simulationResult.resultWording

            estimasiInvestasiAdapter = SimulasiDplkEstimasiInvestasiAdapter(
                this@SimulasiBrifineDplkActivity,
                response.simulationResult.detailDataView
            )
            rvEstimasiInvestasi.adapter = estimasiInvestasiAdapter
            rvEstimasiInvestasi.layoutManager = LinearLayoutManager(
                this@SimulasiBrifineDplkActivity,
                LinearLayoutManager.VERTICAL,
                false
            )

            totalEstimasiInvestasiAdapter = SimulasiDplkTotalEstimasiInvestasiAdapter(
                this@SimulasiBrifineDplkActivity,
                response.simulationResult.totalDataView
            )
            rvTotalEstimasiInvestasi.adapter = totalEstimasiInvestasiAdapter
            rvTotalEstimasiInvestasi.layoutManager = LinearLayoutManager(
                this@SimulasiBrifineDplkActivity,
                LinearLayoutManager.VERTICAL,
                false
            )

            wvInformation.webViewClient = object : WebViewClient() {
                override fun onPageFinished(view: WebView?, url: String?) {
                    skeletonEstimasiInvestasi?.hide()
                    binding.llBukaBrifine.requestFocus()
                    nestedScrollview.post {
                        nestedScrollview.fling(0)
                        nestedScrollview.smoothScrollTo(0, nestedScrollview.bottom)
                    }
                }
            }

            wvInformation.loadDataWithBaseURL(
                null,
                response.simulationResult.information.desc,
                "text/html",
                "utf-8",
                null
            )

        }
    }

    override fun onSuccessInquiryDplkRegis(response: FormPilihBrifineResponse) {
        FormPilihBrifineRevampActivity.launchIntent(
            this@SimulasiBrifineDplkActivity,
            response,
            setoranRutinStr,
            productCode
        )
    }

    override fun onSuccessDplkRegis(response: DplkBoardingResponse) {
        DescriptionDplkProductActivity.launchIntent(this, response)
    }

    override fun onValueSelected(e: Entry?, h: Highlight?) {
        // do nothing
    }

    override fun onNothingSelected() {
        // do nothing
    }

    override fun afterText(editable: Editable?) {
        super.afterText(editable)
        binding.cbSyarat.isChecked = binding.etNominal.text.toString()
            .replace(".", "") == binding.etSetoranRutin.text.toString().replace(".", "")

        checkButton()

    }

    override fun onActivityResult(requestCode: Int, resultCode: Int, data: Intent?) {
        super.onActivityResult(requestCode, resultCode, data)
        if (requestCode == Constant.REQ_PAYMENT) {
            if (resultCode == RESULT_OK) {
                this.setResult(RESULT_OK, data)
                finish()
            } else if (resultCode == RESULT_CANCELED && data != null) {
                this.setResult(RESULT_CANCELED, data)
                finish()
            } else if (resultCode == RESULT_FIRST_USER && data != null) {
                this.setResult(RESULT_FIRST_USER, data)
                finish()
            }
        }

        if (requestCode == Constant.REQ_BUKA_REKENING) {
            if (resultCode == RESULT_OK) {
                this.setResult(RESULT_OK, data)
                finish()
            } else if (resultCode == RESULT_CANCELED && data != null) {
                this.setResult(RESULT_CANCELED, data)
                finish()
            } else if (resultCode == RESULT_FIRST_USER && data != null) {
                this.setResult(RESULT_FIRST_USER, data)
                finish()
            }
        }
    }
}