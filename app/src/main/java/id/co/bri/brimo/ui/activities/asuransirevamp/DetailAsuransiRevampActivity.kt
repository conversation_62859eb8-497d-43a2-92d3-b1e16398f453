package id.co.bri.brimo.ui.activities.asuransirevamp

import android.app.Activity
import android.app.DownloadManager
import android.content.ClipData
import android.content.ClipboardManager
import android.content.Intent
import android.net.Uri
import android.os.Bundle
import android.os.Environment
import android.util.Log
import android.view.View
import androidx.core.app.ActivityCompat
import androidx.recyclerview.widget.LinearLayoutManager
import id.co.bri.brimo.R
import id.co.bri.brimo.adapters.DataTransaksiRevampAdapter
import id.co.bri.brimo.adapters.asuransi.HelpAsuransiAdapter
import id.co.bri.brimo.databinding.ActivityDetailAsuransi2Binding
import id.co.bri.brimo.domain.config.Constant
import id.co.bri.brimo.domain.helpers.GeneralHelper
import id.co.bri.brimo.models.apimodel.response.asuransi.DetailAsuransiResponse
import id.co.bri.brimo.ui.activities.base.BaseActivity
import id.co.bri.brimo.ui.activities.sbn.PreviewMemorandumActivity
import id.co.bri.brimo.ui.activities.sbn.TermAndConditionESBNActivity
import java.net.MalformedURLException
import java.net.URL
import java.text.SimpleDateFormat
import java.util.*

class DetailAsuransiRevampActivity : BaseActivity(), View.OnClickListener {
    private lateinit var binding : ActivityDetailAsuransi2Binding
    private var myClipboard: ClipboardManager? = null
    private var myClip: ClipData? = null
    private var downloadManager: DownloadManager? = null
    private var filename: String? = null
    private var url: URL? = null
    private var downloadID: Long = 0

    companion object{
        var mResponse : DetailAsuransiResponse? =null
        fun launchIntent(caller: Activity, fromFastMenu: Boolean, response: DetailAsuransiResponse) {
            val intent = Intent(caller, DetailAsuransiRevampActivity::class.java)
            isFromFastMenu = fromFastMenu
            mResponse = response
            caller.startActivityForResult(intent, Constant.REQ_PAYMENT)
        }
    }
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        binding = ActivityDetailAsuransi2Binding.inflate(layoutInflater)
        setContentView(binding.root)

        setupView()
        setListener()
    }



    private fun setupView() {
        //toolbar
        GeneralHelper.setToolbarRevamp(this,binding.tbDetailAsuransi.toolbar , "Asuransi")

        //image asuransi
        GeneralHelper.loadImageUrl(this, mResponse!!.insuranceTypeIcon,binding.ivAsuransi,R.drawable.bri,0)
        GeneralHelper.loadImageUrl(this, mResponse!!.helpSection!!.companyImageUrl,binding.ivBantuan,R.drawable.bri,0)

        // Data TextView
        binding.tvTitleAsuransi.text = mResponse!!.productName
        binding.tvPolisMulaiTanggal.text = mResponse!!.coveringStart
        binding.tvPolisAkhirTanggal.text = mResponse!!.coveringEnd
        binding.tvKantor.text = mResponse!!.helpSection!!.companyName
        binding.tvAlamat.text = mResponse!!.helpSection!!.companyAddress

        binding.rvDetail.layoutManager = LinearLayoutManager(this)
        binding.rvDetail.adapter = DataTransaksiRevampAdapter( mResponse!!.detailSection, this)

        binding.rvBantuan.layoutManager = LinearLayoutManager(this)
        binding.rvBantuan.adapter = HelpAsuransiAdapter( this, mResponse!!.helpSection!!.companyContact!!)
//        registerReceiver(onDownloadComplete, IntentFilter(DownloadManager.ACTION_DOWNLOAD_COMPLETE))
    }

    private fun setListener() {
        binding.llPanduan.setOnClickListener(this)
        binding.ivArrowPanduan.setOnClickListener(this)
        binding.rlInformasi.setOnClickListener(this)
        binding.ivCopy.setOnClickListener(this)
        binding.ivDownload.setOnClickListener(this)
    }

    override fun onClick(p0: View?) {
        when (p0!!.id){
            R.id.ll_panduan -> {
                PanduanClaimAsuransiActivity.launchIntent(this, mResponse!!.claimSection!!)
            }
            R.id.iv_arrow_panduan -> {
                PanduanClaimAsuransiActivity.launchIntent(this, mResponse!!.claimSection!!)
            }
            R.id.rl_informasi -> {
                InformasiProductAsuransiActivity.launchIntent(this, false,mResponse!!.informationSection!!)
            }
            R.id.iv_copy -> {
                myClipboard = getSystemService(CLIPBOARD_SERVICE) as ClipboardManager
                val text: String
                text = binding.tvNoPolis.text.toString()
                myClip = ClipData.newPlainText("text", text)
                myClipboard!!.setPrimaryClip(myClip!!)
                showSnackbarErrorMessage("Nomor e-Polis berhasil disalin", ALERT_CONFIRM, this, false)
            }
            R.id.iv_download -> {
               requestPermission()
            }
        }
    }

    fun requestPermission() {
        if (!hasPermissions(this, *PERMISSIONS)) {
            ActivityCompat.requestPermissions(this, PERMISSIONS, PERMISSIONS_ALL)
        } else {
            val urlPdf: String = mResponse!!.converingUrl!!
            try {
                url = URL(urlPdf)
            } catch (e: MalformedURLException) {
                if (!GeneralHelper.isProd()) {
                    Log.e("TAG", "generateTransaksiModel: ", e)
                }
            }

            val timeStamp = SimpleDateFormat("yyyyMMdd_HHmmss", Locale.getDefault()).format(Date())
            val filename2 = url!!.path.substring(url!!.path.lastIndexOf('/') + 1)
            val names: Array<String> = filename2.split("\\.".toRegex()).dropLastWhile { it.isEmpty() }.toTypedArray()
            filename = names[0] + "-" + timeStamp + ".pdf"
            callDownloadManager()
        }
    }

    private fun callDownloadManager() {
        downloadManager = getSystemService(DOWNLOAD_SERVICE) as DownloadManager
        val request = DownloadManager.Request(Uri.parse(url.toString() + ""))
        request.setAllowedNetworkTypes(DownloadManager.Request.NETWORK_WIFI or
                DownloadManager.Request.NETWORK_MOBILE)
        request.setTitle(filename)
        request.allowScanningByMediaScanner()
        request.setAllowedOverMetered(true)
        request.setNotificationVisibility(DownloadManager.Request.VISIBILITY_VISIBLE_NOTIFY_COMPLETED)
        request.setDestinationInExternalPublicDir(Environment.DIRECTORY_DOWNLOADS, filename)
        request.setMimeType("application/pdf")
        downloadID = downloadManager!!.enqueue(request)
    }

}