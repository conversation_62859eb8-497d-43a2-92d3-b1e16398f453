package id.co.bri.brimo.ui.fragments.dashboardLifestyle

import android.annotation.SuppressLint
import android.app.Activity
import android.content.Intent
import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.google.android.material.bottomsheet.BottomSheetDialogFragment
import id.co.bri.brimo.R
import id.co.bri.brimo.adapters.dashboardLifestyle.SubmenuLifestyleAdapter
import id.co.bri.brimo.contract.IPresenter.lifestyle.ISubmenuLifestylePresenter
import id.co.bri.brimo.contract.IView.lifestyle.ISubmenuLifestyleView
import id.co.bri.brimo.databinding.FragmentSubmenuLifestyleBinding
import id.co.bri.brimo.domain.config.Constant
import id.co.bri.brimo.domain.helpers.GeneralHelper
import id.co.bri.brimo.models.apimodel.request.PartnerIdRequest
import id.co.bri.brimo.models.apimodel.response.EmptyStateResponse
import id.co.bri.brimo.models.apimodel.response.ExceptionResponse
import id.co.bri.brimo.models.apimodel.response.GeneralResponse
import id.co.bri.brimo.models.apimodel.response.GeneralWebviewResponse
import id.co.bri.brimo.models.apimodel.response.lifestyle.EODLifestyleResponse
import id.co.bri.brimo.models.apimodel.response.lifestyle.FeatureDataView
import id.co.bri.brimo.models.daomodel.lifestyle.MenuLifestyle
import id.co.bri.brimo.ui.activities.base.BaseActivity
import id.co.bri.brimo.ui.activities.lifestyle.WebviewLifestyleActivity
import id.co.bri.brimo.ui.fragments.lifestyle.DialogEODLifestyle
import javax.inject.Inject

class SubMenuLifestyleFragment : BottomSheetDialogFragment(),
    ISubmenuLifestyleView,
    DialogEODLifestyle.DialogDefaultListener {

    private lateinit var binding: FragmentSubmenuLifestyleBinding

    @Inject
    lateinit var presenter: ISubmenuLifestylePresenter<ISubmenuLifestyleView>

    private var submenuLifestyleAdapter: SubmenuLifestyleAdapter? = null
    private var dialogEOD: DialogEODLifestyle? = null

    companion object {

        private lateinit var mFeatureDataView: List<FeatureDataView>

        @JvmStatic
        fun newInstance(featureDataView: List<FeatureDataView>) : SubMenuLifestyleFragment {
            val fragment = SubMenuLifestyleFragment()
            val args = Bundle()
            mFeatureDataView = featureDataView

            fragment.arguments = args
            return fragment
        }
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setStyle(STYLE_NORMAL, R.style.CustomBottomSheetDialogThemeMaterial)
    }

    override fun onCreateView(
        inflater: LayoutInflater, container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        // Inflate the layout for this fragment
        binding = FragmentSubmenuLifestyleBinding.inflate(inflater, container, false)
        return binding.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        injectDependency()
        setupView()
    }

    private fun injectDependency() {
        (activity as BaseActivity?)?.activityComponent?.inject(this)
        presenter.view = this
        presenter.setUrlWebviewTugu(GeneralHelper.getString(R.string.url_webview_new))
        presenter.getMenuLifestyle()
        presenter.start()
    }

    @SuppressLint("NotifyDataSetChanged")
    private fun setupView(){
        with(binding) {
            submenuLifestyleAdapter = SubmenuLifestyleAdapter(
                onSubmenuClick = {
                    presenter.getWebViewTugu(
                        PartnerIdRequest(it.partnerId),
                        it.subFeatureName,
                        it.featureCode
                    )
                },

                onUpdateFlagNewMenu = {
                    presenter.onUpdateFlagNewMenu(it.featureCode)
                }
            )

            rvSubmenu.setLayoutManager(
                LinearLayoutManager(
                    context,
                    RecyclerView.VERTICAL,
                    false
                )
            )
            submenuLifestyleAdapter?.setDataMenuLifestyles(mFeatureDataView.toMutableList())
            submenuLifestyleAdapter?.notifyDataSetChanged()
            rvSubmenu.apply {
                setHasFixedSize(true)
                adapter = submenuLifestyleAdapter
            }
        }
    }

    override fun onSuccessGetWebviewTugu(
        generalWebviewResponse: GeneralWebviewResponse,
        mTitle: String,
        mCodeMenu: String
    ) {
        dismiss()
        WebviewLifestyleActivity.launchIntent(
            requireActivity(),
            generalWebviewResponse.webviewData.url,
            generalWebviewResponse.sessionId,
            mTitle,
            "",
            "",
            mCodeMenu,
            generalWebviewResponse.webviewData.postData
        )
    }

    @SuppressLint("NotifyDataSetChanged")
    override fun onSuccessGetMenuLocals(menuLifestyle: List<MenuLifestyle>) {
        submenuLifestyleAdapter?.setMenuLifestyleLocal(menuLifestyle)
        submenuLifestyleAdapter?.notifyDataSetChanged()
    }

    override fun onMenuLifestyleEOD(exceptionCase02: EODLifestyleResponse) {
        dialogEOD = DialogEODLifestyle.newInstance(
            this,
            exceptionCase02,
            GeneralHelper.getString(R.string.img_time_out)
        )
        dialogEOD?.show(childFragmentManager, "")
    }

    override fun onRootedDevice() {

    }

    override fun showProgress() {
        GeneralHelper.showDialog(requireActivity())
    }

    override fun hideProgress() {
        GeneralHelper.dismissDialog()
    }

    override fun onSessionEnd(message: String?) {

    }

    override fun onException(message: String?) {
        dismiss()
        try {
            if (GeneralHelper.isContains(Constant.LIST_TYPE_GAGAL, message)) {
                GeneralHelper.showBottomDialog(activity, message)
            } else if (activity != null) {
                GeneralHelper.showSnackBarRevamp(binding.rvSubmenu, message)
            }
        } catch (e: Exception) {
            // donothing
        }
    }

    override fun onExceptionRevamp(message: String?) {
        dismiss()
    }

    override fun onException06(response: ExceptionResponse?) {
        dismiss()

    }

    override fun onException99(message: String?) {
        dismiss()
    }

    override fun onExceptionFO(response: EmptyStateResponse?) {
        dismiss()
    }

    override fun onExceptionLimitExceed(response: GeneralResponse?) {
        //do nothing
    }

    override fun onExceptionNoBackAction(message: String?) {
        //do nothing
    }

    override fun onExceptionStatusNotMatch() {
        val intent = Intent()
        intent.putExtra(
            Constant.STATUS_NOT_MATCH,
            Constant.STATUS_NOT_MATCH
        )
        intent.putExtra(
            Constant.NAME,
            Constant.REGISTRATION_BRIMO
        )
        intent.putExtra(
            Constant.CHECK_POINT,
            Constant.CHECK_POINT_DEFAULT
        )
        activity?.setResult(Activity.RESULT_CANCELED, intent)
        activity?.finish()
    }

    override fun onClickDialogEOD() {
        dialogEOD?.dismiss()
    }

}