package id.co.bri.brimo.ui.fragments.saldodompetdigital;

import android.os.Build;
import android.os.Bundle;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.fragment.app.DialogFragment;
import androidx.recyclerview.widget.GridLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.google.android.material.bottomsheet.BottomSheetDialogFragment;

import java.util.ArrayList;
import java.util.List;

import id.co.bri.brimo.R;
import id.co.bri.brimo.adapters.option.OptionNominalAdapter;
import id.co.bri.brimo.databinding.FragmentBottomSmartTopupBinding;
import id.co.bri.brimo.models.apimodel.response.PulsaList;
import id.co.bri.brimo.models.optionmodel.OptionNominalModel;

public class FragmentBottomSmartTopUp extends BottomSheetDialogFragment implements OptionNominalAdapter.onAddButtonListener {

    private FragmentBottomSmartTopupBinding binding;

    private List<OptionNominalModel> mNominalModelsList = new ArrayList<>();
    private OptionNominalAdapter pulsaAdapter;
    private int mOptionType;
    private String mLabel;

    private selectedOptionListener selectedOptionListener = null;

    public FragmentBottomSmartTopUp() {
    }

    public FragmentBottomSmartTopUp(List<OptionNominalModel> nominalModelsList, int optionType, String label) {
        this.mLabel = label;
        this.mNominalModelsList = nominalModelsList;
        this.mOptionType = optionType;
    }


    @Override
    public void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setStyle(DialogFragment.STYLE_NORMAL, R.style.CustomBottomSheetDialogTheme);
    }

    @Override
    public View onCreateView(LayoutInflater inflater, ViewGroup container,
                             Bundle savedInstanceState) {
binding = FragmentBottomSmartTopupBinding.inflate(inflater, container, false);
        return binding.getRoot();
    }

    @Override
    public void onViewCreated(@NonNull View view, @Nullable Bundle savedInstanceState) {
        super.onViewCreated(view, savedInstanceState);
        generateAdapter(mNominalModelsList);
        binding.tvLabel.setText(mLabel);
    }

    public void generateAdapter(List<OptionNominalModel> nominalModels) {
        mNominalModelsList = nominalModels;
        pulsaAdapter = new OptionNominalAdapter(mNominalModelsList, getActivity(), this);
        pulsaAdapter.notifyItemRangeChanged(0, mNominalModelsList.size());
        binding.rvOpsi.setLayoutManager(new GridLayoutManager(getActivity(), 2, RecyclerView.VERTICAL, false));
        binding.rvOpsi.setHasFixedSize(true);
        binding.rvOpsi.setAdapter(pulsaAdapter);

        pulsaAdapter.notifyDataSetChanged();
    }

    private List<OptionNominalModel> parsePulsaList(List<PulsaList> listNominalText) {
        List<OptionNominalModel> mNominalModelsList = new ArrayList<>();
        for (PulsaList pulsaItem : listNominalText) {
            mNominalModelsList.add(new OptionNominalModel(pulsaItem.getText(),pulsaItem.getValue()));
        }

        return mNominalModelsList;
    }

    @Override
    public void onDestroyView() {
        binding = null;
        super.onDestroyView();
    }

    @Override
    public void callback(int harga, int position) {
        clearSelectedOption();
        mNominalModelsList.get(position).setBol(false);
        pulsaAdapter.notifyDataSetChanged();

        if(selectedOptionListener!= null){
            selectedOptionListener.onOptionSelected(mNominalModelsList.get(position), mOptionType);
        }
    }

    public void clearSelectedOption(){
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.N) {
            mNominalModelsList.forEach((p) -> p.setBol(true));
        } else {
            for (OptionNominalModel p : mNominalModelsList) {
                p.setBol(true);
            }
        }
        pulsaAdapter.notifyDataSetChanged();
    }

    public void setSelectedOptionListener(FragmentBottomSmartTopUp.selectedOptionListener selectedOptionListener) {
        this.selectedOptionListener = selectedOptionListener;
    }

    public interface selectedOptionListener {
        void onOptionSelected(OptionNominalModel nominal, int optionType);
    }

}
