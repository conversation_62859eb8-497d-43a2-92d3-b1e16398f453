package id.co.bri.brimo.ui.activities.onboardingrevamp

import android.app.Activity
import android.content.Intent
import android.os.Bundle
import android.os.Handler
import android.os.Looper
import android.text.Editable
import android.text.TextWatcher
import android.view.View
import androidx.activity.OnBackPressedCallback
import androidx.activity.result.ActivityResultLauncher
import androidx.activity.result.contract.ActivityResultContracts
import id.co.bri.brimo.R
import id.co.bri.brimo.contract.IPresenter.onboardingrevamp.IOnboardingUserPassPresenter
import id.co.bri.brimo.contract.IView.onboardingrevamp.IOnboardingUserPassView
import id.co.bri.brimo.databinding.ActivityRegistrasiUserPassBinding
import id.co.bri.brimo.domain.config.Constant
import id.co.bri.brimo.domain.helpers.GeneralHelper
import id.co.bri.brimo.domain.helpers.ValidationHelper
import id.co.bri.brimo.models.apimodel.request.onboardingrevamp.OnboardingUserPasRequest
import id.co.bri.brimo.models.apimodel.request.onboardingrevamp.OnboardingUsernameRequest
import id.co.bri.brimo.models.apimodel.response.onboardingrevamp.OnboardingUserResponse
import id.co.bri.brimo.ui.activities.base.BaseActivity
import javax.inject.Inject

class OnboardingUserPassActivity : BaseActivity(),
    IOnboardingUserPassView {

    @Inject
    lateinit var presenter: IOnboardingUserPassPresenter<IOnboardingUserPassView>

    private lateinit var binding: ActivityRegistrasiUserPassBinding

    private var boolUser: Boolean = false
    private var boolPass: Boolean = false
    private var boolAvailable = false

    private var tempUser: String = ""
    private var onboardingId = ""

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)

        binding = ActivityRegistrasiUserPassBinding.inflate(layoutInflater)
        setContentView(binding.root)

        onBackPressedDispatcher.addCallback(this, onBackPressedCallback)

        injectDependency()
        setupView()
    }

    private fun injectDependency() {
        activityComponent.inject(this)
        presenter.view = this
        presenter.setUrlUsername(GeneralHelper.getString(R.string.url_onboarding_check_username_v3))
        presenter.setUrlUserPass(GeneralHelper.getString(R.string.url_onboarding_set_password_v3))
        onboardingId = presenter.getDeviceId()
        presenter.start()
    }

    private fun setupView() {
        GeneralHelper.setToolbarRevamp(
            this,
            binding.toolbarRevamp.toolbar,
            GeneralHelper.getString(R.string.buat_username_and_passsword)
        )

        layoutStepGone()
        validateNameUser()
        validateKunciPass()
        validateConfirmKunciPass()

        binding.btnSubmit.setOnClickListener {
            presenter.sendUserPass(OnboardingUserPasRequest(onboardingId, getUser(), getPass()))
        }
    }

    private fun validateNameUser() {
        binding.etUsername.setOnFocusChangeListener { _, b ->
            if (b) binding.layoutUsername.visibility = View.VISIBLE
            else {
                if (getUser().isEmpty()) {
                    binding.layoutUsername.visibility = View.GONE
                }
                if (getUser().length >= 12)
                    presenter.sendUserCheck(OnboardingUsernameRequest(onboardingId, getUser()))
            }
        }

        binding.etUsername.addTextChangedListener(textUserWatcher)
    }

    private val textUserWatcher = object : TextWatcher {
        override fun beforeTextChanged(s: CharSequence?, start: Int, count: Int, after: Int) {
            // do nothing
        }

        override fun onTextChanged(s: CharSequence?, start: Int, before: Int, count: Int) {
            if (!s.toString().contains(" ") &&
                s!!.isNotEmpty()
            ) binding.imgSpasi.setImageResource(R.drawable.checkbox_on)
            else binding.imgSpasi.setImageResource(R.drawable.checkbox_off)

            if (s!!.length >= 12) binding.imgKarakter.setImageResource(R.drawable.checkbox_on)
            else binding.imgKarakter.setImageResource(R.drawable.checkbox_off)

            if (ValidationHelper.isAlphaNumericUsername(s.toString())) binding.imgAngkaHuruf.setImageResource(
                R.drawable.checkbox_on
            )
            else binding.imgAngkaHuruf.setImageResource(R.drawable.checkbox_off)

            if (s.length < 12) {
                boolAvailable = false
                binding.imgUsername.setImageResource(R.drawable.checkbox_off)
            }

            Handler(Looper.getMainLooper()).postDelayed({
                if (s.length >= 12 && getUser() != tempUser) {
                    tempUser = getUser()
                    presenter.sendUserCheck(
                        OnboardingUsernameRequest(onboardingId, getUser())
                    )
                }
            }, 2000)

            boolUser = (!s.toString().contains(" ") && s.isNotEmpty() && s.length >= 12
                    && ValidationHelper.isAlphaNumericUsername(s.toString()))

            validationButton()
        }

        override fun afterTextChanged(s: Editable?) {
            // do nothing
        }

    }

    private fun validateKunciPass() {
        binding.etPassword.setOnFocusChangeListener { _, b ->
            if (b) binding.layoutPassword.visibility = View.VISIBLE
            else if (getPass().isEmpty()) binding.layoutPassword.visibility = View.GONE
        }

        binding.etPassword.addTextChangedListener(textPassWatcher)
    }

    private val textPassWatcher = object : TextWatcher {
        override fun beforeTextChanged(s: CharSequence?, start: Int, count: Int, after: Int) {
            // do nothing
        }

        override fun onTextChanged(s: CharSequence?, start: Int, before: Int, count: Int) {
            if (s.toString() != getUser() && !s.toString()
                    .contains(" ") && s!!.isNotEmpty()
            ) binding.imgTidakSama.setImageResource(R.drawable.checkbox_on)
            else binding.imgTidakSama.setImageResource(R.drawable.checkbox_off)

            if (ValidationHelper.isAlphaNumericPassword(s.toString())) binding.imgKapital.setImageResource(
                R.drawable.checkbox_on
            )
            else binding.imgKapital.setImageResource(R.drawable.checkbox_off)

            if (s!!.length >= 8) binding.imgKarakter2.setImageResource(R.drawable.checkbox_on)
            else binding.imgKarakter2.setImageResource(R.drawable.checkbox_off)

            if (s.toString() != getUser() && !s.toString()
                    .contains(" ") && s.isNotEmpty() && ValidationHelper.isAlphaNumericPassword(s.toString()) && s.length >= 8
            ) {
                boolPass = true
                binding.etUlangiPass.isEnabled = true
            } else {
                boolPass = false
                binding.etUlangiPass.isEnabled = false
            }

            if (s.toString() != getRepeatPass())
                binding.tvErrorUlangi.visibility = View.VISIBLE
            else binding.tvErrorUlangi.visibility = View.GONE


            validationButton()
        }

        override fun afterTextChanged(s: Editable?) {
            // do nothing
        }
    }

    private fun validateConfirmKunciPass() {
        binding.etUlangiPass.addTextChangedListener(textConfirmPassWatcher)
    }

    private val textConfirmPassWatcher = object : TextWatcher {
        override fun beforeTextChanged(s: CharSequence?, start: Int, count: Int, after: Int) {
            // do nothing
        }

        override fun onTextChanged(s: CharSequence?, start: Int, before: Int, count: Int) {
            if (s.toString() != getPass())
                binding.tvErrorUlangi.visibility = View.VISIBLE
            else binding.tvErrorUlangi.visibility = View.GONE

            validationButton()
        }

        override fun afterTextChanged(s: Editable?) {
            // do nothing
        }
    }

    private fun layoutStepGone() {
        binding.layoutStep.imgStep1.visibility = View.GONE
        binding.layoutStep.imgStep2.visibility = View.GONE
        binding.layoutStep.imgStep3.visibility = View.GONE
        binding.layoutStep.imgStep4.visibility = View.GONE
        binding.tvStep.visibility = View.GONE
    }

    private fun getUser(): String {
        return binding.etUsername.text.toString()
    }

    private fun getPass(): String {
        return binding.etPassword.text.toString()
    }

    private fun getRepeatPass(): String {
        return binding.etUlangiPass.text.toString()
    }

    private fun validationButton() {
        if (boolUser && boolPass && (getPass() == getRepeatPass()) && boolAvailable) {
            binding.btnSubmit.alpha = 1f
            binding.btnSubmit.isEnabled = true
        } else {
            binding.btnSubmit.alpha = 0.3f
            binding.btnSubmit.isEnabled = false
        }
    }

    override fun onSuccessCheckUser(onboardUserResponse: OnboardingUserResponse) {
        boolAvailable = onboardUserResponse.available
        if (boolAvailable) binding.imgUsername.setImageResource(R.drawable.checkbox_on)
        else binding.imgUsername.setImageResource(R.drawable.checkbox_off)

        validationButton()
    }

    override fun onSuccessUserPass() {
        val intent = Intent(this, OnboardingPinActivity::class.java)
        startActivityIntent.launch(intent)
    }

    override fun onExceptionUserExist() {
        boolAvailable = false
        binding.imgUsername.setImageResource(R.drawable.checkbox_off)

        validationButton()
    }

    private var startActivityIntent: ActivityResultLauncher<Intent> = registerForActivityResult(
        ActivityResultContracts.StartActivityForResult()
    ) { result ->
        if (result.resultCode == Activity.RESULT_CANCELED && result.data != null) {
            setResult(RESULT_CANCELED, result.data)
            finish()
        }
    }


    private val onBackPressedCallback: OnBackPressedCallback =
        object : OnBackPressedCallback(true) {
            override fun handleOnBackPressed() {
                val intent = Intent()
                intent.putExtra(Constant.CHECK_POINT, 9)
                setResult(Activity.RESULT_CANCELED, intent)
                finish()
            }
        }

    override fun onDestroy() {
        presenter.stop()
        super.onDestroy()
    }
}