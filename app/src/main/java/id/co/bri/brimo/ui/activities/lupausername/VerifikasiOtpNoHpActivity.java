package id.co.bri.brimo.ui.activities.lupausername;

import android.app.Activity;
import android.content.Intent;
import android.os.Bundle;

import androidx.annotation.NonNull;
import androidx.core.text.HtmlCompat;
import androidx.fragment.app.FragmentTransaction;

import javax.inject.Inject;

import id.co.bri.brimo.R;
import id.co.bri.brimo.contract.IPresenter.lupausername.IVerifikasiOtpNoHpPresenter;
import id.co.bri.brimo.contract.IView.lupausername.IVerifikasiOtpNoHpView;
import id.co.bri.brimo.domain.config.Constant;
import id.co.bri.brimo.domain.helpers.GeneralHelper;
import id.co.bri.brimo.models.apimodel.request.forgetuserpass.ValidateOtpUserPassReq;
import id.co.bri.brimo.models.apimodel.request.pengelolaankartu.ResendOtpReissueReq;
import id.co.bri.brimo.models.apimodel.response.forgetuserpass.OtpEmailRes;
import id.co.bri.brimo.models.apimodel.response.forgetuserpass.OtpNoHpRes;
import id.co.bri.brimo.models.apimodel.response.pengelolaankartu.OtpReissueResponse;
import id.co.bri.brimo.ui.activities.base.BaseOtpActivity;
import id.co.bri.brimo.ui.customviews.dialog.DialogExitCustom;
import id.co.bri.brimo.ui.fragments.registrasi.BottomDialogKirimUlangFragment;

public class VerifikasiOtpNoHpActivity extends BaseOtpActivity implements
        IVerifikasiOtpNoHpView,
        BottomDialogKirimUlangFragment.OnBackPressUlangi,
        DialogExitCustom.DialogDefaultListener {

    @Inject
    IVerifikasiOtpNoHpPresenter<IVerifikasiOtpNoHpView> presenter;

    private static OtpNoHpRes otpResponse;

    private static boolean isForgetUsername = false;

    public static void launchIntent(Activity caller, OtpNoHpRes response, boolean isUser) {
        Intent intent = new Intent(caller, VerifikasiOtpNoHpActivity.class);
        otpResponse = response;
        isForgetUsername = isUser;
        caller.startActivityForResult(intent, Constant.REQ_FORGET);
    }

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        injectDependency();
        handleTimer();
        setupViews();
    }

    private void setupViews() {
        infoMessageOtp(GeneralHelper.getString(R.string.tidak_terima_otp));
        checkWaOrSms(otpResponse.getMethod());
    }

    private void handleTimer() {
        if (otpResponse.getExpiredInSecond() != null)
            runTimer(otpResponse.getExpiredInSecond());
        else
            runTimer(0);

        if (otpResponse.getResendInSecond() != null)
            timerResend(otpResponse.getResendInSecond());
        else timerResend(0);
    }

    private void checkWaOrSms(String method) {
        if (method.equalsIgnoreCase("WA")) {
            binding.tvDescOtp.setText(HtmlCompat.fromHtml(String.format(GeneralHelper.getString(R.string.desc_regis_otp_wa),
                    otpResponse.getPhone()), HtmlCompat.FROM_HTML_MODE_LEGACY));
        } else {
            binding.tvDescOtp.setText(HtmlCompat.fromHtml(String.format(GeneralHelper.getString(R.string.desc_regis_otp_sms),
                    otpResponse.getPhone()), HtmlCompat.FROM_HTML_MODE_LEGACY));
        }
    }

    @Override
    protected void injectDependency() {
        getActivityComponent().inject(this);
        if (presenter != null) {
            presenter.setView(this);
            presenter.start();
            if (isForgetUsername) {
                presenter.setUrlValidate(GeneralHelper.getString(R.string.url_forget_username_validate_otp_v2));
                presenter.setUrlResend(GeneralHelper.getString(R.string.url_forget_username_resend_otp_v2));
            } else {
                presenter.setUrlValidate(GeneralHelper.getString(R.string.url_forget_password_validate_otp_v2));
                presenter.setUrlResend(GeneralHelper.getString(R.string.url_forget_password_resend_otp_v2));
            }
        }
    }

    @Override
    protected String getTitleBar() {
        return GeneralHelper.getString(R.string.verifikasi_no_handphone_regis);
    }

    @Override
    protected void doOtpComplete() {
        presenter.validateOtp(new ValidateOtpUserPassReq(otpResponse.getReferenceNumber(), otp, otpResponse.getType()));
    }

    @Override
    protected void doResendOtp() {
        BottomDialogKirimUlangFragment fragment = new BottomDialogKirimUlangFragment(this);
        fragment.show(getSupportFragmentManager(), "");
        fragment.setCancelable(true);
    }

    @Override
    protected String getTitleOtp() {
        return GeneralHelper.getString(R.string.masukkan_kode_otp);
    }

    @Override
    public void onSuccessValidateOtp(OtpEmailRes otpEmailRes) {
        VerifikasiOtpEmailActivity.launchIntent(this, otpEmailRes, isForgetUsername);
    }

    @Override
    public void onSuccessResendOtp(OtpReissueResponse otpReissueResponse) {
        if (otpReissueResponse.getExpiredInSecond() != null)
            runTimer(otpReissueResponse.getExpiredInSecond());
        else
            runTimer(0);

        checkWaOrSms(otpReissueResponse.getMethod());
    }

    @Override
    public void deleteInputOtp() {
        deleteOtp();
    }

    @Override
    public void onExceptionTrxExpired(String errorMessage) {
        Intent i = new Intent();
        i.putExtra(Constant.TAG_ERROR_MESSAGE, errorMessage);
        setResult(Activity.RESULT_CANCELED, i);
        finish();
    }

    @Override
    public void itemResending(@NonNull String method) {
        presenter.resendOtp(new ResendOtpReissueReq(method, otpResponse.getReferenceNumber()));
    }

    @Override
    public void onBackPressed() {
        DialogExitCustom dialogExitCustom = new DialogExitCustom(this,
                GeneralHelper.getString(R.string.title_dialog_nohp_exit),
                GeneralHelper.getString(R.string.desc_dialog_nohp_exit));
        FragmentTransaction ft = this.getSupportFragmentManager().beginTransaction();
        ft.add(dialogExitCustom, null);
        ft.commitAllowingStateLoss();
    }

    @Override
    public void onClickYes() {
        Intent i = new Intent();
        i.putExtra(Constant.TAG_CONFIRM_DIALOG, true);
        setResult(Activity.RESULT_CANCELED, i);
        finish();
    }

    @Override
    protected void onDestroy() {
        if (presenter != null)
            presenter.stop();
        super.onDestroy();
    }
}
