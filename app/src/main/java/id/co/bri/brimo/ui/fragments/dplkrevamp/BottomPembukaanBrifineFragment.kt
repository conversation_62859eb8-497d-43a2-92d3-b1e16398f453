package id.co.bri.brimo.ui.fragments.dplkrevamp

import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import com.google.android.material.bottomsheet.BottomSheetDialogFragment
import id.co.bri.brimo.R
import id.co.bri.brimo.databinding.FragmentBottomPembukaanBrifineBinding


class BottomPembukaanBrifineFragment(
    private val title: String,
    private val description: String,
    private val wordingButton : String,
) : BottomSheetDialogFragment() {

    lateinit var binding : FragmentBottomPembukaanBrifineBinding

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setStyle(STYLE_NORMAL, R.style.CustomBottomSheetDialogThemeInput)

    }

    override fun onCreateView(
        inflater: LayoutInflater, container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View? {
        // Inflate the layout for this fragment
        binding = FragmentBottomPembukaanBrifineBinding.inflate(inflater,container,false)
        return binding.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)

        setupView()
    }

    private fun setupView() {
        with(binding){
            tvTitle.text = title
            tvDesc.text = description
            btnClose.text = wordingButton

            btnClose.setOnClickListener {
                dismiss()
            }
        }
    }

}