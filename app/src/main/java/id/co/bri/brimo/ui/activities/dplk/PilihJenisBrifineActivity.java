package id.co.bri.brimo.ui.activities.dplk;

import android.app.Activity;
import android.content.Intent;
import android.os.Bundle;
import android.view.View;
import android.widget.LinearLayout;

import androidx.annotation.Nullable;
import androidx.fragment.app.Fragment;
import androidx.fragment.app.FragmentTransaction;
import androidx.viewpager.widget.ViewPager;

import com.google.gson.Gson;

import java.util.ArrayList;
import java.util.List;

import id.co.bri.brimo.R;
import id.co.bri.brimo.adapters.CatatanKeuanganAdapter;
import id.co.bri.brimo.adapters.PilihJenisBrifineAdapter;
import id.co.bri.brimo.databinding.ActivityPilihJenisBrifineBinding;
import id.co.bri.brimo.domain.config.Constant;
import id.co.bri.brimo.domain.helpers.GeneralHelper;
import id.co.bri.brimo.models.apimodel.request.dplk.BrifineKombinasiRequest;
import id.co.bri.brimo.models.apimodel.response.dplk.DplkFormResponse;
import id.co.bri.brimo.ui.activities.base.BaseActivity;
import id.co.bri.brimo.ui.customviews.dialog.DialogExitCustom;
import id.co.bri.brimo.ui.fragments.dplk.JenisBrifineFragment;

public class PilihJenisBrifineActivity extends BaseActivity implements ViewPager.OnPageChangeListener, View.OnClickListener, JenisBrifineFragment.onClickProduct, DialogExitCustom.DialogDefaultListener {

    private ActivityPilihJenisBrifineBinding binding;

    public static String codeProduct = "";
    public static String nameProduct = "";
    public static boolean mEdit;
    public static boolean combination;

    public static BrifineKombinasiRequest kombinasiRequest;

    private static final String TAG_FORM = "dataForm";
    private static final String TAG_CODE_PRODUCT = "codeProduct";
    private static final String TAG_NAME_PRODUCT = "nameProduct";

    private static DplkFormResponse response;

    private List<String> titleList;
    private List<Fragment> fragmentList;
    private LinearLayout lyTabs;
    private static String mCodeDefault;
    private CatatanKeuanganAdapter catatanKeuanganFragmentAdapter;

    public static void launchIntent(Activity caller, DplkFormResponse response, boolean edit , boolean combination1) {
        Intent i = new Intent(caller, PilihJenisBrifineActivity.class);
        i.putExtra(TAG_FORM, new Gson().toJson(response));
        mEdit = edit;
        combination = combination1;
        caller.startActivityForResult(i, Constant.REQ_PAYMENT);
    }

    public static void launchIntent(Activity caller, DplkFormResponse response, boolean edit,boolean combination1, String product, String name,String codeDefault) {
        Intent i = new Intent(caller, PilihJenisBrifineActivity.class);
        i.putExtra(TAG_FORM, new Gson().toJson(response));
        mEdit = edit;
        combination = combination1;
        codeProduct = product;
        nameProduct = name;
        mCodeDefault = codeDefault;
        caller.startActivityForResult(i, Constant.REQ_PAYMENT);
    }

    public static void launchIntent(Activity caller, DplkFormResponse response, boolean kombinasi, BrifineKombinasiRequest request) {
        Intent i = new Intent(caller, PilihJenisBrifineActivity.class);
        i.putExtra(TAG_FORM, new Gson().toJson(response));
        combination = kombinasi;
        mEdit = kombinasi;
        kombinasiRequest = request;
        codeProduct = request.getKodeProduct();
        nameProduct = request.getTitle();
        caller.startActivityForResult(i, Constant.REQ_PAYMENT);
    }

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        binding = ActivityPilihJenisBrifineBinding.inflate(getLayoutInflater());
        setContentView(binding.getRoot());

        GeneralHelper.setToolbar(this, binding.toolbar.toolbar, "Pilih Jenis BRIFINE");
        if (!mEdit) {
            binding.btnSubmit.setAlpha(0.3f);
            binding.btnSubmit.setEnabled(false);
        } else {
            binding.btnSubmit.setAlpha(1);
            binding.btnSubmit.setEnabled(true);
        }

        setupView();
        binding.btnSubmit.setOnClickListener(this);
    }

    private void setupView() {

        if (getIntent().hasExtra(TAG_FORM) && getIntent().getStringExtra(TAG_FORM) != null) {
            response = new Gson().fromJson(getIntent().getStringExtra(TAG_FORM), DplkFormResponse.class);
        }

        List<PilihJenisBrifineAdapter> fragmentAdapter = new ArrayList<>();
        for (int i = 0; i < response.getJenisBrifine().size(); i++) {
            PilihJenisBrifineAdapter adapter = new PilihJenisBrifineAdapter(this, response.getJenisBrifine().get(i).getProduct(),mEdit,codeProduct);
            fragmentAdapter.add(adapter);

        }

        fragmentList = new ArrayList<>();
        for (int i = 0; i < response.getJenisBrifine().size(); i++) {
            JenisBrifineFragment fragment = new JenisBrifineFragment(response.getJenisBrifine().get(i).getProduct(), this, fragmentAdapter.get(i));
            fragmentList.add(fragment);

        }

        titleList = new ArrayList<>();
        for (int i = 0; i < response.getJenisBrifine().size(); i++) {
            titleList.add(response.getJenisBrifine().get(i).getTitle());
        }

            catatanKeuanganFragmentAdapter = new CatatanKeuanganAdapter(getSupportFragmentManager(), this, fragmentList, titleList);
            binding.viewpager.setAdapter(catatanKeuanganFragmentAdapter);
            binding.viewpager.setOffscreenPageLimit(4);
            lyTabs = (LinearLayout) binding.viewpagertab.getChildAt(0);
            binding.viewpagertab.setViewPager(binding.viewpager);

            binding.viewpagertab.setOnPageChangeListener(this);
            GeneralHelper.changeTabsFontSimple(this, lyTabs, binding.viewpager.getCurrentItem());

        if (combination){
            BrifineKombinasiActivity.launchIntent(this, true, kombinasiRequest,true);
        }
    }

    @Override
    public void onPageScrolled(int position, float positionOffset, int positionOffsetPixels) {

    }

    @Override
    public void onPageSelected(int position) {
        GeneralHelper.changeTabsFontSimple(this, lyTabs, position);
        fragmentList.get(position).onResume();
        codeProduct = "";
        validationButton();
    }

    @Override
    public void onPageScrollStateChanged(int state) {

    }

    @Override
    public void onClick(View v) {
        switch (v.getId()) {
            case R.id.btnSubmit:

                if (Integer.parseInt(codeProduct) == 21) {
                    if (combination) {
                        BrifineKombinasiActivity.launchIntent(this, true, kombinasiRequest, true);
                    } else {
                        BrifineKombinasiActivity.launchIntent(this, nameProduct, codeProduct, false);
                    }
                } else {
                    Intent resultIntentOk = new Intent();
                    resultIntentOk.putExtra(TAG_CODE_PRODUCT, codeProduct);
                    resultIntentOk.putExtra(TAG_NAME_PRODUCT, nameProduct);
                    resultIntentOk.putExtra("editBoolean", true);
                    resultIntentOk.putExtra("combinationBoolean", false);
                    setResult(Constant.REQ_KATEGORI, resultIntentOk);
                    this.finish();
                }
                break;
        }
    }

    private void validationButton() {
        if (codeProduct.isEmpty()) {
            binding.btnSubmit.setAlpha(0.3f);
            binding.btnSubmit.setEnabled(false);
        } else {
            binding.btnSubmit.setAlpha(1);
            binding.btnSubmit.setEnabled(true);
        }
    }

    @Override
    protected void onActivityResult(int requestCode, int resultCode, @Nullable Intent data) {
        super.onActivityResult(requestCode, resultCode, data);
        if (requestCode == Constant.REQ_PAYMENT && data != null) {
            if (resultCode == Constant.REQ_KATEGORI) {
                setResult(Constant.REQ_PETTUNJUK1, data);
                binding.btnSubmit.setAlpha(0.3f);
                binding.btnSubmit.setEnabled(false);
                combination = data.getBooleanExtra("combinationBoolean", true);
                mEdit = data.getBooleanExtra("editBoolean", true);
                finish();
            }else {
                binding.btnSubmit.setAlpha(0.3f);
                binding.btnSubmit.setEnabled(false);
                combination = data.getBooleanExtra("combinationBoolean", true);
                mEdit = data.getBooleanExtra("editBoolean", true);
                kombinasiRequest = new Gson().fromJson(data.getStringExtra("kombinasiEdit"), BrifineKombinasiRequest.class);
                codeProduct = kombinasiRequest.getKodeProduct();
                nameProduct = kombinasiRequest.getTitle();
            }
        }
    }

    @Override
    public void onClickProduct(DplkFormResponse.JenisBrifine.Product code, int p) {
        codeProduct = code.getCode();
        nameProduct = code.getTitle();
        validationButton();
    }

    /**
     * Alert ketika klik Back Button
     */
    @Override
    public void onBackPressed() {
        if (mEdit) {
            if (!mCodeDefault.equalsIgnoreCase(nameProduct)) {
                DialogExitCustom dialogExitCustom = new DialogExitCustom(this::onClickYes, GeneralHelper.getString(R.string.title_dialog_exit_jenis_brifine), GeneralHelper.getString(R.string.content_dialog_exit_jenis_brifine), "Tidak", "Ya, Batalkan");
                FragmentTransaction ft = this.getSupportFragmentManager().beginTransaction();
                ft.add(dialogExitCustom, null);
                ft.commitAllowingStateLoss();
            } else super.onBackPressed();
        } else
            super.onBackPressed();

    }

    @Override
    public void onClickYes() {
        this.finish();
    }

    @Override
    protected void onDestroy() {
        super.onDestroy();
        binding = null;
    }
}