package id.co.bri.brimo.ui.activities.depositorevamp

import android.app.Activity
import android.content.ClipData
import android.content.ClipboardManager
import android.content.Intent
import android.os.Bundle
import android.text.Html
import android.util.Log
import android.view.LayoutInflater
import android.view.View
import androidx.core.text.HtmlCompat
import com.google.gson.Gson
import com.hookedonplay.decoviewlib.DecoView
import com.hookedonplay.decoviewlib.charts.SeriesItem
import com.hookedonplay.decoviewlib.events.DecoEvent
import id.co.bri.brimo.R
import id.co.bri.brimo.adapters.depositorevamp.ListItemDepositoOptionRevampAdapter
import id.co.bri.brimo.contract.IPresenter.depositorevamp.IDetailDepositoRevampPresenter
import id.co.bri.brimo.contract.IView.depositorevamp.IDetailDepositoRevampView
import id.co.bri.brimo.databinding.ActivityDetailDepositoRevampBinding
import id.co.bri.brimo.databinding.FragmentBottomSheetAturPerpanjanganDepositoRevampBinding
import id.co.bri.brimo.databinding.FragmentBottomSheetDetailDepositoRevampBinding
import id.co.bri.brimo.databinding.FragmentBottomSheetTidakAdaDepositoRevampBinding
import id.co.bri.brimo.domain.config.Constant
import id.co.bri.brimo.domain.helpers.GeneralHelper
import id.co.bri.brimo.models.ParameterKonfirmasiModel
import id.co.bri.brimo.models.apimodel.request.UpdatePerpanjanganDepositoRequest
import id.co.bri.brimo.models.apimodel.response.GeneralConfirmationResponse
import id.co.bri.brimo.models.apimodel.response.RenewalDepositoResponse
import id.co.bri.brimo.models.apimodel.response.depositorevamp.GetListDepositoResponse
import id.co.bri.brimo.models.apimodel.response.depositorevamp.InquiryPenutupanDepositoResponseRevamp
import id.co.bri.brimo.ui.activities.KonfirmasiGeneralRevampActivity
import id.co.bri.brimo.ui.activities.LupaPinActivity
import id.co.bri.brimo.ui.activities.base.BaseActivity
import id.co.bri.brimo.ui.activities.deposito.PenaltiPenutupanDepositoActivity
import id.co.bri.brimo.ui.fragments.PinFragment
import id.co.bri.brimo.ui.fragments.bottomsheet.BottomSheetCustomViewGeneralFragment
import javax.inject.Inject


class DetailDepositoRevampActivity : BaseActivity(), IDetailDepositoRevampView,
    PinFragment.SendPin {
    @Inject
    lateinit var presenter: IDetailDepositoRevampPresenter<IDetailDepositoRevampView>
    private lateinit var binding: ActivityDetailDepositoRevampBinding
    private val TAGDepo = "DetailDepositoRevampActivityTAG"
    private var account: GetListDepositoResponse.Accounts? = null
    private var listItemDepositoOptionRevampAdapter: ListItemDepositoOptionRevampAdapter? = null
    private val addOn = "A"

    companion object {
        fun launchIntent(caller: Activity, jsonDetail: GetListDepositoResponse.Accounts) {
            val intent = Intent(caller, DetailDepositoRevampActivity::class.java)
            val gson = Gson().toJson(jsonDetail)
            intent.putExtra("jsonDetail", gson)
            caller.startActivityForResult(intent, Constant.REQ_PAYMENT)
        }
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        binding = ActivityDetailDepositoRevampBinding.inflate(layoutInflater)
        setContentView(binding.root)
        injectDependency()

        initIntent()
        GeneralHelper.setToolbar(
            this,
            binding.tbReceipt.toolbar,
            getString(R.string.detail_deposito)
        )
        initUI()
        initOnClick()
        setStatusColor(R.color.toolbar_blue)
    }

    private fun initIntent() {
        account = Gson().fromJson(
            intent.getStringExtra("jsonDetail"),
            GetListDepositoResponse.Accounts::class.java
        )
    }

    private fun initUI() {
        binding.apply {
            account?.sourceAccount?.let { source ->
                textViewSourceName.text = source.accountName
                textViewNoRekening.text = source.accountNumberString
                tvInisial.text = GeneralHelper.formatInitialName(source.accountName)
            }
            account?.let {
                textViewBalance.text = it.balanceString
                textViewAccountNumber.text = it.accountNumberString
                textViewDueDate.text = it.dueDate?.value
                textViewStatus.text = it.renewalType
                tvJenisPerpanjangan.text = it.renewalType
                GeneralHelper.setSeekProgress(
                    sbCcSof,
                    it.remainingMatureDate?.percentage!!.toDouble(),
                    100.0
                )
                if (!it.isAro?.status!!) {
                    linearTidakDiperpanjang.show()
                    linearDiperpanjang.hide()
                } else {
                    if (it.isAro?.value == addOn) {
                        line.visibility = View.GONE
                        lyPerpanjangan.visibility = View.GONE
                        expandableLayout.visibility = View.GONE
                        linearAturEditPerpanjang.visibility = View.GONE
                        linearTidakDiperpanjang.hide()
                    } else {
                        linearTidakDiperpanjang.hide()
                        if (it.isFromBranch!!) {
                            line.visibility = View.GONE
                            lyPerpanjangan.visibility = View.GONE
                            linearAturEditPerpanjang.visibility = View.GONE
                            lyRekeningPencairan.visibility = View.GONE
                            expandableLayout.visibility = View.VISIBLE
                            isAro6bulan()
                            tvPembukaan.text = it.openDate
                            tvDatePencairan.text = it.dueDate!!.value
                            tvPencairan.text =
                                GeneralHelper.getString(R.string.perpanjangan_selanjutnya)
                            tvRemainingDate.text = it.isAro!!.remainingDate
                        } else {
                            textViewSiklusPerpanjangan.text = it.periodString?.toString() + " "
                            tvCycleKe.text = it.cycleRun.toString() + "/" + it.cycleCount.toString()
                            tvPembukaan.text = it.openDate
                            tvDatePencairan.text = it.dueDate?.value
                            tvDateRun.text = it.cycleMatureDate
                            tvPerpanjanganKe.text =
                                GeneralHelper.getString(R.string.perpanjangan_otomatis) + (it.cycleRun!! + 1).toString()
                            tvRemainingDate.text = it.isAro?.remainingDate
                            when (it.cycleCount) {
                                1 -> {
                                    //aro 6 bulan
                                    isAro6bulan()
                                }
                                2 -> {
                                    //aro 3 bulan
                                    isAro3bulan(it)
                                }
                                6 -> {
                                    //aro 1 bulan
                                    isAro1bulan(it)
                                }
                            }
                        }
                    }

                }
                textViewBungaPertahun.text = account?.rate
                if (it.isFromBranch!!) {
                    linearAturEditPerpanjang.visibility = View.GONE
                    linearAturEditTidakPerpanjang.visibility = View.GONE
                    lyRekeningPencairan.visibility = View.GONE
                    textViewAlertBranch.text =
                        Html.fromHtml(GeneralHelper.getString(R.string.info_deposito_hanya_bisa_di_cairkan) + " <b> ${it.branchName} </b> ")
                } else {
                    lyAlertBlue.visibility = View.GONE
                }

                textViewRemainingMatureBlue.text = it.remainingMatureDate?.text
                if (it.remainingMatureDate?.percentage!! > 80) {
                    frameNatureRemainingGreen.show()
                    textViewRemainingMatureGreen.text = it.remainingMatureDate?.text
                }
            }
        }
    }

    private fun isAro6bulan() {
        binding.lyCycleRun.visibility = View.GONE
        binding.ivTitikTop.visibility = View.GONE
        binding.ivTitikBot.visibility = View.GONE
        binding.tvDotHide.visibility = View.GONE
        binding.lineMid.visibility = View.GONE
        binding.lyDotChoose.visibility = View.VISIBLE
    }

    private fun isAro3bulan(it: GetListDepositoResponse.Accounts) {
        if (it.cycleRun == 0) {
            binding.lyCycleRun.visibility = View.VISIBLE
            binding.ivTitikTop.visibility = View.GONE
            binding.ivTitikBot.visibility = View.GONE
            binding.tvDotHide.visibility = View.VISIBLE
            binding.lyDotChoose.visibility = View.GONE
        } else {
            binding.lyCycleRun.visibility = View.GONE
            binding.ivTitikTop.visibility = View.GONE
            binding.ivTitikBot.visibility = View.VISIBLE
            binding.lineMid.visibility = View.GONE
            binding.tvDotHide.visibility = View.GONE
            binding.lyDotChoose.visibility = View.VISIBLE
        }
    }

    private fun isAro1bulan(it: GetListDepositoResponse.Accounts) {
        when (it.cycleRun) {
            0 -> {
                binding.lyCycleRun.visibility = View.VISIBLE
                binding.ivTitikTop.visibility = View.GONE
                binding.ivTitikBot.visibility = View.VISIBLE
                binding.tvDotHide.visibility = View.VISIBLE
                binding.lyDotChoose.visibility = View.GONE
            }
            5 -> {
                binding.lyCycleRun.visibility = View.GONE
                binding.ivTitikTop.visibility = View.GONE
                binding.ivTitikBot.visibility = View.VISIBLE
                binding.lineMid.visibility = View.GONE
                binding.tvDotHide.visibility = View.GONE
                binding.lyDotChoose.visibility = View.VISIBLE
            }
            else -> {
                if (it.cycleCount!! - (it.cycleRun!! + 1) <= 1) {
                    binding.lyCycleRun.visibility = View.VISIBLE
                    binding.ivTitikTop.visibility = View.VISIBLE
                    binding.ivTitikBot.visibility = View.GONE
                    binding.tvDotHide.visibility = View.VISIBLE
                    binding.lyDotChoose.visibility = View.GONE
                } else {
                    binding.lyCycleRun.visibility = View.VISIBLE
                    binding.ivTitikTop.visibility = View.VISIBLE
                    binding.ivTitikBot.visibility = View.VISIBLE
                    binding.tvDotHide.visibility = View.VISIBLE
                    binding.lyDotChoose.visibility = View.GONE
                }
            }
        }
    }

    private fun initOnClick() {
        binding.apply {
            expandableLayout.setOnExpandListener {
                if (it) imageViewExpand.setImageResource(R.drawable.ic_up_deposito_revamp)
                else imageViewExpand.setImageResource(R.drawable.ic_down_deposito_revamp)
            }
            imageViewCopy.setOnClickListener {
                val clipboardManager = getSystemService(CLIPBOARD_SERVICE) as ClipboardManager
                val text: String = textViewAccountNumber.text.toString()
                val clipData = ClipData.newPlainText("text", text)
                clipboardManager.setPrimaryClip(clipData)
                GeneralHelper.showSnackBarGreenRevamp(
                    binding.content, getString(R.string.no_deposito_telah_tersalin)
                )

            }
            linearAturEditTidakPerpanjang.setOnClickListener {
                presenter.getDataRenewalDeposito(account?.accountNumber)
            }
            linearAturEditPerpanjang.setOnClickListener {
                presenter.getDataRenewalDeposito(account?.accountNumber)
            }
            linearCairkanDeposito.setOnClickListener {
                val accountNumber = account?.accountNumber
                if (accountNumber != null) {
                    presenter.getInquiryPenutupanDeposito(accountNumber)
                }
            }
            imageViewInfo.setOnClickListener {
                bottomSheetSiklusPerpanjangan()
            }
        }

    }


    private fun bottomSheetSiklusPerpanjangan() {
        val viewBind = FragmentBottomSheetTidakAdaDepositoRevampBinding.inflate(
            LayoutInflater.from(this)
        )
        val bottomSheet = BottomSheetCustomViewGeneralFragment(viewBind.root, true, true) { }
        viewBind.btnWhite.hide()
        account?.cycleRenewalInfo?.let {
            viewBind.tvDesc.text = HtmlCompat.fromHtml(it, HtmlCompat.FROM_HTML_MODE_LEGACY)
            viewBind.tvTitle.text = GeneralHelper.getString(R.string.siklus_perpanjangan)
            viewBind.btnBlue.text = GeneralHelper.getString(R.string.kembali)
            if (!supportFragmentManager.isStateSaved) {
                bottomSheet.show(supportFragmentManager, "")
            }
            viewBind.btnBlue.setOnClickListener { bottomSheet.dismissNow() }
        }
    }


    private fun bottomSheetAturPepanjangan(response: RenewalDepositoResponse?) {
        val viewBind = FragmentBottomSheetAturPerpanjanganDepositoRevampBinding.inflate(
            LayoutInflater.from(this)
        )
        val bottomSheet = BottomSheetCustomViewGeneralFragment(viewBind.root, true, true) {
            //do nothing
        }
        listItemDepositoOptionRevampAdapter =
            ListItemDepositoOptionRevampAdapter(
                this, response?.typeCurrent,
                object : ListItemDepositoOptionRevampAdapter.onClickItem {
                    override fun onClickDetail(currentRenewal: String?) {
                        if (response?.currentRenewal != currentRenewal) {
                            viewBind.confirmBtn.isEnabled = true
                            viewBind.confirmBtn.setTextColor(GeneralHelper.getColor(R.color.white))
                        } else {
                            viewBind.confirmBtn.isEnabled = false
                            viewBind.confirmBtn.setTextColor(GeneralHelper.getColor(R.color.neutral_light80))
                        }
                    }
                }, response?.currentRenewal
            )

        viewBind.recyclerView.adapter = listItemDepositoOptionRevampAdapter
        if (response != null) {
            listItemDepositoOptionRevampAdapter!!.setSelecttor(response.currentRenewal)
            if (response.currentRenewal != listItemDepositoOptionRevampAdapter!!.getSelector()) {
                viewBind.confirmBtn.isEnabled = true
                viewBind.confirmBtn.setTextColor(GeneralHelper.getColor(R.color.white))
            } else {
                viewBind.confirmBtn.isEnabled = false
                viewBind.confirmBtn.setTextColor(GeneralHelper.getColor(R.color.neutral_light80))
            }
        }

        viewBind.confirmBtn.setOnClickListener {
            bottomSheet.dismissNow()
            val pinFragment = PinFragment(this, this)
            pinFragment.show()
        }

        if (!supportFragmentManager.isStateSaved) {
            bottomSheet.show(supportFragmentManager, "")
        }
    }


    private fun bottomSheetDetailDepositoRevamp(data: InquiryPenutupanDepositoResponseRevamp?) {
        val viewBind =
            FragmentBottomSheetDetailDepositoRevampBinding.inflate(LayoutInflater.from(this@DetailDepositoRevampActivity))
        val bottomSheet = BottomSheetCustomViewGeneralFragment(viewBind.root, true, true) {
            //do nothing
        }
        circularProgressBottomSheet(viewBind.decoViewChart)
        if (!supportFragmentManager.isStateSaved) {
            bottomSheet.show(supportFragmentManager, "")
        }
        viewBind.tvDesc.text = HtmlCompat.fromHtml(
            String.format(
                data?.warnNotes.toString()
            ), HtmlCompat.FROM_HTML_MODE_LEGACY
        )

        viewBind.tvTitle.text = GeneralHelper.getString(R.string.title_bottom_sheet_info_pencarian)
        viewBind.progressBar.visibility = View.VISIBLE
        viewBind.textViewHariLagi.text =
            account?.remainingMatureDate?.text!!.filter { it.isDigit() }

        viewBind.cardViewPinalty.setOnClickListener {
            PenaltiPenutupanDepositoActivity.launchIntentRevamp(
                this@DetailDepositoRevampActivity,
                data?.penaltyContent
            )

        }
        viewBind.cancelBtn.setOnClickListener { bottomSheet.dismissNow() }
        viewBind.confirmBtn.setOnClickListener {
            presenter.getConfirmationPenutupanDeposito(data?.referenceNumber!!)
            bottomSheet.dismissNow()
        }
    }

    private fun injectDependency() {
        activityComponent.inject(this)
        presenter.view = this
        presenter.setUrlRenewalDeposito(GeneralHelper.getString(R.string.url_deposito_revamp_renewal_form))
        presenter.setUrlUpdatePerpanjangan(GeneralHelper.getString(R.string.url_deposito_revamp_renewal_update))
        presenter.setUrlInquiryPenutupanDeposito(GeneralHelper.getString(R.string.url_deposito_revamp_inquiry_close))
        presenter.setUrlConfirmationPenutupanDeposito(GeneralHelper.getString(R.string.url_deposito_revamp_confirmation_close))
    }

    private fun circularProgressBottomSheet(mDecoView: DecoView) {
        val percent = account?.remainingMatureDate?.percentage
        initiatePersenView(mDecoView)
        val seriesItem1 = SeriesItem.Builder(
            GeneralHelper.getColor(this, R.color.primaryBlue80),
            GeneralHelper.getColor(this, R.color.primaryBlue80)
        )
            .setRange(0f, 100f, 0f)
            .setInitialVisibility(false)
            .setLineWidth(50f)
            .build()
        val series: Int = mDecoView.addSeries(seriesItem1)
        mDecoView.addEvent(
            DecoEvent.Builder(percent!!.toFloat()).setDelay(1000).setDuration(1000)
                .setIndex(series).build()
        )
    }

    private fun initiatePersenView(mDecoView: DecoView) {
        mDecoView.deleteAll()
        mDecoView.configureAngles(360, 0)
        mDecoView.addSeries(
            SeriesItem.Builder(GeneralHelper.getColor(this, R.color.primary_blue20))
                .setRange(0f, 100f, 100f)
                .setInitialVisibility(false)
                .setLineWidth(50f)
                .build()
        )
        mDecoView.addEvent(
            DecoEvent.Builder(DecoEvent.EventType.EVENT_SHOW, true)
                .setDelay(1000)
                .setDuration(1000)
                .build()
        )
    }

    override fun onRenewalDeposito(response: RenewalDepositoResponse?) {
        bottomSheetAturPepanjangan(response)
    }

    override fun onException(message: String?) {
        showSnackbarErrorMessage(message, ALERT_ERROR, this, false)
    }

    override fun getDataInquiryPenutupanDeposito(response: InquiryPenutupanDepositoResponseRevamp) {
        bottomSheetDetailDepositoRevamp(response)
    }

    private fun View.hide() {
        visibility = View.GONE
    }

    private fun View.show() {
        visibility = View.VISIBLE
    }

    override fun onSendPinComplete(pin: String?) {
        val currentRenewal = listItemDepositoOptionRevampAdapter?.getSelector()
        val request = UpdatePerpanjanganDepositoRequest(
            pin,
            account?.accountNumber,
            currentRenewal
        )
        presenter.sendUpdatePerpanjangan(request)
    }

    override fun onSuccessUpdateDeposito(desc: String) {
        val intent = Intent()
        intent.putExtra("isRefresh", true)
        intent.putExtra(Constant.TAG_MESSAGE, desc)
        setResult(Constant.REQ_PAYMENT, intent)
        finish()
    }

    override fun getDataConfirmationPenutupanDeposito(confirmationPenutupanDepositoResponse: GeneralConfirmationResponse) {
        KonfirmasiGeneralRevampActivity.launchIntent(
            this,
            confirmationPenutupanDepositoResponse,
            GeneralHelper.getString(R.string.url_deposito_revamp_close_payment),
            GeneralHelper.getString(R.string.pencairan_deposito),
            setParameterKonfirmasi(),
            isFromFastMenu,
            true, ""
        )
    }

    private fun setParameterKonfirmasi(): ParameterKonfirmasiModel? {
        val parameterKonfirmasiModel = ParameterKonfirmasiModel()
        parameterKonfirmasiModel.stringLabelTujuan =
            GeneralHelper.getString(R.string.nomor_tujuan)
        parameterKonfirmasiModel.stringButtonSubmit =
            GeneralHelper.getString(R.string.str_konfirmasi)
        parameterKonfirmasiModel.defaultIcon = R.drawable.bri
        return parameterKonfirmasiModel
    }


    override fun onLupaPin() {
        LupaPinActivity.launchIntent(this)
    }

    @Deprecated("Deprecated in Java")
    @Suppress("DEPRECATION")
    override fun onActivityResult(requestCode: Int, resultCode: Int, data: Intent?) {
        super.onActivityResult(requestCode, resultCode, data)
        if (requestCode == Constant.REQ_PAYMENT) {
            if (resultCode == RESULT_OK) {
                this.setResult(RESULT_OK, data)
                finish()
            } else if (resultCode == RESULT_CANCELED && data != null) {
                this.setResult(RESULT_CANCELED, data)
                finish()
            } else this.setResult(RESULT_CANCELED)
        }
    }

    override fun onDestroy() {
        presenter.stop()
        super.onDestroy()
    }

    override fun onBackPressed() {
        presenter.stop()
        val intent = Intent()
        intent.putExtra("isRefresh", false)
        intent.putExtra("isUpdate", false)
        setResult(Constant.REQ_PAYMENT, intent)
        finish()
    }

}