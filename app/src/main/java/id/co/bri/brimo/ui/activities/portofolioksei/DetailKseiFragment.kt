package id.co.bri.brimo.ui.activities.portofolioksei

import android.app.Activity
import android.graphics.Color
import android.os.Bundle
import android.util.Log
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.fragment.app.Fragment
import androidx.recyclerview.widget.LinearLayoutManager
import com.github.aachartmodel.aainfographics.aachartcreator.*
import com.github.aachartmodel.aainfographics.aaoptionsmodel.*
import id.co.bri.brimo.R
import id.co.bri.brimo.adapters.portofolioksei.ItemDetailExpandAdapter
import id.co.bri.brimo.adapters.portofolioksei.ItemDetailNonExpandAdapter
import id.co.bri.brimo.data.preference.BRImoPrefRepository
import id.co.bri.brimo.databinding.FragmentDetailKseiBinding
import id.co.bri.brimo.domain.helpers.GeneralHelper
import id.co.bri.brimo.domain.helpers.GeneralHelper.AppStart
import id.co.bri.brimo.domain.helpers.bubbleShowCaseView.BubbleShowCase
import id.co.bri.brimo.domain.helpers.bubbleShowCaseView.BubbleShowCaseBuilder
import id.co.bri.brimo.domain.helpers.bubbleShowCaseView.BubbleShowCaseSequence
import id.co.bri.brimo.models.apimodel.response.portofolioksei.DetailAssetResponse
import id.co.bri.brimo.ui.activities.DashboardIBActivity


class DetailKseiFragment(private var mContext : Activity ,private var mResponse : DetailAssetResponse,
                         private var mPosition : Int, private var mType : String) : Fragment() {

    private var _binding: FragmentDetailKseiBinding? = null
    private val binding get() = _binding!!
    private var instrumentArray: MutableList<Any>? = mutableListOf()
    private var lihatSiklus: BubbleShowCaseBuilder? = null
    private var activity: Activity? = null
    private var brImoPrefRepository: BRImoPrefRepository? = BRImoPrefRepository(mContext)
    private var toolTip: Boolean = true
    private var grafikColors: MutableList<String> = mutableListOf()

    val s = BubbleShowCaseSequence()

    override fun onCreateView(inflater: LayoutInflater, container: ViewGroup?, savedInstanceState: Bundle?): View {
        _binding = FragmentDetailKseiBinding.inflate(inflater, container, false)
        return binding.root
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        activity = getActivity()

        // Pengaturan Grafik
        setGrafik(mResponse.instruments!![mPosition].asets!!)


        if (mResponse.instruments!![mPosition].asets!![0].issuer.isNullOrEmpty()){
            binding.rcDetailaset.layoutManager = LinearLayoutManager(activity)
            binding.rcDetailaset.adapter = ItemDetailNonExpandAdapter(mContext,mResponse.instruments!![mPosition].asets!!,mType)
        }else{
            binding.rcDetailaset.layoutManager = LinearLayoutManager(activity)
            binding.rcDetailaset.adapter = ItemDetailExpandAdapter(mContext,mResponse.instruments!![mPosition].asets!!,mType)
        }

        setUpTutorial()

    }

    fun setGrafik(dataChart: List<DetailAssetResponse.Aset>){
        val pieChart = binding.chart

        val sumLembar = mResponse.instruments!![mPosition].instrumentTotalString
        instrumentArray!!.clear()
        grafikColors.clear()

        for (i in dataChart.indices) {
            val assetName = dataChart[i].assetCode
            val assetTotal = dataChart[i].assetTotal
            instrumentArray!!.add(listOf(assetName, assetTotal))

        }

        binding.tvTotalAset.text = sumLembar
        binding.tvTotal.text = mResponse.instruments!![mPosition].instrumentInfoString

        val listColors = listOf(
                "#1078CAFF",
                "#27AE60FF",
                "#DEAB10FF",
                "#F87304FF",
                "#E84040FF",
                "#8000C7FF"
        )

        for (i in dataChart.indices) {
                if (i <= listColors.size - 1) {
                    grafikColors.add(listColors[i])
                } else {
                    grafikColors.add(listColors.last())
                }
            }

        if (mResponse.instruments!![mPosition].instrumentTotal!!.toInt() == 0){
            instrumentArray!!.clear()
            grafikColors.clear()
            instrumentArray!!.add(arrayOf("", 100))
            grafikColors.add("#EAEBEBFF")
            toolTip = false
        }


        val labs = AADataLabels()
                .enabled(false)
                .useHTML(true)
                .color("#000")
                .backgroundColor("#ffffff")
                .distance(0f)
                .format("{point.y:.0f} %")
                .borderRadius(4f)

        val aaMarkerHove = AAMarkerHover()
                .enabled(false)

        val aaMarker = AAMarker()
                .states(AAMarkerStates().hover(aaMarkerHove))

        val chartModel = AAChartModel()
                .chartType(AAChartType.Pie)
                .animationType(AAChartAnimationType.Bounce)
                .colorsTheme(grafikColors.toTypedArray())
                .legendEnabled(false)
                .tooltipEnabled(toolTip)
                .series(arrayOf(
                        AASeriesElement()
                                .name("")
                                .data(instrumentArray!!.toTypedArray())
                                .borderWidth(0f)
                                .marker(aaMarker)
                                .innerSize("0%")
                                .dataLabels(labs)
                ))
                .dataLabelsEnabled(false)
                .xAxisLabelsEnabled(false)
                .dataLabelsStyle(AAStyle().color("#0000000"))



        val legend = AALegend()
                .align(AAChartAlignType.Center)
                .layout(AAChartLayoutType.Horizontal)
                .verticalAlign(AAChartVerticalAlignType.Middle)
                .itemMarginTop(8f)

        val options = chartModel.aa_toAAOptions()
        options.legend(legend)
        pieChart.aa_drawChartWithChartModel(chartModel)

    }

    fun setUpTutorial() {
        if (mType != "rdn") {
            if (java.lang.Boolean.FALSE == brImoPrefRepository!!.kseiDetail){
                addBubbleShowCase()
                brImoPrefRepository!!.saveKseiDetail(true)
            }
        }
    }

    fun addBubbleShowCase() {
        try {
            lihatSiklus = BubbleShowCaseBuilder(requireActivity()) //Activity instance
                    .title("Ketuk untuk lihat detail akumulasi") //Any title for the bubble view
                    .description("Aset yang dimiliki telah dikategorisasikan berdasarkan Broker yang mengaturnya")
                    .backgroundColor(Color.WHITE)
                    .textColor(Color.BLACK)
                    .buttonTitle(GeneralHelper.getString(R.string.btn_tutup))
                    .targetView(binding.rcDetailaset)
                    .arrowPosition(BubbleShowCase.ArrowPosition.BOTTOM)

            val list: MutableList<BubbleShowCaseBuilder> = java.util.ArrayList()
            list.add(lihatSiklus!!)
            val myActivity = activity as DetailKseiWithTabActivity
            myActivity.addMenuKsei(myActivity, list)
            s.addShowCases(list)
            s.show()
        } catch (e: Exception) {
        }


    }

}