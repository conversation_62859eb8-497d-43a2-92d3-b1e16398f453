package id.co.bri.brimo.domain.helpers.brizzi;

import android.content.Context;

import id.co.bri.brimo.di.scopes.ApplicationContext;
import id.co.bri.brimo.domain.config.AppConfig;
import id.co.bri.brimo.domain.config.Constant;
import id.co.bri.brimo.domain.helpers.GeneralHelper;
import id.co.bri.brimo.security.MyCryptStatic;

import java.io.IOException;

public class AppB<PERSON>ziHelper implements BrizziHelper {
    Context mContext;

    public AppBrizziHelper(@ApplicationContext Context mContext) {
        this.mContext = mContext;
    }

    @Override
    public String getBrizziToken() {
        return "oM1YcU3vpRyKSbPJLpYLpUhNvu66";
    }

    @Override
    public String getBrizziSecret() {
        return null;
    }
}