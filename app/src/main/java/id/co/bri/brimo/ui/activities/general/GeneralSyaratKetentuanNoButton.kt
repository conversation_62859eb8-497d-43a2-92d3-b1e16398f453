package id.co.bri.brimo.ui.activities.general

import android.app.Activity
import android.content.Intent
import android.os.Bundle
import id.co.bri.brimo.R
import id.co.bri.brimo.databinding.ActivityGeneralSyaratKetentuanNoButtonBinding
import id.co.bri.brimo.domain.config.Constant
import id.co.bri.brimo.domain.helpers.GeneralHelper
import id.co.bri.brimo.ui.activities.base.BaseActivity

class GeneralSyaratKetentuanNoButton : BaseActivity() {

    lateinit var binding : ActivityGeneralSyaratKetentuanNoButtonBinding

    companion object{
        lateinit var mHtml : String
        fun launchIntent(caller: Activity, html: String) {
            val intent = Intent(caller, GeneralSyaratKetentuanNoButton::class.java)
            mHtml = html
            caller.startActivityForResult(intent, Constant.REQ_PAYMENT)
        }
    }
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        binding = ActivityGeneralSyaratKetentuanNoButtonBinding.inflate(layoutInflater)
        setContentView(binding.root)
        GeneralHelper.setToolbarRevamp(this, binding.toolbar.toolbar,GeneralHelper.getString(R.string.syarat_dan_ketentuan))
        GeneralHelper.setWebViewStandart(binding.wvSyarat, "", mHtml)
    }

}