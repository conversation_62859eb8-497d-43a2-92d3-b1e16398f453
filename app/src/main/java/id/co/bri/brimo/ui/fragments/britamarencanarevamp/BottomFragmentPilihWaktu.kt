package id.co.bri.brimo.ui.fragments.britamarencanarevamp

import android.os.Bundle
import android.util.Log
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import com.google.android.material.bottomsheet.BottomSheetDialogFragment
import id.co.bri.brimo.R
import id.co.bri.brimo.databinding.FragmentPilihWaktuRencanaBinding
import id.co.bri.brimo.ui.fragments.ListMonthFragment


class BottomFragmentPilihWaktu(private var mResponse : List<Int>, private var title:String,private var defMonth:  Int, private var onClick : onClickInterfance  ) : BottomSheetDialogFragment() {

    private lateinit var binding: FragmentPilihWaktuRencanaBinding

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setStyle(STYLE_NORMAL, R.style.CustomBottomSheetDialogTheme)

    }

    override fun onCreateView(inflater: LayoutInflater, container: ViewGroup?,
                              savedInstanceState: Bundle?): View? {
        binding = FragmentPilihWaktuRencanaBinding.inflate(inflater, container, false)

        return binding.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        binding.apply {

            val monthValues = mResponse.map { it.toString() }.toTypedArray()
            tvTitle.text = title
            binding.number.displayedValues = null
            binding.number.minValue = 0
            binding.number.maxValue = monthValues.size - 1
            binding.number.wrapSelectorWheel = false
            binding.number.value = defMonth
            binding.number.displayedValues = monthValues

            btnSelect.setOnClickListener {
                val selectedMonth = monthValues[number.value]
                onClick?.onClickWaktu(selectedMonth,title)
                dismiss()
            }
        }

    }

    interface onClickInterfance{
        fun onClickWaktu(waktu : String, title : String)
    }

}