package id.co.bri.brimo.ui.fragments.bottomsheet

import android.content.DialogInterface
import android.os.Bundle
import android.text.Html
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.FrameLayout
import com.google.android.material.bottomsheet.BottomSheetBehavior
import com.google.android.material.bottomsheet.BottomSheetDialogFragment
import id.co.bri.brimo.R
import id.co.bri.brimo.databinding.FragmentBottomSheetGeneralBinding
import id.co.bri.brimo.databinding.FragmentBottomSheetGeneralHtmlBinding
import id.co.bri.brimo.domain.extension.gone
import id.co.bri.brimo.domain.extension.visible
import id.co.bri.brimo.domain.helpers.GeneralHelper

class BottomSheetGeneralHtmlFragment : BottomSheetDialogFragment() {

    enum class DialogType {
        INFORMATION,
        MESSAGE,
        INFORMATIONWITHOUTIMAGE,
        INFORMATIONWITHOUTIMAGEHTML
    }

    private lateinit var binding: FragmentBottomSheetGeneralHtmlBinding

    private var dismissListener: () -> Unit = {}
    private var btnFirstListener: () -> Unit = {}
    private var btnSecondListener: () -> Unit = {}
    private var dialogType: DialogType =
        DialogType.INFORMATION
    var imagePath: String = ""
    var imageName: String = ""
    var titleTopText: String = ""
    var titleText: String = ""
    var subtitleText: String = ""
    var firstBtnText: String = ""
    var secondBtnText: String = ""
    var isClickable: Boolean = true

    fun setOnDismiss(listener: () -> Unit) {
        dismissListener = listener
    }

    fun setOnBtnFirst(listener: () -> Unit) {
        btnFirstListener = listener
    }

    fun setOnBtnSecond(listener: () -> Unit) {
        btnSecondListener = listener
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setStyle(STYLE_NORMAL, R.style.CustomBottomSheetDialogTheme)
    }

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View? {
        binding = FragmentBottomSheetGeneralHtmlBinding.inflate(inflater, container, false)
        return binding.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)

        dialog?.setOnShowListener {
            val bottomSheet = dialog?.findViewById(R.id.design_bottom_sheet) as FrameLayout
            val behavior = BottomSheetBehavior.from(bottomSheet)
            behavior.state = BottomSheetBehavior.STATE_EXPANDED
        }

        dialog?.setCancelable(isClickable)
        dialog?.setCanceledOnTouchOutside(isClickable)

        binding.tvTopTxt.text = titleTopText
        binding.tvTitle.text = titleText
        binding.tvDesc.text = subtitleText
        binding.firstBtn.text = firstBtnText
        binding.secondBtn.text = secondBtnText

        binding.firstBtn.setOnClickListener {
            btnFirstListener()
            dismissListener()
        }

        binding.secondBtn.setOnClickListener {
            btnSecondListener()
            dismissListener()
        }

        setDialogType(dialogType)
    }

    private fun setDialogType(dialogType: DialogType) {
        when (dialogType) {
            DialogType.INFORMATION -> {
                if (titleTopText.isNullOrEmpty()) {
                    binding.tvTopTxt.gone()
                    binding.view2.gone()
                }
                if (imagePath.isNullOrEmpty()) {
                    binding.ivCenter.setImageResource(
                        GeneralHelper.getImageId(
                            binding.root.context,
                            imageName
                        )
                    )
                } else {
                    GeneralHelper.loadImageUrl(
                        context,
                        imagePath,
                        binding.ivCenter,
                        R.drawable.img_sorry_with_card,
                        0
                    )
                }
                if (secondBtnText.isEmpty()) {
                    binding.secondBtn.gone()
                    binding.firstBtn.text = firstBtnText
                }

            }

            DialogType.INFORMATIONWITHOUTIMAGE -> {
                if (titleTopText.isNullOrEmpty()) {
                    binding.tvTopTxt.gone()
                    binding.view2.gone()
                }
                if (imagePath.isNullOrEmpty()) {
                    binding.ivCenter.visibility = View.GONE
                }
                if (secondBtnText.isEmpty()) {
                    binding.secondBtn.gone()
                    binding.firstBtn.text = firstBtnText
                }

            }

            DialogType.INFORMATIONWITHOUTIMAGEHTML -> {
                binding.wvDesc.visibility = View.VISIBLE
                binding.tvDesc.visibility = View.GONE
                GeneralHelper.setWebViewStandart(binding.wvDesc,"",subtitleText)

                if (titleTopText.isNullOrEmpty()) {
                    binding.tvTopTxt.gone()
                    binding.view2.gone()
                }
                if (imagePath.isNullOrEmpty()) {
                    binding.ivCenter.visibility = View.GONE
                }
                if (secondBtnText.isEmpty()) {
                    binding.secondBtn.gone()
                    binding.firstBtn.text = firstBtnText
                }

            }

            DialogType.MESSAGE -> {
                if (imagePath.isNullOrEmpty()) {
                    binding.ivCenter.setImageResource(
                        GeneralHelper.getImageId(
                            binding.root.context,
                            imageName
                        )
                    )
                } else {
                    GeneralHelper.loadImageUrl(
                        context,
                        imagePath,
                        binding.ivCenter,
                        R.drawable.img_sorry_with_card,
                        0
                    )
                }
                if (secondBtnText.isNotEmpty()) {
                    binding.secondBtn.visible()
                    binding.secondBtn.text = secondBtnText
                }
                if (firstBtnText.isNotEmpty()) {
                    binding.firstBtn.visible()
                    binding.firstBtn.text = firstBtnText
                }
            }
        }
    }

    override fun onDismiss(dialog: DialogInterface) {
        super.onDismiss(dialog)
        dismissListener()
    }

    fun setFieldType(type: DialogType) {
        dialogType = type
    }

    override fun onCancel(dialog: DialogInterface) {
        super.onCancel(dialog)
        if (isClickable) {
            dialog.dismiss()
        }
    }
}

