package id.co.bri.brimo.ui.activities.qr;

import android.app.Activity;
import android.content.Intent;
import android.content.IntentFilter;
import android.os.Bundle;
import android.util.Log;
import android.view.View;

import androidx.annotation.Nullable;
import androidx.localbroadcastmanager.content.LocalBroadcastManager;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.google.gson.Gson;

import javax.inject.Inject;

import butterknife.ButterKnife;
import id.co.bri.brimo.R;
import id.co.bri.brimo.adapters.DataTransaksiAdapter;
import id.co.bri.brimo.adapters.DetailTransaksiAdapter;
import id.co.bri.brimo.adapters.TotalTransaksiAdapter;
import id.co.bri.brimo.contract.IPresenter.qrtransfer.IPendingTransferQrPresenter;
import id.co.bri.brimo.contract.IView.qrtransfer.IPendingTransferQrView;
import id.co.bri.brimo.databinding.ActivityPendingTransferQrBinding;
import id.co.bri.brimo.domain.config.Constant;
import id.co.bri.brimo.domain.helpers.GeneralHelper;
import id.co.bri.brimo.models.NotifikasiModel;
import id.co.bri.brimo.models.apimodel.response.GeneralConfirmationResponse;
import id.co.bri.brimo.models.apimodel.response.PendingResponse;
import id.co.bri.brimo.models.apimodel.response.RestResponse;
import id.co.bri.brimo.ui.activities.ReceiptActivity;
import id.co.bri.brimo.ui.activities.ReceiptPendingActivity;
import id.co.bri.brimo.ui.activities.base.BaseActivity;

public class PendingTransferQrActivity extends BaseActivity implements IPendingTransferQrView, View.OnClickListener {

    private ActivityPendingTransferQrBinding binding;

    private static final String TAG = "PendingTransferQrActivi";

    protected static PendingResponse mPaymentResponse;
    protected static GeneralConfirmationResponse mGeneralConfirmationResponse;
    protected String urlPending = "";
    protected boolean isFromMPM = false;

    protected static final String TAG_KONFIRMASI_PENDING = "konfirmasi_pending";
    protected static final String TAG_KONFIRMASI_RESPONSE = "konfirmasi_response";
    protected static final String TAG_FROM_MPM = "from_mpm";

    @Inject
    IPendingTransferQrPresenter<IPendingTransferQrView> presenter;

    public static void launchIntentPending(Activity caller, PendingResponse paymentResponse, GeneralConfirmationResponse generalConfirmationResponse, boolean isFromMPM, boolean isFromFast) {
        Intent intent = new Intent(caller, PendingTransferQrActivity.class);

        isFromFastMenu = isFromFast;

        if (paymentResponse != null) {
            try {
                intent.putExtra(TAG_KONFIRMASI_PENDING, new Gson().toJson(paymentResponse));
            } catch (Exception e) {
                if (!GeneralHelper.isProd())
                    Log.e(TAG_KONFIRMASI_PENDING, "launchIntent: ", e);
            }
        }

        if (generalConfirmationResponse != null) {
            try {
                intent.putExtra(TAG_KONFIRMASI_RESPONSE, new Gson().toJson(generalConfirmationResponse));
            } catch (Exception e) {
                if (!GeneralHelper.isProd())
                    Log.e(TAG_KONFIRMASI_RESPONSE, "launchIntent: ", e);
            }
        }


        intent.putExtra(TAG_FROM_MPM, String.valueOf(isFromMPM));

        caller.startActivityForResult(intent, Constant.REQ_PAYMENT);
    }

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        binding = ActivityPendingTransferQrBinding.inflate(getLayoutInflater());
        setContentView(binding.getRoot());

        //parsing data intent
        if (getIntent().getExtras() != null) {
            parseDataIntentKonfirmasi(getIntent().getExtras());
        }

        if (isFromMPM) {
            if (isFromFastMenu)
                urlPending = GeneralHelper.getString(R.string.url_qr_mpm_cek_status_fm);
            else
                urlPending = GeneralHelper.getString(R.string.url_qr_mpm_cek_status);


        } else {
            urlPending = GeneralHelper.getString(R.string.url_qr_check_status);
        }

        LocalBroadcastManager.getInstance(this).registerReceiver(updates_receiver, new IntentFilter(Constant.TAG_NOTIF));

        injectDependency();
        ButterKnife.bind(this);

        GeneralHelper.setToolbar(this, binding.tbPending.toolbar, "");

        if (mPaymentResponse != null) {
            GeneralHelper.dismissDialog();
            // Data Transaksi
            binding.rvDetailPending.setHasFixedSize(true);
            binding.rvDetailPending.setLayoutManager(new LinearLayoutManager(getApplicationContext(), RecyclerView.VERTICAL, false));
            DataTransaksiAdapter dataTransaksiAdapter = new DataTransaksiAdapter(mPaymentResponse.getHeaderDataView(), this);
            binding.rvDetailPending.setAdapter(dataTransaksiAdapter);

            // Detail Transaksi
            binding.rvNominalPending.setHasFixedSize(true);
            binding.rvNominalPending.setLayoutManager(new LinearLayoutManager(getApplicationContext(), RecyclerView.VERTICAL, false));
            DetailTransaksiAdapter detailTransaksiAdapter = new DetailTransaksiAdapter(mPaymentResponse.getAmountDataView(), this);
            binding.rvNominalPending.setAdapter(detailTransaksiAdapter);

            // Total Transaksi
            binding.rvTotalPending.setHasFixedSize(true);
            binding.rvTotalPending.setLayoutManager(new LinearLayoutManager(getApplicationContext(), RecyclerView.VERTICAL, false));
            TotalTransaksiAdapter totalTransaksiAdapter = new TotalTransaksiAdapter(mPaymentResponse.getTotalDataView(), this);
            binding.rvTotalPending.setAdapter(totalTransaksiAdapter);


        } else {
            Log.d("kosong", "Null");
        }

        binding.btnPending.setOnClickListener(this);
    }

    private void injectDependency() {
        getActivityComponent().inject(this);
        if (presenter != null) {
            presenter.setView(this);
            presenter.start();
        }
    }

    @Override
    public void onException(String message) {
        showSnackbarErrorMessage(message, ALERT_ERROR, this, false);
    }

    @Override
    public void onGetData(PendingResponse pendingResponse) {
        if (pendingResponse.getImmediatelyFlag().equals(true)) {
            if (pendingResponse.getTitleImage().equalsIgnoreCase(Constant.RECEIPT68) || pendingResponse.getTitleImage().equalsIgnoreCase(Constant.RECEIPT58)) {
                ReceiptPendingActivity.launchIntent(this, pendingResponse);
            } else if (pendingResponse.getTitleImage().equalsIgnoreCase(Constant.RECEIPT00)) {
                ReceiptActivity.launchIntent(this, pendingResponse);
            }
        }
    }

    @Override
    public void onGetTransaksiGagal(RestResponse response) {
        GeneralHelper.showDialogGagalBackDescBerubah(this, Constant.TRANSAKSI_GAGAL, response.getDesc());
        setResult(RESULT_OK);
    }

    @Override
    public void onClick(View v) {
        presenter.getDataPaid(mPaymentResponse, urlPending, mGeneralConfirmationResponse);
    }

    @Override
    public void onBackPressed() {
        this.setResult(RESULT_OK);
        this.finish();
        super.onBackPressed();
    }

    @Override
    protected void parseDataNotifForeground(Intent intent) {
        if (intent != null) {

            // Get data from notifikasi
            Bundle extras = intent.getExtras();

            if (extras != null) {
                try {
                    String notifikasiString = extras.getString(Constant.TAG_NOTIF);
                    Gson gson = new Gson();
                    NotifikasiModel notifikasiModel = gson.fromJson(notifikasiString, NotifikasiModel.class);

                    if (notifikasiModel != null &&
                            !notifikasiModel.getType().equalsIgnoreCase("promo_blast") &&
                            !presenter.onLoadGetReceipt()) {
                        presenter.getDataPaid(mPaymentResponse, urlPending, mGeneralConfirmationResponse);
                    }
                } catch (Exception e) {
                    if (!GeneralHelper.isProd())
                        Log.e(TAG, "parseDataNotif: ", e);
                }

            }
        }
    }

    /**
     * Method digunakan untuk meng-extract data Intent dari Form Activity
     *
     * @param savedInstanceState Bundle savedInstanceState
     */
    protected void parseDataIntentKonfirmasi(Bundle savedInstanceState) {
        if (savedInstanceState != null) {
            try {

                //get data konfirmasi dari intent
                String tempConfirm = savedInstanceState.getString(TAG_KONFIRMASI_PENDING);
                if (tempConfirm != null) {
                    mPaymentResponse = new Gson().fromJson(tempConfirm, PendingResponse.class);
                }

            } catch (Exception e) {
                if (!GeneralHelper.isProd()) {
                    Log.e(TAG, "parseDataIntentInquiry: ", e);
                }
            }

            try {

                //get data konfirmasi dari intent
                String tempConfirm = savedInstanceState.getString(TAG_KONFIRMASI_RESPONSE);
                if (tempConfirm != null) {
                    mGeneralConfirmationResponse = new Gson().fromJson(tempConfirm, GeneralConfirmationResponse.class);
                }

            } catch (Exception e) {
                if (!GeneralHelper.isProd()) {
                    Log.e(TAG, "parseDataIntentInquiry: ", e);
                }
            }

            String tempIsFromMPM = savedInstanceState.getString(TAG_FROM_MPM);
            if (tempIsFromMPM != null) {
                isFromMPM = Boolean.parseBoolean(tempIsFromMPM);
            }
        }
    }

    @Override
    protected void onActivityResult(int requestCode, int resultCode, @Nullable Intent data) {
        super.onActivityResult(requestCode, resultCode, data);
        if (requestCode == Constant.REQ_PAYMENT) {
            if (resultCode == RESULT_OK) {
                this.setResult(RESULT_OK, data);
                this.finish();
            } else {
                this.setResult(RESULT_CANCELED, data);
                this.finish();
            }
        } else if (requestCode == Constant.REQ_FORGET_PIN && resultCode == RESULT_OK) {
            this.setResult(RESULT_OK);
            this.finish();
        }
    }

    @Override
    protected void onPause() {
        super.onPause();
        LocalBroadcastManager.getInstance(this).unregisterReceiver(updates_receiver);
    }

    @Override
    protected void onDestroy() {
        super.onDestroy();
        binding = null;
    }
}