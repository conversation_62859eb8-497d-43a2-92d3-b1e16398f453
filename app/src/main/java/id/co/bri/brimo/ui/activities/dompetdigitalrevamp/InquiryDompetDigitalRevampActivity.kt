package id.co.bri.brimo.ui.activities.dompetdigitalrevamp

import android.annotation.SuppressLint
import android.app.Activity
import android.content.Intent
import android.os.Build
import android.os.Bundle
import android.os.SystemClock
import android.text.Editable
import android.view.View
import android.view.ViewGroup
import androidx.recyclerview.widget.DefaultItemAnimator
import androidx.recyclerview.widget.LinearLayoutManager
import com.google.gson.Gson
import id.co.bri.brimo.R
import id.co.bri.brimo.adapters.dompetdigital.RekomendasiTopUpAdapter
import id.co.bri.brimo.contract.IPresenter.dompetdigitalrevamp.IInquiryDompetDigitalRevPresenter
import id.co.bri.brimo.contract.IView.dompetdigitalrevamp.IInquiryDompetDigitalRevView
import id.co.bri.brimo.databinding.ActivityInquiryDompetDigitalRevampBinding
import id.co.bri.brimo.domain.config.Constant
import id.co.bri.brimo.domain.helpers.GeneralHelper
import id.co.bri.brimo.domain.helpers.ValidationHelper
import id.co.bri.brimo.domain.helpers.textwatcher.AmountFormatWatcher
import id.co.bri.brimo.models.AccountModel
import id.co.bri.brimo.models.BillingDetailOpen
import id.co.bri.brimo.models.ParameterKonfirmasiModel
import id.co.bri.brimo.models.ParameterModel
import id.co.bri.brimo.models.apimodel.response.GeneralConfirmationResponse
import id.co.bri.brimo.models.apimodel.response.dompetdigitalrevamp.InquiryDompetDigitalResponse
import id.co.bri.brimo.models.apimodel.response.dompetdigitalrevamp.OptionAmountItem
import id.co.bri.brimo.ui.activities.KonfirmasiGeneralRevampActivity
import id.co.bri.brimo.ui.activities.base.BaseActivity
import id.co.bri.brimo.ui.customviews.switchbutton.SwitchView
import id.co.bri.brimo.ui.fragments.SumberDanaFmFragmentRevamp
import id.co.bri.brimo.ui.fragments.SumberDanaFragmentRevamp
import java.math.BigInteger
import java.util.function.Consumer
import javax.inject.Inject

class InquiryDompetDigitalRevampActivity : BaseActivity(), IInquiryDompetDigitalRevView,
    SumberDanaFragmentRevamp.SelectSumberDanaInterface,
    View.OnClickListener, RekomendasiTopUpAdapter.OnItemClickListener,
    SumberDanaFmFragmentRevamp.SelectSumberDanaInterface, SwitchView.ISwitchListener {

    private lateinit var binding: ActivityInquiryDompetDigitalRevampBinding
    private lateinit var mbrivaOpenResponse: List<BillingDetailOpen>
    private lateinit var openModel: BillingDetailOpen
    private var mListFailed: List<Int>? = mutableListOf()
    private var mListAccountModel: List<AccountModel>? = null
    private var model: AccountModel? = null
    private var saldo: Double = 0.0
    private var counter: Int = 0
    private var minTrx: Long = 0
    private var saveStr: String = ""
    private var saldoString: String = ""
    private var defaultAkun: String? = null
    private var minTrxString: String = ""
    private var feeAdminString: String = ""
    private var nominalStrClr: String = ""
    private var saldoHold: Boolean = false
    private var showDetailPay: Boolean = false
    private var isSwitchSave: Boolean = false

    private lateinit var adapter: RekomendasiTopUpAdapter

    @Inject
    lateinit var presenter: IInquiryDompetDigitalRevPresenter<IInquiryDompetDigitalRevView>

    companion object {

        private lateinit var mInquiryDompetRevampResponse: InquiryDompetDigitalResponse
        private var mUrlConfirm: String = ""
        private var mUrlPayment: String = ""
        private lateinit var mParameterModel: ParameterModel
        private var mNominal = ""

        private const val TAG_RESPONSE = "response"
        private const val TAG_PARAMETER_MODEL = "tag_parameter_model"

        @JvmStatic
        fun launchIntent(
            caller: Activity, inquiryDompetDigitalResponse: InquiryDompetDigitalResponse,
            urlConfirm: String, urlPayment: String, fromFastMenu: Boolean,
            parameterModel: ParameterModel, nominal: String) {
            val intent = Intent(caller, InquiryDompetDigitalRevampActivity::class.java)
            intent.putExtra(TAG_RESPONSE, Gson().toJson(inquiryDompetDigitalResponse))
            intent.putExtra(TAG_PARAMETER_MODEL, Gson().toJson(parameterModel))
            mUrlConfirm = urlConfirm
            mUrlPayment = urlPayment
            isFromFastMenu = fromFastMenu
            mNominal = nominal
            caller.startActivityForResult(intent, Constant.REQ_PAYMENT)
        }
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        binding = ActivityInquiryDompetDigitalRevampBinding.inflate(layoutInflater)
        setContentView(binding.root)

        binding.switchView.setSwitchListener(this)

        if (intent.extras != null) {
            parseIntent()
        }

        injectDependency()
        setupView()
    }

    private fun parseIntent() {
        if (intent.extras != null) {
            if (intent.hasExtra(TAG_RESPONSE)) {
                mInquiryDompetRevampResponse = Gson().fromJson(
                    intent.extras?.getString(TAG_RESPONSE),
                    InquiryDompetDigitalResponse::class.java
                )
            }

            if (intent.hasExtra(TAG_PARAMETER_MODEL)) {
                mParameterModel = Gson().fromJson(
                    intent.extras?.getString(TAG_PARAMETER_MODEL),
                    ParameterModel::class.java
                )
            }

        }
    }

    private fun injectDependency() {
        activityComponent.inject(this)
        presenter.view = this
        presenter.setUrlConfirm(mUrlConfirm)
        presenter.start()
    }

    private fun setupView() {
        GeneralHelper.setToolbarRevamp(this, binding.tbToolbar.toolbar,
            GeneralHelper.getString(R.string.nominal))

        //set nominal jika hit inquiry dari history
        if (mNominal != "") {
            binding.etNominal.setText(GeneralHelper.formatNominalBiasa(mNominal.toDouble()))
        } else {
            binding.etNominal.setText(GeneralHelper.getString(R.string.zero))
        }

        model = AccountModel()

        mListAccountModel = mInquiryDompetRevampResponse.accountModel
        mbrivaOpenResponse = mInquiryDompetRevampResponse.billingDetailOpen
        feeAdminString = mInquiryDompetRevampResponse.adminFee.toString()

        for (billingDetailOpen in mbrivaOpenResponse)
            openModel = billingDetailOpen

        for (accountModel in mListAccountModel!!) {
            if (accountModel.isDefault == 1) {
                model = accountModel
                break
            } else {
                model = mListAccountModel!![0]
            }
        }

        //set minimum
        if (mInquiryDompetRevampResponse.minimumTransactionString.isNotEmpty()) {
            minTrx = mInquiryDompetRevampResponse.minimumTransaction
            minTrxString = mInquiryDompetRevampResponse.minimumTransactionString
        }

        binding.tilNominal.helperText =
            String.format(GeneralHelper.getString(R.string.minimal_topup), minTrxString)

        if (model?.acoountString != null) {
            binding.lySumberDana.tvNoRek.text = model!!.acoountString
        } else {
            binding.lySumberDana.tvNoRek.text = "-"
        }

        if (openModel.title != null && openModel.title.isNotEmpty()) {
            binding.llTopSource.visibility = View.VISIBLE
            binding.tvRecipientName.text = openModel.title
            binding.tvBankName.text = openModel.subtitle
            binding.tvAccNum.text = openModel.description
        } else {
            binding.llTopSource.visibility = View.GONE
        }

        if (model?.alias != null) {
            if (model!!.alias.isEmpty()) {
                binding.lySumberDana.tvAliasRek.text = model!!.name
            } else {
                binding.lySumberDana.tvAliasRek.text = model!!.alias
            }
        } else {
            binding.lySumberDana.tvAliasRek.text = model?.name
        }

        if (model?.imagePath != null) {
            if (model!!.imagePath.isNotEmpty()) {
                GeneralHelper.loadImageUrl(
                    this,
                    model!!.imagePath,
                    binding.lySumberDana.ivIconRek,
                    R.drawable.bri,
                    0
                )
            } else {
                binding.lySumberDana.ivIconRek.setImageResource(R.drawable.bri)
            }
        } else {
            binding.lySumberDana.ivIconRek.setImageResource(R.drawable.bri)
        }

        if (openModel.listType.equals("image")) {
            if (openModel.iconPath != null) {
                if (openModel.iconPath.isNotEmpty()) {
                    GeneralHelper.loadImageUrl(
                        this,
                        openModel.iconPath,
                        binding.ivInisialPenerima,
                        R.drawable.briva,
                        0
                    )
                } else {
                    binding.ivInisialPenerima.setImageResource(R.drawable.briva)
                }
            } else {
                binding.ivInisialPenerima.setImageResource(R.drawable.briva)
            }
        } else {
            if (openModel.title != null) {
                binding.tvInisialPenerima.text = GeneralHelper.formatInitialName(openModel.title)
            } else {
                binding.tvInisialPenerima.text = "I"
            }
        }

        binding.etNominal.setOnFocusChangeListener { _, b ->
            if (b) {
                binding.etNominal.hint = ""
            } else {
                binding.etNominal.hint = "Rp"
            }
        }

        saveStr = mInquiryDompetRevampResponse.nameDefault

        adapter = RekomendasiTopUpAdapter(
            this, mInquiryDompetRevampResponse.optionAmount, applicationContext)
        val layoutManager = LinearLayoutManager(
            applicationContext, LinearLayoutManager.HORIZONTAL, false)
        binding.rvOptionAmount.layoutManager = layoutManager
        binding.rvOptionAmount.itemAnimator = DefaultItemAnimator()
        binding.rvOptionAmount.adapter = adapter

        initiateViews()
        checkButton()
        handlerFastMenu()
        binding.switchView.setVisibilitySavedListSwitch(
            isFromFastMenu,
            mInquiryDompetRevampResponse.saved
        )
    }

    private fun initiateViews() {
        binding.lyTotalDetail.btnPay.text = GeneralHelper.getString(R.string.e_wallet_top_up)
        binding.lyTotalDetail.tvTitlePay.text = GeneralHelper.getString(R.string.e_wallet_total_top_up)
        binding.lyTotalDetail.tvHeader.text = GeneralHelper.getString(R.string.e_wallet_top_up_detail)

        binding.etNominal.addTextChangedListener(activityTextListener)
        binding.etNominal.addTextChangedListener(
            AmountFormatWatcher(
                binding.etNominal,
                null,
                false
            )
        )

        binding.etNominal.addTextChangedListener(activityTextListener)
        binding.etSaveAs.addTextChangedListener(activityTextListener)

        binding.lySumberDana.tvGanti.setOnClickListener(this)
        binding.lyTotalDetail.llPayTotal.setOnClickListener(this)
        binding.lyTotalDetail.btnPay.setOnClickListener(this)
        binding.lyTotalDetail.viewBg.setOnClickListener(this)
        binding.lySumberDana.llSumberDana.setOnClickListener(this)
    }

    override fun afterText(editable: Editable?) {
        clearSelected()
        checkButton()
    }

    override fun setDefaultSaldo(
        saldoPref: Double,
        saldoStringPref: String,
        accountPref: String,
        saldoHoldPref: Boolean
    ) {
        // load default saldo dari preference
        this.saldoString = saldoStringPref
        this.saldo = saldoPref
        defaultAkun = accountPref
        saldoHold = saldoHoldPref

        //set layout
        setupAccount(saldoPref)
    }

    override fun onGetDataConfirmation(generalConfirmationResponse: GeneralConfirmationResponse) {
        KonfirmasiGeneralRevampActivity.launchIntentDompet(
            this,
            generalConfirmationResponse,
            mUrlPayment,
            GeneralHelper.getString(R.string.toolbar_confirmation),
            setParameterKonfirmasi(),
            isFromFastMenu,
            true,
            false,
            true
        )
    }

    private fun setParameterKonfirmasi(): ParameterKonfirmasiModel {
        val parameterKonfirmasiModel = ParameterKonfirmasiModel()
        parameterKonfirmasiModel.stringLabelTujuan = mParameterModel.stringLabelTujuan
        parameterKonfirmasiModel.stringButtonSubmit = GeneralHelper.getString(R.string.txt_konfirmasi)
        parameterKonfirmasiModel.defaultIcon = mParameterModel.defaultIcon
        return parameterKonfirmasiModel
    }

    override fun onExceptionTrxExpired(desc: String) {
        val returnIntent = Intent()
        returnIntent.putExtra(Constant.TAG_ERROR_MESSAGE, desc)
        this.setResult(RESULT_CANCELED, returnIntent)
        finish()
    }

    private fun setupAccount(saldoDefault: Double) {
        //List Account
        model = AccountModel()
        if (mInquiryDompetRevampResponse.accountModel.size > 0)
            mListAccountModel = mInquiryDompetRevampResponse.accountModel

        for (accountModel in mListAccountModel!!) {
            if (accountModel.isDefault == 1) {
                model = accountModel
                break
            } else {
                model = mListAccountModel?.get(0)
            }
        }

        //jika get minimum tidak null
        saldo = if (model?.minimumBalance != null) {
            saldoDefault - model?.minimumBalance!!
        } else {
            saldoDefault
        }

        if (isFromFastMenu) {
            binding.lySumberDana.tvSaldoRek.visibility = View.GONE
        } else {
            binding.lySumberDana.tvSaldoRek.visibility = View.VISIBLE
            if (model?.acoount != null) {
                if (model?.acoount == defaultAkun && saldoString != "-")
                    binding.lySumberDana.tvSaldoRek.text =
                        GeneralHelper.formatNominalIDR(model?.currency, saldoString)
                else binding.lySumberDana.tvSaldoRek.text = "-"
            }
        }

        if (model?.alias != null) {
            if (model!!.alias.isEmpty()) {
                binding.lySumberDana.tvAliasRek.text = model?.name
            } else {
                binding.lySumberDana.tvAliasRek.text = model?.alias
            }
        } else {
            binding.lySumberDana.tvAliasRek.text = model?.name
        }

        if (model?.imagePath != null) {
            if (model!!.imagePath.isNotEmpty()) {
                GeneralHelper.loadImageUrl(
                    this,
                    model?.imagePath,
                    binding.lySumberDana.ivIconRek,
                    R.drawable.bri,
                    0
                )
            } else {
                binding.lySumberDana.ivIconRek.setImageResource(R.drawable.bri)
            }
        } else {
            binding.lySumberDana.ivIconRek.setImageResource(R.drawable.bri)
        }


        //jika string rekening tidak kosong
        if (model?.acoountString != null) {
            binding.lySumberDana.tvNoRek.text = model?.acoountString
        } else {
            binding.lySumberDana.tvNoRek.text = "-"
        }

        //jika nama kosong
        if (model?.name != null) {
            binding.lySumberDana.tvAliasRek.text = GeneralHelper.formatInitialName(model?.name)
        } else {
            binding.lySumberDana.tvAliasRek.text = "I"
        }

        if (!isFromFastMenu && model!!.saldoReponse != null) {
            // cek saldo tertahan
            if (model!!.saldoReponse.isOnHold) binding.lySumberDana.ivAlertSaldo.visibility =
                View.VISIBLE
            else binding.lySumberDana.ivAlertSaldo.visibility = View.GONE
        }

        if (model?.acoount != null) {
            val saldoText: String = saldoDefault.toString()
            if (saldoText != "") {
                saldo = saldoDefault
                binding.lySumberDana.tvSaldoRek.text = "Rp."+GeneralHelper.formatNominal(saldoDefault)
            }
            if (model?.acoount == defaultAkun && saldoString != "-")
                binding.lySumberDana.tvSaldoRek.text =
                    GeneralHelper.formatNominalIDR(model?.currency, saldoString)
            else binding.lySumberDana.tvSaldoRek.text = "-"
        }

        //jika saldo hold
        if (!isFromFastMenu) {
            if (saldoHold) binding.lySumberDana.ivAlertSaldo.visibility = View.VISIBLE
            else binding.lySumberDana.ivAlertSaldo.visibility = View.GONE
        }

        checkButton()
    }

    private fun checkButton() {
        val nominalStr = binding.etNominal.text.toString()
        nominalStrClr = if (binding.etNominal.text.toString().isEmpty()) "0"
        else binding.etNominal.text.toString().replace(".", "")

        if (nominalStr.isEmpty()) {
            setMarginLayoutContent(0)
            binding.lyTotalDetail.llButton.visibility = View.GONE
            return
        }

        if (!ValidationHelper.validateAmountString(nominalStr)) {
            return
        }

        // jika nominal dibandingkan dgn min trx lebih kecil (compare to < 0 ) kurang dari min trx
        if (BigInteger.valueOf(nominalStrClr.toLong()) < BigInteger.valueOf(minTrx)) {
            setMarginLayoutContent(0)
            binding.lyTotalDetail.llButton.visibility = View.GONE
            return
        } else {
            binding.lyTotalDetail.llButton.visibility = View.VISIBLE
            setMarginLayoutContent(250)
            setupButton()
        }

        // jika nominal dibandingkan dgn saldo (lebih besar) (compare to > 0 ) melebihi saldo
        if (BigInteger.valueOf(nominalStrClr.toLong()) > BigInteger.valueOf(saldo.toLong() - getMinBalance(model!!))) {
            if (isFromFastMenu) {
                disableButton(false)
            } else {
                binding.lyTotalDetail.tvErrorTotal.visibility = View.VISIBLE
                disableButton(true)
                return
            }
        } else {
            binding.lyTotalDetail.tvErrorTotal.visibility = View.GONE
            if (binding.switchView.isSwitchChecked()) {
                if (binding.etSaveAs.length() != 0) {
                    disableButton(false)
                } else {
                    disableButton(true)
                }
            } else {
                disableButton(false)
            }
        }
    }

    private fun disableButton(disable: Boolean) {
        if (disable) {
            binding.lyTotalDetail.btnPay.alpha = 0.3f
            binding.lyTotalDetail.btnPay.isEnabled = false
            binding.lyTotalDetail.btnPay.setTextColor(GeneralHelper.getColor(R.color.neutral_light60))
        } else {
            binding.lyTotalDetail.btnPay.alpha = 1f
            binding.lyTotalDetail.btnPay.isEnabled = true
            binding.lyTotalDetail.btnPay.setTextColor(GeneralHelper.getColor(R.color.neutral_light10))
        }
    }

    @SuppressLint("SetTextI18n")
    private fun setupButton() {
        binding.lyTotalDetail.tvPenerima.text = saveStr
        binding.lyTotalDetail.tvNominal.text =
            GeneralHelper.getString(R.string.rp) + GeneralHelper.formatNominal(nominalStrClr.toDouble())
        binding.lyTotalDetail.tvFeeAdmin.text =
            GeneralHelper.getString(R.string.rp) + GeneralHelper.formatNominal(feeAdminString.toDouble())
        val totalPrice = nominalStrClr.toDouble() + feeAdminString.toDouble()
        binding.lyTotalDetail.tvTotalPay.text =
            GeneralHelper.getString(R.string.rp) + GeneralHelper.formatNominal(totalPrice)
    }

    override fun onSelectSumberDana(bankModel: AccountModel?) {
        if (bankModel != null) {
            model = bankModel
        }

        saldo = if (bankModel?.saldoReponse != null) {
            val saldoStr = model?.saldoReponse?.balanceString.toString()
            binding.lySumberDana.tvSaldoRek.text =
                GeneralHelper.formatNominalIDR(model?.currency, saldoStr)
            if (bankModel.saldoReponse.balance != null) {
                bankModel.saldoReponse.balance
            } else {
                binding.lySumberDana.tvSaldoRek.text = String.format("%s%s", bankModel.currency, "-")
                0.0
            }
        } else {
            binding.lySumberDana.tvSaldoRek.text = String.format("%s%s", bankModel?.currency, "-")
            0.0
        }
        binding.lySumberDana.tvNoRek.text = model?.acoountString
        if (bankModel?.alias != null) {
            if (bankModel.alias.isEmpty()) {
                binding.lySumberDana.tvAliasRek.text = bankModel.name
            } else {
                binding.lySumberDana.tvAliasRek.text = bankModel.alias
            }
        } else {
            binding.lySumberDana.tvAliasRek.text = bankModel?.name
        }

        if (bankModel?.imagePath != null) {
            if (bankModel.imagePath.isNotEmpty()) {
                GeneralHelper.loadImageUrl(
                    this,
                    bankModel.imagePath,
                    binding.lySumberDana.ivIconRek,
                    R.drawable.bri,
                    0
                )
            } else {
                binding.lySumberDana.ivIconRek.setImageResource(R.drawable.bri)
            }
        } else {
            binding.lySumberDana.ivIconRek.setImageResource(R.drawable.bri)
        }

        if (!isFromFastMenu && model!!.saldoReponse != null) {
            // cek saldo tertahan
            if (model!!.saldoReponse.isOnHold) binding.lySumberDana.ivAlertSaldo.visibility =
                View.VISIBLE
            else binding.lySumberDana.ivAlertSaldo.visibility = View.GONE
        }

        checkButton()

    }

    override fun onSendFailedList(list: MutableList<Int>?) {
        mListFailed = list
    }

    override fun onClick(v: View) {
        if (SystemClock.elapsedRealtime() - mLastClickTime < 1000) {
            return
        }
        mLastClickTime = SystemClock.elapsedRealtime()
        when (v.id) {
            binding.lySumberDana.tvGanti.id -> {
                gantiSumberDana()
            }
            binding.lySumberDana.llSumberDana.id -> {
                gantiSumberDana()
            }
            binding.lyTotalDetail.llPayTotal.id -> {
                showDetailTotalPay()
            }
            binding.lyTotalDetail.viewBg.id -> {
                showDetailTotalPay()
            }
            binding.lyTotalDetail.btnPay.id -> {
                presenter.getDataConfirmation(
                    mInquiryDompetRevampResponse.referenceNumber,
                    model!!.acoount,
                    nominalStrClr,
                    binding.etSaveAs.text.toString(),
                    binding.etNote.text.toString(),
                    isFromFastMenu
                )
            }
        }
    }

    private fun gantiSumberDana() {
        counter++
        if (mListAccountModel == null) {
            GeneralHelper.showToast(this, GeneralHelper.getString(R.string.no_account_list))
        } else {
            val nominalStr = if (binding.etNominal.text.toString().isEmpty()) "0"
            else binding.etNominal.text.toString().replace(".", "")
            val nominalLong = nominalStr.toLong()
            if (isFromFastMenu) {
                val fragmentFmSumberDanaNew =
                    SumberDanaFmFragmentRevamp(
                        mListAccountModel,
                        counter,
                        GeneralHelper.clearingAmountSigned("Rp$nominalLong").toLong(),
                        this,
                        isFromFastMenu, GeneralHelper.getString(R.string.e_wallet_change_source_of_funds))
                fragmentFmSumberDanaNew.show(supportFragmentManager, Constant.TAG_PICK_ACCOUNT)
            } else {
                val fragmentSumberDanaNew = SumberDanaFragmentRevamp(
                    mListAccountModel,
                    counter,
                    GeneralHelper.clearingAmountSigned("Rp$nominalLong").toLong(),
                    this,
                    mListFailed,
                    isFromFastMenu, GeneralHelper.getString(R.string.e_wallet_change_source_of_funds))
                fragmentSumberDanaNew.show(supportFragmentManager, Constant.TAG_PICK_ACCOUNT)
            }
        }
    }

    private fun setMarginLayoutContent(bottom: Int) {
        val param = binding.content.layoutParams as ViewGroup.MarginLayoutParams
        param.setMargins(0, 0, 0, bottom)
        binding.content.layoutParams = param
    }

    private fun showDetailTotalPay() {
        if (!showDetailPay) {
            binding.lyTotalDetail.viewBg.visibility = View.VISIBLE
            binding.lyTotalDetail.llDetail.visibility = View.VISIBLE
            binding.lyTotalDetail.llPenerima.visibility = View.VISIBLE
            binding.lyTotalDetail.imgArrowPay.rotation = 0f
            showDetailPay = true
            // disable click
            binding.etSaveAs.isEnabled = false
            binding.etNominal.isEnabled = false
            binding.tilNominal.isEnabled = false
            binding.lySumberDana.tvGanti.isEnabled = false
        } else {
            binding.lyTotalDetail.viewBg.visibility = View.GONE
            binding.lyTotalDetail.llDetail.visibility = View.GONE
            binding.lyTotalDetail.llPenerima.visibility = View.GONE
            binding.lyTotalDetail.imgArrowPay.rotation = 180f
            showDetailPay = false
            // enable click
            binding.etSaveAs.isEnabled = true
            binding.etNominal.isEnabled = true
            binding.tilNominal.isEnabled = true
            binding.lySumberDana.tvGanti.isEnabled = true
        }
        binding.switchView.showDetailTotalPay(showDetailPay)
    }

    @Deprecated("Deprecated in Java")
    @Suppress("DEPRECATION")
    override fun onActivityResult(requestCode: Int, resultCode: Int, data: Intent?) {
        super.onActivityResult(requestCode, resultCode, data)
        if (requestCode == Constant.REQ_PAYMENT) {
            if (resultCode == RESULT_OK) {
                this.setResult(RESULT_OK, data)
                finish()
            } else {
                if (data != null) {
                    this.setResult(RESULT_CANCELED, data)
                    finish()
                }
            }
        }
    }

    @SuppressLint("NotifyDataSetChanged")
    override fun onItemClick(position: Int) {
        binding.etNominal.setText(mInquiryDompetRevampResponse.optionAmount[position].name)
        clearSelected()
        mInquiryDompetRevampResponse.optionAmount[position].isSelected = true
        adapter.notifyDataSetChanged()
    }

    @SuppressLint("NotifyDataSetChanged")
    fun clearSelected() {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.N) {
            mInquiryDompetRevampResponse.optionAmount.forEach(Consumer { p: OptionAmountItem ->
                p.isSelected = false
            })
        } else {
            for (p in mInquiryDompetRevampResponse.optionAmount) {
                p.isSelected = false
            }
        }
        adapter.notifyDataSetChanged()
    }

    fun handlerFastMenu() {
        /* digunakan untuk menghilangkan layout togle simpan sebagai dan tersimpan sebagai
        *  ketika dari fast menu
        */
        if (isFromFastMenu) {
            binding.rlInfo.visibility = View.GONE
        } else {
            //ketika sudah login akan menampilkan tersimpan sebagai dan jika belum tersimpan menampilkan togle
            if (mInquiryDompetRevampResponse.saved != "") {
                binding.rlInfo.visibility = View.VISIBLE
                binding.tvInfoSimpan.text = java.lang.String.format(
                    GeneralHelper.getString(R.string.added_to_saved),
                    mInquiryDompetRevampResponse.saved
                )
                saveStr = ""
            }
        }
    }

    override fun onSwitchChange(isChecked: Boolean) {
        if (isChecked) {
            binding.rlSaveAs.visibility = View.VISIBLE
            binding.etSaveAs.setText(saveStr)
        } else {
            binding.rlSaveAs.visibility = View.GONE
            binding.etSaveAs.setText("")
        }
        checkButton()
    }

    private fun getMinBalance(model: AccountModel): Long {
        val tempAccountModel = mListAccountModel?.firstOrNull { it.acoount == model.acoount } ?: AccountModel()
        return when {
            tempAccountModel.minimumBalance != null && tempAccountModel.minimumBalance >= 0 -> tempAccountModel.minimumBalance.toLong()
            model.minimumBalance != null && model.minimumBalance >= 0 -> model.minimumBalance.toLong()
            else -> 0L
        }
    }
}