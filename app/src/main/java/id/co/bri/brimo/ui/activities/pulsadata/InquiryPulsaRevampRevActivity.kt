package id.co.bri.brimo.ui.activities.pulsadata

import android.annotation.SuppressLint
import android.app.Activity
import android.content.Intent
import android.os.Bundle
import android.os.SystemClock
import android.text.Editable
import android.text.TextWatcher
import android.util.Log
import android.view.View
import android.view.inputmethod.EditorInfo
import android.widget.Button
import android.widget.EditText
import android.widget.ImageView
import android.widget.LinearLayout
import android.widget.RelativeLayout
import android.widget.Switch
import android.widget.TextView
import androidx.appcompat.widget.Toolbar
import androidx.cardview.widget.CardView
import androidx.core.content.ContextCompat
import androidx.fragment.app.Fragment
import androidx.viewpager.widget.ViewPager
import butterknife.Bind
import butterknife.ButterKnife
import com.google.android.material.textfield.TextInputLayout
import com.google.gson.Gson
import com.ogaclejapan.smarttablayout.SmartTabLayout
import id.co.bri.brimo.R
import id.co.bri.brimo.adapters.CatatanKeuanganAdapter
import id.co.bri.brimo.contract.IPresenter.pulsarevamp.IInquiryPulsaRevampPresenter
import id.co.bri.brimo.contract.IView.pulsarevamp.IInquiryPulsaRevampView
import id.co.bri.brimo.domain.config.Constant
import id.co.bri.brimo.domain.helpers.GeneralHelper
import id.co.bri.brimo.domain.helpers.SizeHelper
import id.co.bri.brimo.models.ParameterKonfirmasiModel
import id.co.bri.brimo.models.ParameterModel
import id.co.bri.brimo.models.apimodel.response.DataListRevamp
import id.co.bri.brimo.models.apimodel.response.GeneralConfirmationResponse
import id.co.bri.brimo.models.apimodel.response.SavedResponse
import id.co.bri.brimo.models.apimodel.response.pulsarevamp.DataListCustomRevamp
import id.co.bri.brimo.models.apimodel.response.pulsarevamp.FormPulsaDataResponse
import id.co.bri.brimo.models.apimodel.response.pulsarevamp.ProviderItem
import id.co.bri.brimo.models.optionmodel.OptionNominalModel
import id.co.bri.brimo.ui.activities.KonfirmasiGeneralOpenRevampActivity
import id.co.bri.brimo.ui.activities.MicrositeCallbackActivity
import id.co.bri.brimo.ui.activities.base.BaseContactActivity
import id.co.bri.brimo.ui.activities.base.BaseInquiryRevampActivity
import id.co.bri.brimo.ui.fragments.BottomFragmentPulsaRevamp
import id.co.bri.brimo.ui.fragments.pulsadatarevamp.PaketDataRevampFragment
import id.co.bri.brimo.ui.fragments.pulsadatarevamp.PulsaRevampFragment
import javax.inject.Inject


class InquiryPulsaRevampRevActivity : BaseContactActivity(), PulsaRevampFragment.selectedPulsaListener,
    PaketDataRevampFragment.selectedPaketListener,
    ViewPager.OnPageChangeListener, View.OnClickListener, BottomFragmentPulsaRevamp.OnClickListener,
    IInquiryPulsaRevampView, View.OnFocusChangeListener {

    private val listOfCanShowBanner = arrayListOf("0812", "0813", "0821", "0822", "0823", "0851", "0852", "0853")

    @Bind(R.id.et_isi_no_hp_pulsa)
    lateinit var editTextPulsa: EditText

    @Bind(R.id.stPulsaPaket)
    lateinit var stPulsaPaket: SmartTabLayout

    @Bind(R.id.vpPulsaPaket)
    lateinit var vpPulsaPaket: ViewPager

    @Bind(R.id.cv_pulsa)
    lateinit var cardView: RelativeLayout

    @Bind(R.id.llPulsaPaket)
    lateinit var llContentPulsa: LinearLayout

    @Bind(R.id.iv_logo_hp)
    lateinit var ivLogoHp: ImageView

    @Bind(R.id.tv_detail_pembayaran)
    lateinit var tvDetailPembayaran: TextView

    @Bind(R.id.tv_total_pembayaran_pulsa)
    lateinit var tvNominalPembayaran: TextView

    @Bind(R.id.btnSubmitPulsa)
    lateinit var btnSubmitPulsa: Button

    @Bind(R.id.img_contact)
    lateinit var imgContact: ImageView

    @Bind(R.id.tv_recipient_name)
    lateinit var tvNamaProvider: TextView

    @Bind(R.id.img_cancel)
    lateinit var imgCancel: ImageView

    @Bind(R.id.ll_total_bayar)
    lateinit var llTotalBayar: LinearLayout

    @Bind(R.id.ll_button)
    lateinit var llButton: LinearLayout

    @Bind(R.id.rl_parent_top_source)
    lateinit var rlParentTop: RelativeLayout

    @Bind(R.id.til_norek)
    lateinit var tilNorek: TextInputLayout

    @Bind(R.id.tb_inquiry_toolbar)
    lateinit var toolbar: Toolbar

    @Bind(R.id.switch_save)
    lateinit var switchSave: Switch

    @Bind(R.id.rl_favorit)
    lateinit var rlFavorit: RelativeLayout


    @Bind(R.id.tv_tambah)
    lateinit var tvTambah: TextView

    @Bind(R.id.rl_save_as)
    lateinit var rlSaveAs: RelativeLayout

    @Bind(R.id.et_save_as)
    lateinit var etSaveAs: EditText

    @Bind(R.id.cv_banner)
    lateinit var cvBanner: CardView

    @Bind(R.id.tv_banner)
    lateinit var tvBanner: TextView

    @Bind(R.id.banner_relative)
    lateinit var bannerRelative :RelativeLayout

    private var isLoaded = false
    private var isNominalShow: Boolean = false
    private var isProviderFound: Boolean = false
    private var isCardShow: Boolean = false

    var fragmentList: MutableList<Fragment> = mutableListOf()
    var providerList: MutableList<ProviderItem> = mutableListOf()

    private val pulsaRevampFragment = PulsaRevampFragment()
    private val paketDataRevampFragment = PaketDataRevampFragment()
    private lateinit var currentProvider: ProviderItem

    //data untuk request inquiry
    private var mNominal: String = ""
    private var mBiayaAdmin: String = ""
    private var currentAmount: String = ""
    private var currentProductId: String = ""
    private var type: String = ""
    private var total: Double = 0.0
    private var item: String = ""
    private var mNomorPembanding: String = ""
    private var mDataId: String = ""
    private var listStringSaved: MutableList<String> = mutableListOf()
    private var errorMessage: String? = null
    private var saveStr: String = ""
    private var booleanText: Boolean = false
    private var booleanCheck: Boolean = false

    private lateinit var mUrlKonfirmasi: String
    private lateinit var mUrlPayment: String
    private lateinit var mUrlPending: String
    private lateinit var mTitle: String

    private var mProviderId: Int = 0
    private var mProviderName: String = ""
    private var mIsCustomPackage: Boolean = false
    private var mRefNumCustomPackage: String = ""
    private var mTempPhoneNum: String = ""

    private lateinit var linearLayout: LinearLayout
    private var disableButtonBottomFragment: Boolean = false

    @Inject
    lateinit var brivaPresenter: IInquiryPulsaRevampPresenter<IInquiryPulsaRevampView>

    private var fragmentRefreshListener: FragmentRefreshListener? = null

    private fun getFragmentRefreshListener(): FragmentRefreshListener? {
        return fragmentRefreshListener
    }

    fun setFragmentRefreshListener(fragmentRefreshListener: FragmentRefreshListener) {
        this.fragmentRefreshListener = fragmentRefreshListener
    }

    companion object {
        private val TAG = "InquiryPulsaDataRevActivity"
        private var mNoHp: String = ""
        private var mmNominal: String = ""
        private var mmDataId: String = ""
        private var mPurchaseType: String = ""
        private var mSavedResponses = ArrayList<SavedResponse>()
        var TAG_INQUIRY_DATA = "inquiry_data"
        var TAG_PARAMS_DATA = "params_data"
        var TAG_URL_KONFIRM = "url_konfirm"
        var TAG_URL_PAYMENT = "url_payment"
        var TAG_TITLE = "url_title"
        private lateinit var mFormPulsaDataResponse: FormPulsaDataResponse
        private lateinit var mParameterModel: ParameterModel
        private lateinit var mParameterKonfirmasiModel: ParameterKonfirmasiModel

        @JvmStatic
        fun launchIntent(
            caller: Activity,
            fromFastMenu: Boolean,
            urlKonfirmasi: String,
            urlPayment: String,
            formPulsaDataResponse: FormPulsaDataResponse,
            parameterModel: ParameterModel,
            noHp: String,
            nominal: String,
            savedResponses: ArrayList<SavedResponse>,
            dataId: String,
            purchaseType: String,
        ) {
            val intent = Intent(caller, InquiryPulsaRevampRevActivity::class.java)
            isFromFastMenu = fromFastMenu
            mNoHp = noHp
            mSavedResponses = savedResponses
            mPurchaseType = purchaseType
            mFormPulsaDataResponse = formPulsaDataResponse

            if (!nominal.isEmpty()) {
                mmNominal = nominal
            }

            if (!dataId.isEmpty()) {
                mmDataId = dataId
            }

            if (!purchaseType.isEmpty()) {
                mPurchaseType = purchaseType
            }

            //put data to Intent
            try {
                intent.putExtra(
                    TAG_INQUIRY_DATA,
                    Gson().toJson(mFormPulsaDataResponse)
                )
            } catch (e: Exception) {
                if (!GeneralHelper.isProd()) Log.e(
                    TAG_INQUIRY_DATA,
                    "launchIntent: ",
                    e
                )
            }

            try {
                intent.putExtra(
                    TAG_PARAMS_DATA,
                    Gson().toJson(parameterModel)
                )
            } catch (e: Exception) {
                if (!GeneralHelper.isProd()) Log.e(
                    TAG_PARAMS_DATA,
                    "launchIntent: ",
                    e
                )
            }

            //set URL service
            try {
                intent.putExtra(TAG_URL_KONFIRM, urlKonfirmasi)
                intent.putExtra(TAG_URL_PAYMENT, urlPayment)
                intent.putExtra(TAG_TITLE, "title")
            } catch (e: Exception) {
                if (!GeneralHelper.isProd()) Log.e(
                    TAG,
                    "launchIntent: ",
                    e
                )
            }

            caller.startActivityForResult(intent, Constant.REQ_PAYMENT)
        }
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContentView(R.layout.activity_inquiry_pulsa_data_rev)

        ButterKnife.bind(this)
        //parsing data intent
        if (intent.extras != null) {
            parseDataIntentInquiry(intent.extras)
        }
        GeneralHelper.setToolbar(this, toolbar, getTitleBar())

        mParameterKonfirmasiModel = setParameterKonfirmasi()

        //Error Snackbar
        if (errorMessage != null) {
            showSnackbarErrorMessage(errorMessage, ALERT_ERROR, this, false)
            errorMessage = null
        }
        injectDependency()
        setupView()
    }

    override fun onResume() {
        injectDependency()
        super.onResume()
    }

    private fun parseDataIntentInquiry(savedInstanceState: Bundle?) {
        if (savedInstanceState != null) {
            try {
                val tempInquiry = savedInstanceState.getString(TAG_INQUIRY_DATA)
                if (tempInquiry != null) {
                    mFormPulsaDataResponse = Gson().fromJson(
                        tempInquiry,
                        FormPulsaDataResponse::class.java
                    )
                }
                val tempParams = savedInstanceState.getString(TAG_PARAMS_DATA)
                if (tempParams != null) {
                    mParameterModel = Gson().fromJson(tempParams, ParameterModel::class.java)
                }
                mUrlKonfirmasi =
                    savedInstanceState.getString(BaseInquiryRevampActivity.TAG_URL_KONFIRM) ?: ""
                mUrlPayment =
                    savedInstanceState.getString(BaseInquiryRevampActivity.TAG_URL_PAYMENT) ?: ""
                mUrlPending =
                    savedInstanceState.getString(BaseInquiryRevampActivity.TAG_URL_PENDING) ?: ""
                mTitle = savedInstanceState.getString(BaseInquiryRevampActivity.TAG_TITLE) ?: ""
            } catch (e: Exception) {
                if (!GeneralHelper.isProd()) {
                    Log.e(TAG, "parseDataIntentInquiry: ", e)
                }
            }
        }
    }

    private fun injectDependency() {
        activityComponent.inject(this)

        //get prresenter from dagger inject
        brivaPresenter.view = this
        brivaPresenter.setUrlConfirmation(mUrlKonfirmasi)
        if (isFromFastMenu) {
            brivaPresenter.setUrlIndosat(GeneralHelper.getString(R.string.url_list_paket_indosat_fm))
        } else {
            brivaPresenter.setUrlIndosat(GeneralHelper.getString(R.string.url_list_paket_indosat))
        }
        brivaPresenter.start()
    }

    private fun setupView() {
        if (brivaPresenter.getLanguage().equals(Constant.LANGUAGE_ENGLISH)){
            bannerRelative.setBackgroundResource(R.drawable.background_cardview_banner_telkomsel_v2_english)
        }
        if (mmNominal != "") {
            mNominal = mmNominal
        }

        if (!mDataId.equals("")) {
            mDataId = mmDataId
        }

        //set callback on klik paket atau pulsa
        pulsaRevampFragment.setSelectedPulsaListener(this)
        paketDataRevampFragment.setSelectedPaketListener(this)

        providerList.addAll(mFormPulsaDataResponse.provider)
        editTextPulsa.addTextChangedListener(activityTextListener)
        editTextPulsa.setOnEditorActionListener { v, actionId, event ->
            if (actionId == EditorInfo.IME_ACTION_DONE) {
                this.onFocusChange(editTextPulsa, false)
                return@setOnEditorActionListener true
            }
            return@setOnEditorActionListener false
        }
        editTextPulsa.onFocusChangeListener = this@InquiryPulsaRevampRevActivity
        if (isFromFastMenu) {
            editTextPulsa.isEnabled = false
            editTextPulsa.setTextColor(resources.getColor(R.color.neutral_light60))
            tilNorek.background = resources.getDrawable(R.drawable.edittext_disable_revamp)
            imgContact.isEnabled = false
            imgContact.alpha = 0.3f
        } else {
            editTextPulsa.isEnabled = true
            imgContact.isEnabled = true
            imgContact.alpha = 1f
        }
        etSaveAs.addTextChangedListener(object : TextWatcher {
            override fun beforeTextChanged(p0: CharSequence?, p1: Int, p2: Int, p3: Int) {
            }

            override fun onTextChanged(p0: CharSequence?, p1: Int, p2: Int, p3: Int) {
            }

            override fun afterTextChanged(p0: Editable?) {
                if (etSaveAs.length() > 0) {
                    booleanText = false
                    saveStr = etSaveAs.text.toString()
                    disableButtonSubmit(false)
                } else if (etSaveAs.text.toString() == "") {
                    booleanText = true
                    disableButtonSubmit(true)
                }
            }

        })
        editTextPulsa.setText(mNoHp)
        imgContact.setOnClickListener(this)
        imgCancel.setOnClickListener(this)
        btnSubmitPulsa.setOnClickListener(this)
        cvBanner.setOnClickListener(this)
        btnSubmitPulsa.text = mParameterModel.stringButtonSubmit
        llTotalBayar.setOnClickListener(this)

        checkCustomPaket()

    }

    /**
     * Method untuk mencari prefix sesuai dengan Prefix 5 digit nomor telpon.
     *
     * @param phone 5 digit pertaman nomor telpon
     */
    fun prefix(phone: String) {
        setRightProvider(0, "Operator", "")
        var prefixInput = phone.substring(0, 4)
        var prefixInputCustom = "";
        if (phone.length >= 5) {
            prefixInputCustom = phone.substring(0, 5)
        }

        for (p in providerList) {
            if ((prefixInputCustom.isNotEmpty() && GeneralHelper.isContains(
                    p.prefix,
                    prefixInputCustom
                )) || GeneralHelper.isContains(p.prefix, prefixInput)
            ) {
                isNominalShow = false
                currentProvider = p
                if (isFromFastMenu) {
                    imgCancel.visibility = View.GONE
                    imgContact.visibility = View.VISIBLE
                } else {
                    imgCancel.visibility = View.VISIBLE
                    imgContact.visibility = View.GONE
                }
                setRightProvider(p.pulsaCode.toInt(), p.name, p.iconPath)
                showNominal(currentProvider)
                isProviderFound = true
                checkBanner()
                break
            }
        }
    }


    private fun checkCustomPaket() {
        if (isProviderFound && currentProvider.dataCode.equals("20") && editTextPulsa.text.toString().length > 9 && !mTempPhoneNum.equals(
                editTextPulsa.text.toString()
            )
        ) {
            brivaPresenter.getCustomPaket(
                editTextPulsa.text.toString(),
                currentProvider.dataCode,
                isFromFastMenu
            )
        } else if (mTempPhoneNum != editTextPulsa.text.toString()) {
            if (getFragmentRefreshListener() != null) {
                getFragmentRefreshListener()?.hideCustomData()
            }
        } else if (getFragmentRefreshListener() != null) {
            getFragmentRefreshListener()?.notifyRefresh()
        }
    }


    /**
     * menampilkan kode provider pada view atas edittext
     *
     * @param providerId kode provider
     */
    protected fun setRightProvider(providerId: Int, providerName: String, urlImage: String) {
        isLoaded = if (!isLoaded && providerId != 0) {
            try {
                tvNamaProvider.text = providerName
                GeneralHelper.loadImageUrl(
                    applicationContext,
                    urlImage,
                    ivLogoHp,
                    R.drawable.logo_bri,
                    0
                )
                mProviderId = providerId
                mProviderName = providerName
            } catch (e: java.lang.Exception) {
            }
            true
        } else {
            ivLogoHp.setImageResource(R.drawable.ic_default_pulsa)
            tvNamaProvider.text = providerName
            false
        }
    }

    fun showNominal(newProvider: ProviderItem?) {
        pulsaRevampFragment.provider = newProvider
        paketDataRevampFragment.provider = newProvider

        if (!mmNominal.isEmpty()) {
            pulsaRevampFragment.nominal = mNominal
        }

        if (!mmDataId.isEmpty()) {
            paketDataRevampFragment.dataId = mDataId
        }

        fragmentList.add(pulsaRevampFragment)
        fragmentList.add(paketDataRevampFragment)

        if (!isNominalShow) {
            onAnimatorShow(llContentPulsa, true, Constant.REQUEST_PULSA)
            onAnimatorShow(vpPulsaPaket, true, Constant.REQUEST_PULSA)
            onAnimatorShow(rlParentTop, true, Constant.REQUEST_PULSA)

            val listJudul = listOf(GeneralHelper.getString(R.string.txt_pulsa), GeneralHelper.getString(R.string.paket_data))
            val catatanKeuanganAdapter =
                CatatanKeuanganAdapter(supportFragmentManager, this, fragmentList, listJudul)
            catatanKeuanganAdapter.notifyDataSetChanged()
            vpPulsaPaket.adapter = catatanKeuanganAdapter
            stPulsaPaket.setViewPager(vpPulsaPaket)//Add bold effect on selected tab
            stPulsaPaket.setOnPageChangeListener(this)
            linearLayout = stPulsaPaket.getChildAt(0) as LinearLayout
            GeneralHelper.changeTabsFontBoldRevamp(this, linearLayout, 0)

            //jika purchase type dari history "pulsa" set tab ke tab Pulsa, jika "data" ke Paket Data
            if (mPurchaseType.lowercase().contains("pulsa")) {
                vpPulsaPaket.currentItem = 0
            } else if (mPurchaseType.lowercase().contains("data")) {
                vpPulsaPaket.currentItem = 1
            } else {
                vpPulsaPaket.currentItem = 0
            }


            //set default page adapter
            isNominalShow = true
        }
        mmNominal = ""
        mmDataId = ""
    }

    fun checkBanner() {
        if (editTextPulsa.text.length > 8) {
            try {
                val phone = editTextPulsa.text.toString()
                val prefix = phone.substring(0, 4)
                if (currentProvider.omniUrl.isNotEmpty() && listOfCanShowBanner.any { it == prefix }) {
                    cvBanner.visibility = View.VISIBLE
                    tvBanner.text = currentProvider.omniDesc
                } else {
                    cvBanner.visibility = View.GONE
                }
            } catch (e: java.lang.Exception) {
                // do nothing
            }
        } else {
            cvBanner.visibility = View.GONE
        }
    }


    override fun changeText(charSequence: CharSequence, i: Int, i1: Int, i2: Int) {
        checkBanner()
        when {
            (charSequence.length >= 4 && charSequence.length < 5) -> {
                if (!isProviderFound) {
                    prefix(charSequence.toString().substring(0, 4))
                }
            }

            (charSequence.length == 5 || (charSequence.length >= 5 && !isProviderFound)) -> {
                prefix(charSequence.toString().substring(0, 5))
            }

            charSequence.length > 8 -> {
                disableButtonSubmit(false)
            }

            charSequence.length < 4 -> {
                resetNominal()
            }

        }
    }

    override fun afterText(editable: Editable) {
        if (editable.length in 2..7) {
            setHelperTextError(true)
        } else setHelperTextError(false)
        if (editable.length >= 4) {
            if (!isProviderFound) {
                if (SystemClock.elapsedRealtime() - mLastClickTime < 3000) {
                    return
                }
                mLastClickTime = SystemClock.elapsedRealtime()
                GeneralHelper.showSnackBarRevampLines(findViewById(R.id.content), GeneralHelper.getString(R.string.txt_nomor_anda), 3)
            }
            if (editable.length > 8 && isProviderFound) {
                disableButtonSubmit(true)
                hideCard()
                setupFavorite()
            } else {
                disableButtonSubmit(true)
                switchSave.isChecked = false
                rlFavorit.visibility = View.GONE
            }
        }

        if (editable.length < 9 && getFragmentRefreshListener() != null) {
            getFragmentRefreshListener()?.hideCustomData()
        }
    }

    override fun getAmount(): Int {
        return 0
    }

    override fun onSubmit() {

    }

    override fun setDefaultSaldo(
        saldo: Double,
        saldoString: String,
        account: String,
        saldoHold: Boolean,
    ) {
    }

    override fun setParameterKonfirmasi(): ParameterKonfirmasiModel {
        val parameterKonfirmasiModel = ParameterKonfirmasiModel()
        parameterKonfirmasiModel.stringLabelTujuan =
            mParameterModel.stringLabelTujuan
        parameterKonfirmasiModel.stringButtonSubmit =
            GeneralHelper.getString(R.string.txt_konfirmasi)
        parameterKonfirmasiModel.defaultIcon =
            mParameterModel.defaultIcon

        return parameterKonfirmasiModel
    }

    override fun onSuccessGetPaketCustom(
        dataListCustomRevamp: DataListCustomRevamp,
        phoneNumber: String,
    ) {
        if (fragmentList.size > 1) {
            mTempPhoneNum = phoneNumber
            mRefNumCustomPackage = dataListCustomRevamp.referenceNumber
            updateFragmentPaketData(dataListCustomRevamp.listData as ArrayList<DataListRevamp>?)
        }
    }

    private fun getTitleBar(): String {
        return GeneralHelper.getString(R.string.pulsa_paket_data_toolbar_title_pulsadata)
    }

    private fun setupFavorite() {
        for (i in mSavedResponses.indices) {
            val yourArray: List<String> = mSavedResponses[i].value.split("|")
            mNomorPembanding = yourArray[1]
            listStringSaved.add(mNomorPembanding)

            if (GeneralHelper.isContains(listStringSaved, editTextPulsa.text.toString())) {
                rlFavorit.visibility = View.GONE
            } else {
                if (isFromFastMenu) {
                    rlFavorit.visibility = View.GONE
                } else {
                    switchSave.setOnCheckedChangeListener { _, b ->
                        if (b) {
                            rlSaveAs.visibility = View.VISIBLE
                            etSaveAs.setText(currentProvider.name)
                            saveStr = etSaveAs.text.toString()
                            booleanCheck = true
                            disableButtonSubmit(false)
                        } else {
                            rlSaveAs.visibility = View.GONE
                            etSaveAs.setText("")
                            booleanCheck = false
                            saveStr = etSaveAs.text.toString()
                            disableButtonSubmit(false)
                        }
                    }
                    etSaveAs.hint = currentProvider.name
                    saveStr = etSaveAs.text.toString()
                    rlFavorit.visibility = View.VISIBLE
                }
            }
        }

        if (listStringSaved.isEmpty()) {
            switchSave.setOnCheckedChangeListener { _, b ->
                if (b) {
                    rlSaveAs.visibility = View.VISIBLE
                    etSaveAs.setText(currentProvider.name)
                    saveStr = etSaveAs.text.toString()
                    disableButtonSubmit(false)

                } else {
                    rlSaveAs.visibility = View.GONE
                    etSaveAs.setText("")
                    saveStr = etSaveAs.text.toString()
                    disableButtonSubmit(false)
                }
            }
            etSaveAs.hint = saveStr
            saveStr = etSaveAs.text.toString()
            rlFavorit.visibility = View.VISIBLE
        }
    }

    private fun resetNominal() {
        isProviderFound = false
        isLoaded = false
        imgCancel.visibility = View.GONE
        imgContact.visibility = View.VISIBLE
        //hide RV Nominal Option
        hideNominal()
        //hide card selected packet
        hideCard()
        //remove icon provider
        setRightProvider(0, "Operator", "")
    }

    private fun hideNominal() {
        if (isNominalShow) {
            onAnimatorFade(llContentPulsa, true, Constant.REQUEST_PULSA)
            onAnimatorFade(vpPulsaPaket, true, Constant.REQUEST_PULSA)
            onAnimatorFade(rlParentTop, true, Constant.REQUEST_PULSA)
            isNominalShow = false
        }
    }

    private fun showCard() {
        if (!isCardShow) {
            onAnimatorShow(llButton, true, Constant.REQUEST_PULSA)
            isCardShow = true
        }
        SizeHelper.setMarginsView(this, llContentPulsa, 0, 0, 0, 0)
    }

    override fun onClick(view: View) {
        when (view.id) {
            R.id.img_contact -> {
                checkContactPermission()
            }

            R.id.img_cancel -> {
                editTextPulsa.text.clear()
                rlFavorit.visibility = View.GONE
                rlSaveAs.visibility = View.GONE

            }

            R.id.btnSubmitPulsa -> {
                submit()
            }

            R.id.ll_total_bayar -> {
                val bottomFragmentPulsaRevamp = BottomFragmentPulsaRevamp.newInstance(
                    mBiayaAdmin,
                    mNominal,
                    this,
                    disableButtonBottomFragment,
                    type
                )
                bottomFragmentPulsaRevamp.show(supportFragmentManager, "")
            }

            R.id.cv_banner -> {
                if (switchSave.isChecked && etSaveAs.text.toString().isEmpty()) {
                    showSnackbarErrorMessageRevamp(
                        GeneralHelper.getString(R.string.snackbar_message_empty_save),
                        ALERT_ERROR,
                        this@InquiryPulsaRevampRevActivity,
                        false
                    )
                    return
                }
                val currNoHp = editTextPulsa.text.toString()
                val intentLaunch = Intent();
                intentLaunch.putExtra(Constant.INTENT_PROVIDER_ID, currentProvider.dataCode)
                intentLaunch.putExtra(
                    Constant.INTENT_PARAMETER_KONFIRMASI,
                    mParameterKonfirmasiModel.toString()
                )
                intentLaunch.putExtra(
                    Constant.INTENT_PAKET_CUSTOM_SAVE_AS,
                    etSaveAs.text.toString()
                )
                intentLaunch.putExtra(Constant.INTENT_PAKET_CUSTOM_PHONE_NO, currNoHp)
                MicrositeCallbackActivity.launchIntent(
                    this,
                    currentProvider.omniUrl + currNoHp,
                    GeneralHelper.getString(R.string.pulsa_paket_data_pulsa_data_title),
                    Constant.CALLBACK_MICROSITE_TELKOMSEL,
                    GeneralHelper.getString(R.string.journey_type_microsite_paket),
                    intentLaunch
                )
            }
        }
    }

    private fun submit() {
        if (mIsCustomPackage) {
            handleCustomPackage()
        } else {
            brivaPresenter.setUrlConfirmation(mUrlKonfirmasi)
            brivaPresenter.getDataConfirmationPulsaRevamp(
                mFormPulsaDataResponse.referenceNumber,
                currentProductId,
                editTextPulsa.text.toString(),
                currentAmount,
                saveStr,
                item,
                type,
                "",
                isFromFastMenu
            )
        }
    }

    private fun handleCustomPackage() {
        if (isFromFastMenu) {
            brivaPresenter.setUrlConfirmation(GeneralHelper.getString(R.string.url_confirmation_data_indosat_fm))
        } else {
            brivaPresenter.setUrlConfirmation(GeneralHelper.getString(R.string.url_confirmation_data_indosat))
        }
        brivaPresenter.getDataConfirmationPulsaRevampCustom(
            mRefNumCustomPackage,
            currentProductId,
            editTextPulsa.text.toString(),
            saveStr,
            item,
            type,
            "",
            isFromFastMenu
        )
    }

    private fun hideCard() {
        //jika user pernah memilih nominal paket
        if (isCardShow) {
            onAnimatorFade(llButton, true, Constant.REQUEST_PULSA)
            try {
                if (currentProvider.dataList != null) {
                    if (currentProvider.dataList.size > 0) paketDataRevampFragment.clearSelected()
                }
                if (currentProvider.pulsaList != null) {
                    if (currentProvider.pulsaList.size > 0) pulsaRevampFragment.clearSelectedOption()
                }
            } catch (e: java.lang.Exception) {
                if (!GeneralHelper.isProd()) {
                    Log.e(TAG, "hideCard: ", e)
                }
            }
            isCardShow = false
        }
        SizeHelper.setMarginsView(this, llContentPulsa, 0, 0, 0, 0)
    }


    @SuppressLint("SetTextI18n")
    override fun onPaketSelected(paketDataModel: DataListRevamp, isCustomPackage: Boolean) {
        mIsCustomPackage = isCustomPackage
        showCard()
        // wrong input validation after hit endpoint
        if (editTextPulsa.length() < 8) {
            setHelperTextError(true)
            return
        } else {
            setHelperTextError(false)
        }

        type = GeneralHelper.getString(R.string.txt_data)
        tvDetailPembayaran.text = GeneralHelper.getString(R.string.total_payment)
        total = paketDataModel.amount.toDouble() + paketDataModel.adminFee.toDouble()
        tvNominalPembayaran.text = "Rp" + GeneralHelper.formatNominal(total)
        currentAmount = paketDataModel.amount.toString()
        currentProductId = currentProvider.dataCode
        item = paketDataModel.id
        mNominal = paketDataModel.amount.toString()
        mBiayaAdmin = paketDataModel.adminFee

        if (booleanCheck) {
            if (booleanText) {
                disableButtonSubmit(true)
            } else
                disableButtonSubmit(false)
        }

    }

    @SuppressLint("SetTextI18n")
    override fun onBuyPaket(paketDataModel: DataListRevamp) {
        showCard()
        tvDetailPembayaran.text = GeneralHelper.getString(R.string.total_pembelian)
        tvNominalPembayaran.text = "Rp" + GeneralHelper.formatNominal(paketDataModel.amountString)
        currentAmount = paketDataModel.amount.toString()
        currentProductId = currentProvider.dataCode
        item = paketDataModel.id
    }

    @SuppressLint("SetTextI18n")
    override fun onPulsaSelected(nominal: OptionNominalModel) {
        showCard()
        if (editTextPulsa.length() < 8) {
            setHelperTextError(true)
            return
        } else {
            setHelperTextError(false)
        }
        type = GeneralHelper.getString(R.string.txt_pulsa).lowercase()
        total = nominal.harga.toDouble() + nominal.biaya_admin.toDouble()
        tvDetailPembayaran.text = GeneralHelper.getString(R.string.total_payment)
        tvNominalPembayaran.text = "Rp" + GeneralHelper.formatNominal(total)
        mNominal = nominal.harga.toString()
        mBiayaAdmin = nominal.biaya_admin
        currentAmount = nominal.harga.toString()
        currentProductId = currentProvider.pulsaCode
        item = nominal.harga.toString()
        if (booleanCheck) {
            if (booleanText) {
                disableButtonSubmit(true)
            } else
                disableButtonSubmit(false)
        }
    }

    private fun setHelperTextError(enable: Boolean) {
        if (enable) {
            tilNorek.helperText = GeneralHelper.getString(R.string.txt_masukan_minimal)
            disableButtonSubmit(true)
            tilNorek.setHelperTextColor(
                ContextCompat.getColorStateList(
                    this,
                    R.color.error80
                )
            )
            tilNorek.setBoxStrokeColorStateList(
                ContextCompat.getColorStateList(
                    this,
                    R.color.error80
                )!!
            )
        } else {
            tilNorek.helperText = ""
            disableButtonSubmit(false)
            tilNorek.setHelperTextColor(
                ContextCompat.getColorStateList(
                    this,
                    R.color.neutral_dark40
                )
            )
            tilNorek.setBoxStrokeColorStateList(
                ContextCompat.getColorStateList(
                    this,
                    R.color.primary_blue80
                )!!
            )
        }
    }

    override fun onPageScrolled(position: Int, positionOffset: Float, positionOffsetPixels: Int) {
    }

    override fun onPageSelected(position: Int) {
        GeneralHelper.changeTabsFontBoldRevamp(this, linearLayout, position)
    }

    override fun onPageScrollStateChanged(state: Int) {
        hideCard()
    }

    /**
     * Override method untuk disable/enable button submit
     *
     * @param disable
     */
    protected fun disableButtonSubmit(disable: Boolean) {
        if (disable) {
            btnSubmitPulsa.isEnabled = false
            disableButtonBottomFragment = false
            btnSubmitPulsa.setTextColor(GeneralHelper.getColor(R.color.neutral_light60))
        } else {
            btnSubmitPulsa.isEnabled = true
            disableButtonBottomFragment = true
            btnSubmitPulsa.setTextColor(GeneralHelper.getColor(R.color.whiteColor))
        }
    }

    override fun onContactPicked(number: String) {
        super.onContactPicked(number)
        var newNumber = number
        if (number.substring(0, 1) != "0") {
            newNumber = "0" + number.substring(2)
        }
        editTextPulsa.setText(newNumber)
        if (newNumber.length >= 4) {
            prefix(newNumber)
        } else {
            disableButtonSubmit(true)
        }
        if (isProviderFound && currentProvider.dataCode.equals("20") && editTextPulsa.text.toString().length > 9) {
            brivaPresenter.getCustomPaket(editTextPulsa.text.toString(), currentProvider.dataCode, isFromFastMenu)
        }
    }

    @Suppress("DEPRECATION")
    override fun onActivityResult(requestCode: Int, resultCode: Int, data: Intent?) {
        super.onActivityResult(requestCode, resultCode, data)
        if (requestCode == Constant.REQ_PAYMENT) {
            if (resultCode == RESULT_OK) {
                setResult(RESULT_OK, data)
                finish()
            } else if (resultCode == Constant.REQ_PETTUNJUK1) {

            } else {
                this.setResult(RESULT_CANCELED, data)
                finish()
            }

        } else if (requestCode == Constant.REQ_CODE_MICROSITE) {
            if (resultCode == RESULT_OK) {
                setResult(RESULT_OK, data)
                finish()
            } else {
                this.setResult(RESULT_CANCELED, data)
                errorMessage = data?.getStringExtra(Constant.TAG_ERROR_MESSAGE)
                if (errorMessage != null) {
                    showSnackbarErrorMessageRevamp(errorMessage, ALERT_ERROR, this, false)
                    errorMessage = null
                }
            }
        }
    }

    override fun onBackPressed() {
        brivaPresenter.stop()
        super.onBackPressed()
    }

    override fun onClickItem(type: String) {
        this.type = type
        submit()
    }

    override fun onSuccessGetConfirmation(brivaConfirmationResponse: GeneralConfirmationResponse) {
        KonfirmasiGeneralOpenRevampActivity.launchIntent(
            this,
            brivaConfirmationResponse,
            mUrlPayment,
            GeneralHelper.getString(R.string.pulsa_paket_data_toolbar_title_pulsadata),
            setParameterKonfirmasi(),
            isFromFastMenu,
            true,
            isBack = false
        )
    }

    override fun onSuccessGetConfirmationCustom(brivaConfirmationResponse: GeneralConfirmationResponse) {
        val mUrlPaymentCustom =
            if (isFromFastMenu) {
                GeneralHelper.getString(R.string.url_payment_data_indosat_fm)
            } else {
                GeneralHelper.getString(R.string.url_payment_data_indosat)
            }

        KonfirmasiGeneralOpenRevampActivity.launchIntent(
            this,
            brivaConfirmationResponse,
            mUrlPaymentCustom,
            GeneralHelper.getString(R.string.pulsa_paket_data_toolbar_title_pulsadata),
            setParameterKonfirmasi(),
            isFromFastMenu,
            true,
            isBack = false
        )
    }

    override fun onFailedGetCustomPackage() {
        onSuccessGetPaketCustom(DataListCustomRevamp(), "")
    }

    override fun onException93(message: String) {
        val returnIntent = Intent()
        returnIntent.putExtra(Constant.TAG_ERROR_MESSAGE, message)
        this.setResult(RESULT_CANCELED, returnIntent)
        finish()
    }

    override fun onFocusChange(v: View?, hasFocus: Boolean) {
        when (v) {
            editTextPulsa -> {
                if (!hasFocus) {
                    checkCustomPaket()
                }
            }
        }
    }

    private fun updateFragmentPaketData(dataList: ArrayList<DataListRevamp>?) {
        if (getFragmentRefreshListener() != null) {
            getFragmentRefreshListener()?.onRefresh(dataList);
        }
    }

    interface FragmentRefreshListener {
        fun onRefresh(dataList: ArrayList<DataListRevamp>?)
        fun hideCustomData()
        fun notifyRefresh()
    }
}