package id.co.bri.brimo.ui.activities.depositorevamp

import android.app.Activity
import android.content.Intent
import android.graphics.Color
import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.widget.LinearLayout
import androidx.fragment.app.Fragment
import androidx.fragment.app.FragmentManager
import androidx.fragment.app.FragmentStatePagerAdapter
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.swiperefreshlayout.widget.SwipeRefreshLayout
import androidx.viewpager.widget.ViewPager
import com.ethanhua.skeleton.Skeleton
import com.ethanhua.skeleton.SkeletonScreen
import id.co.bri.brimo.R
import id.co.bri.brimo.adapters.dashboardInvestasi.FaqInvestasiAdapter
import id.co.bri.brimo.adapters.depositorevamp.ListDepositoRevampAdapter
import id.co.bri.brimo.adapters.depositorevamp.ListUpdateRekeningDepositoRevampAdapter
import id.co.bri.brimo.contract.IPresenter.depositorevamp.IOnBoardingDepositoRevampPresenter
import id.co.bri.brimo.contract.IView.depositorevamp.IOnBoardingDepositoRevampView
import id.co.bri.brimo.data.preference.BRImoPrefRepository
import id.co.bri.brimo.databinding.ActivityDashboardDepositoBinding
import id.co.bri.brimo.databinding.FragmentBottomSheetTidakAdaDepositoRevampBinding
import id.co.bri.brimo.databinding.FragmentBottomSheetUpdateRekeningDepositoRevampBinding
import id.co.bri.brimo.domain.config.Constant
import id.co.bri.brimo.domain.config.FontConfig
import id.co.bri.brimo.domain.helpers.GeneralHelper
import id.co.bri.brimo.domain.helpers.bubbleShowCaseView.BubbleShowCase
import id.co.bri.brimo.domain.helpers.bubbleShowCaseView.BubbleShowCaseBuilder
import id.co.bri.brimo.domain.helpers.bubbleShowCaseView.BubbleShowCaseSequence
import id.co.bri.brimo.models.apimodel.response.EmptyRekeningResponse
import id.co.bri.brimo.models.apimodel.response.TermConditionTabRes
import id.co.bri.brimo.models.apimodel.response.depositorevamp.GetListDepositoResponse
import id.co.bri.brimo.models.apimodel.response.depositorevamp.ListDepositoUpdateResponse
import id.co.bri.brimo.ui.activities.base.BaseActivity
import id.co.bri.brimo.ui.activities.deposito.InfoBukaDepositoActivity
import id.co.bri.brimo.ui.customviews.dialog.DialogExitCustom
import id.co.bri.brimo.ui.customviews.dialog.DialogInformation
import id.co.bri.brimo.ui.customviews.dialog.DialogValas
import id.co.bri.brimo.ui.fragments.bottomsheet.BottomSheetCustomViewGeneralFragment
import id.co.bri.brimo.ui.fragments.deposito.ListDepositoFragment
import javax.inject.Inject


class DashboardDepositoActivity : ViewPager.OnPageChangeListener, BaseActivity(),
    IOnBoardingDepositoRevampView, DialogInformation.OnActionClick,
    DialogValas.DialogDefaultButtonListener, ListDepositoFragment.OnRefreshListener,
    DialogExitCustom.DialogClickYesNoListener,
    ListDepositoFragment.OnBubleCaseListener {
    private lateinit var binding: ActivityDashboardDepositoBinding
    private var lyTabs: LinearLayout? = null
    private val brImoPrefRepository = BRImoPrefRepository(this)

    @Inject
    lateinit var presenter: IOnBoardingDepositoRevampPresenter<IOnBoardingDepositoRevampView>

    private var skeletonListDepo: SkeletonScreen? = null
    private var skelotonCurrency: SkeletonScreen? = null
    private var skeletonInfoDeposito: SkeletonScreen? = null
    private var listDepositoAdapter: ListDepositoRevampAdapter? = null
    private var skelotonCardCurrencyItem1: SkeletonScreen? = null
    private var skelotonCardCurrencyItem2: SkeletonScreen? = null
    private var skelotonCardCurrencyItem3: SkeletonScreen? = null

    private var listDepositoFragment: ListDepositoFragment? = null

    private var listDepositoFragmentAdapter: MyPagerAdapter? = null
    private var listTitle: ArrayList<String> = arrayListOf()
    private var currentTabActive = 0
    private var isUpdate = false
    private var isBubleCaseShow = false

    companion object {
        private var isFromInvestasi = false

        @JvmStatic
        fun launchIntent(
            caller: Activity,
            fromFastMenu: Boolean,
            isFromIvestasi: Boolean? = false
        ) {
            val intent = Intent(caller, DashboardDepositoActivity::class.java)
            isFromFastMenu = fromFastMenu
            if (isFromIvestasi != null) {
                this.isFromInvestasi = isFromIvestasi
            }
            caller.startActivityForResult(intent, Constant.REQ_PAYMENT)
        }
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        binding = ActivityDashboardDepositoBinding.inflate(layoutInflater)
        setContentView(binding.root)
        isUpdate = false
        injectDependency()
        setSkeletonView()
        setOnFloatButton()
        binding.tbDeposito.toolbar.setBackgroundColor(GeneralHelper.getColor(R.color.colorStatusDiterima))
        GeneralHelper.setToolbarRevamp(
            this,
            binding.tbDeposito.toolbar,
            GeneralHelper.getString(R.string.toolbar_deposito)
        )
        presenter.getListDepositoAccount()
        setStatusColor(R.color.colorStatusDiterima)
    }

    private fun setOnFloatButton() {
        binding.btnScrollTop.setOnClickListener {
            binding.scrlView.fullScroll(binding.scrlView.top);
        }

        binding.scrlView.viewTreeObserver.addOnScrollChangedListener {
            val scrollY = binding.scrlView.scrollY
            val scrollViewHeight = binding.scrlView.height
            val contentHeight = binding.scrlView.getChildAt(0).height

            if (scrollY + scrollViewHeight >= contentHeight * 0.6) {
                binding.btnScrollTop.visibility = View.VISIBLE
            } else {
                binding.btnScrollTop.visibility = View.GONE
            }
        }
    }

    private fun initiateAdapterListDepo(getListDepositoResponse: GetListDepositoResponse) {
        listTitle.clear()
        getListDepositoResponse.accountList.forEach {
            listTitle.add(it.currency.toString())
        }

        listDepositoFragmentAdapter = MyPagerAdapter(
            supportFragmentManager,
            presenter,
            listTitle,
            getListDepositoResponse.accountList,
            getListDepositoResponse.faq,
            this,
            this
        )
        binding.vpDashboardDeposito.adapter = listDepositoFragmentAdapter
        binding.tabListCurrency.setViewPager(binding.vpDashboardDeposito)
        binding.tabListCurrency.setOnPageChangeListener(this)
        lyTabs = binding.tabListCurrency.getChildAt(0) as LinearLayout
        if (getListDepositoResponse.accountList.size == 1) {
            binding.tabListCurrency.visibility = View.GONE
            lyTabs?.visibility = View.GONE
            binding.flListCurrency.visibility = View.GONE
            binding.spacer.visibility = View.VISIBLE
        }
        binding.vpDashboardDeposito.currentItem = currentTabActive
    }

    private fun injectDependency() {
        activityComponent.inject(this)
        presenter.view = this
        presenter.setTermUrl(GeneralHelper.getString(R.string.url_deposito_open_term_condition))
        presenter.setUrl(GeneralHelper.getString(R.string.url_deposito_revamp_dashboard))
        presenter.start()
    }

    override fun onPageScrolled(position: Int, positionOffset: Float, positionOffsetPixels: Int) {

        //do nothing

    }


    override fun onPageSelected(position: Int) {
        //change default style toolbar font
        GeneralHelper.changeTabsFontBoldForDeposito(this, lyTabs, position)
    }

    override fun onPageScrollStateChanged(state: Int) {
        //do nothing
    }

    override fun showProgressTerm() {
        GeneralHelper.showDialog(this)
    }

    override fun hideProgressTerm() {
        GeneralHelper.dismissDialog()
    }

    override fun onGetDataTerm(termConditionTabRes: TermConditionTabRes?) {
        InfoBukaDepositoActivity.launchIntent(this, termConditionTabRes)
    }

    override fun onGetDataProses(rekeningResponse: EmptyRekeningResponse) {
        val dialog = DialogInformation(
            this, "maaf_maaf",
            rekeningResponse.title, rekeningResponse.description,
            GeneralHelper.getString(R.string.ok), this, true, true
        )
        val ft = supportFragmentManager.beginTransaction()
        ft.add(dialog, null)
        ft.commitAllowingStateLoss()
    }


    private fun addBubbleShowCase() {
        listDepositoFragment = listDepositoFragmentAdapter?.getFragment()

        val list: MutableList<BubbleShowCaseBuilder> = arrayListOf()

        if (listTitle.size > 1) {
            list.add(getBubleShowCaseCurrency())
        }

        val listBubleCase = listDepositoFragment?.getListBubleCase()
        if (listBubleCase != null) {
            list.addAll(listBubleCase)
        }

        BubbleShowCaseSequence()
            .addShowCases(list)
            .show()
    }


    private fun getBubleShowCaseCurrency(): BubbleShowCaseBuilder {
        return BubbleShowCaseBuilder(this) //Activity instance
            .title(GeneralHelper.getString(R.string.deposito_revamp_bubble_case_title)) //Any title for the bubble view
            .description(GeneralHelper.getString(R.string.deposito_revamp_bubble_case_desc))
            .setFontTitle(FontConfig.BRI_BOLD)
            .setFontText(FontConfig.BRI_MEDIUM)
            .backgroundColor(Color.WHITE)
            .textColor(Color.BLACK)
            .titleTextSize(18)
            .buttonTitle(GeneralHelper.getString(R.string.berikutnya))
            .targetView(findViewById(R.id.fl_list_currency))
            .arrowPosition(BubbleShowCase.ArrowPosition.TOP)
            .enableLewati(true)
            .textViewLewati(GeneralHelper.getString(R.string.lewati))
            .textColorDesc(GeneralHelper.getColor(R.color.neutral_dark10))
    }

    override fun onNoDepositoFound(getListDepositoResponse: GetListDepositoResponse) {
        val dataFaq = getListDepositoResponse.faq
        OnBoardingDepositoRevampActivity.launchIntent(this, dataFaq)
    }

    override fun onDashboardDepositoView(getListDepositoResponse: GetListDepositoResponse) {
        binding.vpDashboardDeposito.adapter = null
        binding.tabListCurrency.setViewPager(null)
        lyTabs = null
        initiateAdapterListDepo(getListDepositoResponse)
        onShowDashboard()
        GeneralHelper.changeTabsFontBoldForDeposito(this, lyTabs, 0)
        if (isUpdate) {
            GeneralHelper.showSnackBarGreenRevamp(
                binding.content, getString(R.string.list_deposito_berhasil_diperbarui)
            )
        }
    }

    override fun showProgress() {
        GeneralHelper.showDialog(this)
    }

    override fun hideProgress() {
        GeneralHelper.dismissDialog()
    }


    override fun showDialogLimit(response: EmptyRekeningResponse?) {
        if (response!!.subDescription != null) {
            val dialogValas = DialogValas(
                this, this, response.title,
                response.description,
                response.subDescription,
                false,
                response.imageName
            )
            val ft = this.supportFragmentManager.beginTransaction()
            ft.add(dialogValas, null)
            ft.commitAllowingStateLoss()
        } else {
            val dialog = DialogInformation(
                this,
                response.imageName,
                response.title,
                response.description,
                this,
                true
            )
            val ft = supportFragmentManager.beginTransaction()
            ft.add(dialog, null)
            ft.commitAllowingStateLoss()
        }
    }


    override fun onClickAction() {
        //do nothing
    }

    override fun onClickYesBtn() {
        //do nothing
    }

    override fun onRefreshDashboard(swipeRefreshLayout: SwipeRefreshLayout) {
        isUpdate = false
        onShowSkeleton()
        currentTabActive = binding.vpDashboardDeposito.currentItem
        presenter.getListDepositoAccount()
        swipeRefreshLayout.isRefreshing = false
    }

    private fun setSkeletonView() {
        binding.tvTotalSaldo.text = ""
        binding.btnScrollTop.visibility = View.GONE
        listDepositoAdapter = ListDepositoRevampAdapter(
            this,
            GetListDepositoResponse.AccountList()
        )
        binding.rvDeposito.adapter = listDepositoAdapter
        binding.rvDeposito.layoutManager =
            LinearLayoutManager(this, LinearLayoutManager.VERTICAL, false)

        skelotonCardCurrencyItem1 = Skeleton.bind(binding.card1)
            .shimmer(true)
            .angle(5)
            .angle(5)
            .color(R.color.white)
            .duration(1200)
            .load(R.layout.item_skeleton_single_card_currency)
            .show()
        skelotonCardCurrencyItem2 = Skeleton.bind(binding.card2)
            .shimmer(true)
            .angle(5)
            .angle(5)
            .color(R.color.white)
            .duration(1200)
            .load(R.layout.item_skeleton_single_card_currency)
            .show()
        skelotonCardCurrencyItem3 = Skeleton.bind(binding.card3)
            .shimmer(true)
            .angle(5)
            .angle(5)
            .color(R.color.white)
            .duration(1200)
            .load(R.layout.item_skeleton_single_card_currency)
            .show()

        skelotonCurrency = Skeleton.bind(binding.tvTotalSaldo)
            .shimmer(true)
            .angle(5)
            .angle(5)
            .color(R.color.white)
            .duration(1200)
            .load(R.layout.item_skeleton_single_deposito_revamp)
            .show()

        skeletonListDepo = Skeleton.bind(binding.rvDeposito)
            .adapter(listDepositoAdapter)
            .shimmer(true)
            .count(3)
            .angle(5)
            .frozen(false)
            .color(R.color.white)
            .duration(1200)
            .load(R.layout.item_deposito_revamp_skeleton)
            .show()

        val infoDepositoAdapter = FaqInvestasiAdapter(this, arrayListOf())
        binding.rvInfoDeposito.adapter = infoDepositoAdapter
        binding.rvInfoDeposito.layoutManager =
            LinearLayoutManager(this, LinearLayoutManager.VERTICAL, false)
        skeletonInfoDeposito = Skeleton.bind(binding.rvInfoDeposito)
            .adapter(infoDepositoAdapter)
            .shimmer(true)
            .count(3)
            .angle(5)
            .frozen(false)
            .color(R.color.white)
            .duration(1200)
            .load(R.layout.item_skeleton_faq_deposito_revamp)
            .show()
    }

    private fun onShowDashboard() {
        binding.rlDashboard.visibility = View.VISIBLE
        binding.tabListCurrency.visibility = View.VISIBLE
        binding.lySkeleton.visibility = View.GONE
    }

    fun onShowSkeleton() {
        binding.rlDashboard.visibility = View.GONE
        binding.tabListCurrency.visibility = View.GONE
        binding.lySkeleton.visibility = View.VISIBLE
    }

    override fun onSuccessRekeningDepositoListUpdate(accounts: ListDepositoUpdateResponse) {
        bottomSheetUpdateRekening(accounts)
    }

    override fun onSuccessRekeningDepositoNoListUpdate(emptyRekeningResponse: EmptyRekeningResponse) {
        bottomSheetTidakAdaDeposito(emptyRekeningResponse)
    }

    private fun bottomSheetTidakAdaDeposito(emptyRekeningResponse: EmptyRekeningResponse) {
        val viewBind =
            FragmentBottomSheetTidakAdaDepositoRevampBinding.inflate(LayoutInflater.from(this))
        val bottomSheet = BottomSheetCustomViewGeneralFragment(viewBind.root, false, false) { }
        viewBind.tvDesc.text = emptyRekeningResponse.description
        viewBind.tvTitle.text = emptyRekeningResponse.title
        viewBind.btnBlue.text = GeneralHelper.getString(R.string.kembali)
        viewBind.btnWhite.visibility = View.GONE
        if (!supportFragmentManager.isStateSaved) {
            bottomSheet.show(supportFragmentManager, "")
        }
        viewBind.btnBlue.setOnClickListener {
            bottomSheet.dismissNow()
        }

    }

    private fun bottomSheetUpdateRekening(accounts: ListDepositoUpdateResponse) {
        val viewBind =
            FragmentBottomSheetUpdateRekeningDepositoRevampBinding.inflate(LayoutInflater.from(this))
        val bottomSheet = BottomSheetCustomViewGeneralFragment(viewBind.root, false, false) {

        }

        val list: ArrayList<Triple<String, String, Int>> = arrayListOf()
        list.add(Triple("", "", 0))
        accounts.account.forEach {
            if (it.new == true) {
                val accountString = it.accountString.toString()
                val name = it.name.toString()
                list.add(Triple(accountString, name, 1))
            }
        }


        viewBind.confirmBtn.setOnClickListener {
            isUpdate = true
            onShowSkeleton()
            presenter.getListDepositoAccount()
            bottomSheet.dismissNow()
        }

        viewBind.recyclerView.adapter = ListUpdateRekeningDepositoRevampAdapter(this, list)
        if (!supportFragmentManager.isStateSaved) {
            bottomSheet.show(supportFragmentManager, "")
        }

    }

    override fun onActivityResult(requestCode: Int, resultCode: Int, data: Intent?) {
        super.onActivityResult(requestCode, resultCode, data)

        if (requestCode == Constant.REQ_PAYMENT) {
            if (data != null) {

                isUpdate = false
                if (data.hasExtra("isRefresh") || data.hasExtra("isUpdate")) {
                    if (data.hasExtra("isUpdate")) {
                        isUpdate = data.getBooleanExtra("isUpdate", false)
                    }
                    if (isUpdate || data.getBooleanExtra("isRefresh", false)) {
                        if (data.hasExtra(Constant.TAG_MESSAGE)) {
                            val message = data.getStringExtra(Constant.TAG_MESSAGE)
                            GeneralHelper.showSnackBarGreenRevamp(binding.content, message)
                        }
                        onShowSkeleton()
                        presenter.getListDepositoAccount()
                    }

                } else {

                    if (resultCode == RESULT_OK) {
                        if (!isFromInvestasi) {
                            onShowSkeleton()
                            presenter.getListDepositoAccount()
                            setResult(RESULT_OK, data)
                        } else {
                            setResult(RESULT_OK, data)
                            finish()
                        }

                    } else if (resultCode == RESULT_CANCELED) {
                        if (data.hasExtra(Constant.TAG_ERROR_MESSAGE)) {
                            val message = data.getStringExtra(Constant.TAG_ERROR_MESSAGE)
                            GeneralHelper.showSnackBarRevamp(binding.content, message)
                            onShowSkeleton()
                            presenter.getListDepositoAccount()
                            setResult(RESULT_CANCELED, data)
                        } else {
                            setResult(RESULT_CANCELED, data)
                            finish()
                        }


                    }
                }
            } else {
                onShowSkeleton()
                presenter.getListDepositoAccount()
                setResult(RESULT_OK, data)
                if (isFromInvestasi) {
                    finish()
                }
            }

        }
    }

    class MyPagerAdapter(
        fragmentManager: FragmentManager?,
        var presenter: IOnBoardingDepositoRevampPresenter<IOnBoardingDepositoRevampView>,
        private val mTitle: List<String>,
        private val listDeposito: List<GetListDepositoResponse.AccountList>,
        private val listFaq: List<GetListDepositoResponse.Faq>,
        private val listener: ListDepositoFragment.OnRefreshListener,
        private val bubleCaseListener: ListDepositoFragment.OnBubleCaseListener
    ) : FragmentStatePagerAdapter(fragmentManager!!) {

        private var fragmentDeposito: ListDepositoFragment? = null

        fun getFragment() = fragmentDeposito

        // Returns total number of pages
        override fun getCount(): Int {
            return mTitle.size
        }

        // Returns the fragment to display for that page
        override fun getItem(position: Int): Fragment {
            var posFragmentShow = 0
            val fragment =
                ListDepositoFragment(
                    presenter,
                    listDeposito,
                    listFaq,
                    listener,
                    position,
                    bubleCaseListener
                )
            if (position == posFragmentShow) {
                fragmentDeposito = fragment
            }
            return fragment
        }

        override fun getPageTitle(position: Int): CharSequence? {
            return mTitle[position]
        }

    }

    override fun onClickBtnYes() {
        //do nothing
    }

    override fun onClickBtnNo() {
        //do nothing
    }

    override fun onDestroy() {
        presenter.stop()
        super.onDestroy()
    }

    override fun onBackPressed() {
        presenter.stop()
        super.onBackPressed()
    }

    override fun onReadyShowBubleCase() {
        initBubleCase()
    }

    private fun initBubleCase() {
        if (!isBubleCaseShow) {
            if (!brImoPrefRepository.depositoRevampBuble) {
                addBubbleShowCase()
                brImoPrefRepository.saveDepositoRevampBuble(true)
            }
        }
        isBubleCaseShow = true
    }

}