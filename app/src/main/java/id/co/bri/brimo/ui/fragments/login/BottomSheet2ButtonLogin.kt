package id.co.bri.brimo.ui.fragments.login

import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import com.google.android.material.bottomsheet.BottomSheetDialogFragment
import id.co.bri.brimo.R
import id.co.bri.brimo.databinding.BottomSheet2ButtonBinding
import id.co.bri.brimo.domain.helpers.GeneralHelper
import id.co.bri.brimo.util.extension.createInstance

class BottomSheet2ButtonLogin : BottomSheetDialogFragment() {

    private var _binding: BottomSheet2ButtonBinding? = null
    private val binding get() = _binding!!

    private var image: String = ""
    private var title: String = ""
    private var desc: String = ""
    private var txtButtonOk: String = ""
    private var txtButtonCancel: String = ""
    private var onListener: DialogDefaultListener? = null
    private var isUpdate: Boolean = true

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setStyle(STYLE_NORMAL, R.style.CustomBottomSheetDialogTheme)
    }

    override fun onCreateView(
        inflater: LayoutInflater, container: ViewGroup?, savedInstanceState: Bundle?
    ): View {
        _binding = BottomSheet2ButtonBinding.inflate(inflater, container, false)

        parseArgsData()

        if (isUpdate) {
            binding.btnLogin.visibility = View.VISIBLE
            binding.ll2button.visibility = View.GONE
        } else {
            binding.btnLogin.visibility = View.GONE
        }

        binding.ivImage.setImageResource(GeneralHelper.getImageId(requireContext(), image))
        binding.tvTitle.text = title
        binding.tvDesc.text = desc
        binding.btnOk.text = txtButtonOk
        binding.btnCancel.text = txtButtonCancel

        binding.btnOk.setOnClickListener {
            onListener?.onClickOk()
            dismiss()
        }

        binding.btnCancel.setOnClickListener {
            onListener?.onClickCancel()
            dismiss()
        }

        binding.btnLogin.setOnClickListener {
            onListener?.onClickLogin()
            dismiss()
        }

        return binding.root
    }

    interface DialogDefaultListener {
        fun onClickOk()
        fun onClickCancel()
        fun onClickLogin()
    }

    override fun onDestroyView() {
        _binding = null
        super.onDestroyView()
    }

    companion object {

        private const val IMAGE = "IMAGE"
        private const val TITLE = "TITLE"
        private const val DESC = "DESC"
        private const val BUTTON_OK = "BUTTON_OK"
        private const val BUTTON_CANCEL = "BUTTON_CANCEL"
        private const val IS_UPDATE = "IS_UPDATE"

        fun newInstance(
            image: String,
            title: String,
            desc: String,
            txtButtonOk: String,
            txtButtonCancel: String,
            listener: DialogDefaultListener,
            isUpdate: Boolean
        ): BottomSheet2ButtonLogin = BottomSheet2ButtonLogin().createInstance {
            putString(IMAGE, image)
            putString(TITLE, title)
            putString(DESC, desc)
            putString(BUTTON_OK, txtButtonOk)
            putString(BUTTON_CANCEL, txtButtonCancel)
            putBoolean(IS_UPDATE, isUpdate)
        }.setOnDialogListener(listener)
    }

    private fun setOnDialogListener(listener: DialogDefaultListener): BottomSheet2ButtonLogin {
        this.onListener = listener
        return this
    }

    private fun parseArgsData() {
        arguments?.let {
            image = it.getString(IMAGE).toString()
            title = it.getString(TITLE).toString()
            desc = it.getString(DESC).toString()
            txtButtonOk = it.getString(BUTTON_OK).toString()
            txtButtonCancel = it.getString(BUTTON_CANCEL).toString()
            isUpdate = it.getBoolean(IS_UPDATE)
        }
    }
}