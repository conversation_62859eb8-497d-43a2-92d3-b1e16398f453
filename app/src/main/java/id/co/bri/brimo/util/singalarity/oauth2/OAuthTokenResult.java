/**
 * OAuthTokenResult.java
 * Model class representing OAuth2 token response data.
 * Handles token information and expiration logic.
 */
package id.co.bri.brimo.util.singalarity.oauth2;

import org.json.JSONException;
import org.json.JSONObject;

import java.util.Date;

public class OAuthTokenResult {
    private String accessToken;
    private long expiredAtTimestampMs;
    private String tokenType;

    /**
     * Constructs a new OAuthTokenResult from JSON response data.
     *
     * @param jsonData JSON object containing token data
     * @param bufferExpiredMs Time buffer before actual expiration
     * @throws JSONException If JSON parsing fails
     */
    public OAuthTokenResult(JSONObject jsonData, long bufferExpiredMs) throws JSONException {
        initializeFromJson(jsonData, bufferExpiredMs);
    }

    /**
     * Initializes token data from JSON response.
     *
     * @param jsonData JSON object containing token data
     * @param bufferExpiredMs Buffer time before expiration
     */
    private void initializeFromJson(JSONObject jsonData, long bufferExpiredMs) {
        this.accessToken = jsonData.optString("access_token", "");
        long expiresInSec = jsonData.optInt("expires_in", 0);
        this.expiredAtTimestampMs = new Date().getTime() + expiresInSec * 1000 - bufferExpiredMs;
        this.tokenType = jsonData.optString("token_type", "");
    }

    /**
     * Checks if the token has expired.
     *
     * @return true if token has expired, false otherwise
     */
    public boolean isExpired() {

        return System.currentTimeMillis() > expiredAtTimestampMs;
    }

    /**
     * Returns the complete access token with type prefix.
     * Example: "Bearer xyz123..."
     *
     * @return Complete access token string
     */
    public String getFullAccessToken() {
        return tokenType + " " + accessToken;
    }

    // Standard getters and setters with documentation
    public String getAccessToken() { return accessToken; }
    public void setAccessToken(String accessToken) { this.accessToken = accessToken; }
    public long getExpiredAtTimestampMs() { return expiredAtTimestampMs; }
    public void setExpiredAtTimestampMs(long expiredAtTimestampMs) { this.expiredAtTimestampMs = expiredAtTimestampMs; }
    public String getTokenType() { return tokenType; }
    public void setTokenType(String tokenType) { this.tokenType = tokenType; }
}