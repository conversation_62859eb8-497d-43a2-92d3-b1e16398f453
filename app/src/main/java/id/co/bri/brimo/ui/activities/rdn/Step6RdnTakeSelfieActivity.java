package id.co.bri.brimo.ui.activities.rdn;

import android.Manifest;
import android.app.Activity;
import android.content.Intent;
import android.content.pm.PackageManager;
import android.graphics.Bitmap;
import android.os.Build;
import android.os.Bundle;
import android.util.DisplayMetrics;
import android.util.Log;
import android.util.Size;
import android.view.Surface;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.annotation.RequiresApi;
import androidx.camera.core.CameraSelector;
import androidx.camera.core.ImageCapture;
import androidx.camera.core.ImageCaptureException;
import androidx.camera.core.Preview;
import androidx.camera.lifecycle.ProcessCameraProvider;
import androidx.core.app.ActivityCompat;
import androidx.core.content.ContextCompat;

import com.google.common.util.concurrent.ListenableFuture;

import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.OutputStream;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.concurrent.ExecutionException;

import id.co.bri.brimo.R;
import id.co.bri.brimo.databinding.ActivityStep6RdnTakeSelfieBinding;
import id.co.bri.brimo.domain.config.Constant;
import id.co.bri.brimo.domain.helpers.GeneralHelper;
import id.co.bri.brimo.domain.helpers.image.ImageHelper;
import id.co.bri.brimo.ui.activities.base.BaseActivity;

public class Step6RdnTakeSelfieActivity extends BaseActivity {

    private ActivityStep6RdnTakeSelfieBinding binding;

    private static final String TAG_RESPONSE = "response";
    @RequiresApi(api = Build.VERSION_CODES.TIRAMISU)
    static String[] permissions_camera_33 = {
            Manifest.permission.READ_MEDIA_IMAGES,
            Manifest.permission.CAMERA,
    };
    final String[] permissions_camera = new String[]{
            Manifest.permission.CAMERA,
            Manifest.permission.WRITE_EXTERNAL_STORAGE,
            Manifest.permission.READ_EXTERNAL_STORAGE
    };

    Bitmap bitmapSelfie;

    ListenableFuture<ProcessCameraProvider> cameraProviderFuture;
    private File tempDir = null; //directori image
    private File savedPhoto = null;
    private ImageCapture imageCapture = null;
    private ImageHelper imageHelper = new ImageHelper(this);
    private boolean isCapturing = false;


    public static void launchIntent(Activity caller, String response) {
        Intent i = new Intent(caller, Step6RdnTakeSelfieActivity.class);
        i.putExtra(TAG_RESPONSE, response);
        caller.startActivityForResult(i, Constant.REQ_PAYMENT);
    }

    public static Bitmap resizeImage(Bitmap realImage, float maxImageSize,
                                     boolean filter) {
        float ratio = Math.min(
                maxImageSize / realImage.getWidth(),
                maxImageSize / realImage.getHeight());
        int width = Math.round(ratio * realImage.getWidth());
        int height = Math.round(ratio * realImage.getHeight());

        Bitmap newBitmap = Bitmap.createScaledBitmap(realImage, width,
                height, filter);
        return newBitmap;
    }

    private String[] getPermissionCamera() {
        String[] permission;
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU) {
            permission = permissions_camera_33;
        } else {
            permission = permissions_camera;
        }
        return permission;
    }

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        binding = ActivityStep6RdnTakeSelfieBinding.inflate(getLayoutInflater());
        setContentView(binding.getRoot());

        startCamera();

        binding.capture.setOnClickListener(v -> {
            getPermission();
        });
    }

    private void startCamera() {
        cameraProviderFuture = ProcessCameraProvider.getInstance(this);
        cameraProviderFuture.addListener(() -> {
            // Used to bind the lifecycle of cameras to the lifecycle owner
            ProcessCameraProvider cameraProvider;
            try {
                cameraProvider = cameraProviderFuture.get();

                // Select back camera as a default
                CameraSelector cameraSelector = CameraSelector.DEFAULT_FRONT_CAMERA;

                imageCapture = new ImageCapture.Builder()
                        .setTargetRotation(Surface.ROTATION_0)
                        .setJpegQuality(100)
                        .setTargetResolution(new Size(1080, 1920))
                        .build();

                // Preview
                Preview preview = new Preview.Builder().build();
                preview.setSurfaceProvider(binding.camera.getSurfaceProvider());

                try {
                    // Unbind use cases before rebinding
                    cameraProvider.unbindAll();
                    // Bind use cases to camera
                    cameraProvider.bindToLifecycle(this, cameraSelector, preview, imageCapture);
                } catch (Exception exc) {
                    if (!GeneralHelper.isProd())
                        Log.e("TestCamera", "Use case binding failed", exc);
                }
            } catch (ExecutionException | InterruptedException e) {
                if (!GeneralHelper.isProd()) {
                    Log.e("TAG", "startCamera: ", e);
                }
            }
        }, ContextCompat.getMainExecutor(this));
    }

    private void generateSavedImage() {
        String timeNow = new SimpleDateFormat("yyyyMMddHHmmss").format(new Date());
        String fileName = timeNow + "_selfie.png";

        prepareImageDirectory();
        if (tempDir != null) {
            savedPhoto = new File(tempDir, fileName);
        }
    }

    private void prepareImageDirectory() {
        tempDir = new File(getCacheDir(), Constant.TAG_START_NAME);
        if (!tempDir.exists()) {
            tempDir.mkdirs(); // membuat direktori jika belum ada
        }
    }

    private void getPermission() {
        if (hasPermissions(this, getPermissionCamera())) {
            takePhoto();
        } else {
            ActivityCompat.requestPermissions(this, getPermissionCamera(), 1);
        }
    }

    private void takePhoto() {
        // Get a stable reference of the modifiable image capture use case
        if (imageCapture == null) {
            isCapturing = false;
            return;
        }

        //initialize image
        generateSavedImage();

        ImageCapture.OutputFileOptions outputFileOptions = new ImageCapture.OutputFileOptions.Builder(savedPhoto).build();

        // Set up image capture listener, which is triggered after photo has been taken
        imageCapture.takePicture(
                outputFileOptions,
                ContextCompat.getMainExecutor(this),
                new ImageCapture.OnImageSavedCallback() {
                    @Override
                    public void onImageSaved(ImageCapture.OutputFileResults outputFileResults) {
                        try {
                            Bitmap bitmapKtp = imageHelper.outputFileResultsToBitmap(outputFileResults);
                            if (bitmapKtp != null) {
                                // Mendapatkan ukuran layar perangkat
                                DisplayMetrics displayMetrics = getResources().getDisplayMetrics();
                                int screenWidth = displayMetrics.widthPixels;
                                int screenHeight = displayMetrics.heightPixels;

                                // Proporsi aspek gambar
                                float aspectRatio = 9f / 16f; // Contoh proporsi aspek 4:3

                                // Menghitung ukuran tampilan yang dapat disesuaikan
                                int targetHeight = (int) (screenWidth / aspectRatio);

                                // Menghitung skala faktor berdasarkan densitas piksel perangkat
                                float scaleWidth = (float) screenWidth / bitmapKtp.getWidth();
                                float scaleHeight = (float) screenHeight / bitmapKtp.getHeight();
                                float scaleFactor = Math.min(scaleWidth, scaleHeight);

                                // Menghitung ukuran potongan gambar yang disesuaikan dengan skala faktor
                                int adjustedWidth = (int) (screenWidth / scaleFactor);
                                int adjustedHeight = (int) (targetHeight / scaleFactor);

                                // Menghitung posisi awal potongan gambar
                                int startX = (bitmapKtp.getWidth() - adjustedWidth) / 2;
                                int startY = (bitmapKtp.getHeight() - adjustedHeight) / 2;

                                // Membuat potongan gambar
                                bitmapSelfie = Bitmap.createBitmap(
                                        bitmapKtp,
                                        startX,
                                        startY,
                                        adjustedWidth,
                                        adjustedHeight
                                );

                                saveImageToStorage();

                                //go to preview image kTP
                                File deletePicture = new File(savedPhoto.getPath());
                                if (deletePicture.delete()) {
                                    if (!isCapturing) {
                                        isCapturing = true;
                                        Step7RdnSelfieResultActivity.launchIntent(Step6RdnTakeSelfieActivity.this, bitmapSelfie, getIntent().getStringExtra(TAG_RESPONSE));
                                    }
                                }
                            }
                        } catch (Exception e) {

                        }

                    }

                    @Override
                    public void onError(@NonNull ImageCaptureException exception) {
                        if (!GeneralHelper.isProd()) {
                            Log.d("TestCamera", "onError: " + exception);
                        }
                        isCapturing = false;
                    }

                }
        );
    }

    private void saveImageToStorage() {
        OutputStream outputStream = null;
        try {
            outputStream = new FileOutputStream(savedPhoto != null ? savedPhoto.getPath() : null);
            outputStream.write(imageHelper.bitmapToByteArray(bitmapSelfie));
            outputStream.flush();
        } catch (IOException e) {
            if (!GeneralHelper.isProd()) {
                Log.e("TestCamera", "onImageKtp: ", e);
            }
        } finally {
            if (outputStream != null) {
                try {
                    outputStream.close();
                } catch (IOException e) {
                    if (!GeneralHelper.isProd()) {
                        Log.e("TestCamera", "onImageKtp: ", e);
                    }
                }
            }
        }
    }


    @Override
    protected void onStart() {
        super.onStart();
    }

    @Override
    protected void onResume() {
        super.onResume();
        try {
            Thread.sleep(100);
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
        }
    }

    @Override
    protected void onStop() {
        super.onStop();
    }

    @Override
    public void onRequestPermissionsResult(int requestCode, @NonNull String[] permissions, @NonNull int[] grantResults) {
        super.onRequestPermissionsResult(requestCode, permissions, grantResults);
        boolean grantAll = true;
        if (grantResults.length > 0) {
            for (int i = 0; i < grantResults.length; i++) {
                if (grantResults[i] != PackageManager.PERMISSION_GRANTED) {
                    grantAll = false;
                    break;
                }
            }
        }

        if (!grantAll) {
            showAlertFinish(getString(R.string.notes_need_permission));
        } else {
            takePhoto();
        }
    }

    @Override
    protected void onActivityResult(int requestCode, int resultCode, @Nullable Intent data) {
        super.onActivityResult(requestCode, resultCode, data);

        if (resultCode == RESULT_OK && data != null) {
            setResult(RESULT_OK, data);
            finish();
        } else {
            if (data != null) {
                setResult(RESULT_CANCELED, data);
                finish();
            } else {
                isCapturing = false;
                setResult(RESULT_CANCELED);
            }
        }

        if (requestCode == Constant.REQ_PAYMENT && data == null) {
            if (resultCode == RESULT_OK) {
                setResult(RESULT_OK);
                this.finish();
            }
        }

        if (requestCode == Constant.REQ_REGIS) {
            if (resultCode == Activity.RESULT_OK) {
                setResult(RESULT_OK);
                this.finish();
            } else if (resultCode == Activity.RESULT_CANCELED && data != null) {
                setResult(RESULT_CANCELED, data);
                this.finish();
            } else {
                setResult(RESULT_CANCELED);
                this.finish();
            }
        }

    }

    @Override
    protected void onDestroy() {
        super.onDestroy();
        binding = null;
    }
}