package id.co.bri.brimo.models.apimodel.request;

import com.google.gson.annotations.SerializedName;

public class UpdateSavedRequestTfMethod {

    @SerializedName("saved_id")
    private String savedId;

    @SerializedName("product_id")
    private String paymentType;

    @SerializedName("method")
    private String method;


    public UpdateSavedRequestTfMethod(String savedId) {
        this.savedId = savedId;
    }

    public UpdateSavedRequestTfMethod(String savedId, String paymentType, String method) {
        this.savedId = savedId;
        this.paymentType = paymentType;
        this.method = method;
    }

    public String getPaymentType() {
        return paymentType;
    }

    public void setPaymentType(String paymentType) {
        this.paymentType = paymentType;
    }

    public String getMethod() {
        return method;
    }

    public void setMethod(String method) {
        this.method = method;
    }
}
