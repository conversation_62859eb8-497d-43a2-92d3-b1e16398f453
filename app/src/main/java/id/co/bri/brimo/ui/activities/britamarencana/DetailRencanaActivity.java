package id.co.bri.brimo.ui.activities.britamarencana;

import android.annotation.SuppressLint;
import android.app.Activity;
import android.content.Intent;
import android.os.Bundle;
import android.os.SystemClock;
import android.view.MotionEvent;
import android.view.View;
import androidx.annotation.Nullable;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;
import com.ethanhua.skeleton.Skeleton;
import com.ethanhua.skeleton.SkeletonScreen;
import javax.inject.Inject;
import id.co.bri.brimo.R;
import id.co.bri.brimo.adapters.DataAkunRencanaAdapter;
import id.co.bri.brimo.contract.IPresenter.britamarencana.IDetailRencanaPresenter;
import id.co.bri.brimo.contract.IView.britamarencana.IDetailRencanaView;
import id.co.bri.brimo.databinding.ActivityDetailRencanaBinding;
import id.co.bri.brimo.domain.config.Constant;
import id.co.bri.brimo.domain.helpers.GeneralHelper;
import id.co.bri.brimo.models.ParameterKonfirmasiModel;
import id.co.bri.brimo.models.apimodel.response.CertificateResponse;
import id.co.bri.brimo.models.apimodel.response.DetailRencanaResponse;
import id.co.bri.brimo.models.apimodel.response.FormPencairanResponse;
import id.co.bri.brimo.models.apimodel.response.GeneralConfirmationResponse;
import id.co.bri.brimo.models.apimodel.response.ListRekeningResponse;
import id.co.bri.brimo.ui.activities.KonfirmasiGeneralActivity;
import id.co.bri.brimo.ui.activities.base.BaseActivity;
import id.co.bri.brimo.ui.fragments.CairFragmentDialog;
import id.co.bri.brimo.ui.fragments.CancelRencanaFragment;
import id.co.bri.brimo.ui.fragments.ViewCertifFragment;

public class DetailRencanaActivity extends BaseActivity implements View.OnClickListener,
        IDetailRencanaView,
        CancelRencanaFragment.DialogDefaultListener,
        ViewCertifFragment.DialogDefaultListener,
        CairFragmentDialog.DialogDefaultListener {

    private static ListRekeningResponse.Account rekeningData = null;
    SkeletonScreen skeletonCard, skeletonInfo;

    Double saldo;
    Integer saldopersen;

    DetailRencanaResponse mDetailRencanaResponse = null;
    @Inject
    IDetailRencanaPresenter<IDetailRencanaView> presenter;
    private ActivityDetailRencanaBinding binding;

    public static void launchIntent(Activity caller, ListRekeningResponse.Account rekening) {
        Intent intent = new Intent(caller, DetailRencanaActivity.class);
        rekeningData = rekening;
        caller.startActivityForResult(intent, Constant.REQ_PAYMENT);
    }

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        binding = ActivityDetailRencanaBinding.inflate(getLayoutInflater());
        setContentView(binding.getRoot());

        GeneralHelper.setToolbar(this, binding.toolbar.toolbar, "Info Rekening");
        injectDependency();
        setupView();
        skeletonArmy();
    }

    public void setupView() {
        binding.llCair.setAlpha((float) 0.3);
        binding.llClose.setAlpha((float) 0.3);
        binding.llUbah.setAlpha((float) 0.3);
        binding.llCertif.setAlpha((float) 0.3);

        binding.llCair.setEnabled(false);
        binding.llClose.setEnabled(false);
        binding.llUbah.setEnabled(false);
        binding.llCertif.setEnabled(false);
    }

    private void injectDependency() {
        getActivityComponent().inject(this);
        if (presenter != null) {
            presenter.setView(this);
            presenter.start();
            presenter.setUrl(GeneralHelper.getString(R.string.url_detail_rencana));
            presenter.setUrlPencairan(GeneralHelper.getString(R.string.url_form_pencairan));
            presenter.setUrlClose(GeneralHelper.getString(R.string.url_konfirmasi_tutup));
            presenter.setUrlCertif(GeneralHelper.getString(R.string.url_certif));
            presenter.getDetailRencana(rekeningData.getAccount());
        }
    }

    public void skeletonArmy() {
//        skeletonCard = Skeleton.bind(ivCard)
//                .shimmer(true)
//                .angle(20)
//                .duration(1200)
//                .load(R.layout.skeleton_card)
//                .show();
        skeletonInfo = Skeleton.bind(binding.llDetail)
                .shimmer(true)
                .angle(20)
                .duration(1200)
                .load(R.layout.skeleton_detail)
                .show();
    }

    @Override
    public void onClick(View view) {
        if (SystemClock.elapsedRealtime() - mLastClickTime < 1000) {
            return;
        }
        mLastClickTime = SystemClock.elapsedRealtime();
        int id = view.getId();
        switch (id) {
            case R.id.ll_cair:
                CairFragmentDialog cairFragmentDialog = new CairFragmentDialog(this, GeneralHelper.getString(R.string.cair_title), GeneralHelper.getString(R.string.cair_subtitle), GeneralHelper.getString(R.string.cair_ya), GeneralHelper.getString(R.string.batal), true);
                cairFragmentDialog.show(getSupportFragmentManager(), "");
                break;
            case R.id.ll_ubah:
                CairFragmentDialog cairFragmentDialog2 = new CairFragmentDialog(this, GeneralHelper.getString(R.string.ubah_title), GeneralHelper.getString(R.string.ubah_subtitle), GeneralHelper.getString(R.string.ubah_ya), GeneralHelper.getString(R.string.batal), false);
                cairFragmentDialog2.show(getSupportFragmentManager(), "");
                break;
            case R.id.ll_close:
                CancelRencanaFragment cancelRencanaFragment = new CancelRencanaFragment(saldopersen, GeneralHelper.getString(R.string.cancel_text), this);
                cancelRencanaFragment.show(getSupportFragmentManager(), "");
                break;
            case R.id.ll_certif:
                presenter.getCertif(rekeningData.getAccount());
                break;
        }
    }

    @SuppressLint("ClickableViewAccessibility")
    @Override
    public void onSuccessGetDetail(DetailRencanaResponse detailRencanaResponse) {
        skeletonInfo.hide();
        mDetailRencanaResponse = detailRencanaResponse;

        binding.llCair.setOnClickListener(this);
        binding.llClose.setOnClickListener(this);
        binding.llUbah.setOnClickListener(this);
        binding.llCertif.setOnClickListener(this);

        if (mDetailRencanaResponse.isFlexyFlag()) {
            binding.llCair.setAlpha(1);
            binding.llClose.setAlpha(1);
            binding.llUbah.setAlpha(1);
            binding.llCertif.setAlpha(1);

            binding.llCair.setEnabled(true);
            binding.llClose.setEnabled(true);
            binding.llUbah.setEnabled(true);
            binding.llCertif.setEnabled(true);
        } else {
            binding.llCair.setAlpha((float) 0.3);
            binding.llClose.setAlpha((float) 0.3);
            binding.llUbah.setAlpha((float) 0.3);
            binding.llCertif.setAlpha((float) 0.3);

            binding.llCair.setEnabled(false);
            binding.llClose.setEnabled(false);
            binding.llUbah.setEnabled(false);
            binding.llCertif.setEnabled(false);
        }
        binding.tvTarget.setText(detailRencanaResponse.getAccount().getBalanceString());
        saldo = detailRencanaResponse.getAccount().getBalance();
        binding.tvKekurangan.setText(detailRencanaResponse.getAccount().getTargetAmountString());
        saldopersen = (int) ((saldo / detailRencanaResponse.getAccount().getTargetAmount()) * 100);
        binding.sbRencana.setMax(0);
        binding.sbRencana.setMax(100);
        binding.sbRencana.setProgress(saldopersen);
        binding.sbRencana.refreshDrawableState();
        binding.sbRencana.setOnTouchListener(new View.OnTouchListener() {
            @Override
            public boolean onTouch(View v, MotionEvent event) {
                return true;
            }
        });
        binding.rvDataView.setHasFixedSize(true);
        binding.rvDataView.setLayoutManager(new LinearLayoutManager(getApplicationContext(), RecyclerView.VERTICAL, false));
        DataAkunRencanaAdapter dataAkunRencanaAdapter = new DataAkunRencanaAdapter(detailRencanaResponse.getDetailDataView(), DetailRencanaActivity.this);
        binding.rvDataView.setAdapter(dataAkunRencanaAdapter);
    }

    @Override
    public void onSuccessGetCertif(CertificateResponse certificateResponse) {
        ViewCertifFragment viewCertifFragment = new ViewCertifFragment(certificateResponse.getCertificate(), mDetailRencanaResponse.getAccount().getName(), mDetailRencanaResponse.getAccount().getAccountString(), this);
        viewCertifFragment.show(getSupportFragmentManager(), "");
    }

    public ParameterKonfirmasiModel setParameterKonfirmasi() {
        ParameterKonfirmasiModel parameterKonfirmasiModel = new ParameterKonfirmasiModel();

        parameterKonfirmasiModel.setStringLabelTujuan("Nomor Tujuan");
        parameterKonfirmasiModel.setStringButtonSubmit("Berhenti");

        return parameterKonfirmasiModel;
    }

    @Override
    public void onSuccessFormPencairan(FormPencairanResponse formPencairanResponse) {
        PencairanRencanaActivity.launchIntent(this, formPencairanResponse);
    }

    @Override
    public void onSuccessKonfirmasiClose(GeneralConfirmationResponse generalConfirmationResponse) {
        KonfirmasiGeneralActivity.launchIntent(this, generalConfirmationResponse, GeneralHelper.getString(R.string.url_tutup_payment), "Konfirmasi", setParameterKonfirmasi(), false, false, false);
    }

    @Override
    protected void onActivityResult(int requestCode, int resultCode, @Nullable Intent data) {
        super.onActivityResult(requestCode, resultCode, data);
        if (requestCode == Constant.REQ_PAYMENT) {
            if (resultCode == RESULT_OK) {
                this.setResult(RESULT_OK, data);
                this.finish();
            } else {
                this.setResult(RESULT_CANCELED, data);
                if (data != null) {
                    showSnackbarErrorMessage(data.getStringExtra(Constant.TAG_ERROR_MESSAGE), ALERT_ERROR, this, false);
                }
            }
        }
    }

    @Override
    public void onClickYes() {
        presenter.getKonfirmasiTutup(rekeningData.getAccount());
    }

    @Override
    public void onClickYesCertif() {

    }

    @Override
    public void onClickYesCair() {
        presenter.getFormPencairan(rekeningData.getAccount());
    }

    @Override
    public void onClickYesUbah() {
        HitungRencanaActivity.launchIntent(this, true, mDetailRencanaResponse);
    }

    @Override
    protected void onDestroy() {
        super.onDestroy();
        binding = null;
    }
}