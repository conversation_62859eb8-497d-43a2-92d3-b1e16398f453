package id.co.bri.brimo.models.apimodel.request;

import com.google.gson.annotations.Expose;
import com.google.gson.annotations.SerializedName;

public class InquiryBrivaWebviewRequest {
    @SerializedName("partner_id")
    @Expose
    private String partnerId;
    @SerializedName("order_id")
    @Expose
    private String orderId;

    public InquiryBrivaWebviewRequest(String partnerId, String orderId) {
        this.partnerId = partnerId;
        this.orderId = orderId;
    }
}
