package id.co.bri.brimo.ui.activities.tartun;


import android.app.Activity;
import android.content.Intent;
import android.os.Build;
import android.os.Bundle;
import android.os.SystemClock;
import android.view.View;
import android.widget.Button;
import android.widget.EditText;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.RelativeLayout;
import android.widget.TextView;

import com.ethanhua.skeleton.Skeleton;
import com.ethanhua.skeleton.SkeletonScreen;

import androidx.annotation.Nullable;
import androidx.appcompat.widget.Toolbar;
import androidx.coordinatorlayout.widget.CoordinatorLayout;
import androidx.core.widget.NestedScrollView;
import androidx.fragment.app.FragmentTransaction;
import androidx.recyclerview.widget.GridLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import id.co.bri.brimo.R;
import id.co.bri.brimo.adapters.option.OptionNominalAdapter;
import id.co.bri.brimo.contract.IPresenter.tariktunai.IFormTarikPresenter;
import id.co.bri.brimo.contract.IView.tariktunai.IFormTarikView;
import id.co.bri.brimo.domain.config.Constant;
import id.co.bri.brimo.domain.helpers.GeneralHelper;
import id.co.bri.brimo.models.AccountModel;
import id.co.bri.brimo.models.Amount;
import id.co.bri.brimo.models.ParameterKonfirmasiModel;
import id.co.bri.brimo.models.apimodel.response.KonfirmasiTarikTunaiResponse;
import id.co.bri.brimo.models.apimodel.response.MerchantTartunOptionResponse;
import id.co.bri.brimo.models.apimodel.response.PaymentTarikResponse;
import id.co.bri.brimo.models.apimodel.response.TarikTunaiResponse;
import id.co.bri.brimo.models.optionmodel.OptionGeneralStatusModel;
import id.co.bri.brimo.models.optionmodel.OptionNominalModel;
import id.co.bri.brimo.ui.activities.DashboardIBActivity;
import id.co.bri.brimo.ui.activities.KonfirmasiTarikActivity;
import id.co.bri.brimo.ui.activities.base.BaseActivity;
import id.co.bri.brimo.ui.customviews.dialog.DialogInformation;
import id.co.bri.brimo.ui.fragments.GeneralOptionFragment;
import id.co.bri.brimo.ui.fragments.SumberDanaFragment;

import java.util.ArrayList;
import java.util.List;

import javax.inject.Inject;

import butterknife.Bind;
import butterknife.ButterKnife;

import static id.co.bri.brimo.domain.config.Constant.TAG_ERROR_MESSAGE;

public class FormTarikTunaiActivity extends BaseActivity implements
        IFormTarikView,
        OptionNominalAdapter.onAddButtonListener,
        View.OnClickListener,
        SumberDanaFragment.SelectSumberDanaInterface,
        DialogInformation.OnActionClick {

    @Bind(R.id.item_layout_background)
    LinearLayout layout;
    @Bind(R.id.ll_tarik_tunai)
    LinearLayout lLTarikTunai;
    @Bind(R.id.et_jalur_tarik)
    EditText etJalurTarik;
    @Bind(R.id.iv_logo_merchant)
    ImageView ivLogoMerchant;
    @Bind(R.id.tv_petunjuk)
    TextView textView;
    @Bind(R.id.tb_petunjuk)
    Toolbar toolbar;
    @Bind(R.id.rv_button_tarik)
    RecyclerView rv_tarik;
    @Bind(R.id.btnSubmit)
    Button button;
    @Bind(R.id.ll_btn_submit)
    LinearLayout lLBtnSubmit;
    @Bind(R.id.tv_norek)
    TextView tvNorek;
    @Bind(R.id.tv_saldo)
    TextView tvSaldo;
    @Bind(R.id.tv_inisial)
    TextView tvInisial;
    @Bind(R.id.content)
    CoordinatorLayout coordinatorLayout;
    @Bind(R.id.ll_form)
    LinearLayout linearLayout;
    @Bind(R.id.ll_input_nominal)
    LinearLayout llInputNominal;
    @Bind(R.id.rl_petunjuk)
    RelativeLayout rlPetunjuk;
    @Bind(R.id.panah_tarik_tunai)
    ImageView panahTarikTunai;
    @Bind(R.id.iv_alert_saldo)
    ImageView ivAlertSaldo;
    @Bind(R.id.scrollview)
    NestedScrollView scrollView;
    @Bind(R.id.iv_scroll_to_bottom)
    ImageView ivScroll;
    @Bind(R.id.ll_jalur)
    LinearLayout llJalur;


    int counter = 0;
    int hargaperbandingan;
    AccountModel accountDefaultModel;
    Double saldo = 0.0;
    Double saldoPerbandingan = 0.0;
    String mSadloString = "";
    String mAkunDefault;
    boolean isSaldoHold = false;

    private List<Amount> mNominals = new ArrayList<>();
    private List<OptionNominalModel> pulsaModelList;
    private List<AccountModel> mListAccountModel;
    private List<Integer> mListFailed;
    private OptionNominalAdapter pulsaAdapter;
    private TarikTunaiResponse mTariktunai;
    private int selectedMerchantPosition;

    protected SkeletonScreen skeletonScreen1;
    protected SkeletonScreen skeletonScreen2;
    protected static ParameterKonfirmasiModel mParameterKonfirmasiModel;
    protected static String errorMessage = null;
    boolean isArrow = false;

    @Inject
    IFormTarikPresenter<IFormTarikView> tarikPresenter;

    public static void launchIntent(Activity caller, boolean fromFastMenu) {
        Intent intent = new Intent(caller, FormTarikTunaiActivity.class);
        caller.startActivityForResult(intent, Constant.REQ_PAYMENT);
        isFromFastMenu = fromFastMenu;
    }

    public static void launchIntentReciept(Activity caller) {
        Intent intent = new Intent(caller, FormTarikTunaiActivity.class);
        caller.startActivityForResult(intent, Constant.REQ_PAYMENT);
        caller.finish();
    }

    public static void launchIntentReciept(Activity caller, String errorMessageL) {
        errorMessage = errorMessageL;
        Intent intent = new Intent(caller, FormTarikTunaiActivity.class);
        caller.startActivityForResult(intent, Constant.REQ_PAYMENT);
        caller.finish();
    }


    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(getLayoutResource());

        ButterKnife.bind(this);

        initiateAdapter();

        injectDependency();

        GeneralHelper.setToolbar(this, toolbar, getTitleBar());

        button.setOnClickListener(this);
        button.setEnabled(true);
        button.setAlpha(0.3f);
        layout.setOnClickListener(this);
        lLTarikTunai.setOnClickListener(this);
        textView.setOnClickListener(this);
        etJalurTarik.setOnClickListener(this);
        panahTarikTunai.setOnClickListener(this);
        ivScroll.setOnClickListener(this);
        llJalur.setOnClickListener(this);
        validasiButton();
        if (errorMessage != null) {
            showSnackbarErrorMessage(errorMessage, ALERT_ERROR, this, false);
            errorMessage = null;
        }

        mParameterKonfirmasiModel = setParameterKonfirmasi();

        skeletonScreen1 = Skeleton.bind(layout)
                .shimmer(true)
                .angle(20)
                .duration(1200)
                .load(R.layout.skeleton_pilih_sumber_dana)
                .show();
        skeletonScreen2 = Skeleton.bind(lLTarikTunai)
                .shimmer(true)
                .angle(20)
                .duration(1200)
                .load(R.layout.skeleton_pilih_jalur)
                .show();


        scrollView.getViewTreeObserver().addOnScrollChangedListener(() -> {
            View view = scrollView.getChildAt(scrollView.getChildCount() - 1);

            int diff = (view.getBottom() - (scrollView.getHeight() + scrollView
                    .getScrollY()));

            if (scrollView.getHeight() == scrollView.getChildAt(0).getHeight())
                ivScroll.setVisibility(View.GONE);
            else if (diff == 0){
                ivScroll.setVisibility(View.GONE);
            }else {
                ivScroll.setVisibility(View.VISIBLE);
            }
        });
    }

    private void injectDependency() {
        getActivityComponent().inject(this);
        if (tarikPresenter != null) {
            tarikPresenter.setView(this);
            tarikPresenter.setFormUrl(GeneralHelper.getString(R.string.url_form_tarik_tunai));
//            payment, delete. confirmation

            tarikPresenter.start();
            tarikPresenter.getFormTarik();

        }
    }


    @Override
    protected void onResume() {
        super.onResume();
        if (tarikPresenter != null) {
            tarikPresenter.setView(this);
            tarikPresenter.setFormUrl(GeneralHelper.getString(R.string.url_form_tarik_tunai));
        }

    }


    private List<OptionNominalModel> fetchbtnnama() {
        List<OptionNominalModel> pulsaModelList = new ArrayList<>();
        for (Amount nominal : mNominals) {
            pulsaModelList.add(new OptionNominalModel(nominal.getText(), nominal.getValue()));
        }

        return pulsaModelList;
    }

    @Override
    public void callback(int harga, int position) {

        hargaperbandingan = harga;
        hargaperbandingan = mNominals.get(position).getValue();

        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.N) {
            pulsaModelList.forEach((p) -> p.setBol(true));
        } else {
            for (OptionNominalModel p : pulsaModelList) {
                p.setBol(true);
            }
        }

        validasiButton();

        pulsaModelList.get(position).setBol(false);
        pulsaAdapter.notifyDataSetChanged();

    }

    private List<OptionGeneralStatusModel> fetchOptionList() {
        List<OptionGeneralStatusModel> list = new ArrayList<OptionGeneralStatusModel>();
        if (mTariktunai != null) {
            for (int i = 0; i < mTariktunai.getMerchant().size(); i++) {
                MerchantTartunOptionResponse tarikTunaiResponse = mTariktunai.getMerchant().get(i);
                int icon =  R.drawable.atm;
                String desc ="";
                desc = "";

                list.add(new OptionGeneralStatusModel(icon, mTariktunai.getMerchant().get(i).getName(),
                        tarikTunaiResponse.getIconName().replaceAll("\\s+","_").split("\\.")[0].toLowerCase(),
                        tarikTunaiResponse.getIconPath(),
                        mTariktunai.getMerchant().get(i).getStatus(), desc));
            }
        }
        return list;
    }

    public ParameterKonfirmasiModel setParameterKonfirmasi() {
        ParameterKonfirmasiModel parameterModel = new ParameterKonfirmasiModel();

        parameterModel.setStringLabelTujuan(GeneralHelper.getString(R.string.nomor_tujuan));
        parameterModel.setStringButtonSubmit(GeneralHelper.getString(R.string.txt_konfirmasi));
        parameterModel.setDefaultIcon(getDefaultIconResource());

        return parameterModel;
    }


    @Override
    public int getLayoutResource() {
        return R.layout.activity_form_tarik_tunai;
    }


    /**
     * Hide Progress
     */
    @Override
    public void hideProgress() {
        runOnUiThread(() -> {
            super.hideProgress();
            rlPetunjuk.setVisibility(View.VISIBLE);
            skeletonScreen1.hide();
            skeletonScreen2.hide();
        });
    }

    /**
     * Show progress
     */
    @Override
    public void showProgress() {
        super.showProgress();
        skeletonScreen1.show();
        skeletonScreen2.show();
    }

    @Override
    public int getDefaultIconResource() {
        return R.drawable.ic_menu_qna_tariktunai;
    }

    @Override
    public void onException12(String message) {
        Intent returnIntent = new Intent();

        returnIntent.putExtra(Constant.TAG_ERROR_MESSAGE, message);

        this.setResult(RESULT_CANCELED, returnIntent);
        this.finish();
    }

    @Override
    public void setTextForm() {

    }

    @Override
    public String getTitleBar() {
        return GeneralHelper.getString(R.string.toolbar_tarik_tunai);
    }

    @Override
    public long getAmount() {
        long amount = 0;
        String stringAmount;

        stringAmount = String.valueOf(hargaperbandingan);
        if (!stringAmount.isEmpty()) {
            amount = Long.parseLong(stringAmount);
        }

        return amount;
    }

    @Override
    public void showInputError(String message) {
        //GeneralHelper.showSnackBar(Objects.requireNonNull(this).findViewById(R.id.content), message);
    }

    @Override
    public void setDefaultSaldo(double defaultSaldo, String saldoString, String akun, boolean saldoHold) {
        saldo = defaultSaldo;
        mSadloString = saldoString;
        mAkunDefault = akun;
        isSaldoHold = saldoHold;
    }

    protected void setupAccount() {
        // saldo tertahan
        if (isSaldoHold) {
            ivAlertSaldo.setVisibility(View.VISIBLE);
        } else ivAlertSaldo.setVisibility(View.GONE);

        //List Account
        accountDefaultModel = new AccountModel();
        if (mTariktunai.getAccountList().size() > 0) {
            mListAccountModel = mTariktunai.getAccountList();
        }

        for (AccountModel accountModel : mListAccountModel) {
            if (accountModel.getIsDefault() == 1) {
                this.accountDefaultModel = accountModel;
                break;
            } else {
                this.accountDefaultModel = mListAccountModel.get(0);
            }
        }
        if (accountDefaultModel != null) {
            saldoPerbandingan = saldo - accountDefaultModel.getMinimumBalance();

            if (accountDefaultModel.getAcoountString() != null) {
                tvNorek.setText(accountDefaultModel.getAcoountString());
            }

            //jika rekening default tidak ada dilist rekening
            if (accountDefaultModel.getAcoount() != null) {
                if (accountDefaultModel.getAcoount().equals(mAkunDefault)) {
                    tvSaldo.setText(GeneralHelper.formatNominalIDR(accountDefaultModel.getCurrency(), mSadloString));
                } else {
                    tvSaldo.setText("-");
                }
            } else {
                tvSaldo.setText("-");
            }

            if (accountDefaultModel.getName() != null) {
                tvInisial.setText(GeneralHelper.formatInitialName(accountDefaultModel.getName()));
            }

            validasiButton();
        }

    }


    @Override
    public void onClick(View view) {
        if (SystemClock.elapsedRealtime() - mLastClickTime < 1000) {
            return;
        }
        mLastClickTime = SystemClock.elapsedRealtime();
        int id = view.getId();
        switch (id) {

            case R.id.btnSubmit:
                MerchantTartunOptionResponse merchantTartunOptionResponse = mTariktunai.getMerchant().get(selectedMerchantPosition);
                tarikPresenter.getDataConfirmation(accountDefaultModel.getAcoount(), merchantTartunOptionResponse.getName(), String.valueOf(hargaperbandingan), merchantTartunOptionResponse.getCode());
                break;
            case R.id.et_jalur_tarik:
            case R.id.panah_tarik_tunai:
            case R.id.ll_jalur:
                showOpsiAccount();
                break;
            case R.id.item_layout_background:
                counter++;
                if (mListAccountModel == null) {
                    GeneralHelper.showToast(this, GeneralHelper.getString(R.string.no_account_list));
                } else {
                    SumberDanaFragment fragmentSumberDana = new SumberDanaFragment(mListAccountModel, this, counter, mListFailed, isFromFastMenu);
                    fragmentSumberDana.show(getSupportFragmentManager(), "");
                }
                validasiButton();
                break;
            case R.id.tv_petunjuk:
                PetunjukActivity.launchIntent(this, mTariktunai.getMerchant().get(selectedMerchantPosition).getTutorial());
                break;
            case R.id.iv_scroll_to_bottom:
                scrollView.smoothScrollTo(0, scrollView.getChildAt(0).getHeight());
                ivScroll.setVisibility(View.GONE);
                break;
        }
    }

    public void initiateAdapter() {
        rv_tarik.setLayoutManager(new GridLayoutManager(this, 2, RecyclerView.VERTICAL, false));
    }

    private void showOpsiAccount() {
        GeneralOptionFragment fragmentPilihanDompet = new GeneralOptionFragment(fetchOptionList(), (position, optionModel) -> {
            selectedMerchantPosition = position;
            updateNominal();
            if (mTariktunai.getMerchant().get(selectedMerchantPosition).getCode().equalsIgnoreCase(Constant.TARTUNATMCODE)){
                tarikPresenter.setUrlConfirmation(GeneralHelper.getString(R.string.url_konfirmasi_tunai_atm));
            } else {
                tarikPresenter.setUrlConfirmation(GeneralHelper.getString(R.string.url_konfirmasi_tunai_merhant));
            }
            etJalurTarik.setText(optionModel.getOptionName());
            hargaperbandingan = 0;
            validasiButton();
            //load icon transaction
            GeneralHelper.loadIconTransaction(
                    this,
                    optionModel.getOptionIconPath(),
                    optionModel.getOptionIconName().replaceAll("\\s+","_").toLowerCase().split("\\.")[0],
                    ivLogoMerchant,
                    GeneralHelper.getImageId(FormTarikTunaiActivity.this, optionModel.getOptionIconPath())
            );
        });
        fragmentPilihanDompet.show(getSupportFragmentManager(), "");
    }


    @Override
    public void onSelectSumberDana(AccountModel bankModel) {
        accountDefaultModel = bankModel;

        // saldo tertahan
        if (bankModel.getSaldoReponse().isOnHold()) {
            ivAlertSaldo.setVisibility(View.VISIBLE);
            dialogInfoSaldoHold();
        } else ivAlertSaldo.setVisibility(View.GONE);

        if (bankModel.getSaldoReponse() != null) {
            tvSaldo.setText(GeneralHelper.formatNominalIDR(accountDefaultModel.getCurrency(), accountDefaultModel.getSaldoReponse().getBalanceString()));
            saldo = bankModel.getSaldoReponse().getBalance();
            saldoPerbandingan = saldo - accountDefaultModel.getMinimumBalance();
            isSaldoHold = bankModel.getSaldoReponse().isOnHold();
        } else {
            tvSaldo.setText(String.format("%s%s", bankModel.getCurrency(), "-"));
            saldo = 0.0;
            isSaldoHold = false;
        }

        validasiButton();

        tvNorek.setText(accountDefaultModel.getAcoountString());
        tvInisial.setText(GeneralHelper.formatInitialName(bankModel.getName()));
    }

    private void dialogInfoSaldoHold() {
        DialogInformation dialog = new DialogInformation(this, "maaf_maaf",
                GeneralHelper.getString(R.string.title_popup_saldo_tertahan),
                GeneralHelper.getString(R.string.desc_popup_saldo_tertahan),
                GeneralHelper.getString(R.string.ok), this, true, false);
        FragmentTransaction ft = getSupportFragmentManager().beginTransaction();
        ft.add(dialog, null);
        ft.commitAllowingStateLoss();
    }

    public void validasiButton() {
        if (saldoPerbandingan != 0 && saldoPerbandingan > hargaperbandingan && etJalurTarik.length() > 1
                && !isSaldoHold) {
            button.setVisibility(View.VISIBLE);
            llInputNominal.setVisibility(View.VISIBLE);
            if (hargaperbandingan > 0) {
                button.setAlpha(1);
                button.setEnabled(true);
            } else {
                button.setAlpha(0.3f);
                button.setEnabled(false);
            }
        } else {
            button.setVisibility(View.VISIBLE);
            if (etJalurTarik.length() > 1) {
                llInputNominal.setVisibility(View.VISIBLE);
            } else {
                llInputNominal.setVisibility(View.GONE);
            }
            button.setAlpha(0.3f);
            button.setEnabled(false);
        }

    }

    @Override
    public void onSendFailedList(List<Integer> list) {
        this.mListFailed = list;
    }

    @Override
    public void onSuccessGetTarikTunai(TarikTunaiResponse tarikTunaiResponse) {
        mTariktunai = tarikTunaiResponse;

        mNominals = mTariktunai.getMerchant().get(selectedMerchantPosition).getAmount();
        if (mTariktunai.getMerchant().get(selectedMerchantPosition).getTutorial() == null){
            rlPetunjuk.setVisibility(View.GONE);
        }
        setupAccount();

        rv_tarik.setLayoutManager(new GridLayoutManager(getApplicationContext(), 2, RecyclerView.VERTICAL, false));
        rv_tarik.setHasFixedSize(true);
        pulsaModelList = fetchbtnnama();
        pulsaAdapter = new OptionNominalAdapter(pulsaModelList, getApplicationContext(), this);
        pulsaAdapter.notifyItemRangeChanged(0, pulsaModelList.size());
        rv_tarik.setAdapter(pulsaAdapter);

    }
    public void updateNominal(){
        mNominals = mTariktunai.getMerchant().get(selectedMerchantPosition).getAmount();
        pulsaModelList = fetchbtnnama();
        pulsaAdapter = null;
        pulsaAdapter = new OptionNominalAdapter(pulsaModelList, getApplicationContext(), this);
        pulsaAdapter.notifyItemRangeChanged(0, pulsaModelList.size());
        rv_tarik.setAdapter(pulsaAdapter);
    }

    @Override
    public void onSuccessGetConfirmation(KonfirmasiTarikTunaiResponse brivaConfirmationResponse) {
        String urlPayment = "";
        String cardlessTypePay = brivaConfirmationResponse.getCardlessType();

        if (cardlessTypePay.equalsIgnoreCase(Constant.TARTUNMERCHANT)){
            urlPayment = GeneralHelper.getString(R.string.url_payment_tunai_merchant);
        }else if(cardlessTypePay.equalsIgnoreCase(Constant.TARTUNBRILINK)){
            urlPayment = GeneralHelper.getString(R.string.url_payment_tunai_merchant);
        } else {
            urlPayment = GeneralHelper.getString(R.string.url_payment_tunai_atm);
        }
        KonfirmasiTarikActivity.launchIntent(this, brivaConfirmationResponse, urlPayment, GeneralHelper.getString(R.string.txt_konfirmasi), setParameterKonfirmasi(), isFromFastMenu);
    }

    @Override
    public void onTokenActive(PaymentTarikResponse statusTarikResponse) {
        if (statusTarikResponse.getExpireTime() < 0)
            DashboardIBActivity.launchIntent(this);
        else
            ReceiptTarikActivity.launchIntent(this, statusTarikResponse);
    }


    @Override
    public void onException(String message) {
        if (GeneralHelper.isContains(Constant.LIST_TYPE_GAGAL, message))
            GeneralHelper.showDialogGagalBack(this, message);
        else
            showSnackbarErrorMessage(message, -2, this, false);

    }

    @Override
    protected void onActivityResult(int requestCode, int resultCode, @Nullable Intent data) {
        super.onActivityResult(requestCode, resultCode, data);
        if (requestCode == Constant.REQ_PAYMENT) {
            if (resultCode == RESULT_OK) {
                setResult(RESULT_OK, data);
                this.finish();
            } else {
                if (data != null) {
                    errorMessage = data.getStringExtra(TAG_ERROR_MESSAGE);
                    cekErrorMessage();
                }
            }
        }
    }

    /*
     *  method untuk menampilkan error snackbar dari Intent aktivity inquiry dan konfirmasi
     */
    protected void cekErrorMessage() {
        if (errorMessage != null) {
            //menampilkan snacknar error
            showSnackbarErrorMessage(errorMessage, ALERT_ERROR, this, false);

            //clear error message
            errorMessage = null;
        }
    }

    @Override
    protected void onDestroy() {
        tarikPresenter.stop();
        super.onDestroy();
    }

    @Override
    public void onBackPressed() {
        tarikPresenter.stop();
        super.onBackPressed();
    }

    @Override
    public void onClickAction() {
        // do nothing
    }
}