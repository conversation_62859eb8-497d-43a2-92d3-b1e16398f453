package id.co.bri.brimo.ui.activities.britamarencana;

import android.app.Activity;
import android.content.Intent;
import android.os.Bundle;
import android.os.SystemClock;
import android.view.View;
import android.widget.LinearLayout;
import androidx.annotation.Nullable;
import androidx.fragment.app.Fragment;
import androidx.viewpager.widget.ViewPager;
import com.google.gson.Gson;
import java.util.ArrayList;
import java.util.List;
import javax.inject.Inject;
import id.co.bri.brimo.R;
import id.co.bri.brimo.adapters.CatatanKeuanganAdapter;
import id.co.bri.brimo.contract.IPresenter.britamarencana.IPilihRekeningPresenter;
import id.co.bri.brimo.contract.IView.britamarencana.IPilihRekeningView;
import id.co.bri.brimo.databinding.ActivityProdukTabunganBinding;
import id.co.bri.brimo.domain.config.Constant;
import id.co.bri.brimo.domain.helpers.GeneralHelper;
import id.co.bri.brimo.models.ParameterPilihKantorModel;
import id.co.bri.brimo.models.apimodel.response.bukarekening.JenisTabunganResponse;
import id.co.bri.brimo.ui.activities.PilihKantorGeneralActivity;
import id.co.bri.brimo.ui.activities.base.BaseActivity;
import id.co.bri.brimo.ui.activities.britamajunio.PilihKantorJunioActivity;
import id.co.bri.brimo.ui.activities.bukaValas.PilihKantorValas;
import id.co.bri.brimo.ui.fragments.BottomFragmentSafteyMode;
import id.co.bri.brimo.ui.fragments.DetailTabTabunganFragment;
import id.co.bri.brimo.ui.fragments.FragmentDialogNoImage;

public class PilihRekeningActivity extends BaseActivity implements ViewPager.OnPageChangeListener, View.OnClickListener, IPilihRekeningView, FragmentDialogNoImage.DialogDefaultListener {

    private ActivityProdukTabunganBinding binding;

    private static JenisTabunganResponse.Product product;
    private static List<String> titleTab = new ArrayList<>();
    private static List<String> webTab = new ArrayList<>();

    private String urlLokasiSendiri;
    private String urlLokasiPencarian;
    private String urlInquiry;

    LinearLayout linearLayout;

    private long mLastClickTime = 0;

    private FragmentDialogNoImage fragmentDialogNoImage;

    private BottomFragmentSafteyMode bottomFragmentSafteyMode;

    @Inject
    IPilihRekeningPresenter<IPilihRekeningView> presenter;

    private static String TAG_PRODUCT = "product";

    public static void launchIntent(Activity caller, List<JenisTabunganResponse.Product> productList, int position) {
        Intent intent = new Intent(caller, PilihRekeningActivity.class);

        intent.putExtra(TAG_PRODUCT,new Gson().toJson(productList.get(position)));
        product = productList.get(position);
        //Log.i("TAG", "launchIntent: "+new Gson().toJson(product));
        //Log.i("TAG", "launchIntent: "+product.getSafety());
        caller.startActivityForResult(intent, Constant.REQ_PAYMENT);

        titleTab.clear();
        webTab.clear();
        for (int i = 0; i < productList.get(position).getTab().size(); i++) {
            titleTab.add(productList.get(position).getTab().get(i).getTitle());
            webTab.add(productList.get(position).getTab().get(i).getContent());
        }
    }

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        binding = ActivityProdukTabunganBinding.inflate(getLayoutInflater());
        setContentView(binding.getRoot());

        /*
            if (getIntent().getStringExtra(TAG_PRODUCT) != null){
                product = new Gson().fromJson(getIntent().getStringExtra(TAG_PRODUCT),JenisTabunganResponse.Product.class);
            }
           
         */

        //switch product type
        switch (product.getType()){
            case Constant.OPEN_ACCOUNT_GENERAL:
                setToolbarRekening(product.getName());
                urlLokasiSendiri = GeneralHelper.getString(R.string.url_s3f_lokasi_sendiri);
                urlLokasiPencarian = GeneralHelper.getString(R.string.url_s3f_lokasi_pencarian);
                urlInquiry = GeneralHelper.getString(R.string.url_general_open_account_inquiry);
                break;
            case Constant.OPEN_ACCOUNT_RENCANA:
                setToolbarRekening(product.getName());
                break;
            case Constant.OPEN_ACCOUNT_S3f:
                urlLokasiSendiri = GeneralHelper.getString(R.string.url_s3f_lokasi_sendiri);
                urlLokasiPencarian = GeneralHelper.getString(R.string.url_s3f_lokasi_pencarian);
                urlInquiry = GeneralHelper.getString(R.string.url_s3f_open_inquiry);
                setToolbarRekening(product.getName());
                break;
            case Constant.OPEN_ACCOUNT_VALAS:
                setToolbarRekening(product.getName());
                break;
            case Constant.OPEN_ACCOUNT_JUNIO:
                urlLokasiSendiri = GeneralHelper.getString(R.string.url_s3f_lokasi_sendiri);
                urlLokasiPencarian = GeneralHelper.getString(R.string.url_s3f_lokasi_pencarian);
                urlInquiry = GeneralHelper.getString(R.string.url_form_junio);
                setToolbarRekening(product.getName());
                break;
            default:
                break;
        }

        injectDependency();

        binding.btnSubmit.setOnClickListener(this);

        setupView();
    }

    private void injectDependency() {
        getActivityComponent().inject(this);
        if (presenter != null) {
            presenter.setView(this);
            presenter.start();
            presenter.setUrlValidationS3f(GeneralHelper.getString(R.string.url_s3f_open_validate));
        }
    }

    private void setToolbarRekening(String sToolbar) {
        GeneralHelper.setToolbar(this, binding.toolbar.toolbar, sToolbar);
    }

    private void setupView() {
        GeneralHelper.loadImageUrl(getApplicationContext(), product.getProductUrlPath(), binding.imageRekening, R.drawable.logo_bri, 0);
        binding.textRekening.setText(product.getFullDescription());

        List<Fragment> fragmentList = new ArrayList<>();
        for (int i = 0; i < webTab.size(); i++) {
            fragmentList.add(new DetailTabTabunganFragment(webTab.get(i)));
        }

        CatatanKeuanganAdapter viewAdapter = new CatatanKeuanganAdapter(getSupportFragmentManager(), this, fragmentList, titleTab);
        binding.viewpager.setAdapter(viewAdapter);
        binding.tabDetailRekening.setViewPager(binding.viewpager);

        binding.tabDetailRekening.setOnPageChangeListener(this);
        linearLayout = (LinearLayout) binding.tabDetailRekening.getChildAt(0);

        GeneralHelper.changeTabsFontSimple(this, linearLayout, 0);
    }

    public ParameterPilihKantorModel setParameterModel() {
        ParameterPilihKantorModel parameterModel = new ParameterPilihKantorModel();

        parameterModel.setType(product.getType());
        parameterModel.setProductType(product.getProductType());
        parameterModel.setUrlOwnLocation(urlLokasiSendiri);
        parameterModel.setUrlSearchLocation(urlLokasiPencarian);
        parameterModel.setUrlInquiry(urlInquiry);
        parameterModel.setName(product.getName());

        return parameterModel;
    }

    @Override
    public void onClick(View view) {

        if (SystemClock.elapsedRealtime() - mLastClickTime < 1000){
            return;
        }
        mLastClickTime = SystemClock.elapsedRealtime();
        switch (product.getType()) {
            case Constant.OPEN_ACCOUNT_RENCANA:
//                if (product.getSafety()){
//                    bottomFragmentSafteyMode = new BottomFragmentSafteyMode(this::onClickYes, product.getSafetyContent().getTitle(), product.getSafetyContent().getDescription(), "Saya Mengerti", "Pelajari Lebih Lanjut", Constant.OPEN_ACCOUNT_S3f);
//                    bottomFragmentSafteyMode.show(getSupportFragmentManager(), "");
//                } else {
                    HitungRencanaActivity.launchIntent(this, false, product.getProductType());
                //}
                break;
            case Constant.OPEN_ACCOUNT_S3f:
//                if (product.getSafety() != null) {
//                    if (product.getSafety()){
//                        bottomFragmentSafteyMode = new BottomFragmentSafteyMode(this::onClickYes, product.getSafetyContent().getTitle(), product.getSafetyContent().getDescription(), "Saya Mengerti", "Pelajari Lebih Lanjut", Constant.OPEN_ACCOUNT_S3f);
//                        bottomFragmentSafteyMode.show(getSupportFragmentManager(), "");
//                    } else {
                        presenter.getDataValidationS3F();
//                    }
//                }
                break;
            case Constant.OPEN_ACCOUNT_GENERAL:
//                if (product.getSafety() != null) {
//                    if (product.getSafety()){
//                        bottomFragmentSafteyMode = new BottomFragmentSafteyMode(this::onClickYes, product.getSafetyContent().getTitle(), product.getSafetyContent().getDescription(), "Saya Mengerti", "Pelajari Lebih Lanjut", Constant.OPEN_ACCOUNT_S3f);
//                        bottomFragmentSafteyMode.show(getSupportFragmentManager(), "");
//                    } else {
                        PilihKantorGeneralActivity.launchIntent(this, setParameterModel());
//                    }
//                }
                break;
            case Constant.OPEN_ACCOUNT_VALAS:
//                if (product.getSafety() != null) {
//                    if (product.getSafety()){
//                        bottomFragmentSafteyMode = new BottomFragmentSafteyMode(this::onClickYes, product.getSafetyContent().getTitle(), product.getSafetyContent().getDescription(), "Saya Mengerti", "Pelajari Lebih Lanjut", Constant.OPEN_ACCOUNT_S3f);
//                        bottomFragmentSafteyMode.show(getSupportFragmentManager(), "");
//                    } else {
                        PilihKantorValas.launchIntent(this);
//                    }
//                }
                break;
            case Constant.OPEN_ACCOUNT_JUNIO:
//                if (product.getSafety()){
//                    bottomFragmentSafteyMode = new BottomFragmentSafteyMode(this::onClickYes, product.getSafetyContent().getTitle(), product.getSafetyContent().getDescription(), "Saya Mengerti", "Pelajari Lebih Lanjut", Constant.OPEN_ACCOUNT_S3f);
//                    bottomFragmentSafteyMode.show(getSupportFragmentManager(), "");
//                } else {
                    PilihKantorJunioActivity.launchIntent(this, setParameterModel());
//                }
                break;
            default:
                break;
        }
    }

    @Override
    public void onPageScrolled(int position, float positionOffset, int positionOffsetPixels) {
        // Do nothing
    }

    @Override
    public void onPageSelected(int position) {
        GeneralHelper.changeTabsFontSimple(this, linearLayout, position);
    }

    @Override
    public void onPageScrollStateChanged(int state) {
        // Do nothing
    }

    @Override
    protected void onActivityResult(int requestCode, int resultCode, @Nullable Intent data) {
        super.onActivityResult(requestCode, resultCode, data);
        if (requestCode == Constant.REQ_PAYMENT) {
            if (resultCode == RESULT_OK && data!=null) {
                setResult(RESULT_OK, data);
                finish();
            }
            else if (resultCode == RESULT_OK && data==null){
                //perubahan general
                setResult(RESULT_OK);
                finish();
            }
            else {
                setResult(RESULT_CANCELED, data);
                if (data!=null){
                    finish();
                }
            }
        }

    }

    @Override
    public void onSuccessGetData() {
        PilihKantorGeneralActivity.launchIntent(this, setParameterModel());
    }

    @Override
    public void onGetS3E(String message) {
        Intent returnIntent = new Intent();
        returnIntent.putExtra(Constant.TAG_ERROR_MESSAGE, message);
        returnIntent.putExtra(Constant.TAG_TITLE, "S3E");
        setResult(RESULT_CANCELED, returnIntent);
        finish();
    }
    
    @Override
    public void onClickYes(String type) {
        switch (type) {
            case Constant.OPEN_ACCOUNT_GENERAL:
                PilihKantorGeneralActivity.launchIntent(this, setParameterModel());
                break;
            case Constant.OPEN_ACCOUNT_S3f:
                presenter.getDataValidationS3F();
                break;
            case Constant.OPEN_ACCOUNT_VALAS:
                PilihKantorValas.launchIntent(this);
                break;
            default:
                break;
        }
    }

    @Override
    public void onPointerCaptureChanged(boolean hasCapture) {
        super.onPointerCaptureChanged(hasCapture);
    }

    @Override
    protected void onDestroy() {
        super.onDestroy();
        binding = null;
    }
}