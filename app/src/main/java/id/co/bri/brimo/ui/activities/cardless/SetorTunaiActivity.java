package id.co.bri.brimo.ui.activities.cardless;

import android.app.Activity;
import android.content.ClipData;
import android.content.ClipboardManager;
import android.content.Context;
import android.content.Intent;
import android.os.Bundle;
import android.os.CountDownTimer;
import android.os.SystemClock;
import android.view.View;
import android.widget.Toast;
import androidx.annotation.Nullable;
import androidx.fragment.app.FragmentTransaction;
import javax.inject.Inject;
import id.co.bri.brimo.R;
import id.co.bri.brimo.contract.IPresenter.cardless.ISetorTunaiPresenter;
import id.co.bri.brimo.contract.IView.cardless.ISetorTunaiView;
import id.co.bri.brimo.databinding.ActivitySetorTunaiBinding;
import id.co.bri.brimo.domain.config.Constant;
import id.co.bri.brimo.domain.helpers.GeneralHelper;
import id.co.bri.brimo.models.apimodel.response.PaymentSetorResponse;
import id.co.bri.brimo.ui.activities.DashboardIBActivity;
import id.co.bri.brimo.ui.activities.base.BaseActivity;
import id.co.bri.brimo.ui.customviews.dialog.DialogExitCustom;

public class SetorTunaiActivity extends BaseActivity implements View.OnClickListener, ISetorTunaiView, DialogExitCustom.DialogDefaultListener {

    private ActivitySetorTunaiBinding binding;

    private final int SECOND = 1000;

    private static PaymentSetorResponse paymentSetorResponse;
    protected boolean isFinish = false;


    @Inject
    ISetorTunaiPresenter<ISetorTunaiView> setorTunaiPresenter;

    private CountDownTimer countDownTimer;



    public static void launchIntent(Activity caller, PaymentSetorResponse pendingResponse, boolean fromFastMenu) {
        isFromFastMenu = fromFastMenu;
        Intent intent = new Intent(caller, SetorTunaiActivity.class);
        paymentSetorResponse = pendingResponse;
        caller.startActivityForResult(intent, Constant.REQ_PAYMENT);
        caller.setResult(RESULT_OK);
        caller.finish();
    }

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        binding = ActivitySetorTunaiBinding.inflate(getLayoutInflater());
        setContentView(binding.getRoot());

        injectDependency();
        GeneralHelper.setToolbar(this, binding.mToolbar.toolbar, GeneralHelper.getString(R.string.txt_setor_tunai));
        setupView();

    }
    private void setupView(){
        binding.btnSubmit.setOnClickListener(this);
        binding.btnCopy.setOnClickListener(this);
        binding.lihatPetunjuk.setOnClickListener(this);
        binding.batalPenyetoran.setOnClickListener(this);
        if (paymentSetorResponse != null) {
            binding.noRekeningTujuan.setText(paymentSetorResponse.getAccountNumber());
            binding.textOTP.setText(paymentSetorResponse.getOtp());
            if (paymentSetorResponse.getExpireTime() != null)
                setTextTimer(paymentSetorResponse.getExpireTime());
            else
                setTextTimer(0);
        }
    }
    @Override
    protected void onResume() {
        super.onResume();
        if (setorTunaiPresenter != null) {
            setorTunaiPresenter.setView(this);
            if (isFromFastMenu){
                setorTunaiPresenter.setFormUrl(GeneralHelper.getString(R.string.url_fm_cash_deposit_delete));
            }else{
                setorTunaiPresenter.setFormUrl(GeneralHelper.getString(R.string.url_batal_penyetoran_tunai));
            }

            setorTunaiPresenter.start();
        }
    }

    private void injectDependency(){
        getActivityComponent().inject(this);
        if (setorTunaiPresenter != null) {
            setorTunaiPresenter.setView(this);
            if (isFromFastMenu){
                setorTunaiPresenter.setFormUrl(GeneralHelper.getString(R.string.url_fm_cash_deposit_delete));
            }else{
                setorTunaiPresenter.setFormUrl(GeneralHelper.getString(R.string.url_batal_penyetoran_tunai));
            }

            setorTunaiPresenter.start();

        }
    }
    private void setTextTimer(int timer) {
        isFinish = true;
        int countDown = SECOND * timer;
        countDownTimer = new CountDownTimer(countDown,  SECOND) {
            public void onTick(long millisUntilFinished) {
                int seconds = (int) millisUntilFinished /  SECOND;
                int[] timeFormat = GeneralHelper.getTimeFormat(seconds);

                binding.txtTimer.setText(String.format(getResources().getString(R.string.txt_countdown_setor), timeFormat[1], timeFormat[2]));
            }

            public void onFinish() {
                cancel();
                finishCountDown();
                isFinish = false;
            }

        }.start();
    }

    private void finishCountDown(){
        if (isFinish){
            isFinish = false;
            if (isFromFastMenu){
                setorTunaiPresenter.onGetBatalSetorFm(isFromFastMenu);
            }else{
                setorTunaiPresenter.onGetBatalSetor();
            }
        }
    }


    @Override
    public void onClick(View v) {
        if (SystemClock.elapsedRealtime() - mLastClickTime < 1000) {
            return;
        }
        mLastClickTime = SystemClock.elapsedRealtime();
        int id = v.getId();
        switch (id) {
            case R.id.btnSubmit:
                if (isFromFastMenu){
                    finish();
                }else{
                    DashboardIBActivity.launchIntent(this);
                }
                break;
            case R.id.btn_copy:
                String getstring = binding.textOTP.getText().toString();
                ClipboardManager clipboard = (ClipboardManager) getSystemService(Context.CLIPBOARD_SERVICE);
                ClipData clip = ClipData.newPlainText(getstring, getstring);
                clipboard.setPrimaryClip(clip);
                Toast.makeText(this, GeneralHelper.getString(R.string.txt_succed_copy), Toast.LENGTH_SHORT).show();
                break;
            case R.id.batalPenyetoran:
                DialogExitCustom dialogExitCustom = new DialogExitCustom(this::onClickYes, GeneralHelper.getString(R.string.cancel_trx), GeneralHelper.getString(R.string.cancel_trx_confirm));
                FragmentTransaction ft = this.getSupportFragmentManager().beginTransaction();
                ft.add(dialogExitCustom, null);
                ft.commitAllowingStateLoss();

                break;
            case R.id.lihat_petunjuk:
                PetunjukSetorActivity.launchIntent(this);
                break;
            default:
                break;
        }



    }

    @Override
    public void onSuccesGetBatal(String message) {
        FormSetorTunaiActivity.launchIntentReciept(this, isFromFastMenu);
    }

    @Override
    protected void onActivityResult(int requestCode, int resultCode, @Nullable Intent data) {
        super.onActivityResult(requestCode, resultCode, data);
        if (requestCode == Constant.REQ_PAYMENT) {
            if (resultCode == RESULT_OK) {
                this.setResult(RESULT_OK,data);
                this.finish();
            } else {
                this.setResult(RESULT_CANCELED);
                this.setResult(RESULT_CANCELED,data);
            }
        }
    }

    @Override
    public void onException93(String message) {
        FormSetorTunaiActivity.launchIntentReciept(this, message, isFromFastMenu);
    }

    @Override
    public void onException12(String message) {
        FormSetorTunaiActivity.launchIntentReciept(this, message, isFromFastMenu);
    }

    @Override
    public void onClickYes() {
        if (isFromFastMenu){
            setorTunaiPresenter.onGetBatalSetorFm(isFromFastMenu);
        }else{
            setorTunaiPresenter.onGetBatalSetor();
        }
    }

    @Override
    protected void onDestroy() {
        if (countDownTimer != null){
            countDownTimer.cancel();
        }
        setorTunaiPresenter.stop();
        binding = null;
        super.onDestroy();
    }

    @Override
    public void onBackPressed() {
        if  (countDownTimer != null){
            countDownTimer.cancel();
        }
        setorTunaiPresenter.stop();
        if (isFromFastMenu){
            finish();
        }else{
            DashboardIBActivity.launchIntent(this);
        }
        super.onBackPressed();
    }
}