package id.co.bri.brimo.ui.customviews.switchbutton

import android.content.Context
import android.util.AttributeSet
import android.view.LayoutInflater
import android.widget.FrameLayout
import android.widget.ImageView
import android.widget.TextView
import id.co.bri.brimo.R
import id.co.bri.brimo.domain.config.Constant


class SwitchLanguageButtonView : FrameLayout {

    private var mContext: Context? = null
    private lateinit var ivLanguage : ImageView
    private var onClickListener: (() -> Unit)? = null

    constructor(mContext: Context) : super(mContext) {
        this.mContext = mContext
        initView(mContext)
    }

    constructor(mContext: Context, attri: AttributeSet?): super(mContext, attri) {
        loadDataXml(attri, mContext)
        initView(mContext)
    }


    private fun loadDataXml(attri: AttributeSet?, mContext: Context) {
        // Accessing attributes from AttributeSet
        val dataView = mContext.obtainStyledAttributes(attri, R.styleable.CustomSwitchButton)
        dataView.recycle()
    }

    private fun initView(mContext: Context) {
        val view = LayoutInflater.from(mContext).inflate(R.layout.custom_switch_language_button_view, this, true)
        ivLanguage = findViewById(R.id.iv_language)
        view.setOnClickListener {
            onClickListener?.invoke()
        }
    }


    fun setText(value :String){
        val imageResource = when(value){
            Constant.LANGUAGE_ENGLISH -> R.drawable.ic_us_flag
            else -> R.drawable.ic_indonesia_flag
        }
        ivLanguage.setImageResource(imageResource)
    }

    fun setOnClickListener(mOnClick: () -> Unit){
        onClickListener =  mOnClick
    }

}
