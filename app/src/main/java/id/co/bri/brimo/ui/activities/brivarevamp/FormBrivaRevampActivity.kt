package id.co.bri.brimo.ui.activities.brivarevamp

import android.annotation.SuppressLint
import android.app.Activity
import android.content.Intent
import android.os.Build
import android.view.View
import androidx.fragment.app.FragmentTransaction
import id.co.bri.brimo.R
import id.co.bri.brimo.contract.IPresenter.brivarevamp.IFormBrivaRevampPresenter
import id.co.bri.brimo.contract.IView.brivarevamp.IFormBrivaRevampView
import id.co.bri.brimo.domain.config.Constant
import id.co.bri.brimo.domain.helpers.GeneralHelper
import id.co.bri.brimo.models.ParameterModel
import id.co.bri.brimo.models.apimodel.response.DataBrivaResponse
import id.co.bri.brimo.models.apimodel.response.HistoryResponse
import id.co.bri.brimo.models.apimodel.response.InquiryBrivaRevampResponse
import id.co.bri.brimo.models.apimodel.response.RestResponse
import id.co.bri.brimo.models.apimodel.response.SavedResponse
import id.co.bri.brimo.models.apimodel.response.TopBriva
import id.co.bri.brimo.models.optionmodel.OptionSearchRevampModel
import id.co.bri.brimo.ui.activities.FormBpjsActivity
import id.co.bri.brimo.ui.activities.InquiryKonfirmasiBrivaRevampCloseActivity
import id.co.bri.brimo.ui.activities.base.BaseFormRevampActivity
import id.co.bri.brimo.ui.activities.dompetdigitalrevamp.FormDompetDigitalRevamp
import id.co.bri.brimo.ui.activities.transferrevamp.FormEditSavedRevampActivity
import id.co.bri.brimo.ui.activities.travel.TravelMenuActivity
import id.co.bri.brimo.ui.customviews.dialog.DialogNotice
import id.co.bri.brimo.ui.customviews.dialog.DialogSetDefaultRevamp
import id.co.bri.brimo.ui.fragments.FragmentBottomDialog
import id.co.bri.brimo.ui.fragments.SearchRevampFragment
import id.co.bri.brimo.util.extension.visibleView
import java.util.Locale
import javax.inject.Inject

class FormBrivaRevampActivity : BaseFormRevampActivity(),
    IFormBrivaRevampView,
    DialogNotice.OnClickOK {

    private lateinit var topBriva: List<TopBriva>
    private lateinit var str1: Array<String>
    private lateinit var value: String
    private var isEwallet: Boolean = false
    private var isTravel: Boolean = false

    @Inject
    lateinit var brivaPresenter: IFormBrivaRevampPresenter<IFormBrivaRevampView>

    companion object {
        @JvmStatic
        fun launchIntent(caller: Activity, fromFastMenu: Boolean) {
            val intent = Intent(caller, FormBrivaRevampActivity::class.java)
            isFromFastMenu = fromFastMenu
            caller.startActivityForResult(intent, Constant.REQ_PAYMENT)
        }

        fun setParameter(): ParameterModel {
            val parameterModel = ParameterModel()
            parameterModel.stringLabelTujuan = "Nomor Tujuan"
            parameterModel.stringLabelNominal = "Nominal Pembayaran"
            parameterModel.stringButtonSubmit = "BRIVA"
            parameterModel.stringLabelMinimum = "BRIVA"
            parameterModel.defaultIcon = getDefaultIconResource()
            return parameterModel
        }

        fun getDefaultIconResource(): Int {
            return R.drawable.briva
        }
    }

    override fun buttonClick() {
        binding.btnSubmit.setOnClickListener {
            TambahDaftarBrivaRevampActivity.launchIntent(
                this,
                GeneralHelper.getString(R.string.url_briva_inquiry_v3),
                GeneralHelper.getString(R.string.url_briva_confirmation_v3),
                GeneralHelper.getString(R.string.url_briva_pay_v3),
                topBriva,
                setParameter()
            )
        }
    }

    override fun injectDependency() {
        super.injectDependency()
        activityComponent.inject(this)
        brivaPresenter.view = this
        if (isFromFastMenu) {
            brivaPresenter.setUrlForm(GeneralHelper.getString(R.string.url_fm_briva_form_v3))
            brivaPresenter.setUrlInquiry(GeneralHelper.getString(R.string.url_fm_briva_inquiry_v3))
            brivaPresenter.setUrlConfirm(GeneralHelper.getString(R.string.url_fm_briva_confirmation_v3))
            brivaPresenter.setUrlPayment(GeneralHelper.getString(R.string.url_fm_briva_pay_v3))
            brivaPresenter.getDataFormFastMenu()
        } else {
            brivaPresenter.setUrlForm(GeneralHelper.getString(R.string.url_briva_form_v3))
            brivaPresenter.setUrlInquiry(GeneralHelper.getString(R.string.url_briva_inquiry_v3))
            brivaPresenter.setUrlConfirm(GeneralHelper.getString(R.string.url_briva_confirmation_v3))
            brivaPresenter.setUrlPayment(GeneralHelper.getString(R.string.url_briva_pay_v3))
            brivaPresenter.getDataForm()
        }
        brivaPresenter.start()
    }

    override fun getTitleBar(): String {
        return GeneralHelper.getString(R.string.briva)
    }

    override fun getDefaultIconResource(): Int {
        return R.drawable.briva
    }

    override fun setTextForm() {
        binding.searchview.queryHint = GeneralHelper.getString(R.string.briva_cari_nama_disini)
        binding.tvLastTrx.text = GeneralHelper.getString(R.string.pembayaran_terakhir)
        binding.btnSubmit.text = GeneralHelper.getString(R.string.tambah_transaksi_baru)
        binding.tvSavedData.text = GeneralHelper.getString(R.string.daftar_tersimpan)
        binding.tvNoHistory.text = GeneralHelper.getString(R.string.desc_no_history)
        binding.tvDescNoDataSaved.text = GeneralHelper.getString(R.string.desc_briva_no_saved)
    }

    override fun onSuccessGetRestResponse(restResponse: RestResponse) {
        val dataBrivaResponse = restResponse.getData(
            DataBrivaResponse::class.java
        )
        topBriva = dataBrivaResponse.topBriva
    }

    override fun onSuccessGetInquiry(
        inquiryRevampResponse: InquiryBrivaRevampResponse,
        urlConfirm: String,
        urlPayment: String
    ) {
        if (inquiryRevampResponse.openPayment) {
            if (inquiryRevampResponse.isBilling) {
                InquiryBrivaPartialRevampActivity.launchIntent(
                    this,
                    inquiryRevampResponse,
                    urlConfirm,
                    urlPayment,
                    isFromFastMenu,
                    setParameter()
                )
            } else {
                InquiryBrivaOpenRevampActivity.launchIntent(
                    this, inquiryRevampResponse, urlConfirm, urlPayment, isFromFastMenu,
                    setParameter()
                )
            }
        } else
            InquiryKonfirmasiBrivaRevampCloseActivity.launchIntent(
                this, inquiryRevampResponse,
                urlConfirm, urlPayment, isFromFastMenu, setParameter(), Constant.TRX_TYPE_BRIVA
            )
    }

    override fun onException58(errorMessage: String) {
        isEwallet = true
        val dialogNotice = DialogNotice(
            this, GeneralHelper.getString(R.string.title_fitur_wallet), errorMessage,
            GeneralHelper.getString(R.string.ok), true, false
        )
        val ft = this.supportFragmentManager.beginTransaction()
        ft.add(dialogNotice, null)
        ft.commitAllowingStateLoss()
    }

    override fun onException59(errorMessage: String) {
        isEwallet = false
        val dialogNotice = DialogNotice(
            this, GeneralHelper.getString(R.string.title_fitur_bpjs), errorMessage,
            GeneralHelper.getString(R.string.baiklah_revamp), true, false
        )
        val ft = this.supportFragmentManager.beginTransaction()
        ft.add(dialogNotice, null)
        ft.commitAllowingStateLoss()
    }

    override fun onException88(errorMessage: String) {
        val fragmentBottomDialog = FragmentBottomDialog(
            this,
            Constant.TRX_TYPE_BRIVA,
            GeneralHelper.getString(R.string.already_paid_bill_title),
            GeneralHelper.getString(R.string.already_paid_bill_desc),
            "ic_berhasil",
            false,
            { },
            GeneralHelper.getString(R.string.btn_tutup)
        )
        fragmentBottomDialog.isCancelable = false
        fragmentBottomDialog.show(supportFragmentManager, "")
    }

    override fun onException60(errorMessage: String) {
        isTravel = true
        val dialogNotice = DialogNotice(
            this, GeneralHelper.getString(R.string.title_fitur_kereta_cepat), errorMessage,
            GeneralHelper.getString(R.string.ok), true, false
        )
        val ft = this.supportFragmentManager.beginTransaction()
        ft.add(dialogNotice, null)
        ft.commitAllowingStateLoss()
    }

    override fun onExpiredBill(errorMessage: String) {
        val fragmentBottomDialog = FragmentBottomDialog(
            this,
            Constant.TRX_TYPE_BRIVA,
            GeneralHelper.getString(R.string.error_briva_expired_title),
            GeneralHelper.getString(R.string.error_briva_expired_desc),
            "img_sorry_with_card",
            false,
            { },
            GeneralHelper.getString(R.string.mengerti)
        )
        fragmentBottomDialog.isCancelable = false
        fragmentBottomDialog.show(supportFragmentManager, "")
    }


    @SuppressLint("NotifyDataSetChanged")
    override fun onSuccessUpdate(savedResponse: SavedResponse, item: Int, type: Int) {
        val message = GeneralHelper.getString(R.array.type_option_desc, type)
        showSnackbarErrorMessageRevamp(message, ALERT_CONFIRM, this, false)

        val newPosition = savedResponses.indexOfFirst { it.value == savedResponse.value }
        if (type == Constant.EditOption.NON_FAV || type == Constant.EditOption.FAV) {
            savedResponses[newPosition].favorite = !savedResponse.favorite
            savedResponses.toMutableList()
            savedResponses.sortWith(
                compareBy<SavedResponse> {
                    it.favorite
                }.reversed().thenBy {
                    it.title.lowercase(Locale.getDefault())
                })
        } else if (type == Constant.EditOption.HAPUS) {
            savedResponses.removeAt(newPosition)
            binding.llNoDataSaved.visibleView(savedResponses.size > 0)
        }
        savedAdapter.setSavedResponses(savedResponses)
        savedAdapter.notifyDataSetChanged()
    }

    override fun onClickHistoryItem(historyResponse: HistoryResponse) {
        brivaPresenter.getDataInquiry(historyResponse.value, isFromFastMenu)
    }

    override fun onClickSavedItem(savedResponse: SavedResponse) {
        str1 =
            savedResponse.value.split("\\|".toRegex()).dropLastWhile { it.isEmpty() }.toTypedArray()

        if (str1.isNotEmpty()) {
            value = str1[1]
        }
        brivaPresenter.getDataInquiry(value, isFromFastMenu)
    }

    override fun onClickUpdateItem(savedResponse: SavedResponse, position: Int) {
        val SearchRevampFragment = SearchRevampFragment(
            fetchList(savedResponse),
            { optionSearchRevampModel: OptionSearchRevampModel ->
                when (optionSearchRevampModel.codeModel) {
                    Constant.EditOption.EDIT.toString() -> {
                        FormEditSavedRevampActivity.launchIntentBriva(
                            this,
                            savedResponse,
                            position,
                            0,
                            GeneralHelper.getString(R.string.url_briva_update_nickname_v3),
                            GeneralHelper.getString(R.string.nomor_tujuan),
                            "",
                            true
                        )
                    }

                    Constant.EditOption.HAPUS.toString() -> {
                        val dialogTitle =
                            GeneralHelper.getString(R.string.konfirmasi_hapus_tersimpan_title)
                        val dialogDesc = String.format(
                            GeneralHelper.getString(R.string.konfirmasi_hapus_tersimpan_desc),
                            savedResponse.title
                        )
                        val dialogSetDefaultRevamp = DialogSetDefaultRevamp(
                            object : DialogSetDefaultRevamp.DialogDefaultListener {
                                override fun onClickYesDefault(requestId: Int) {
                                    brivaPresenter.setUpdateItem(
                                        GeneralHelper.getString(R.string.url_briva_delete_saved_v3),
                                        savedResponse,
                                        position,
                                        Constant.EditOption.HAPUS
                                    )
                                }

                                override fun onClickNoDefault(requestId: Int) {}
                            },
                            dialogTitle,
                            dialogDesc,
                            GeneralHelper.getString(R.string.hapus),
                            GeneralHelper.getString(R.string.batal),
                            Constant.REQ_EDIT_SAVED
                        )
                        val ft: FragmentTransaction =
                            this.supportFragmentManager.beginTransaction()
                        ft.add(dialogSetDefaultRevamp, null)
                        ft.commitAllowingStateLoss()
                    }

                    Constant.EditOption.NON_FAV.toString() -> {
                        brivaPresenter.setUpdateItem(
                            GeneralHelper.getString(R.string.url_briva_remove_favorite_saved_v3),
                            savedResponse,
                            position,
                            Constant.EditOption.NON_FAV
                        )
                    }

                    Constant.EditOption.FAV.toString() -> {
                        brivaPresenter.setUpdateItem(
                            GeneralHelper.getString(R.string.url_briva_favorite_saved_v3),
                            savedResponse,
                            position,
                            Constant.EditOption.FAV
                        )
                    }
                }
            }, "", GeneralHelper.getString(R.string.setting), false
        )
        SearchRevampFragment.show(supportFragmentManager, "")
    }

    private fun fetchList(savedResponse: SavedResponse): List<OptionSearchRevampModel> {
        val optionSearchRevampModelArrayList: MutableList<OptionSearchRevampModel> = ArrayList()
        if (java.lang.Boolean.TRUE == savedResponse.favorite) {
            optionSearchRevampModelArrayList.add(
                OptionSearchRevampModel(
                    "ic_opt_fav",
                    GeneralHelper.getString(this, R.string.opt_non_fav),
                    "",
                    0,
                    0,
                    Constant.EditOption.NON_FAV.toString(),
                    true
                )
            )
        } else {
            optionSearchRevampModelArrayList.add(
                OptionSearchRevampModel(
                    "ic_opt_fav",
                    GeneralHelper.getString(this, R.string.opt_fav),
                    "",
                    0,
                    0,
                    Constant.EditOption.FAV.toString(),
                    true
                )
            )
        }
        optionSearchRevampModelArrayList.add(
            OptionSearchRevampModel(
                "ic_opt_edit",
                GeneralHelper.getString(this, R.string.ubah_nama),
                "",
                0,
                0,
                Constant.EditOption.EDIT.toString(),
                true
            )
        )
        optionSearchRevampModelArrayList.add(
            OptionSearchRevampModel(
                "ic_opt_hapus",
                GeneralHelper.getString(this, R.string.unfavorit_saved),
                "",
                0,
                0,
                Constant.EditOption.HAPUS.toString(),
                true
            )
        )

        return optionSearchRevampModelArrayList
    }

    @Deprecated("Deprecated in Java")
    @SuppressLint("NotifyDataSetChanged")
    override fun onActivityResult(requestCode: Int, resultCode: Int, data: Intent?) {
        super.onActivityResult(requestCode, resultCode, data)
        if (requestCode == Constant.REQ_EDIT_SAVED && data != null) {
            if (resultCode == RESULT_OK) {
                val message =
                    GeneralHelper.getString(R.array.type_option_desc, Constant.EditOption.EDIT)
                showSnackbarErrorMessageRevamp(message, ALERT_CONFIRM, this, false)
                val lastPos = savedResponses.indexOfFirst { it.value == data.getStringExtra(Constant.TAG_VALUE) }
                val newPos = if (lastPos != -1) lastPos else data.getIntExtra(Constant.TAG_POSITION, 0)
                savedResponses[newPos].title = data.getStringExtra(Constant.TAG_TITLE)
                savedResponses.toMutableList()
                if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.N) {
                    savedResponses.sortWith(
                        compareBy<SavedResponse> { it.favorite }.reversed().thenBy {
                            it.title.lowercase(
                                Locale.getDefault()
                            )
                        })
                }

                savedAdapter.setSavedResponses(this.savedResponses)
                savedAdapter.notifyDataSetChanged()
            }
        }
    }

    override fun clickOk() {
        if (isTravel) {
            TravelMenuActivity.launchIntent(this)
        } else if (isEwallet)
            FormDompetDigitalRevamp.launchIntent(this, isFromFastMenu)
        else
            FormBpjsActivity.launchIntent(this, isFromFastMenu)
    }

    override fun checkDataHistorySavedList() {
        if (historyResponses.isEmpty() && savedResponses.isEmpty() && !isFromFastMenu) {
            binding.btnSubmit.performClick()
        }
    }
}