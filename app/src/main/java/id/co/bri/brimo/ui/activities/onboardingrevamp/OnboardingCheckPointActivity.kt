package id.co.bri.brimo.ui.activities.onboardingrevamp

import android.app.Activity
import android.content.ActivityNotFoundException
import android.content.Intent
import android.net.Uri
import android.os.Bundle
import androidx.activity.result.ActivityResultLauncher
import androidx.activity.result.contract.ActivityResultContracts
import androidx.recyclerview.widget.LinearLayoutManager
import com.google.gson.Gson
import id.co.bri.brimo.R
import id.co.bri.brimo.adapters.OnboardingCheckPointAdapter
import id.co.bri.brimo.contract.IPresenter.onboardingrevamp.IOnboardingCheckPointPresenter
import id.co.bri.brimo.contract.IView.onboardingrevamp.IOnboardingCheckPointView
import id.co.bri.brimo.databinding.ActivityOnboardingCheckPointBinding
import id.co.bri.brimo.domain.config.Constant
import id.co.bri.brimo.domain.config.MenuConfig
import id.co.bri.brimo.domain.helpers.GeneralHelper
import id.co.bri.brimo.models.InfoModel
import id.co.bri.brimo.models.apimodel.response.StatusResponse
import id.co.bri.brimo.models.apimodel.response.onboardingrevamp.ForceUpdateResponse
import id.co.bri.brimo.models.apimodel.response.onboardingrevamp.OnboardingReceiptResponse
import id.co.bri.brimo.models.apimodel.response.onboardingrevamp.TabunganResponse
import id.co.bri.brimo.models.apimodel.response.registrasi.RegisCheckPointResponse
import id.co.bri.brimo.ui.activities.base.BaseActivity
import id.co.bri.brimo.ui.customviews.dialog.DialogExitCustom
import id.co.bri.brimo.ui.fragments.bottomsheet.OpenBottomSheetGeneralFragment
import javax.inject.Inject

class OnboardingCheckPointActivity : BaseActivity(),
    IOnboardingCheckPointView,
    DialogExitCustom.DialogDefaultListener {

    @Inject
    lateinit var presenter: IOnboardingCheckPointPresenter<IOnboardingCheckPointView>

    private lateinit var binding: ActivityOnboardingCheckPointBinding

    private var checkPointResponses: ArrayList<RegisCheckPointResponse> = ArrayList()

    private var checkPoint: Int? = null

    private var responseString: String = ""

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        binding = ActivityOnboardingCheckPointBinding.inflate(layoutInflater)
        setContentView(binding.root)

        injectDependency()
        intentExtra()
        dataList()
        setupView()
    }

    private fun intentExtra() {
        if (intent.hasExtra(Constant.CHECK_POINT))
            checkPoint = intent.getIntExtra(Constant.CHECK_POINT, 0)

        if (intent.hasExtra(Constant.GENRES))
            responseString = intent.getStringExtra(Constant.GENRES)!!

        if (intent.hasExtra(Constant.NAME))
            presenter.getProgressOnboarding(true)
    }

    private fun injectDependency() {
        activityComponent.inject(this)
        presenter.view = this
        presenter.start()
        presenter.setUrlReset(GeneralHelper.getString(R.string.url_onboarding_reset_v3))
        presenter.setUrlProgress(GeneralHelper.getString(R.string.url_onboarding_check_progress_v3))
    }

    private fun dataList() {
        checkPointResponses.add(
            RegisCheckPointResponse(
                GeneralHelper.getString(R.string.ktp_dan_isi_data_diri),
                MenuConfig.OnboardingCheckpoint.KTP_SELFDATA
            )
        )
        checkPointResponses.add(
            RegisCheckPointResponse(
                GeneralHelper.getString(R.string.verifikasi_no_handphone_email),
                MenuConfig.OnboardingCheckpoint.VERIFY_HP_EMAIL
            )
        )
        checkPointResponses.add(
            RegisCheckPointResponse(
                GeneralHelper.getString(R.string.verifikasi_wajah),
                MenuConfig.OnboardingCheckpoint.VERIFY_WAJAH
            )
        )
        checkPointResponses.add(
            RegisCheckPointResponse(
                GeneralHelper.getString(R.string.data_ar_check),
                MenuConfig.OnboardingCheckpoint.DATA_AR
            )
        )
        checkPointResponses.add(
            RegisCheckPointResponse(
                GeneralHelper.getString(R.string.pembuatan_akun_brimo),
                MenuConfig.OnboardingCheckpoint.CREATE_BRIMO
            )
        )
        checkPointResponses.add(
            RegisCheckPointResponse(
                GeneralHelper.getString(R.string.pengesahan_akun_brimo),
                MenuConfig.OnboardingCheckpoint.VALIDATE_BRIMO
            )
        )
    }

    private fun setupView() {
        GeneralHelper.setToolbarRevamp(
            this,
            binding.toolbarRevamp.toolbar,
            GeneralHelper.getString(R.string.pembukaan_rekening)
        )

        binding.recylerCheck.layoutManager = LinearLayoutManager(this)
        binding.recylerCheck.adapter =
            OnboardingCheckPointAdapter(this, checkPointResponses, checkPoint!!)

        binding.btnLanjut.setOnClickListener { checkProgress() }
        binding.btnMulaiAwal.setOnClickListener { dialogStartAgain() }
    }

    private fun checkProgress() {
        if (checkPoint == 3 || checkPoint == 4 || checkPoint == 6 || checkPoint == 81 || checkPoint == 11)
            presenter.getProgressOnboarding(false)
        else launchToCheckPoint(checkPoint!!, responseString)
    }

    private fun launchToCheckPoint(status: Int, resString: String) {
        when (status) {
            1 -> {
                val intent = Intent(this, OnboardingCameraActivity::class.java)
                intent.putExtra(Constant.CHECK_POINT, checkPoint)
                startActivityIntent.launch(intent)
            }

            2 -> {
                val intent = Intent(this, OnboardingInputDataActivity::class.java)
                intent.putExtra(Constant.CHECK_POINT, true)
                intent.putExtra(Constant.GENRES, resString)
                startActivityIntent.launch(intent)
            }

            3 -> {
                val intent = Intent(this, OnboardingOtpActivity::class.java)
                intent.putExtra(Constant.GENRES, resString)
                startActivityIntent.launch(intent)
            }

            4 -> {
                val intent = Intent(this, OnboardingVerifyEmailActivity::class.java)
                intent.putExtra(Constant.GENRES, resString)
                startActivityIntent.launch(intent)
            }

            5 -> {
                val intent = Intent(this, OnboardingVerifyWajahActivity::class.java)
                intent.putExtra(Constant.CHECK_POINT, checkPoint)
                intent.putExtra(Constant.GENRES, resString)
                startActivityIntent.launch(intent)
            }

            51 -> {
                val infoModel = InfoModel(
                    51,
                    R.drawable.ic_foto_ktp_gagal,
                    GeneralHelper.getString(R.string.verify_gagal_ktp),
                    GeneralHelper.getString(R.string.desc_verify_gagal_ktp),
                    GeneralHelper.getString(R.string.ulangi_foto_ktp)
                )
                val intent = Intent(this, OnboardingInformationActivity::class.java)
                intent.putExtra(Constant.TAG_ADDITIONAL, infoModel)
                intent.putExtra(Constant.CHECK_POINT, checkPoint)
                intent.putExtra(Constant.GENRES, responseString)
                startActivityIntent.launch(intent)
            }

            52 -> {
                val infoModel = InfoModel(
                    52,
                    R.drawable.ic_foto_ktp_gagal,
                    GeneralHelper.getString(R.string.verify_gagal_foto_video),
                    GeneralHelper.getString(R.string.desc_verify_foto_video),
                    GeneralHelper.getString(R.string.ulangi_foto_video)
                )
                val intent = Intent(this, OnboardingInformationActivity::class.java)
                intent.putExtra(Constant.TAG_ADDITIONAL, infoModel)
                intent.putExtra(Constant.CHECK_POINT, checkPoint)
                intent.putExtra(Constant.GENRES, responseString)
                startActivityIntent.launch(intent)
            }

            53 -> {
                val infoModel = InfoModel(
                    53,
                    R.drawable.ic_rekam_wajah_gagal,
                    GeneralHelper.getString(R.string.verify_gagal_video),
                    GeneralHelper.getString(R.string.desc_verify_gagal_video),
                    GeneralHelper.getString(R.string.ulangi_rekam_wajah)
                )
                val intent = Intent(this, OnboardingInformationActivity::class.java)
                intent.putExtra(Constant.TAG_ADDITIONAL, infoModel)
                intent.putExtra(Constant.CHECK_POINT, checkPoint)
                intent.putExtra(Constant.GENRES, responseString)
                startActivityIntent.launch(intent)
            }

            6 -> {
                val intent = Intent(this, OnboardingPendingActivity::class.java)
                intent.putExtra(Constant.GENRES, resString)
                startActivityIntent.launch(intent)
            }

            7 -> {
                val infoModel = InfoModel(
                    7,
                    R.drawable.ic_regis_success,
                    GeneralHelper.getString(R.string.verify_berhasil),
                    GeneralHelper.getString(R.string.desc_verify_berhasil_onboarding),
                    GeneralHelper.getString(R.string.lanjutkan_pendaftaran)
                )
                val intent = Intent(this, OnboardingInformationActivity::class.java)
                intent.putExtra(Constant.TAG_ADDITIONAL, infoModel)
                intent.putExtra(Constant.CHECK_POINT, checkPoint)
                startActivityIntent.launch(intent)
            }

            80 -> {
                val intent = Intent(this, OnboardingDataPribadiActivity::class.java)
                intent.putExtra(Constant.GENRES, resString)
                intent.putExtra(Constant.CHECK_POINT, checkPoint)
                startActivityIntent.launch(intent)
            }

            81 -> {
                val intent = Intent(this, OnboardingDataAlamatActivity::class.java)
                intent.putExtra(Constant.GENRES, resString)
                intent.putExtra(Constant.CHECK_POINT, checkPoint)
                startActivityIntent.launch(intent)
            }

            82 -> {
                val intent = Intent(this, OnboardingDataPekerjaanActivity::class.java)
                intent.putExtra(Constant.GENRES, resString)
                intent.putExtra(Constant.CHECK_POINT, checkPoint)
                startActivityIntent.launch(intent)
            }

            83 -> {
                val intent = Intent(this, OnboardingDataKeuanganActivity::class.java)
                intent.putExtra(Constant.GENRES, resString)
                intent.putExtra(Constant.CHECK_POINT, checkPoint)
                startActivityIntent.launch(intent)
            }

            84 -> {
                val intent = Intent(this, OnboardingKonfirmasiActivity::class.java)
                intent.putExtra(Constant.GENRES, resString)
                intent.putExtra(Constant.CHECK_POINT, checkPoint)
                intent.putExtra(Constant.PERSISTENCE_ID, presenter.persistenceId)
                startActivityIntent.launch(intent)
            }

            85 -> {
                val intent = Intent(this, OnboardingSyaratKetentuanActivity::class.java)
                intent.putExtra(Constant.GENRES, resString)
                intent.putExtra(Constant.CHECK_POINT, checkPoint)
                startActivityIntent.launch(intent)
            }

            9 -> {
                val intent = Intent(this, OnboardingInformationActivity::class.java)
                val infoModel = InfoModel(
                    checkPoint!!,
                    R.drawable.ic_info_tabungan_bri,
                    GeneralHelper.getString(R.string.title_sukses_pembukaan),
                    GeneralHelper.getString(R.string.desc_sukses_pembukaan),
                    GeneralHelper.getString(R.string.lanjutkan_buat_akun_brimo)
                )
                intent.putExtra(Constant.TAG_ADDITIONAL, infoModel)
                intent.putExtra(Constant.CHECK_POINT, checkPoint)
                startActivityIntent.launch(intent)
            }

            10, 11 -> {
                val intent = Intent(this, OnboardingPinActivity::class.java)
                intent.putExtra(Constant.CHECK_POINT, status)
                startActivityIntent.launch(intent)
            }

            12,
            13 -> {
                val intent = Intent(this, OnboardingInformationActivity::class.java)
                val infoModel = InfoModel(
                    checkPoint!!,
                    R.drawable.ic_onboarding_user,
                    GeneralHelper.getString(R.string.title_proses_tabungan),
                    GeneralHelper.getString(R.string.desc_proses_tabungan),
                    GeneralHelper.getString(R.string.check_status)
                )
                intent.putExtra(Constant.TAG_ADDITIONAL, infoModel)
                intent.putExtra(Constant.CHECK_POINT_DS, checkPoint)
                startActivityIntent.launch(intent)
            }

            14 -> {
                val receiptResponse =
                    Gson().fromJson(resString, OnboardingReceiptResponse::class.java)

                val intent = Intent(this, OnboardingInformationActivity::class.java)
                val infoModel = InfoModel(
                    checkPoint!!,
                    R.drawable.ic_akun_terbentuk,
                    String.format(
                        GeneralHelper.getString(R.string.selamat_nama), receiptResponse.accountName
                    ),
                    GeneralHelper.getString(R.string.desc_sukses_onboarding),
                    GeneralHelper.getString(R.string.lanjutkan)
                )
                intent.putExtra(Constant.TAG_ADDITIONAL, infoModel)
                intent.putExtra(Constant.GENRES, resString)
                intent.putExtra(Constant.USERNAMEDS, receiptResponse.accountName)
                startActivityIntent.launch(intent)
            }
        }
    }

    private fun dialogStartAgain() {
        val dialogNotice = DialogExitCustom(
            this,
            GeneralHelper.getString(R.string.dialog_title_mulai_awal),
            GeneralHelper.getString(R.string.dialog_desc_mulai_awal),
            GeneralHelper.getString(R.string.batal2),
            GeneralHelper.getString(R.string.ulangi)
        )
        val ft = this.supportFragmentManager.beginTransaction()
        ft.add(dialogNotice, null)
        ft.commitAllowingStateLoss()
    }

    override fun onResetView(tabunganResponse: TabunganResponse) {
        val intent = Intent(this, OnboardingTabunganActivity::class.java)
        intent.putExtra(Constant.GENRES, tabunganResponse)
        startActivityIntent.launch(intent)
        finish()
    }

    override fun onCheckPointView(
        responseString: String,
        statusResponse: StatusResponse
    ) {
        launchToCheckPoint(statusResponse.status, responseString)
    }

    override fun onCheckPointBack(responseString: String, statusResponse: StatusResponse) {
        checkPoint = statusResponse.status
        this.responseString = responseString
        binding.recylerCheck.adapter =
            OnboardingCheckPointAdapter(this, checkPointResponses, checkPoint!!)
    }

    override fun onUpdateVersion(forceUpdate: ForceUpdateResponse) {
        OpenBottomSheetGeneralFragment.showDialogInformation(
            supportFragmentManager,
            forceUpdate.image,
            "ic_forced_update",
            forceUpdate.title,
            forceUpdate.description,
            { openPlaystore() },
            false,
            forceUpdate.button
        )
    }

    private fun openPlaystore() {
        try {
            startActivity(
                Intent(
                    Intent.ACTION_VIEW,
                    Uri.parse("market://details?id=${Constant.APP_PACKAGE_NAME}")
                )
            )
        } catch (e: ActivityNotFoundException) {
            startActivity(
                Intent(
                    Intent.ACTION_VIEW,
                    Uri.parse("https://play.google.com/store/apps/details?id=${Constant.APP_PACKAGE_NAME}")
                )
            )
        }
    }

    override fun onClickYes() {
        presenter.sendDataReset()
    }

    private var startActivityIntent: ActivityResultLauncher<Intent> = registerForActivityResult(
        ActivityResultContracts.StartActivityForResult()
    ) { result ->
        if (result.resultCode == Activity.RESULT_CANCELED && result.data != null) {
            if (result.data!!.hasExtra(Constant.BACK_GENERAL))
                finish()
            else {
                if (result.data!!.hasExtra(Constant.CHECK_POINT))
                    checkPoint = result.data!!.getIntExtra(Constant.CHECK_POINT, 0)

                if (result.data!!.hasExtra(Constant.GENRES))
                    responseString = result.data!!.getStringExtra(Constant.GENRES)!!

                presenter.getProgressOnboarding(true)

                binding.recylerCheck.adapter =
                    OnboardingCheckPointAdapter(this, checkPointResponses, checkPoint!!)
            }
        }
    }

    override fun onDestroy() {
        presenter.stop()
        super.onDestroy()
    }
}