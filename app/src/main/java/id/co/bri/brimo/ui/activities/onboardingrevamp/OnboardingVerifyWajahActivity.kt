package id.co.bri.brimo.ui.activities.onboardingrevamp

import android.app.Activity
import android.content.Intent
import android.os.Bundle
import androidx.activity.OnBackPressedCallback
import androidx.activity.result.ActivityResultLauncher
import androidx.activity.result.contract.ActivityResultContracts
import com.google.gson.Gson
import id.co.bri.brimo.R
import id.co.bri.brimo.contract.IPresenter.onboardingrevamp.IOnboardingVerifyWajahPresenter
import id.co.bri.brimo.contract.IView.onboardingrevamp.IOnboardingVerifyWajahView
import id.co.bri.brimo.databinding.ActivityRegistrasiVerifyWajahBinding
import id.co.bri.brimo.domain.config.AppConfig
import id.co.bri.brimo.domain.config.Constant
import id.co.bri.brimo.domain.helpers.GeneralHelper
import id.co.bri.brimo.domain.helpers.awss3.AwsHelper
import id.co.bri.brimo.models.apimodel.request.onboardingrevamp.OnboardingSendKycReq
import id.co.bri.brimo.models.apimodel.response.onboardingrevamp.OnboardingVerifyFaceRes
import id.co.bri.brimo.ui.activities.base.BaseActivity
import id.co.bri.brimo.ui.fragments.FragmentBottomDialog
import id.co.bri.brimo.util.liveness.PrivyLiveness
/**
import id.co.bri.brimo.util.liveness.VidaLiveness
import id.vida.liveness.dto.VidaLivenessResponse
 **/
import java.util.HashMap
import javax.inject.Inject

class OnboardingVerifyWajahActivity : BaseActivity(),
    IOnboardingVerifyWajahView,
    AwsHelper.OnUploadListener {

    @Inject
    lateinit var presenter: IOnboardingVerifyWajahPresenter<IOnboardingVerifyWajahView>

    private lateinit var binding: ActivityRegistrasiVerifyWajahBinding

    private lateinit var onboardingVerifyResponse: OnboardingVerifyFaceRes

    private var onboardId: String = ""

    private var isStatus52: Boolean = false

    private var checkpoint: Int = 0

    private var countUploadSuccess = 0
    private var countUploadError = 0
    private var maxUpload = 0

    private var privyLiveness: PrivyLiveness? = null

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)

        binding = ActivityRegistrasiVerifyWajahBinding.inflate(layoutInflater)
        setContentView(binding.root)

        onBackPressedDispatcher.addCallback(this, onBackPressedCallback)

        injectDependency()
        intentExtra()
        setupView()
    }

    private fun intentExtra() {
        onboardingVerifyResponse = Gson().fromJson(
            intent.getStringExtra(Constant.GENRES),
            OnboardingVerifyFaceRes::class.java
        )

        if (intent.hasExtra(Constant.IMAGEKTP))
            isStatus52 = true

        if (intent.hasExtra(Constant.CHECK_POINT))
            checkpoint = intent.getIntExtra(Constant.CHECK_POINT, 5)
    }

    private fun injectDependency() {
        activityComponent.inject(this)
        presenter.view = this
        presenter.start()
        presenter.setUrlVideo(GeneralHelper.getString(R.string.url_onboarding_send_kyc_v3))
        onboardId = presenter.getDeviceId()
    }

    private fun setupView() {
        GeneralHelper.setToolbarRevamp(
            this,
            binding.toolbarRevamp.toolbar,
            " "
        )

        binding.btnLanjut.setOnClickListener {
            setEventAppsFlyer()
            checkingLiveness()
        }
    }

    private fun checkingLiveness() {
        when (onboardingVerifyResponse.partnerId) {
            Constant.LivenessType.PRIVY.type -> {
                privyLiveness = PrivyLiveness(
                    this,
                    PrivyLiveness.IndicatorSetting.ONBOARDING,
                    object : PrivyLiveness.Callback {
                        override fun onResultPrivyLiveness(result: PrivyLiveness.LivenessResult) {
                            when (result) {
                                is PrivyLiveness.LivenessResult.Success -> {
                                    resultPrivyLiveness(
                                        result.image1,
                                        result.image2,
                                        result.videoResult
                                    )
                                }

                                is PrivyLiveness.LivenessResult.NetworkError -> {
                                    showNetworkErrorLiveness()
                                }

                                is PrivyLiveness.LivenessResult.CameraError -> {
                                    showCameraErrorLiveness(result.errorMessage)
                                }
                            }
                        }
                    })

                privyLiveness?.startLiveness()
            }
/*
            Constant.LivenessType.VIDA.type -> {
                VidaLiveness(this, object : VidaLiveness.Callback {
                    override fun onResultVidaLiveness(result: VidaLiveness.LivenessResult) {
                        when (result) {
                            is VidaLiveness.LivenessResult.Success -> {
                                resultVidaLiveness(result.response)
                            }

                            is VidaLiveness.LivenessResult.Error -> {
                                if (result.errorMessage.contains(GeneralHelper.getString(R.string.timed_out)) ||
                                    result.errorMessage.contains(GeneralHelper.getString(R.string.failed))
                                )
                                    GeneralHelper.showSnackBarRevamp(
                                        findViewById(R.id.content),
                                        result.errorMessage
                                    )
                            }
                        }
                    }
                })
            }
            */
        }
    }

    private fun setEventAppsFlyer() {
        val eventValue: MutableMap<String, Any> = HashMap()
        eventValue[Constant.CUSTOMER_ID] = presenter.persistenceId
        trackAppsFlyerAnalyticEvent("openaccount_verifikasi_wajah_start", eventValue)
    }

    /*
    private fun resultVidaLiveness(response: VidaLivenessResponse) {
        maxUpload = response.additionalImages!!.size + 1
        showProgress()
        AwsHelper(this).uploadToAwsVida(
            this,
            AwsHelper.FileName.IMAGE1,
            response.imageBytes!!,
            AppConfig.getBucketOnboarding(),
            "$onboardId/",
            AwsHelper.ContentType.IMAGE
        )

        presenter.checkAdditionalImages(response.additionalImages!!)
    }
    */


    override fun onAdditionalImages(additionalImages: ByteArray, number: Int) {
        AwsHelper(this).uploadToAwsVida(
            this,
            AwsHelper.FileName.IMAGE + "$number.png",
            additionalImages,
            AppConfig.getBucketOnboarding(),
            "$onboardId/",
            AwsHelper.ContentType.IMAGE
        )
    }

    private fun resultPrivyLiveness(image1: String, image2: String, videoResult: String) {
        maxUpload = 3
        val tempImage1 = image1.substringAfter(",")
        val tempImage2 = image2.substringAfter(",")
        showProgress()
        //upload image1
        AwsHelper(this).uploadToAwsPrivy(
            this,
            AwsHelper.FileName.IMAGE1,
            tempImage1,
            AppConfig.getBucketOnboarding(),
            "$onboardId/",
            AwsHelper.ContentType.IMAGE
        )
        //upload image2
        AwsHelper(this).uploadToAwsPrivy(
            this,
            AwsHelper.FileName.IMAGE2,
            tempImage2,
            AppConfig.getBucketOnboarding(),
            "$onboardId/",
            AwsHelper.ContentType.IMAGE
        )
        //upload video
        AwsHelper(this).uploadToAwsPrivy(
            this,
            AwsHelper.FileName.VIDEO,
            videoResult,
            AppConfig.getBucketOnboarding(),
            "$onboardId/",
            AwsHelper.ContentType.VIDEO
        )
    }

    private fun showNetworkErrorLiveness() {
        GeneralHelper.showBottomDialog(this, Constant.KONEKSI_TERPUTUS)
    }

    private fun showCameraErrorLiveness(message: String) {
        val fragmentBottomDialog = FragmentBottomDialog(
            message, "", message,
            Constant.IMAGE_SERVER_UNDER_MAINTENANCE
        )
        fragmentBottomDialog.show(supportFragmentManager, "")
    }

    override fun onSuccessVideo(stringResponse: String) {
        val intent = Intent(this, OnboardingPendingActivity::class.java)
        intent.putExtra(Constant.GENRES, stringResponse)
        startActivityIntent.launch(intent)
    }

    override fun onSuccessUpload(imgUrl: String) {
        countUploadSuccess += 1
        if (countUploadSuccess == maxUpload && countUploadError == 0) {
            if (isStatus52) {
                presenter.sendVideoOnboard(
                    OnboardingSendKycReq(
                        onboardId,
                        ktp = true,
                        video = true
                    )
                )
            } else {
                presenter.sendVideoOnboard(
                    OnboardingSendKycReq(
                        onboardId,
                        ktp = false,
                        video = true
                    )
                )
            }
            countUploadSuccess = 0
        } else if (countUploadError != 0) {
            hideProgress()
            showDialogErrorUpload(
                GeneralHelper.getString(R.string.gagal_upload_foto),
                GeneralHelper.getString(R.string.desc_gagal_upload_foto)
            )
        }
    }

    override fun onErrorUpload(errorMsg: String) {
        countUploadError += 1
        if (countUploadError != 0) {
            hideProgress()
            showDialogErrorUpload(
                GeneralHelper.getString(R.string.gagal_upload_foto),
                GeneralHelper.getString(R.string.desc_gagal_upload_foto)
            )
            return
        }
    }

    override fun onExceptionRevamp(message: String?) {
        GeneralHelper.showBottomDialog(this, message)
    }

    private fun showDialogErrorUpload(
        title: String,
        desc: String,
    ) {
        countUploadSuccess = 0
        countUploadError = 0
        val fragmentBottomDialog =
            FragmentBottomDialog(
                this, Constant.SERVER_UNDER_MAINTENANCE, title, desc,
                "ic_server_maintenance", true
            )
        fragmentBottomDialog.show(supportFragmentManager, "")
    }

    private var startActivityIntent: ActivityResultLauncher<Intent> = registerForActivityResult(
        ActivityResultContracts.StartActivityForResult()
    ) { result ->
        if (result.resultCode == Activity.RESULT_CANCELED && result.data != null) {
            setResult(RESULT_CANCELED, result.data)
            finish()
        }
    }

    private val onBackPressedCallback: OnBackPressedCallback =
        object : OnBackPressedCallback(true) {
            override fun handleOnBackPressed() {
                val intent = Intent()
                intent.putExtra(Constant.CHECK_POINT, checkpoint)
                intent.putExtra(Constant.GENRES, Gson().toJson(onboardingVerifyResponse))
                setResult(Activity.RESULT_CANCELED, intent)
                finish()
            }
        }

    override fun onDestroy() {
        presenter.stop()
        privyLiveness?.stopLiveness()
        super.onDestroy()
    }
}