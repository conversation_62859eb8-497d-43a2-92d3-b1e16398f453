package id.co.bri.brimo.models.apimodel.request;

import com.google.gson.annotations.Expose;
import com.google.gson.annotations.SerializedName;

public class RegisKontakRequest {
    @SerializedName("verification_id")
    @Expose
    private String verificationId;
    @SerializedName("phone_number")
    @Expose
    private String phoneNumber;
    @SerializedName("email")
    @Expose
    private String email;

    public RegisKontakRequest(String verificationId, String phoneNumber, String email) {
        this.verificationId = verificationId;
        this.phoneNumber = phoneNumber;
        this.email = email;
    }


    public String getVerificationId() {
        return verificationId;
    }

    public void setVerificationId(String verificationId) {
        this.verificationId = verificationId;
    }

    public String getPhoneNumber() {
        return phoneNumber;
    }

    public void setPhoneNumber(String phoneNumber) {
        this.phoneNumber = phoneNumber;
    }

    public String getEmail() {
        return email;
    }

    public void setEmail(String email) {
        this.email = email;
    }

}
