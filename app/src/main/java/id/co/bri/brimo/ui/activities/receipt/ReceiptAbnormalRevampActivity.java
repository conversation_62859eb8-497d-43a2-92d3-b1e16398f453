package id.co.bri.brimo.ui.activities.receipt;

import android.app.Activity;
import android.content.Intent;
import android.content.IntentFilter;
import android.os.Bundle;
import android.util.Log;
import android.view.View;

import androidx.annotation.NonNull;
import androidx.core.app.ActivityCompat;
import androidx.localbroadcastmanager.content.LocalBroadcastManager;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.google.gson.Gson;

import java.util.ArrayList;
import java.util.List;

import javax.inject.Inject;

import id.co.bri.brimo.R;
import id.co.bri.brimo.adapters.DataTransaksiRevampAdapter;
import id.co.bri.brimo.adapters.receipt.ReceiptAccountDataAdapter;
import id.co.bri.brimo.adapters.receipt.ReceiptDataTotalAdapter;
import id.co.bri.brimo.adapters.receipt.ReceiptVoucherDataAdapter;
import id.co.bri.brimo.contract.IPresenter.receipt.IReceiptAbnormalRevampPresenter;
import id.co.bri.brimo.contract.IView.receipt.IReceiptAbnormalRevampView;
import id.co.bri.brimo.databinding.ActivityReceiptAbnormalRevampBinding;
import id.co.bri.brimo.domain.config.Constant;
import id.co.bri.brimo.domain.helpers.GeneralHelper;
import id.co.bri.brimo.domain.helpers.image.ImageHelper;
import id.co.bri.brimo.models.NotifikasiModel;
import id.co.bri.brimo.models.apimodel.response.DetailListType;
import id.co.bri.brimo.models.apimodel.response.ReceiptRevampInboxResponse;
import id.co.bri.brimo.models.apimodel.response.ReceiptRevampResponse;
import id.co.bri.brimo.models.apimodel.response.ValidateResponse;
import id.co.bri.brimo.ui.activities.base.BaseActivity;
import id.co.bri.brimo.ui.activities.ssc.SelfServiceActivity;
import id.co.bri.brimo.ui.activities.voucher.HistoryVoucherActivity;


public class ReceiptAbnormalRevampActivity extends BaseActivity implements
        ActivityCompat.OnRequestPermissionsResultCallback,
        ReceiptVoucherDataAdapter.onClick,
        IReceiptAbnormalRevampView,
        View.OnClickListener {


    private static final String TAG = "ReceiptAbnormalRevampActivity";
    //untuk save instance state
    private static final String TAG_PENDING = "pending_data";
    private static final String TAG_VALIDATE = "validate_data";
    private static Boolean mIsFromKonfirmasi = false;
    private static Boolean mIsFromTrackingEmas = false;

    private ActivityReceiptAbnormalRevampBinding binding;
    ImageHelper imageHelper;

    private ReceiptRevampResponse mPendingrespon;
    private ValidateResponse mvalidateResponse;
    private static String mVoucherType;

    private static int defaultIcon;

    @Inject
    IReceiptAbnormalRevampPresenter<IReceiptAbnormalRevampView> receiptPresenter;

    public static void launchIntent(Activity caller, ReceiptRevampResponse receiptRevampResponse, boolean isFromFast, int icon) {
        isFromFastMenu = isFromFast;
        defaultIcon = icon;
        Intent intent = new Intent(caller, ReceiptAbnormalRevampActivity.class);
        intent.putExtra(TAG_PENDING, new Gson().toJson(receiptRevampResponse));
        caller.startActivityForResult(intent, Constant.REQ_PAYMENT);
        caller.setResult(RESULT_OK, setResultReceipt(receiptRevampResponse));
        caller.finish();
    }

    public static void launchIntent(Activity caller, ReceiptRevampResponse receiptRevampResponse, boolean isFromFast, String voucherType, int icon) {
        isFromFastMenu = isFromFast;
        mVoucherType = voucherType;
        defaultIcon = icon;
        Intent intent = new Intent(caller, ReceiptAbnormalRevampActivity.class);
        intent.putExtra(TAG_PENDING, new Gson().toJson(receiptRevampResponse));
        caller.startActivityForResult(intent, Constant.REQ_PAYMENT);
        caller.setResult(RESULT_OK, setResultReceipt(receiptRevampResponse));
        caller.finish();
    }

    public static void launchIntentReceipt(Activity caller, ReceiptRevampResponse receiptRevampResponse, boolean isFromFast, int icon) {
        isFromFastMenu = isFromFast;
        defaultIcon = icon;
        Intent intent = new Intent(caller, ReceiptAbnormalRevampActivity.class);
        intent.putExtra(TAG_PENDING, new Gson().toJson(receiptRevampResponse));
        caller.startActivityForResult(intent, Constant.REQ_PAYMENT);
        caller.setResult(RESULT_OK, setResultReceipt(receiptRevampResponse));
    }

    public static void launchIntentInvestasi(Activity caller, ReceiptRevampResponse receiptRevampResponse, boolean isFromFast, boolean isFromKonfrimasiCetakEmas, boolean isFromTracking) {
        isFromFastMenu = isFromFast;
        mIsFromKonfirmasi = isFromKonfrimasiCetakEmas;
        mIsFromTrackingEmas = isFromTracking;
        Intent intent = new Intent(caller, ReceiptAbnormalRevampActivity.class);
        intent.putExtra(TAG_PENDING, new Gson().toJson(receiptRevampResponse));
        caller.startActivityForResult(intent, Constant.REQ_PAYMENT);
        caller.setResult(RESULT_OK, setResultReceipt(receiptRevampResponse));
    }


    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        overridePendingTransition(R.anim.bottom_up, R.anim.nothing);
        binding = ActivityReceiptAbnormalRevampBinding.inflate(getLayoutInflater());
        setContentView(binding.getRoot());

        imageHelper = new ImageHelper(this);

        GeneralHelper.setToolbarRevamp(this, binding.toolbar, "");
        //parsing data intent
        if (getIntent().getExtras() != null) {
            parseDataIntent(getIntent().getExtras());
        }

        LocalBroadcastManager.getInstance(this).registerReceiver(updates_receiver, new IntentFilter(Constant.TAG_NOTIF));


        //inject presenter
        injectDependency();

        parseDataReceipt();

        binding.btnClose.setOnClickListener(this);
        binding.llPusatBantuan.setOnClickListener(this);
    }

    @Override
    protected void onPause() {
        super.onPause();
        LocalBroadcastManager.getInstance(this).unregisterReceiver(updates_receiver);
    }

    @Override
    protected void parseDataNotifForeground(Intent intent) {
        if (intent != null) {

            try {
                // Get data from notifikasi
                Bundle extras = intent.getExtras();

                if (extras != null) {
                    try {
                        String notifikasiString = extras.getString(Constant.TAG_NOTIF);
                        Gson gson = new Gson();
                        NotifikasiModel notifikasiModel = gson.fromJson(notifikasiString, NotifikasiModel.class);
                        if (notifikasiModel != null) {
                            if (notifikasiModel.getType().equalsIgnoreCase(Constant.TRANSFER_BLAST_TYPE)) {
                                String refNumber = notifikasiModel.getRequestRefnum();
                                receiptPresenter.getInboxDetail(refNumber);
                            }

                        }
                    } catch (Exception e) {
                        //do nothing
                    }

                }
            } catch (Exception e) {
                //do nothing
            }
        }
    }


    protected void parseDataReceipt() {
        if (mPendingrespon != null) {
            /*total_data_view*/
            totalDataView();
            /*additional_info*/
            additionalInfo();
            /*header_data_view*/
            headerTransaksi();
            /*source_account_data_view*/
            sourceAccountDataView();
            /*billing_detail*/
            billingDetailView();
            /*voucher_data_view*/
            setVoucherDataView();
            /*transaction_data_view*/
            /*detail_data_view*/
            detailDataView();
            /*footer*/
            /*title*/
            setTitleView();
            /*share*/
            setShareAndButton();
        }
    }

    private void totalDataView() {
        if (mPendingrespon.getTotalDataView() != null) {
            binding.rvTotalDataView.setHasFixedSize(true);
            binding.rvTotalDataView.setLayoutManager(new LinearLayoutManager(getApplicationContext(), RecyclerView.VERTICAL, false));
            ReceiptDataTotalAdapter DataTransaksiRevampAdapter = new ReceiptDataTotalAdapter(mPendingrespon.getTotalDataView(), ReceiptAbnormalRevampActivity.this);
            binding.rvTotalDataView.setAdapter(DataTransaksiRevampAdapter);
        }
    }

    private void additionalInfo() {
        if (mPendingrespon.getAdditionalInfo() != null && !mPendingrespon.getAdditionalInfo().isEmpty()) {
            binding.cvAdditionalInfo.setVisibility(View.VISIBLE);
            binding.tvAdditionalInfo.setText(mPendingrespon.getAdditionalInfo());
        }
    }

    private void headerTransaksi() {
        if (mPendingrespon.getHeaderDataView() != null) {
            binding.rvHeaderDataView.setHasFixedSize(true);
            binding.rvHeaderDataView.setLayoutManager(new LinearLayoutManager(getApplicationContext(), RecyclerView.VERTICAL, false));
            DataTransaksiRevampAdapter DataTransaksiRevampAdapter = new DataTransaksiRevampAdapter(mPendingrespon.getHeaderDataView(), ReceiptAbnormalRevampActivity.this);
            binding.rvHeaderDataView.setAdapter(DataTransaksiRevampAdapter);
        }
    }

    private void sourceAccountDataView() {
        if (mPendingrespon.getSourceAccountDataView() != null) {
            binding.rvItemReceiptSourceAccountData.setHasFixedSize(true);
            binding.rvItemReceiptSourceAccountData.setLayoutManager(new LinearLayoutManager(getApplicationContext(), RecyclerView.VERTICAL, false));
            List<DetailListType> detailListTypes = new ArrayList<>();
            if (mPendingrespon.getSourceAccountDataView() != null) {
                detailListTypes.add(mPendingrespon.getSourceAccountDataView());
            }
            ReceiptAccountDataAdapter receiptAccountDataAdapter = new ReceiptAccountDataAdapter(detailListTypes, ReceiptAbnormalRevampActivity.this, true, defaultIcon);
            binding.rvItemReceiptSourceAccountData.setAdapter(receiptAccountDataAdapter);
        }
    }

    private void billingDetailView() {
        if (mPendingrespon.getBillingDetail() != null) {
            binding.rvItemReceiptBillingDetail.setHasFixedSize(true);
            binding.rvItemReceiptBillingDetail.setLayoutManager(new LinearLayoutManager(getApplicationContext(), RecyclerView.VERTICAL, false));
            List<DetailListType> detailListTypesBilling = new ArrayList<>();
            if (mPendingrespon.getBillingDetail() != null) {
                detailListTypesBilling.add(mPendingrespon.getBillingDetail());
            }
            ReceiptAccountDataAdapter receiptAccountDataAdapter = new ReceiptAccountDataAdapter(detailListTypesBilling, ReceiptAbnormalRevampActivity.this, false, defaultIcon);
            binding.rvItemReceiptBillingDetail.setAdapter(receiptAccountDataAdapter);
        }
    }

    private void setVoucherDataView() {
        if (mPendingrespon.getVoucherDataView() != null && mPendingrespon.getVoucherDataView().size() > 0) {
            binding.rvVoucherDataView.setVisibility(View.VISIBLE);
            binding.rvVoucherDataView.setHasFixedSize(true);
            binding.rvVoucherDataView.setLayoutManager(new LinearLayoutManager(getApplicationContext(), RecyclerView.VERTICAL, false));
            ReceiptVoucherDataAdapter receiptVoucherDataAdapter = new ReceiptVoucherDataAdapter(mPendingrespon.getVoucherDataView(), ReceiptAbnormalRevampActivity.this, this);
            binding.rvVoucherDataView.setAdapter(receiptVoucherDataAdapter);
        }
    }

    private void detailDataView() {
        if (mPendingrespon.getDetailDataView() != null) {

        }
    }

    private void setTitleView() {
        if (mPendingrespon.getTitle() != null) {
            binding.tvTitle.setText(mPendingrespon.getTitle());
        }

        if (mPendingrespon.getTitleImage() != null) {
            String titleImage;
            if (mPendingrespon.getTitleImage().equalsIgnoreCase(Constant.RECEIPT58_REVAMP) || mPendingrespon.getTitleImage().equalsIgnoreCase(Constant.RECEIPT68_REVAMP)) {
                titleImage = Constant.RECEIPT68_REVAMP;
                int imageId = this.getResources().getIdentifier(titleImage, Constant.DEFTYPEIDENTIFYER, this.getPackageName());
                binding.imgCeklist.setImageResource(imageId);
                binding.imgCeklist2.setImageResource((R.drawable.receipt_68_revamp_gif));
            } else {
                binding.imgCeklist2.setImageResource((R.drawable.checklist));
            }
        }
        String subs = (mPendingrespon.getSubtitle() != null) ? mPendingrespon.getSubtitle() : GeneralHelper.getString(R.string.subtitle_diproses);
        binding.tvSubtitle.setText(subs);
    }

    private void setShareAndButton() {
        String btnCloseStr = (mPendingrespon.getCloseButtonString() != null) ? mPendingrespon.getCloseButtonString() : "";
        binding.btnClose.setText(btnCloseStr);

        if (mPendingrespon.isHelpFlag() || mPendingrespon.isHelpCenter()) {
            binding.llPusatBantuan.setVisibility(View.VISIBLE);
        } else {
            binding.llPusatBantuan.setVisibility(View.GONE);
        }

        if (isFromFastMenu) {
            binding.btnClose.setText(GeneralHelper.getString(R.string.ok));
        }
    }


    protected void injectDependency() {
        getActivityComponent().inject(this);

        if (receiptPresenter != null) {
            receiptPresenter.setView(this);
            receiptPresenter.start();
            receiptPresenter.setUrlDetailInbox(GeneralHelper.getString(R.string.url_activity_detail));
            receiptPresenter.initBackroundWatermark();
        }
    }

    /**
     * Method digunakan untuk meng-extract data Intent
     *
     * @param extras Bundle savedInstanceState
     */
    protected void parseDataIntent(Bundle extras) {
        if (extras != null) {
            String pendingTemp = extras.getString(TAG_PENDING);
            if (pendingTemp != null) {
                mPendingrespon = new Gson().fromJson(pendingTemp, ReceiptRevampResponse.class);
            }

            String validationTemp = extras.getString(TAG_VALIDATE);
            if (pendingTemp != null) {
                mvalidateResponse = new Gson().fromJson(validationTemp, ValidateResponse.class);
            }
        }
    }

    /**
     * Get return Intent untuk Dashboard activity
     *
     * @return intent
     */
    public static Intent setResultReceipt(ReceiptRevampResponse response) {
        Intent intentReturn = new Intent();

        try {
            if (response.getTitleImage() != null) {
                if (response.getTitleImage().contains("00")) {
                    intentReturn.putExtra(Constant.REQUEST_RECEIPT, true);
                    return intentReturn;
                } else {
                    intentReturn.putExtra(Constant.REQUEST_RECEIPT, false);
                    return intentReturn;
                }
            } else {
                intentReturn.putExtra(Constant.REQUEST_RECEIPT, true);
                return intentReturn;
            }
        } catch (Exception e) {
            intentReturn.putExtra(Constant.REQUEST_RECEIPT, true);
            return intentReturn;
        }
    }


    @Override
    public void onClick(View view) {
        int id = view.getId();
        switch (id) {
            case R.id.btn_close:
                if ((mPendingrespon.getVoucherGameId() != null && !mPendingrespon.getVoucherGameId().isEmpty())) {
                    if (mVoucherType == null || mVoucherType.isEmpty()) {
                        mVoucherType = Constant.Voucher.GAME.name();
                    }
                    HistoryVoucherActivity.launchIntent(this, true, mVoucherType);
                } else if ((mPendingrespon.getStreamingId() != null && !mPendingrespon.getStreamingId().isEmpty())) {
                    if (mVoucherType == null || mVoucherType.isEmpty()) {
                        mVoucherType = Constant.Voucher.STREAMING.name();
                    }
                    HistoryVoucherActivity.launchIntent(this, true, mVoucherType);
                } else {
                    onBackPressed();
                }
                break;
            case R.id.ll_pusat_bantuan:
                SelfServiceActivity.launchIntent(this);
                break;
            default:
                break;
        }
    }

    /**
     * method override animasi ketika activity close
     */
    @Override
    public void onBackPressed() {
        super.onBackPressed();
        overridePendingTransition(R.anim.nothing, R.anim.bottom_down);
    }

    @Override
    public void onResume() {
        super.onResume();

    }


    /**
     * Save data response Payment
     *
     * @param outState
     */
    @Override
    protected void onSaveInstanceState(@NonNull Bundle outState) {
        super.onSaveInstanceState(outState);

        if (mPendingrespon != null) {
            try {
                outState.putString(TAG_PENDING, new Gson().toJson(mPendingrespon));
            } catch (Exception e) {
                if (!GeneralHelper.isProd())
                    Log.e(TAG, "onSaveInstanceState: ", e);
            }
        }

        if (mvalidateResponse != null) {
            try {
                outState.putString(TAG_VALIDATE, new Gson().toJson(mvalidateResponse));
            } catch (Exception e) {
                if (!GeneralHelper.isProd())
                    Log.e(TAG, "onSaveInstanceState: ", e);
            }
        }
    }

    /**
     * Extract data response Payment for view response
     *
     * @param savedInstanceState
     */
    @Override
    protected void onRestoreInstanceState(@NonNull Bundle savedInstanceState) {
        super.onRestoreInstanceState(savedInstanceState);

        String pendingTemp = savedInstanceState.getString(TAG_PENDING);
        if (pendingTemp != null) {
            mPendingrespon = new Gson().fromJson(pendingTemp, ReceiptRevampResponse.class);
        }

        String validationTemp = savedInstanceState.getString(TAG_VALIDATE);
        if (pendingTemp != null) {
            mvalidateResponse = new Gson().fromJson(validationTemp, ValidateResponse.class);
        }
    }

    @Override
    public void onResultLanguage(boolean isEnglish) {
        if (isEnglish) binding.ivBgWatermark.setImageResource(R.drawable.ic_watermark_receipt_pending_english);
    }

    @Override
    protected void onDestroy() {
        if (receiptPresenter != null) {
            receiptPresenter.stop();
        }
        super.onDestroy();
    }

    /**
     * Callback ketika click copy dari Adapter ReceiptVoucherDataAdapter utk menampilkan Snackbar
     *
     * @param dataValue copyed data
     */

    @Override
    public void onCopyClick(String label, String dataValue) {
        showSnackbarErrorMessage(label + GeneralHelper.getString(R.string.text_berhasil_disalin), ALERT_CONFIRM, this, false);
    }

    @Override
    public void onSuccessGetInboxDetail(ReceiptRevampInboxResponse receiptRevampInboxResponse) {
        ReceiptRevampActivity.launchIntent(this, receiptRevampInboxResponse.getReceiptRevampResponse(), defaultIcon);
    }
}