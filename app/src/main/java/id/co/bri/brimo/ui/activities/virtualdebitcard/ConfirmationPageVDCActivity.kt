package id.co.bri.brimo.ui.activities.virtualdebitcard

import android.app.Activity
import android.content.Intent
import android.os.Build
import android.os.Bundle
import android.text.Spannable
import android.text.SpannableString
import android.text.style.ForegroundColorSpan
import android.view.View
import android.view.WindowManager
import androidx.activity.result.ActivityResultLauncher
import androidx.activity.result.contract.ActivityResultContracts
import androidx.recyclerview.widget.LinearLayoutManager
import com.google.gson.Gson
import id.co.bri.brimo.R
import id.co.bri.brimo.adapters.virtualdebitcard.DetailDataVDCVerticalAdapter
import id.co.bri.brimo.contract.IPresenter.virtualdebitcard.IConfirmationCreateVDCPresenter
import id.co.bri.brimo.contract.IView.virtualdebitcard.IConfirmationCreateVDCView
import id.co.bri.brimo.databinding.ActivityConfirmationPageVirtualCardBinding
import id.co.bri.brimo.domain.config.Constant
import id.co.bri.brimo.domain.helpers.GeneralHelper
import id.co.bri.brimo.models.AccountModel
import id.co.bri.brimo.models.apimodel.request.virtualdebitcard.CreateVDCRequest
import id.co.bri.brimo.models.apimodel.response.virtualdebitcard.ConfirmationCreateVDCResponse
import id.co.bri.brimo.models.apimodel.response.virtualdebitcard.CreateVDCResponse
import id.co.bri.brimo.models.apimodel.response.virtualdebitcard.DetailData
import id.co.bri.brimo.models.apimodel.response.virtualdebitcard.VirtualCard
import id.co.bri.brimo.ui.activities.GeneralSyaratActivity
import id.co.bri.brimo.ui.activities.LupaPinActivity
import id.co.bri.brimo.ui.activities.base.BaseActivity
import id.co.bri.brimo.ui.fragments.PinFragment
import id.co.bri.brimo.ui.fragments.PinFragment.SendPin
import javax.inject.Inject

class ConfirmationPageVDCActivity : BaseActivity(), IConfirmationCreateVDCView, SendPin {

    @Inject
    lateinit var presenter: IConfirmationCreateVDCPresenter<IConfirmationCreateVDCView>

    private lateinit var binding: ActivityConfirmationPageVirtualCardBinding

    private var isChecklist: Boolean = false
    private val gson = Gson()
    private lateinit var responseConfirmation: ConfirmationCreateVDCResponse
    private lateinit var account: AccountModel
    private lateinit var virtualCard: VirtualCard
    private lateinit var detailData: List<DetailData>
    private val detailDataVDCAdapter = DetailDataVDCVerticalAdapter()

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)

        binding = ActivityConfirmationPageVirtualCardBinding.inflate(layoutInflater)
        setContentView(binding.root)

        responseConfirmation =
            gson.fromJson(
                intent.getStringExtra(Constant.TAG_CONTENT),
                ConfirmationCreateVDCResponse::class.java
            )

        account = responseConfirmation.accounts
        virtualCard = responseConfirmation.virtualCardData
        detailData = responseConfirmation.detailDataView

        injectDependency()
        setupView()
    }

    private fun injectDependency() {
        activityComponent.inject(this)
        presenter.view = this
        presenter.start()
    }

    private fun setupView() {
        //set blue bar
        if (Build.VERSION.SDK_INT >= 21) {
            val window = window
            window.addFlags(WindowManager.LayoutParams.FLAG_DRAWS_SYSTEM_BAR_BACKGROUNDS)
            window.clearFlags(WindowManager.LayoutParams.FLAG_TRANSLUCENT_STATUS)
            window.statusBarColor = resources.getColor(R.color.primary_blue80)
        }

        GeneralHelper.setToolbarRevamp(
            this,
            binding.toolbar.toolbar, getString(R.string.submission_confirmation)
        )

        setupAccount()
        setupCard()
        setupData()
        setupSpannableTnC()

        binding.llTnc.setOnClickListener {
            handleGoToTnC()
        }

        binding.btnSubmit.setOnClickListener {
            val pinFragment = PinFragment(this, this)
            pinFragment.show()
        }
    }

    private fun handleGoToTnC() {
        GeneralSyaratActivity.launchIntentNoArrow(this, responseConfirmation.tnc, false)
    }

    private fun setupSpannableTnC() {
        val fullText = getString(R.string.virtual_card_tnc)
        val blueText = getString(R.string.tnc)

        val spannable = SpannableString(fullText)

        // Find the starting index of the blue text
        val startIndex = fullText.indexOf(blueText)

        if (startIndex != -1) {
            // Apply blue color to the blue text
            spannable.setSpan(
                ForegroundColorSpan(GeneralHelper.getColor(R.color.colorTextBlueBri)),
                startIndex,
                startIndex + blueText.length,
                Spannable.SPAN_EXCLUSIVE_EXCLUSIVE
            )
        }

        binding.tvTnc.text = spannable
    }

    private fun setupAccount() {
        binding.sofView.tvSaldoRek.visibility = View.GONE
        if (account.isDefault == 1) binding.sofView.lyRekUtama.visibility = View.VISIBLE else binding.sofView.lyRekUtama.visibility = View.GONE
        binding.sofView.tvNoRek.text = responseConfirmation.accounts.acoountString
        binding.sofView.tvAliasRek.text = account.alias.takeIf { it.isNotEmpty() } ?: account.name
        if (account.imageProductType.isNullOrEmpty()) {
            binding.sofView.ivIconRek.setImageResource(R.drawable.bri)
        } else {
            GeneralHelper.loadImageUrl(
                this,
                account.imageProductType,
                binding.sofView.ivIconRek,
                R.drawable.bri,
                0
            )
        }
    }

    private fun setupCard() {
        GeneralHelper.loadImageUrl(
            this,
            virtualCard.imageCardVertical,
            binding.dataView.ivCard,
            R.drawable.bg_vdc_vertical,
            0
        )
    }

    private fun setupData() {
        binding.dataView.rvData.apply {
            layoutManager =
                LinearLayoutManager(context, LinearLayoutManager.VERTICAL, false)
            adapter = detailDataVDCAdapter
        }
        detailDataVDCAdapter.detailData = detailData.toMutableList()
    }

    private var startActivityIntent: ActivityResultLauncher<Intent> =
        registerForActivityResult(ActivityResultContracts.StartActivityForResult()) { result ->
            if (result.resultCode == Activity.RESULT_OK) {
                // There are no request codes
                // val data: Intent? = result.data
            } else {
                if (result.data != null) {
                    setResult(RESULT_CANCELED, result.data)
                    finish()
                }
            }
        }

    override fun onActivityResult(requestCode: Int, resultCode: Int, data: Intent?) {
        super.onActivityResult(requestCode, resultCode, data)

        if (requestCode == Constant.REQ_PETTUNJUK1 && data != null && resultCode == RESULT_OK) {
            isChecklist = data.getStringExtra("checkbox").toBoolean()
            val checklistImageResource =
                if (isChecklist) R.drawable.ic_checklist_blue else R.drawable.ic_checklist_grey
            binding.ivChecklist.setImageResource(checklistImageResource)

            if (isChecklist) {
                enableButton()
            } else {
                disableButton()
            }
        }

    }

    override fun onDestroy() {
        super.onDestroy()
        presenter.stop()
    }

    override fun onSendPinComplete(pin: String) {
        createVDC(pin)
    }

    override fun onLupaPin() {
        LupaPinActivity.launchIntent(this)
    }

    private fun createVDC(pin: String) {
        var request = CreateVDCRequest(
            pin = pin,
            accountNumber = account.acoount,
            idCardType = virtualCard.cardTypeId
        )
        presenter.setUrlCreateVDC(getString(R.string.url_v1_submit_create_vdc))
        presenter.createVDC(request)
    }

    fun enableButton() {
        binding.btnSubmit.apply {
            isEnabled = true
            alpha = 1f
        }
    }

    fun disableButton() {
        binding.btnSubmit.apply {
            isEnabled = false
            alpha = 0.3f
        }
    }

    override fun onSuccessCreateVDC(response: CreateVDCResponse) {
        val responseJson = gson.toJson(response)
        val intent = Intent(this, ProcessPageVDCActivity::class.java)
        intent.putExtra(Constant.TAG_CONTENT, responseJson)
        startActivityIntent.launch(intent)
    }
}