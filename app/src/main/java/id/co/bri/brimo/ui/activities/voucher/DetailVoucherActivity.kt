package id.co.bri.brimo.ui.activities.voucher

import android.annotation.SuppressLint
import android.app.Activity
import android.content.Intent
import android.os.Build
import android.os.Bundle
import android.os.SystemClock
import android.text.TextUtils
import android.view.View
import androidx.recyclerview.widget.GridLayoutManager
import androidx.recyclerview.widget.LinearLayoutManager
import com.bumptech.glide.Glide
import com.ethanhua.skeleton.Skeleton
import com.ethanhua.skeleton.SkeletonScreen
import com.google.gson.Gson
import id.co.bri.brimo.R
import id.co.bri.brimo.adapters.option.OptionVoucherGameAdapter
import id.co.bri.brimo.adapters.voucher.SnkVoucherAdapter
import id.co.bri.brimo.contract.IPresenter.voucher.IDetailVoucherPresenter
import id.co.bri.brimo.contract.IView.voucher.IDetailVoucherView
import id.co.bri.brimo.databinding.ActivityDetailVoucherBinding
import id.co.bri.brimo.domain.config.Constant
import id.co.bri.brimo.domain.helpers.GeneralHelper
import id.co.bri.brimo.domain.helpers.SizeHelper
import id.co.bri.brimo.models.apimodel.request.voucher.VoucherRequest
import id.co.bri.brimo.models.apimodel.response.GeneralConfirmationResponse
import id.co.bri.brimo.models.apimodel.response.voucher.TutorialVoucherResponse
import id.co.bri.brimo.models.apimodel.response.voucher.VoucherDetail
import id.co.bri.brimo.models.apimodel.response.voucher.VoucherDetail.ProductList
import id.co.bri.brimo.models.apimodel.response.voucher.VoucherGameResponse
import id.co.bri.brimo.ui.activities.base.BaseActivity
import id.co.bri.brimo.ui.fragments.voucher.DetailVoucherFragment
import id.co.bri.brimo.ui.fragments.voucher.InformasiTambahanFragment
import java.util.function.Consumer
import javax.inject.Inject

class DetailVoucherActivity : BaseActivity(), IDetailVoucherView,
    OptionVoucherGameAdapter.OnAddButtonListener, SnkVoucherAdapter.OnClickItem,
    View.OnClickListener, DetailVoucherFragment.onClickItem {

    private lateinit var binding: ActivityDetailVoucherBinding

    private var gameModel: VoucherGameResponse.Game? = null

    private var mResponse: TutorialVoucherResponse? = null

    private var dataVoucherList: MutableList<VoucherDetail.ProductList> = ArrayList()

    private var snkList: MutableList<VoucherDetail.TermCondition> = ArrayList()

    private var mTermConditionList: MutableList<VoucherDetail.TermCondition> = ArrayList()

    private var mGameId = ""
    private var mProductCode = ""

    private var showDetailPay: Boolean = false

    private var skeletonScreenOption: SkeletonScreen? = null

    private var skeletonDesc: SkeletonScreen? = null

    private lateinit var optionVoucherAdapter: OptionVoucherGameAdapter

    private lateinit var snkVoucherAdapter: SnkVoucherAdapter

    var errorMessage: String? = null
    private var showDetail: Boolean = false
    private var mIsDetailShow: Boolean = false

    private var detailVoucherFragment = DetailVoucherFragment()

    @Inject
    lateinit var presenter: IDetailVoucherPresenter<IDetailVoucherView>

    companion object {

        private var mVoucherId: Int = 0
        private const val TAG_GAME_RESPONSE = "id_game_response"
        private lateinit var mVoucherType: String

        @JvmStatic
        fun launchIntent(
            caller: Activity, voucherDetail: VoucherGameResponse.Game, voucherType: String
        ) {
            val intent = Intent(caller, DetailVoucherActivity::class.java)
            mVoucherId = voucherDetail.id
            mVoucherType = voucherType
            intent.putExtra(TAG_GAME_RESPONSE, Gson().toJson(voucherDetail))

            caller.startActivityForResult(intent, Constant.REQ_PAYMENT)
        }

    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        binding = ActivityDetailVoucherBinding.inflate(layoutInflater)
        setContentView(binding.root)

        handleDataIntent()
        injectDependency()
        setupView()

    }

    private fun injectDependency() {
        activityComponent.inject(this)
        if (this::presenter.isInitialized) {
            presenter.view = this
            presenter.start()
            if (mVoucherType.equals(Constant.Voucher.GAME.name)) {
                presenter.setUrlDetailVoucher(GeneralHelper.getString(R.string.url_detail_voucher_game))
                presenter.setUrlInquiry(GeneralHelper.getString(R.string.url_confirmation_voucher_game))
                presenter.setUrlPayment(GeneralHelper.getString(R.string.url_purchase_voucher_game))
                presenter.setUrlCaraRedeem(GeneralHelper.getString(R.string.url_cara_redeem_voucher_game))
            } else if (mVoucherType.equals(Constant.Voucher.STREAMING.name)) {
                presenter.setUrlDetailVoucher(GeneralHelper.getString(R.string.url_detail_voucher_streaming))
                presenter.setUrlInquiry(GeneralHelper.getString(R.string.url_confirmation_voucher_streaming))
                presenter.setUrlPayment(GeneralHelper.getString(R.string.url_purchase_voucher_streaming))
                presenter.setUrlCaraRedeem(GeneralHelper.getString(R.string.url_cara_redeem_voucher_streaming))
            }
            generateGameId()?.let { presenter.getDetailVoucher(it) }
        }
    }

    fun handleDataIntent() {
        if (intent.getStringExtra(TAG_GAME_RESPONSE) != null) {
            gameModel = Gson().fromJson(
                intent.getStringExtra(TAG_GAME_RESPONSE), VoucherGameResponse.Game::class.java
            )
        }
    }

    @SuppressLint("NotifyDataSetChanged")
    fun setupView() {
        mGameId = gameModel!!.getId().toString()

        if (mVoucherType.equals(Constant.Voucher.GAME.name)) {
            binding.llInfoTambahan.visibility = View.GONE
            binding.ivHistory.visibility = View.VISIBLE
        } else if (mVoucherType.equals(Constant.Voucher.STREAMING.name)) {
            binding.llInfoTambahan.visibility = View.GONE
            binding.ivHistory.visibility = View.GONE
        }

        GeneralHelper.setToolbarRevamp(
            this, binding.toolbar, GeneralHelper.getString(R.string.pilih_pembelian)
        )

        //to set and resize image from backend
        Glide.with(this).load(gameModel!!.getImage_path()).override(340, 230)
            .error(R.drawable.load_fail_voucher_bg).into(binding.imgGame)

        binding.tvTitleVoucher.text = gameModel!!.getName()
        binding.lyTotalDetail.btnPay.text = GeneralHelper.getString(R.string.lanjutkan)
        binding.lyTotalDetail.tvNominal.setTextAppearance(R.style.Body3SmallText_Bold_NeutralDark40)
        binding.lyTotalDetail.tvFeeAdmin.setTextAppearance(R.style.Body3SmallText_Bold_NeutralDark40)

        initiateAdapter()
        initiateSkeleton()

        enableLayoutButton(false)

        binding.lyTotalDetail.llPayTotal.setOnClickListener(this)
        binding.lyTotalDetail.btnPay.setOnClickListener(this)
        binding.lyTotalDetail.viewBg.setOnClickListener(this)
        binding.rlCaraRedeem.setOnClickListener(this)

        binding.ivHistory.setOnClickListener {
            val informasiTambahanFragment = InformasiTambahanFragment(this, mTermConditionList)
            informasiTambahanFragment.show(supportFragmentManager, "")
        }
    }

    @SuppressLint("NotifyDataSetChanged")
    private fun initiateAdapter() {
        //initiate recyclerview adapter informasi tambahan
        snkVoucherAdapter = SnkVoucherAdapter(this, snkList, this, false)
        snkVoucherAdapter.notifyDataSetChanged()
        binding.rvSnk.setHasFixedSize(true)
        binding.rvSnk.layoutManager = LinearLayoutManager(this)
        binding.rvSnk.adapter = snkVoucherAdapter

        //initiate recyclerview adapter optionlist
        optionVoucherAdapter = OptionVoucherGameAdapter(this, dataVoucherList, this)
        optionVoucherAdapter.notifyItemRangeChanged(0, dataVoucherList.size)
        optionVoucherAdapter.notifyDataSetChanged()
        val layoutManager = GridLayoutManager(this, 2)
        binding.rvVoucher.setHasFixedSize(true)
        binding.rvVoucher.layoutManager = layoutManager
        binding.rvVoucher.adapter = optionVoucherAdapter
    }

    private fun initiateSkeleton() {
        //initiate skeletonscreen view deskripsi voucher dan lihat cara redeem
        skeletonDesc = Skeleton
            .bind(binding.llDeskripsi)
            .shimmer(true)
            .angle(3)
            .duration(1200)
            .load(R.layout.item_skeleton_deskripsi_voucher)
            .show()

        //initiate skeletonscreen recyclerview optionlist
        skeletonScreenOption = Skeleton
                .bind(binding.rvVoucher)
                .adapter(optionVoucherAdapter)
                .shimmer(true).angle(3)
                .count(6)
                .frozen(false)
                .duration(1200)
                .load(R.layout.item_skeleton_list_voucher_streaming)
                .show()
    }

    @SuppressLint("NotifyDataSetChanged")
    override fun onSuccessGetDetail(voucherDetail: VoucherDetail) {
        skeletonScreenOption?.hide()
        skeletonDesc?.hide()

        dataVoucherList.addAll(voucherDetail.productList)
        binding.tvDescVoucherLess.visibility = View.VISIBLE
        binding.tvDescVoucherMore.visibility = View.GONE

        binding.tvLihatDetail.text = GeneralHelper.getString(R.string.lihat_lebih_banyak)
        binding.ivArrow.rotation = 0f

        binding.tvDescVoucherLess.ellipsize = TextUtils.TruncateAt.END
        binding.tvDescVoucherLess.maxLines = 2
        binding.tvDescVoucherLess.text = voucherDetail.description

        showDetail = false

        binding.llLhtLebih.setOnClickListener {
            if (!showDetail) {
                binding.tvDescVoucherLess.visibility = View.GONE
                binding.tvDescVoucherMore.visibility = View.VISIBLE

                binding.tvLihatDetail.text =
                    GeneralHelper.getString(R.string.lihat_lebih_sedikit_revamp)
                binding.ivArrow.rotation = 180f

                binding.tvDescVoucherMore.text = voucherDetail.description

                showDetail = true
            } else {
                binding.tvDescVoucherLess.visibility = View.VISIBLE
                binding.tvDescVoucherMore.visibility = View.GONE

                binding.tvLihatDetail.text = GeneralHelper.getString(R.string.lihat_lebih_banyak)
                binding.ivArrow.rotation = 0f

                binding.tvDescVoucherLess.ellipsize = TextUtils.TruncateAt.END
                binding.tvDescVoucherLess.maxLines = 2
                binding.tvDescVoucherLess.text = voucherDetail.description

                showDetail = false
            }
        }

        if (mVoucherType.equals(Constant.Voucher.GAME.name)) {
            //jika termcondition dari backend kosong hide list informasi tambahan
            if (voucherDetail.termCondition.isEmpty()) {
                binding.llInfoTambahan.visibility = View.GONE
            } else {
                snkList.addAll(voucherDetail.termCondition)
                mTermConditionList.addAll(voucherDetail.termCondition)
            }
            binding.llInfoTambahan.visibility = View.GONE
            binding.tvInfo.visibility = View.GONE
            binding.rvSnk.visibility = View.GONE

        } else if (mVoucherType.equals(Constant.Voucher.STREAMING.name)) {
            binding.llInfoTambahan.visibility = View.GONE
        }

        optionVoucherAdapter.notifyDataSetChanged()
        snkVoucherAdapter.notifyDataSetChanged()
    }

    override fun onSuccessGetTutorial(tutorialVoucherResponse: TutorialVoucherResponse) {
        mResponse = tutorialVoucherResponse
        CaraRedeemVocActivity.launchIntent(this, mGameId, tutorialVoucherResponse)
    }

    override fun generateGameId(): VoucherRequest? {
        var mVoucherRequest: VoucherRequest? = null
        if (mVoucherType.equals(Constant.Voucher.GAME.name)) {
            mVoucherRequest = VoucherRequest(gameModel!!.getId())
        } else if (mVoucherType.equals(Constant.Voucher.STREAMING.name)) {
            mVoucherRequest = VoucherRequest(gameModel!!.getId().toString())
        }
        return mVoucherRequest
    }

    override fun isHideSkeleton(hide: Boolean) {
        if (hide) {
            skeletonScreenOption?.hide()
        } else {
            skeletonScreenOption?.show()
        }
    }

    private fun enableLayoutButton(isEnable: Boolean) {
        if (isEnable) {
            binding.lyTotalDetail.llButton.visibility = View.VISIBLE
            SizeHelper.setMarginsView(this, binding.content, 0, 0, 0, 80)
        } else {
            binding.lyTotalDetail.llButton.visibility = View.GONE
            SizeHelper.setMarginsView(this, binding.content, 0, 0, 0, 0)
        }
    }

    @SuppressLint("NotifyDataSetChanged")
    override fun onItemClick(data: MutableList<ProductList>, position: Int) {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.N) {
            dataVoucherList.forEach(Consumer { tO: ProductList ->
                tO.isBol = false
            })
        } else {
            for (p in dataVoucherList) {
                p.isBol = false
            }
        }
        dataVoucherList[position].isBol = true

        optionVoucherAdapter.notifyDataSetChanged()

        binding.lyTotalDetail.tvTotalPay.text = data[position].nominalString
        binding.lyTotalDetail.tvNominal.text = data[position].nominalString
        binding.lyTotalDetail.tvFeeAdmin.text = data[position].adminFeeString

        mGameId = data[position].gameId.toString()
        mProductCode = data[position].productCode

        enableLayoutButton(true)

        if (data[position].nominal != 0) {
            binding.lyTotalDetail.btnPay.isEnabled = true
            binding.lyTotalDetail.btnPay.setTextColor(GeneralHelper.getColor(R.color.neutral_baseWhite))
        } else {
            binding.lyTotalDetail.btnPay.isEnabled = false
            binding.lyTotalDetail.btnPay.setTextColor(GeneralHelper.getColor(R.color.neutral_light60))
        }
    }

    override fun onItemDetailClick(voucherList: MutableList<ProductList>, position: Int) {
        detailVoucherFragment = DetailVoucherFragment(
            this,
            mVoucherType,
            voucherList[position].label,
            voucherList[position].expired,
            voucherList[position].description,
            voucherList[position].information,
            voucherList[position].nominalString,
            voucherList[position].adminFeeString,
            this::onClickBeli,
            voucherList[position].nominal

        )
        detailVoucherFragment.show(supportFragmentManager, "")

        mGameId = voucherList[position].gameId.toString()
        mProductCode = voucherList[position].productCode
        mIsDetailShow = true

    }

    override fun onClickSnk(productSnk: List<VoucherDetail.TermCondition>, position: Int) {
        SnkVoucherActivity.launchIntent(
            this, productSnk[position].style, productSnk[position].value
        )
    }

    private fun cekErrorMessage() {
        if (errorMessage != null) {
            //menampilkan snacknar error
            showSnackbarErrorMessageRevamp(errorMessage, ALERT_ERROR, this, false)

            //clear error message
            errorMessage = null.toString()
        }
    }

    @Suppress("DEPRECATION")
    @Deprecated("Deprecated in Java")
    override fun onActivityResult(requestCode: Int, resultCode: Int, data: Intent?) {
        super.onActivityResult(requestCode, resultCode, data)
        if (requestCode == Constant.REQ_PAYMENT) {
            if (resultCode == RESULT_OK) {
                setResult(RESULT_OK, data)
                finish()
            } else {
                if (data != null) {
                    val message = data.getStringExtra(Constant.TAG_ERROR_MESSAGE)
                    showSnackbarErrorMessageRevamp(message, ALERT_ERROR, this, false)
                    this.setResult(RESULT_CANCELED, data)
                }
            }
        } else {
            this.setResult(RESULT_CANCELED, data)
            if (data != null) {
                if (data.getStringExtra(Constant.TAG_ERROR_MESSAGE) != null) {
                    errorMessage = data.getStringExtra(Constant.TAG_ERROR_MESSAGE)
                    cekErrorMessage()
                } else {
                    this.setResult(RESULT_CANCELED, data)
                    finish()

                }
            }
        }
    }

    override fun onClick(v: View) {
        if (SystemClock.elapsedRealtime() - mLastClickTime < 1000) {
            return
        }
        mLastClickTime = SystemClock.elapsedRealtime()
        when (v.id) {
            binding.lyTotalDetail.llPayTotal.id -> showDetailTotalPay()

            binding.lyTotalDetail.viewBg.id -> showDetailTotalPay()
            binding.lyTotalDetail.btnPay.id -> presenter.getDataInquiry(
                mGameId, mProductCode, mVoucherType
            )
            binding.rlCaraRedeem.id -> {
                if (mVoucherType.equals(Constant.Voucher.GAME.name)) {
                    presenter.getCaraRedeemVoucherGame(
                        VoucherRequest(
                            mGameId.toInt(), mProductCode
                        )
                    )
                } else if (mVoucherType.equals(Constant.Voucher.STREAMING.name)) {
                    presenter.getCaraRedeemVoucherGame(
                        VoucherRequest(
                            mGameId, mProductCode
                        )
                    )
                }
            }
        }
    }

    private fun showDetailTotalPay() {
        if (!showDetailPay) {
            binding.lyTotalDetail.viewBg.visibility = View.VISIBLE
            binding.lyTotalDetail.llDetail.visibility = View.VISIBLE
            binding.lyTotalDetail.imgArrowPay.rotation = 180f
            showDetailPay = true
        } else {
            binding.lyTotalDetail.viewBg.visibility = View.GONE
            binding.lyTotalDetail.llDetail.visibility = View.GONE
            binding.lyTotalDetail.imgArrowPay.rotation = 0f
            showDetailPay = false
        }
    }

    override fun onSuccessGetInquiry(
        inquiryKonfirmasiVoucherResponse: GeneralConfirmationResponse, urlPayment: String
    ) {

        InquiryKonfirmasiVoucherActivity.launchIntent(
            this, inquiryKonfirmasiVoucherResponse, urlPayment, mGameId, mVoucherType
        )
    }

    override fun onClickBeli() {
        presenter.getDataInquiry(mGameId, mProductCode, mVoucherType)
    }

    override fun onException(message: String) {
        if (mIsDetailShow) {
            detailVoucherFragment.dismiss()
        }

        if (showDetailPay) {
            showDetailTotalPay()
        }

        if (GeneralHelper.isContains(Constant.LIST_TYPE_GAGAL, message))  {
            GeneralHelper.showDialogGagalBack(this, message)
        } else  {
            showSnackbarErrorMessageRevamp(
                message,
                ALERT_ERROR,
                this,
                false
            )
        }
    }

}