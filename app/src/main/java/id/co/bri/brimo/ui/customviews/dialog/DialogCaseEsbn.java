package id.co.bri.brimo.ui.customviews.dialog;

import android.annotation.SuppressLint;
import android.app.Dialog;
import android.graphics.drawable.ColorDrawable;
import android.os.Bundle;
import android.view.View;
import android.view.ViewGroup;
import android.view.Window;
import android.widget.Button;
import android.widget.ImageView;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.fragment.app.DialogFragment;
import id.co.bri.brimo.R;
import id.co.bri.brimo.domain.helpers.GeneralHelper;
import id.co.bri.brimo.models.apimodel.response.esbn.DashboardDataSbnResponse;

public class DialogCaseEsbn extends DialogFragment {
    private Dialog alertDialog;
    private Button btnYes, btnNanti;
    private TextView title, desc;
    private ImageView image;
    private String sTitle, sDesc, sCode, sLink,mBtn;
    private DialogDefaultListener dialogDefaulListener;
    private DashboardDataSbnResponse modelData;

    @SuppressLint("ValidFragment")
    public DialogCaseEsbn(){

    }

    public DialogCaseEsbn(DialogDefaultListener clickListener, DashboardDataSbnResponse modelDataDialogSbn,String btn, String code) {
        this.dialogDefaulListener = clickListener;
        this.sTitle = modelDataDialogSbn.getDialogData().getTitle();
        this.sDesc = modelDataDialogSbn.getDialogData().getDesc();
        this.sCode = code;
        this.mBtn = btn;
        this.sLink = modelDataDialogSbn.getDialogData().getImageUrl();
        this.modelData = modelDataDialogSbn;
    }

    @NonNull
    @Override
    public Dialog onCreateDialog(@Nullable Bundle savedInstanceState) {

        alertDialog = new Dialog(getActivity());
        alertDialog.setCanceledOnTouchOutside(false);
        alertDialog.getWindow().requestFeature(Window.FEATURE_NO_TITLE);
        alertDialog.getWindow().setBackgroundDrawable(new ColorDrawable(0));
        alertDialog.setContentView(R.layout.dialog_case_esbn);
        alertDialog.setOnKeyListener((dialogInterface, i, keyEvent) -> false);
        alertDialog.show();

        initView();

        btnYes.setOnClickListener(view -> {
            if (dialogDefaulListener != null) {
//                if (!sCode.equals("R2")){
//                    dialogDefaulListener.onNantiSaja();
//                }else {
                    dialogDefaulListener.onClickYes(modelData);
//                }
            }
            dismiss();
        });

        btnNanti.setOnClickListener(view -> {
            if (dialogDefaulListener != null) {
                dialogDefaulListener.onNantiSaja();
            }
            dismiss();
        });

        return alertDialog;
    }

    @Override
    public void onStart() {
        super.onStart();
        if (alertDialog!= null){
            alertDialog.getWindow().setLayout(ViewGroup.LayoutParams.MATCH_PARENT, ViewGroup.LayoutParams.WRAP_CONTENT);
        }
    }

    private void initView() {

        btnYes = alertDialog.findViewById(R.id.btn_ok);
        title = alertDialog.findViewById(R.id.tv_title_default);
        desc = alertDialog.findViewById(R.id.tv_desc_default);
        image = alertDialog.findViewById(R.id.iv_dialog_esbn);
        btnNanti = alertDialog.findViewById(R.id.btn_nanti_saja);

        btnYes.setText(mBtn);
        title.setText(sTitle);
        desc.setText(sDesc);
        GeneralHelper.loadImageUrl(getContext(),sLink,image , R.drawable.maaf_maaf, 0);


        /**
         * R1 -> Case menunggu sid & sre
         * R2 -> Case belum terdaftar di kemenkeu
         * R3 -> Case belum pengkinian data
         * R4 -> Case menunggu approvel
         */
//        if (sCode.equals("R1")){
//            btnNanti.setVisibility(View.GONE);
//            image.setImageResource(GeneralHelper.getImageId(getActivity(), "ic_sbn_dialog_sre"));
//        }else if (sCode.equals("R2")){
//            sLink = modelData.getDialogData().getLink();
//            btnNanti.setVisibility(View.VISIBLE);
//            btnYes.setText("Registrasi Sekarang");
//            image.setImageResource(GeneralHelper.getImageId(getActivity(), "ic_sbn_dialog"));
//        }else if (sCode.equals("R3")){
//            btnNanti.setVisibility(View.GONE);
//            image.setImageResource(GeneralHelper.getImageId(getActivity(), "ic_sbn_dialog"));
//        }else if (sCode.equals("R4")){
//            btnNanti.setVisibility(View.GONE);
//            image.setImageResource(GeneralHelper.getImageId(getActivity(), "ic_sbn_dialog"));
//        }
    }

    public interface DialogDefaultListener {
        void onClickYes(DashboardDataSbnResponse dataSbnResponse);

        void onNantiSaja();
    }
}