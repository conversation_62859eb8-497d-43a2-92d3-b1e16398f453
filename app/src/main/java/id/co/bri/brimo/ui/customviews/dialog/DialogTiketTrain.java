package id.co.bri.brimo.ui.customviews.dialog;

import android.app.Dialog;
import android.graphics.Bitmap;
import android.graphics.drawable.ColorDrawable;
import android.os.AsyncTask;
import android.os.Bundle;
import android.util.Log;
import android.view.Window;
import android.widget.Button;
import android.widget.ImageView;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.fragment.app.DialogFragment;
import id.co.bri.brimo.R;
import id.co.bri.brimo.domain.helpers.GeneralHelper;
import id.co.bri.brimo.domain.helpers.image.ImageHelper;
import id.co.bri.brimo.models.apimodel.response.DetailHistoryResponse;
import id.co.bri.brimo.models.apimodel.response.InquiryBusResponse;
import id.co.bri.brimo.models.apimodel.response.InquiryTrainResponse;
import id.co.bri.brimo.ui.fragments.QrMPMCodeFragment;

public class DialogTiketTrain extends DialogFragment {

    TextView tvKodeBooking;
    TextView tvNama;
    TextView tvKursi;
    ImageView ivTiket;
    private Bitmap bitmapQrCode;

    private ImageHelper imageHelper = new ImageHelper(getActivity());
    private Dialog alertDialog;
    private Button btnOk;
    GenerateQr qrCode = new GenerateQr();
    String mKodeBooking;

    public DialogTiketTrain(String kodeBooking) {
        this.mKodeBooking = kodeBooking;
    }

    @NonNull
    @Override
    public Dialog onCreateDialog(@Nullable Bundle savedInstanceState) {
        alertDialog = new Dialog(getActivity());
        alertDialog.setCanceledOnTouchOutside(false);
        alertDialog.getWindow().requestFeature(Window.FEATURE_NO_TITLE);
        alertDialog.getWindow().setBackgroundDrawable(new ColorDrawable(0));
        alertDialog.setContentView(R.layout.dialog_ticket_train);
        alertDialog.setOnKeyListener((dialogInterface, i, keyEvent) -> false);

        initView();
        alertDialog.show();

        btnOk.setOnClickListener(v -> {
            dismiss();
        });

        return alertDialog;
    }

    public void initView() {
        btnOk = alertDialog.findViewById(R.id.btnSubmit);
        tvKodeBooking = alertDialog.findViewById(R.id.tv_kode_booking);
        btnOk = alertDialog.findViewById(R.id.btnSubmit);
        ivTiket = alertDialog.findViewById(R.id.iv_tiket);

        tvKodeBooking.setText(mKodeBooking);

        qrCode.execute(mKodeBooking);
//        ivTiket.setImageBitmap(imageHelper.decodeImage(mTiketPenumpang.getQrString()));

    }

    class GenerateQr extends AsyncTask<String, Void, Bitmap> {

        @Override
        protected Bitmap doInBackground(String... strings) {
            try {
                bitmapQrCode = imageHelper.generateQRCode(getActivity(), strings[0], 180, 180);
            } catch (Exception e) {

            }
            return bitmapQrCode;
        }

        @Override
        protected void onPostExecute(Bitmap result) {
            ivTiket.setImageBitmap(result);
//            skeletonScreen.hide();
        }
    }

}
