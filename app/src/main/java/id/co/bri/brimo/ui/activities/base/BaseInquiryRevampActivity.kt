package id.co.bri.brimo.ui.activities.base

import android.content.Intent
import android.os.Bundle
import android.util.Log
import android.view.View
import android.widget.*
import androidx.appcompat.widget.Toolbar
import butterknife.Bind
import butterknife.ButterKnife
import com.google.gson.Gson
import id.co.bri.brimo.R
import id.co.bri.brimo.contract.IView.base.IBaseInquiryView
import id.co.bri.brimo.domain.config.Constant
import id.co.bri.brimo.domain.helpers.GeneralHelper
import id.co.bri.brimo.models.AccountModel
import id.co.bri.brimo.models.BillingDetailOpen
import id.co.bri.brimo.models.ParameterKonfirmasiModel
import id.co.bri.brimo.models.ParameterModel
import id.co.bri.brimo.models.apimodel.response.GeneralConfirmationResponse
import id.co.bri.brimo.models.apimodel.response.InquiryTransferRevampResponse
import id.co.bri.brimo.models.apimodel.response.pulsarevamp.FormPulsaDataResponse
import id.co.bri.brimo.ui.activities.KonfirmasiGeneralRevampActivity
import id.co.bri.brimo.ui.fragments.SumberDanaFragmentRevamp

abstract class BaseInquiryRevampActivity : BaseActivity(), SumberDanaFragmentRevamp.SelectSumberDanaInterface,
    IBaseInquiryView{

    private val TAG = "BaseInquiryRevampActivity"

    @Bind(R.id.tb_inquiry_toolbar)
    lateinit var toolbar: Toolbar

    @Bind(R.id.switch_save)
    lateinit var switchSave : Switch

    @Bind(R.id.rl_favorit)
    lateinit var  rlFavorit : RelativeLayout

    @Bind(R.id.tv_alias_rek)
    lateinit var  tvAliasRek : TextView

    @Bind(R.id.tv_no_rek)
    lateinit var  tvNoRek : TextView

    @Bind(R.id.tv_saldo_rek)
    lateinit var  tvSaldoRek : TextView

    @Bind(R.id.iv_icon_rek)
    lateinit var  ivIconRek : ImageView

    @Bind(R.id.tv_ganti)
    lateinit var tvGanti : TextView

    @Bind(R.id.tv_tambah)
    lateinit var tvTambah : TextView

    @Bind(R.id.rl_save_as)
    lateinit var rlSaveAs : RelativeLayout

    @Bind(R.id.et_save_as)
    lateinit var etSaveAs : EditText


    protected var errorMessage: String? = null

    protected var mUrlKonfirmasi: String? = null
    protected var mUrlPayment: String? = null
    protected var mUrlPending :  String? = null
    protected var mTitle: String? = null
    protected var mParameterModel: ParameterModel? = null
    protected var mParameterKonfirmasiModel: ParameterKonfirmasiModel? = null

    lateinit var mSaldoString : String
    var mSaldo : Double = 0.0
    lateinit var mDefaultAkun : String
    lateinit var mFormPulsaDataResponse: FormPulsaDataResponse
    lateinit var mInquiryPulsaDataResponse: InquiryTransferRevampResponse
    var saveStr : String = ""
    private lateinit var openModel: BillingDetailOpen
    protected var isLoading = false
    open var model: AccountModel? = null
    private var mListFailed: MutableList<Int>? = null
    var mListAccountModel: MutableList<AccountModel>? = null
    var saldo = 0.0
    var counter = 0
    protected var isSaldoHold = false



    companion object {
        var TAG_INQUIRY_DATA = "inquiry_data"
        var TAG_INQUIRY_DATA_SUMBER = "inquiry_data_sumber_dana"
        var TAG_PARAMS_DATA = "params_data"
        var TAG_URL_KONFIRM = "url_konfirm"
        var TAG_URL_PAYMENT = "url_payment"
        var TAG_URL_PENDING = "url_pending"
        var TAG_TITLE = "url_title"
        private var mParameterModel: ParameterModel? = null

    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContentView(getLayoutResource())
        ButterKnife.bind(this)

        //parsing data intent
        if (intent.extras != null) {
            parseDataIntentInquiry(intent.extras)
        }
        GeneralHelper.setToolbar(this, toolbar, getTitleBar())


        mParameterKonfirmasiModel = setParameterKonfirmasi()

        //Error Snackbar
        if (errorMessage != null) {
            showSnackbarErrorMessage(errorMessage, ALERT_ERROR, this, false)
            errorMessage = null
        }

    }

    /**
     * Method digunakan untuk meng-extract data Intent dari Form Activity
     *
     * @param savedInstanceState Bundle savedInstanceState
     */
    protected fun parseDataIntentInquiry(savedInstanceState: Bundle?) {
        if (savedInstanceState != null) {
            try {
                val tempInquiry = savedInstanceState.getString(TAG_INQUIRY_DATA)
                if (tempInquiry != null) {
                    mFormPulsaDataResponse = Gson().fromJson(
                        tempInquiry,
                        FormPulsaDataResponse::class.java
                    )
                }
                val tempInquirySumber = savedInstanceState.getString(TAG_INQUIRY_DATA_SUMBER)
                if (tempInquirySumber != null) {
                    mInquiryPulsaDataResponse = Gson().fromJson(
                        tempInquirySumber,
                        InquiryTransferRevampResponse::class.java
                    )
                }
                val tempParams = savedInstanceState.getString(TAG_PARAMS_DATA)
                if (tempParams != null) {
                    mParameterModel = Gson().fromJson(tempParams, ParameterModel::class.java)
                }
                mUrlKonfirmasi = savedInstanceState.getString(TAG_URL_KONFIRM)
                mUrlPayment = savedInstanceState.getString(TAG_URL_PAYMENT)
                mUrlPending = savedInstanceState.getString(TAG_URL_PENDING)
                mTitle = savedInstanceState.getString(TAG_TITLE)
            } catch (e: Exception) {
                if (!GeneralHelper.isProd()) {
                    Log.e(TAG, "parseDataIntentInquiry: ", e)
                }
            }
        }
    }

    override fun onSuccessGetConfirmation(brivaConfirmationResponse: GeneralConfirmationResponse?) {
        KonfirmasiGeneralRevampActivity.launchIntent(
            this,
            brivaConfirmationResponse,
            mUrlPayment,
            GeneralHelper.getString(R.string.pulsa_paket_data_toolbar_title_pulsadata),
            setParameterKonfirmasi(),
            isFromFastMenu,
            true
        )

    }

    override fun onException93(message: String?) {
        isLoading = false
        val returnIntent = Intent()

        returnIntent.putExtra(Constant.TAG_ERROR_MESSAGE, message)

        this.setResult(RESULT_CANCELED, returnIntent)
        finish()
    }


    override fun setDefaultSaldo(saldo: Double, saldoString: String?, account: String?, saldohold : Boolean) {
        // load default saldo dari preference
        mSaldoString = saldoString!!
        mSaldo = saldo
        mDefaultAkun = account!!
        isSaldoHold = saldohold
        setupAccount(mSaldo)
    }

    override fun setParameterKonfirmasi(): ParameterKonfirmasiModel {
        val parameterKonfirmasiModel = ParameterKonfirmasiModel()

        parameterKonfirmasiModel.stringLabelTujuan = mParameterModel!!.stringLabelTujuan
        parameterKonfirmasiModel.stringButtonSubmit = mParameterModel!!.stringButtonSubmit
        parameterKonfirmasiModel.defaultIcon = mParameterModel!!.defaultIcon

        return parameterKonfirmasiModel
    }


    override fun onSelectSumberDana(bankModel: AccountModel?) {
            model = bankModel
            if (bankModel!!.saldoReponse != null) {
                val saldoStr: String = model!!.saldoReponse.balanceString.toString()
                tvSaldoRek.text =
                    GeneralHelper.formatNominalIDR(
                        model!!.currency,
                        saldoStr
                    )

                if (bankModel.saldoReponse.balance != null) {
                    saldo = bankModel.saldoReponse.balance
                    isSaldoHold = bankModel.saldoReponse.isOnHold
                } else {
                    tvSaldoRek.text = String.format("%s%s", bankModel.currency, "-")
                    saldo = 0.0
                    isSaldoHold = false
                }
            } else {
                tvSaldoRek.text = String.format("%s%s", bankModel.currency, "-")
                saldo = 0.0
            }
            tvNoRek.text =model!!.acoountString
            if (bankModel.alias != null) {
                if (bankModel.alias.isEmpty()) {
                    tvAliasRek.text =bankModel.name
                } else {
                    tvAliasRek.text =bankModel.alias
                }
            } else {
                tvAliasRek.text =bankModel.name
            }
            if (bankModel.imagePath != null) {
                if (!bankModel.imagePath.isEmpty()) {
                    GeneralHelper.loadImageUrl(
                        this,
                        bankModel.imagePath,
                        ivIconRek,
                        R.drawable.bri,
                        0
                    )
                } else {
                    ivIconRek.setImageResource(R.drawable.bri)
                }
            } else {
                ivIconRek.setImageResource(R.drawable.bri)
            }
            checkButton()
    }

    protected open fun setupAccount(saldoDefault: Double) {

        //List Account
        model = AccountModel()
        if (mInquiryPulsaDataResponse.accountList.size > 0) mListAccountModel =
            mInquiryPulsaDataResponse.accountList
        for (accountModel in mListAccountModel!!.iterator()) {
            if (accountModel.isDefault == 1) {
                model = accountModel
                break
            } else {
                model = mListAccountModel!![0]
            }
        }

        //jika get minimum tidak null
        saldo = if (model!!.minimumBalance != null) {
            saldoDefault - model!!.minimumBalance
        } else {
            saldoDefault
        }
        if (isFromFastMenu) {
            tvSaldoRek.visibility = View.GONE
            onSelectSumberDana(model)
        } else {
            tvSaldoRek.visibility = View.VISIBLE
            if (model!!.acoount != null) {
                if (model!!.acoount == mDefaultAkun) {
                    onSelectSumberDana(model)
                } else {
                    tvNoRek.text = "-"
                    tvSaldoRek.text = "-"
                }
            }
        }

        //jika string rekening tidak kosong
        if (model!!.acoountString != null) {
            tvNoRek.text = model!!.acoountString
        } else {
            tvNoRek.text = "-"
        }
        if (model!!.saldoReponse != null) {
            isSaldoHold = model!!.saldoReponse.isOnHold
        }

        if (model!!.acoount != null) {
            val saldoText: String = saldoDefault.toString()
            if (saldoText != "") {
                saldo = java.lang.Double.valueOf(saldoText)
            }
            if (model!!.acoount == mDefaultAkun) {
                tvSaldoRek.text = GeneralHelper.formatNominalIDR(model!!.currency, saldo)
            } else {
                tvSaldoRek.text = "-"
            }
        }
        checkButton()
    }

    override fun onSendFailedList(list: MutableList<Int>?) {
        this.mListFailed = list
    }

    abstract fun setupView()

    abstract override fun onSubmit()

    abstract override fun getAmount(): Int

    abstract fun getLayoutResource(): Int

    abstract fun getTitleBar(): String?

    abstract fun checkButton()

    abstract fun setupFavorite()

    open fun injectDependency(){
        activityComponent.inject(this)
    }


    override fun onActivityResult(requestCode: Int, resultCode: Int, data: Intent?) {
        super.onActivityResult(requestCode, resultCode, data)
        if (requestCode == Constant.REQ_PAYMENT) {
            if (resultCode == RESULT_OK) {
                this.setResult(RESULT_OK, data)
                finish()
            } else if (resultCode == Constant.REQ_PETTUNJUK1){
                this.setResult(RESULT_CANCELED, data)
            }
            else {
                this.setResult(RESULT_CANCELED, data)
                finish()
            }
        }
    }
}