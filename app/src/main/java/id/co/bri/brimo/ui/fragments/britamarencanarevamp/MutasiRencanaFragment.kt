package id.co.bri.brimo.ui.fragments.britamarencanarevamp

import android.app.Activity
import android.content.Context
import android.content.Intent
import android.os.Bundle
import android.os.SystemClock
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.ethanhua.skeleton.Skeleton
import com.ethanhua.skeleton.SkeletonScreen
import id.co.bri.brimo.R
import id.co.bri.brimo.adapters.ListBulanMutasiAdapter
import id.co.bri.brimo.adapters.ListMutasiAdapter
import id.co.bri.brimo.contract.IPresenter.mutasi.IMutasiPresenter
import id.co.bri.brimo.contract.IView.mutasi.IMutasiView
import id.co.bri.brimo.databinding.FragmentMutasiRencanaBinding
import id.co.bri.brimo.domain.config.Constant
import id.co.bri.brimo.domain.helpers.GeneralHelper
import id.co.bri.brimo.domain.helpers.calendar.CalendarHelper
import id.co.bri.brimo.models.AccountModel
import id.co.bri.brimo.models.TransactionTypeModel
import id.co.bri.brimo.models.YearModel
import id.co.bri.brimo.models.apimodel.request.MutationDateRangeFilterRequest
import id.co.bri.brimo.models.apimodel.request.MutationFilterRequest
import id.co.bri.brimo.models.apimodel.request.MutationMonthFilterRequest
import id.co.bri.brimo.models.apimodel.response.EmptyStateResponse
import id.co.bri.brimo.models.apimodel.response.InquiryFiveMutasiResponse
import id.co.bri.brimo.models.apimodel.response.ListRekeningResponse
import id.co.bri.brimo.models.apimodel.response.MutasiResponse
import id.co.bri.brimo.models.apimodel.response.dashboardrecana.DashboardRencanaResponse
import id.co.bri.brimo.ui.activities.MutationFilterActivity
import id.co.bri.brimo.ui.activities.StickHeaderItemDecoration
import id.co.bri.brimo.ui.fragments.BaseFragment
import id.co.bri.brimo.ui.fragments.ListRekeningFragment
import id.co.bri.brimo.ui.fragments.SumberDanaFragment
import javax.inject.Inject


class MutasiRencanaFragment : BaseFragment(), SumberDanaFragment.SelectSumberDanaInterface,
        View.OnClickListener, IMutasiView {
    private lateinit var binding: FragmentMutasiRencanaBinding

    private var skeletonScreenMutasi: SkeletonScreen? = null
    protected var errorMessage: String? = null
    private var mLastClickTime: Long = 0
    private val counter = 0
    private  var recylerviewHeight:Int = 0
    private  var mTotalScrolled:Int = 0
    private val isLoading = false
    private var endDate: String? = null
    private var startDate: String? = null

    protected var model: AccountModel? = null
    protected var mListAccountModel: MutableList<AccountModel> = mutableListOf()
    protected var accountList = ArrayList<ListRekeningResponse.Account>()
    protected var mListFailed: List<Int>? = null
    protected var mutasiResponses = ArrayList<MutasiResponse>()
    protected var mutasiAdapter: ListMutasiAdapter? = null
    protected var listBulanMutasiAdapter: ListBulanMutasiAdapter? = null
    private var accoutString: String? = null
    private var rangeFilter: String? = null
    private var transactionTypeId: String? = null
    private var years: String? = null
    private var monthId: String? = null
    private var accountNumber: String? = null
    private var transactionTypeName: String? = null
    private var month: String? = null
    private var getmContext: Context? = null
    private var fromRekening = false
    protected var transactionType = java.util.ArrayList<TransactionTypeModel>()
    protected var yearList: List<YearModel> = java.util.ArrayList()
    protected var mutationFilterRequest: MutationFilterRequest? = null
    protected var mutationMonthFilterRequest: MutationMonthFilterRequest? = null
    protected var mutationDateRangeFilterRequest: MutationDateRangeFilterRequest? = null
    private var recyclerItemDecoration: StickHeaderItemDecoration? = null
    private var mResponse : List<DashboardRencanaResponse.DashboardData.Account>? = null
    private var position : Int? = null

    @Inject
    lateinit var presenter: IMutasiPresenter<IMutasiView>

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)

    }

    override fun onCreateView(inflater: LayoutInflater, container: ViewGroup?,
                              savedInstanceState: Bundle?): View? {
        binding = FragmentMutasiRencanaBinding.inflate(inflater, container, false)
        injectDependency()

        initiateBulanAdapter()

        skeletonScreenMutasi = Skeleton.bind(binding.rvItemMutasi)
                .adapter(listBulanMutasiAdapter)
                .shimmer(true)
                .angle(20)
                .frozen(false)
                .duration(1200)
                .count(1)
                .load(R.layout.item_skeleton_mutasi)
                .show()
        return binding.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        if (presenter != null) {
            presenter.setView(this)
            presenter.start()
            callService()
        }
    }

    protected fun injectDependency() {
        activityComponent.inject(this)
    }

    fun callService(){
        if (presenter != null) {
            presenter.setRekeningUrl(GeneralHelper.getString(R.string.url_rekening))
            presenter.setUrlGetMutationDateRange(GeneralHelper.getString(R.string.url_v3_mutation_date_range))
            presenter.setAccount(accountNumber)
            presenter.getDataRekening()
        }
    }

    override fun onSelectSumberDana(bankModel: AccountModel?) {
        model = bankModel
        binding.tvNoRek.setText(model?.getAcoountString())
        accountNumber = model?.getAcoount()
        presenter.setAccount(accountNumber)
        if (rangeFilter == null) {
            getFirstMutasi()
        } else {
            getListMutation()
        }
    }

    private fun getListMutation() {
        if (rangeFilter.equals(Constant.TODAY_FILTER, ignoreCase = true) || rangeFilter.equals(Constant.SEVEN_DAY_FILTER, ignoreCase = true)) {
            mutationFilterRequest = if (transactionTypeId == null) {
                MutationFilterRequest(rangeFilter, accountNumber, Constant.TRANSACTION_TYPE_ALL)
            } else {
                MutationFilterRequest(rangeFilter, accountNumber, transactionTypeId)
            }
            presenter.getMutationRange(mutationFilterRequest, null, null)
        } else if (rangeFilter.equals(Constant.MONTH_FILTER, ignoreCase = true)) {
            mutationMonthFilterRequest = if (transactionTypeId == null) {
                MutationMonthFilterRequest(rangeFilter, years, monthId, accountNumber, Constant.TRANSACTION_TYPE_ALL)
            } else {
                MutationMonthFilterRequest(rangeFilter, years, monthId, accountNumber, transactionTypeId)
            }
            presenter.getMutationRange(null, mutationMonthFilterRequest, null)
        } else if (rangeFilter.equals(Constant.RANGE_FILTER, ignoreCase = true)) {
            mutationDateRangeFilterRequest = if (transactionTypeId == null) {
                MutationDateRangeFilterRequest(rangeFilter, startDate, endDate, accountNumber, Constant.TRANSACTION_TYPE_ALL)
            } else {
                MutationDateRangeFilterRequest(rangeFilter, startDate, endDate, accountNumber, transactionTypeId)
            }
            presenter.getMutationRange(null, null, mutationDateRangeFilterRequest)
        }
    }

    private fun getFirstMutasi() {
        presenter.setUrlGetMutationLastFive(GeneralHelper.getString(R.string.url_v3_mutation_last_five))
        presenter.getMutationLastFive()
    }

    override fun onSendFailedList(list: MutableList<Int>?) {

    }

    override fun onClick(p0: View?) {
        if (SystemClock.elapsedRealtime() - mLastClickTime < 1000) {
            return
        }

        mLastClickTime = SystemClock.elapsedRealtime()
        when(p0!!.id) {
            R.id.ll_time_filter -> MutationFilterActivity.launchIntent(activity, transactionType, yearList, rangeFilter, transactionTypeId, monthId, years, startDate, endDate,false)
        }
    }

    override fun onException12(message: String?) {
    }

    override fun onException93(message: String?) {
    }

    override fun onSuccessMutasiFive(mutasiResponse: InquiryFiveMutasiResponse?) {
        binding.rvItemMutasi.setVisibility(View.VISIBLE)
        binding.llEmptyState.setVisibility(View.GONE)
        mutasiResponses.clear()
        if (mutasiResponse != null) {
            mutasiResponses.addAll(mutasiResponse.getMutation())
            transactionType = mutasiResponse.getListTypeTransaction()
            yearList = mutasiResponse.getListYear()
        }
        if (yearList != null) {
            binding.llTimeFilter.setOnClickListener(this)
            binding.llTimeFilter.setClickable(true)
        }
    }

    override fun isHideSkeleton(hide: Boolean) {
        if (hide) {
            try {
                skeletonScreenMutasi!!.hide()
                binding.llTimeFilter.setOnClickListener(this)
                recyclerItemDecoration?.let { binding.rvItemMutasi.addItemDecoration(it) }

            } catch (e: Exception) {
            }
        } else {
            try {
                skeletonScreenMutasi!!.show()
                binding.llTimeFilter.setClickable(false)
                recyclerItemDecoration?.let { binding.rvItemMutasi.removeItemDecoration(it) }

            } catch (e: Exception) {
            }
        }
    }

    override fun isShowData(show: Boolean) {
        if (!show) {
            binding.rvItemMutasi.setVisibility(View.GONE)
        }
    }

    override fun onSuccessGetAccount(account: ArrayList<ListRekeningResponse.Account>?) {
        accountList.clear()
        accountList.addAll(account!!)
        setupField()
        presenter.setAccount(mResponse?.get(0)?.bancassAccount)
        getFirstMutasi()
    }

    private fun setupField() {
        accountNumber = mResponse?.get(position!!)?.bancassAccount
        accoutString = mResponse?.get(position!!)?.bancassAccountString
        if (accoutString != null) {
            binding.tvNoRek.setText(accoutString)
        }
    }
    fun initiateBulanAdapter() {
        if (SystemClock.elapsedRealtime() - mLastClickTime < 1000) {
            return
        }
        binding.rvItemMutasi.setLayoutManager(LinearLayoutManager(context, RecyclerView.VERTICAL, false))
        binding.rvItemMutasi.smoothScrollToPosition(0)
        listBulanMutasiAdapter = ListBulanMutasiAdapter(mutasiResponses, mutasiAdapter, activity)
        binding.rvItemMutasi.setAdapter(listBulanMutasiAdapter)
        recyclerItemDecoration = StickHeaderItemDecoration(activity,
                requireActivity().resources.getDimensionPixelSize(R.dimen.recycler_section_header_height), true, getSectionCallback(mutasiResponses))
    }

    private fun fetchAccountModel(): List<AccountModel>? {
        val accountModelList: MutableList<AccountModel> = java.util.ArrayList()
        if (accountList != null) {
            for (i in accountList.indices) {
                accountModelList.add(AccountModel(accountList[i].account, accountList[i].accountString,
                        accountList[i].name, accountList[i].currency, accountList[i].cardNumber, accountList[i].productType,
                        accountList[i].accountType, accountList[i].scCode, accountList[i].default,
                        accountList[i].alias))
            }
        }
        return accountModelList
    }

    override fun showEmptyState(response: EmptyStateResponse?) {
        binding.rvItemMutasi.setVisibility(View.GONE)
        binding.llEmptyState.setVisibility(View.VISIBLE)
        binding.ivEmptyState.setImageResource(GeneralHelper.getImageId(activity, response!!.imageName))
        if (response.title.isEmpty()) {
            binding.tvTitleEmpty.setText(response.description)
            binding.tvDescEmpty.setVisibility(View.GONE)
        } else {
            binding.tvTitleEmpty.setText(response.title)
            binding.tvDescEmpty.setVisibility(View.VISIBLE)
            binding.tvDescEmpty.setText(response.description)
        }
        if (response.listYear != null && response.listTypeTransaction != null) {
            transactionType = response.listTypeTransaction
            yearList = response.listYear
        }

        if (yearList != null) {
            binding.llTimeFilter.setOnClickListener(this)
            binding.llTimeFilter.setClickable(true)
        }
    }

    override fun onSuccessGetMutation(mutasiResponse: InquiryFiveMutasiResponse?) {
        binding.rvItemMutasi.setVisibility(View.VISIBLE)
        binding.llEmptyState.setVisibility(View.GONE)
        mutasiResponses.clear()
        mutasiResponse?.let { mutasiResponses.addAll(it.mutation) }
        listBulanMutasiAdapter!!.notifyDataSetChanged()
    }


    override fun onActivityResult(requestCode: Int, resultCode: Int, data: Intent?) {
        super.onActivityResult(requestCode, resultCode, data)
        if (requestCode == Constant.REQ_DURATION) {
            if (resultCode == Activity.RESULT_OK) {
                if (data != null) {
                    if (data.getStringExtra(Constant.FILTER) != null) {
                        rangeFilter = data.getStringExtra(Constant.FILTER)
                        transactionTypeId = data.getStringExtra(Constant.TRANSACTION_TYPE_ID)
                        transactionTypeName = data.getStringExtra(Constant.TRANSACTION_TYPE_NAME)
                        accountNumber = binding.tvNoRek.getText().toString().replace(" ", "")
                        getMutationWithFilter(data)
                    } else {
                        setResetFilter()
                    }
                } else {
                    setResetFilter()
                }
            }
        }
    }

    private fun setResetFilter() {
        binding.tvTime.setText(GeneralHelper.getString(R.string.date_range))
        binding.tvTime.setTextColor(resources.getColor(R.color.system10))
        binding.llTimeFilter.setBackground(resources.getDrawable(R.drawable.bg_white_border_accent2))
        binding.ivFilter.setVisibility(View.VISIBLE)
        rangeFilter = null
        transactionTypeId = null
        monthId = null
        years = null
        startDate = null
        endDate = null
        getFirstMutasi()
    }

    private fun getMutationWithFilter(data: Intent) {
        if (rangeFilter.equals(Constant.TODAY_FILTER, ignoreCase = true) || rangeFilter.equals(Constant.SEVEN_DAY_FILTER, ignoreCase = true)) {
            mutationFilterRequest = if (transactionTypeId == null) {
                MutationFilterRequest(rangeFilter, accountNumber, Constant.TRANSACTION_TYPE_ALL)
            } else {
                MutationFilterRequest(rangeFilter, accountNumber, transactionTypeId)
            }
            presenter.getMutationRange(mutationFilterRequest, null, null)
        } else if (rangeFilter.equals(Constant.MONTH_FILTER, ignoreCase = true)) {
            years = data.getStringExtra(Constant.YEAR)
            monthId = data.getStringExtra(Constant.MONTH)
            month = data.getStringExtra(Constant.MONTH_TEXT)
            mutationMonthFilterRequest = if (transactionTypeId == null) {
                MutationMonthFilterRequest(rangeFilter, years, monthId, accountNumber, Constant.TRANSACTION_TYPE_ALL)
            } else {
                MutationMonthFilterRequest(rangeFilter, years, monthId, accountNumber, transactionTypeId)
            }
            presenter.getMutationRange(null, mutationMonthFilterRequest, null)
        } else if (rangeFilter.equals(Constant.RANGE_FILTER, ignoreCase = true)) {
            startDate = data.getStringExtra(Constant.TAG_START_DATE)
            endDate = data.getStringExtra(Constant.TAG_END_DATE)
            mutationDateRangeFilterRequest = if (transactionTypeId == null) {
                MutationDateRangeFilterRequest(rangeFilter, startDate, endDate, accountNumber, Constant.TRANSACTION_TYPE_ALL)
            } else {
                MutationDateRangeFilterRequest(rangeFilter, startDate, endDate, accountNumber, transactionTypeId)
            }
            presenter.getMutationRange(null, null, mutationDateRangeFilterRequest)
        }
        binding.tvTime.setTextColor(resources.getColor(R.color.whiteColor))
        binding.llTimeFilter.setBackground(resources.getDrawable(R.drawable.rounded_button_blue))
        binding.ivFilter.setVisibility(View.GONE)
        if (rangeFilter.equals(Constant.TODAY_FILTER, ignoreCase = true)) {
            binding.tvTime.setText(Constant.TODAY_TEXT)
        } else if (rangeFilter.equals(Constant.SEVEN_DAY_FILTER, ignoreCase = true)) {
            binding.tvTime.setText(Constant.SEVEN_DAY_TEXT)
        } else if (rangeFilter.equals(Constant.MONTH_FILTER, ignoreCase = true)) {
            binding.tvTime.setText("$month $years")
        } else {
            binding.tvTime.setText(CalendarHelper.dateFormatyyyyMMdd(startDate) + " - " + CalendarHelper.dateFormatyyyyMMdd(endDate))
        }
    }

    private fun getSectionCallback(mutasi: List<MutasiResponse>): StickHeaderItemDecoration.SectionCallback? {
        return object : StickHeaderItemDecoration.SectionCallback {
            override fun isSection(position: Int): Boolean {
                return position == 0 || mutasi[position].dateString !== mutasi[position - 1].dateString
            }

            override fun getSectionHeaderName(pos: Int): String {
                return mutasi[pos].dateString
            }
        }
    }

    override fun onException(message: String?) {
        if (GeneralHelper.isContains(Constant.LIST_TYPE_GAGAL, message)) GeneralHelper.showBottomDialog(activity, message) else showSnackbarErrorMessage(message, ALERT_ERROR, this, false)
    }

    fun setAccount(response: List<DashboardRencanaResponse.DashboardData.Account>?) {
        mResponse = response
    }

    fun setPosition(positions: Int) {
        position = positions
    }

    override fun onDetach(){
        presenter.stop() // stop RX android disposable
        super.onDetach()
    }
}