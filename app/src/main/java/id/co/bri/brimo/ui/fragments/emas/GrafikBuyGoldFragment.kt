package id.co.bri.brimo.ui.fragments.emas

import android.os.Bundle
import android.os.Handler
import android.os.Looper
import android.view.LayoutInflater
import android.view.MotionEvent
import android.view.View
import android.view.ViewGroup
import androidx.appcompat.widget.AppCompatButton
import com.github.aachartmodel.aainfographics.aachartcreator.AAChartView
import com.github.aachartmodel.aainfographics.aachartcreator.AAMoveOverEventMessageModel
import com.google.gson.Gson
import id.co.bri.brimo.R
import id.co.bri.brimo.databinding.FragmentGrafikBuyGoldBinding
import id.co.bri.brimo.domain.helpers.GrafikHelperRevamp
import id.co.bri.brimo.models.apimodel.response.emas.GrafikEmasResponse
import id.co.bri.brimo.ui.customviews.GrafikEmasViewPager
import id.co.bri.brimo.ui.customviews.dialog.DialogCase58
import id.co.bri.brimo.ui.fragments.BaseFragment
import id.co.bri.brimo.ui.fragments.DialogFragmentRc02
import id.co.bri.brimo.ui.fragments.emas.GrafikSellGoldFragment.Companion
import id.co.bri.brimo.ui.fragments.emas.GrafikSellGoldFragment.OnItemClickListener
import java.util.Collections

class GrafikBuyGoldFragment: BaseFragment(),
    AAChartView.AAChartViewCallBack,
    View.OnClickListener {
    lateinit var binding : FragmentGrafikBuyGoldBinding

    //set Up Grafik
    var date : String? = null
    private var arrayTanggal : MutableList<String> = mutableListOf()
    private var xArray : List<String> = listOf()
    private var yArray : List<Int> = listOf()
    private var xStringArray : MutableList<String>? = mutableListOf()
    private var yStringArray : MutableList<Int>? = mutableListOf()
    private var xStringArray1month : MutableList<String>? = mutableListOf()
    private var yStringArray1month : MutableList<Int>? = mutableListOf()
    private var xStringArray3month : MutableList<String>? = mutableListOf()
    private var yStringArray3month : MutableList<Int>? = mutableListOf()
    private var xStringArray6month : MutableList<String>? = mutableListOf()
    private var yStringArray6month : MutableList<Int>? = mutableListOf()
    private var xStringArrayJual : MutableList<String>? = mutableListOf()
    private var yStringArrayJual : MutableList<Int>? = mutableListOf()
    private var xStringArray1monthJual : MutableList<String>? = mutableListOf()
    private var yStringArray1monthJual : MutableList<Int>? = mutableListOf()
    private var xStringArray3monthJual : MutableList<String>? = mutableListOf()
    private var yStringArray3monthJual : MutableList<Int>? = mutableListOf()
    private var xStringArray6monthJual : MutableList<String>? = mutableListOf()
    private var yStringArray6monthJual : MutableList<Int>? = mutableListOf()
    private var xStringArrayTemp : MutableList<String>? = mutableListOf()
    private var yStringArrayTemp : MutableList<Int>? = mutableListOf()
    private var index : Int? = null
    private var mBoolean = false
    var isTouchListenerEnabled = true
    private var maxNominal : Int? = null
    private var minNominal : Int? = null
    var dialog : DialogFragmentRc02? = null
    var dialog58 : DialogCase58? = null

    private lateinit var viewPager: GrafikEmasViewPager
    private lateinit var onListener: OnItemClickListener
    private lateinit var mResponse: GrafikEmasResponse
    companion object {
        private const val ARG_RESPONSE = "ARG_RESPONSE"

        fun newInstance(
            response: GrafikEmasResponse,
            viewPager: GrafikEmasViewPager,
            onListener: OnItemClickListener
        ): GrafikBuyGoldFragment {
            val fragment = GrafikBuyGoldFragment()
            val args = Bundle()
            args.putString(ARG_RESPONSE, Gson().toJson(response))
            fragment.arguments = args
            fragment.viewPager = viewPager
            fragment.onListener = onListener
            return fragment
        }
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        arguments?.getString(ARG_RESPONSE)?.let {
            mResponse = Gson().fromJson(it, GrafikEmasResponse::class.java)
        }
    }

    override fun onCreateView(
        inflater: LayoutInflater, container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View? {
        // Inflate the layout for this fragment
        binding = FragmentGrafikBuyGoldBinding.inflate(inflater,container,false)
        return binding.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)

        setupView()
    }

    private fun setupView() {
        binding.btn1minggu.setOnClickListener(this)
        binding.btn1bulan.setOnClickListener(this)
        binding.btn3bulan.setOnClickListener(this)
        binding.btn6bulan.setOnClickListener(this)
        binding.btnBuy.setOnClickListener(this)
        statusButton(binding.btn1minggu,true)
        setupDataGrafik(7,mBoolean, mResponse!!.buyInfo!!.buyRateHistory!!, mResponse.sellInfo!!.sellRateHistory!!)
        setupDataGrafik1Month(30,mBoolean, mResponse.buyInfo!!.buyRateHistory!!, mResponse.sellInfo.sellRateHistory!!)
        setupDataGrafik3month(90,mBoolean, mResponse.buyInfo.buyRateHistory!!, mResponse.sellInfo.sellRateHistory)
        if (mResponse.buyInfo.buyRateHistory.size <= 180){
            setupDataGrafik6month(
                mResponse.buyInfo.buyRateHistory.size,mBoolean, mResponse.buyInfo.buyRateHistory,
                mResponse.sellInfo.sellRateHistory)
        }else{
            setupDataGrafik6month(180,mBoolean, mResponse.buyInfo.buyRateHistory,
                mResponse.sellInfo.sellRateHistory)

        }
        binding.aaChartView.setOnTouchListener { _, event ->
            when (event.action) {
                MotionEvent.ACTION_DOWN -> viewPager.isPagingEnabled = false
                MotionEvent.ACTION_UP, MotionEvent.ACTION_CANCEL -> viewPager.isPagingEnabled = true
            }
            false  // Return false to allow the chart view to handle the touch event
        }

        xStringArrayTemp = xStringArray
        yStringArrayTemp = yStringArray
        GrafikHelperRevamp.setupChart(xStringArrayTemp,yStringArrayTemp,minNominal!!,maxNominal!!,binding.aaChartView)
        binding.aaChartView.callBack = this

        binding.tvDateLatest.text = mResponse.lastUpdate?.value
        binding.tvHargaTerakhir.text = mResponse.buyInfo.buyRate?.buyRateString

    }


    private fun setupDataGrafik(range : Int, fromDifferent : Boolean , buyRate : List<GrafikEmasResponse.BuyRateHistory>, sellRate : List<GrafikEmasResponse.SellRateHistory>){
        if (!fromDifferent){
            xStringArray!!.clear()
            yStringArray!!.clear()
            val array = arrayOf(*buyRate.toTypedArray())
            val lastSeven = if (array.size >= range) {
                array.sliceArray(array.size - range until array.size)
            } else {
                array.sliceArray(0 until array.size)
            }

            // Ensure the result array is always of length 'range'

            for (element in lastSeven) {
                xArray = listOf(element.date!!.title!!)
                yArray = listOf(element.buyRate!!.buyRateFloat!!.toInt())
                val stringArray = arrayOf(*xArray.toTypedArray())
                val aStringArray = arrayOf(*yArray.toTypedArray())
                for (string in stringArray) {
                    xStringArray!!.add(string)
                }
                for (string in aStringArray) {
                    yStringArray!!.add(string)
                }
            }
            val min = Collections.min(yStringArray)
            val max = Collections.max(yStringArray)

            minNominal = min - 10
            maxNominal = max + 10
        }else{
            xStringArrayJual!!.clear()
            yStringArrayJual!!.clear()
            val array = arrayOf(*sellRate.toTypedArray())
            val lastSeven = array.sliceArray(array.size - range until array.size)

            for (element in lastSeven) {
                xArray = listOf(element.date!!.title!!)
                yArray = listOf(element.sellRate!!.sellRateFloat!!.toInt())
                val stringArray = arrayOf(*xArray.toTypedArray())
                val aStringArray = arrayOf(*yArray.toTypedArray())
                for (string in stringArray) {
                    xStringArrayJual!!.add(string)
                }
                for (string in aStringArray) {
                    yStringArrayJual!!.add(string)
                }
            }
            val min = Collections.min(yStringArrayJual)
            val max = Collections.max(yStringArrayJual)

            minNominal = min - 10
            maxNominal = max + 10
        }


    }

    private fun setupDataGrafik1Month(range : Int, fromDifferent : Boolean , buyRate : List<GrafikEmasResponse.BuyRateHistory>, sellRate : List<GrafikEmasResponse.SellRateHistory>){
        if (!fromDifferent){
            xStringArray1month!!.clear()
            yStringArray1month!!.clear()
            val array = arrayOf(*buyRate.toTypedArray())
            val lastSeven = if (array.size >= range) {
                array.sliceArray(array.size - range until array.size)
            } else {
                array.sliceArray(0 until array.size)
            }

            for (element in lastSeven) {
                xArray = listOf(element.date!!.title!!)
                yArray = listOf(element.buyRate!!.buyRateFloat!!.toInt())
                val stringArray = arrayOf(*xArray.toTypedArray())
                val aStringArray = arrayOf(*yArray.toTypedArray())
                for (string in stringArray) {
                    xStringArray1month!!.add(string)
                }
                for (string in aStringArray) {
                    yStringArray1month!!.add(string)
                }
            }
            val min = Collections.min(yStringArray1month)
            val max = Collections.max(yStringArray1month)

            minNominal = min - 10
            maxNominal = max + 10
        }else{
            xStringArray1monthJual!!.clear()
            yStringArray1monthJual!!.clear()
            val array = arrayOf(*sellRate.toTypedArray())
            val lastSeven = array.sliceArray(array.size - range until array.size)

            for (element in lastSeven) {
                xArray = listOf(element.date!!.title!!)
                yArray = listOf(element.sellRate!!.sellRateFloat!!.toInt())
                val stringArray = arrayOf(*xArray.toTypedArray())
                val aStringArray = arrayOf(*yArray.toTypedArray())
                for (string in stringArray) {
                    xStringArray1monthJual!!.add(string)
                }
                for (string in aStringArray) {
                    yStringArray1monthJual!!.add(string)
                }
            }
            val min = Collections.min(yStringArray1monthJual)
            val max = Collections.max(yStringArray1monthJual)

            minNominal = min - 10
            maxNominal = max + 10
        }


    }

    private fun setupDataGrafik3month(range : Int, fromDifferent : Boolean , buyRate : List<GrafikEmasResponse.BuyRateHistory>, sellRate : List<GrafikEmasResponse.SellRateHistory>){
        if (!fromDifferent){
            xStringArray3month!!.clear()
            yStringArray3month!!.clear()
            val array = arrayOf(*buyRate.toTypedArray())
            val lastSeven = if (array.size >= range) {
                array.sliceArray(array.size - range until array.size)
            } else {
                array.sliceArray(0 until array.size)
            }

            for (element in lastSeven) {
                xArray = listOf(element.date!!.title!!)
                yArray = listOf(element.buyRate!!.buyRateFloat!!.toInt())
                val stringArray = arrayOf(*xArray.toTypedArray())
                val aStringArray = arrayOf(*yArray.toTypedArray())
                for (string in stringArray) {
                    xStringArray3month!!.add(string)
                }
                for (string in aStringArray) {
                    yStringArray3month!!.add(string)
                }
            }
            val min = Collections.min(yStringArray3month)
            val max = Collections.max(yStringArray3month)

            minNominal = min - 10
            maxNominal = max + 10
        }else{
            xStringArray3monthJual!!.clear()
            yStringArray3monthJual!!.clear()
            val array = arrayOf(*sellRate.toTypedArray())
            val lastSeven = array.sliceArray(array.size - range until array.size)

            for (element in lastSeven) {
                xArray = listOf(element.date!!.title!!)
                yArray = listOf(element.sellRate!!.sellRateFloat!!.toInt())
                val stringArray = arrayOf(*xArray.toTypedArray())
                val aStringArray = arrayOf(*yArray.toTypedArray())
                for (string in stringArray) {
                    xStringArray3monthJual!!.add(string)
                }
                for (string in aStringArray) {
                    yStringArray3monthJual!!.add(string)
                }
            }
            val min = Collections.min(yStringArray3monthJual)
            val max = Collections.max(yStringArray3monthJual)

            minNominal = min - 10
            maxNominal = max + 10
        }


    }

    private fun setupDataGrafik6month(range : Int, fromDifferent : Boolean , buyRate : List<GrafikEmasResponse.BuyRateHistory>, sellRate : List<GrafikEmasResponse.SellRateHistory>){

        if (!fromDifferent){
            xStringArray6month!!.clear()
            yStringArray6month!!.clear()
            val array = arrayOf(*buyRate.toTypedArray())
            val lastSeven = if (array.size >= range) {
                array.sliceArray(array.size - range until array.size)
            } else {
                array.sliceArray(0 until array.size)
            }

            for (element in lastSeven) {
                xArray = listOf(element.date!!.title!!)
                yArray = listOf(element.buyRate!!.buyRateFloat!!.toInt())
                val stringArray = arrayOf(*xArray.toTypedArray())
                val aStringArray = arrayOf(*yArray.toTypedArray())
                for (string in stringArray) {
                    xStringArray6month!!.add(string)
                }
                for (string in aStringArray) {
                    yStringArray6month!!.add(string)
                }

            }
            val min = Collections.min(yStringArray6month)
            val max = Collections.max(yStringArray6month)

            minNominal = min - 10
            maxNominal = max + 10
        }else{
            xStringArray6monthJual!!.clear()
            yStringArray6monthJual!!.clear()
            val array = arrayOf(*sellRate.toTypedArray())
            val lastSeven = array.sliceArray(array.size- range until array.size)

            for (element in lastSeven) {
                xArray = listOf(element.date!!.title!!)
                yArray = listOf(element.sellRate!!.sellRateFloat!!.toInt())
                val stringArray = arrayOf(*xArray.toTypedArray())
                val aStringArray = arrayOf(*yArray.toTypedArray())
                for (string in stringArray) {
                    xStringArray6monthJual!!.add(string)
                }
                for (string in aStringArray) {
                    yStringArray6monthJual!!.add(string)
                }
            }
            val min = Collections.min(yStringArray6monthJual)
            val max = Collections.max(yStringArray6monthJual)

            minNominal = min - 10
            maxNominal = max + 10
        }

    }

    private fun statusButton(button : AppCompatButton, boolean: Boolean) {
        if (boolean){
            button.setTextColor(resources.getColor(R.color.highlightColor))
            button.setBackgroundResource(R.drawable.bg_blue_border_circle)
        }
        else{
            button.setTextColor(resources.getColor(R.color.neutralLight60))
            button.background = null
        }
    }

    override fun chartViewDidFinishLoad(aaChartView: AAChartView) {

    }

    override fun chartViewMoveOverEventMessage(
        aaChartView: AAChartView,
        messageModel: AAMoveOverEventMessageModel
    ) {
        // Ensure that the WebView methods are called on the main thread
        aaChartView.post {
            aaChartView.aa_hideTheSeriesElementContent(0)
            Handler(Looper.getMainLooper()).postDelayed({
                aaChartView.aa_showTheSeriesElementContent(0)
            }, 2000)
        }
    }

    override fun onClick(p0: View?) {
        when(p0!!.id) {
            R.id.btn_1minggu -> {
                //cara pertama
                statusButton(binding.btn1minggu,true)
                statusButton(binding.btn6bulan,false)
                statusButton(binding.btn1bulan,false)
                statusButton(binding.btn3bulan,false)
                isTouchListenerEnabled = true
                if (!mBoolean){
                    xStringArrayTemp = xStringArray
                    yStringArrayTemp = yStringArray
                    GrafikHelperRevamp.setupChart(xStringArrayTemp,yStringArrayTemp,minNominal!!,maxNominal!!,binding.aaChartView)
                }else{
                    xStringArrayTemp = xStringArrayJual
                    yStringArrayTemp = yStringArrayJual
                    GrafikHelperRevamp.setupChart(xStringArrayTemp,yStringArrayTemp,minNominal!!,maxNominal!!,binding.aaChartView)
                }


            }
            R.id.btn_1bulan -> {
                statusButton(binding.btn1bulan,true)
                statusButton(binding.btn1minggu,false)
                statusButton(binding.btn6bulan,false)
                statusButton(binding.btn3bulan,false)
                isTouchListenerEnabled = true
                if (!mBoolean){
                    xStringArrayTemp = xStringArray1month
                    yStringArrayTemp = yStringArray1month
                    GrafikHelperRevamp.setupChart(xStringArray1month,yStringArray1month,minNominal!!,maxNominal!!,binding.aaChartView)
                }else{
                    xStringArrayTemp = xStringArray1monthJual
                    yStringArrayTemp = yStringArray1monthJual
                    GrafikHelperRevamp.setupChart(xStringArray1monthJual,yStringArray1monthJual,minNominal!!,maxNominal!!,binding.aaChartView)
                }

            }
            R.id.btn_3bulan -> {
                statusButton(binding.btn3bulan,true)
                statusButton(binding.btn1minggu,false)
                statusButton(binding.btn1bulan,false)
                statusButton(binding.btn6bulan,false)
                isTouchListenerEnabled = true
                if (!mBoolean){
                    xStringArrayTemp = xStringArray3month
                    yStringArrayTemp = yStringArray3month
                    GrafikHelperRevamp.setupChart(xStringArrayTemp,yStringArrayTemp,minNominal!!,maxNominal!!,binding.aaChartView)
                }else{
                    xStringArrayTemp = xStringArray3monthJual
                    yStringArrayTemp = yStringArray3monthJual
                    GrafikHelperRevamp.setupChart(xStringArrayTemp,yStringArrayTemp,minNominal!!,maxNominal!!,binding.aaChartView)
                }
            }
            R.id.btn_6bulan ->{
                statusButton(binding.btn6bulan,true)
                statusButton(binding.btn1minggu,false)
                statusButton(binding.btn1bulan,false)
                statusButton(binding.btn3bulan,false)
                isTouchListenerEnabled = true
                if (!mBoolean){
                    xStringArrayTemp = xStringArray6month
                    yStringArrayTemp = yStringArray6month
                    GrafikHelperRevamp.setupChart(xStringArrayTemp,yStringArrayTemp,minNominal!!,maxNominal!!,binding.aaChartView)
                }else{
                    xStringArrayTemp = xStringArray6monthJual
                    yStringArrayTemp = yStringArray6monthJual
                    GrafikHelperRevamp.setupChart(xStringArrayTemp,yStringArrayTemp,minNominal!!,maxNominal!!,binding.aaChartView)
                }
            }

            R.id.btn_buy -> {
                onListener.onBuyGold()
            }
        }
    }
    interface OnItemClickListener {
        fun onBuyGold()
    }
}