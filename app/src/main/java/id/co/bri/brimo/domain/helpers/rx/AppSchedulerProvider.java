package id.co.bri.brimo.domain.helpers.rx;

import io.reactivex.Scheduler;
import io.reactivex.android.schedulers.AndroidSchedulers;
import io.reactivex.schedulers.Schedulers;

/**
 * Created by hapis on 03/03/2020.
 */

public class AppSchedulerProvider implements SchedulerProvider {

    @Override
    public Scheduler mainThread() {
        return AndroidSchedulers.mainThread();
    }

    @Override
    public Scheduler computation() {
        return Schedulers.computation();
    }

    @Override
    public Scheduler newThread() {
        return Schedulers.newThread();
    }


    @Override
    public Scheduler io() {
        return Schedulers.io();
    }


    @Override
    public Scheduler single() {
        return Schedulers.single();
    }

}
