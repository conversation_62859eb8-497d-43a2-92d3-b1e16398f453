package id.co.bri.brimo.ui.fragments.voucher;

import android.os.Bundle;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.fragment.app.DialogFragment;
import androidx.fragment.app.FragmentActivity;
import androidx.recyclerview.widget.LinearLayoutManager;

import com.google.android.material.bottomsheet.BottomSheetDialogFragment;

import java.util.List;

import id.co.bri.brimo.R;
import id.co.bri.brimo.adapters.voucher.SnkVoucherAdapter;
import id.co.bri.brimo.databinding.FragmentInformasiTambahanBinding;
import id.co.bri.brimo.models.apimodel.response.voucher.VoucherDetail;
import id.co.bri.brimo.ui.activities.voucher.SnkVoucherActivity;

public class InformasiTambahanFragment extends BottomSheetDialogFragment implements SnkVoucherAdapter.OnClickItem {

    private FragmentInformasiTambahanBinding binding;
    private FragmentActivity fragmentActivity;
    private SnkVoucherAdapter adapter;
    private List<VoucherDetail.TermCondition> termConditionList;

    public InformasiTambahanFragment(FragmentActivity mActivity, List<VoucherDetail.TermCondition> mTermConditionList) {
        this.fragmentActivity = mActivity;
        this.termConditionList = mTermConditionList;
    }

    @Override
    public void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setStyle(DialogFragment.STYLE_NORMAL, R.style.CustomBottomSheetDialogThemeInput);
    }

    @Override
    public View onCreateView(LayoutInflater inflater, ViewGroup container,
                             Bundle savedInstanceState) {
        binding = FragmentInformasiTambahanBinding.inflate(getLayoutInflater());

        adapter = new SnkVoucherAdapter(requireActivity(), termConditionList, this, true);
        adapter.notifyDataSetChanged();

        binding.rvSnk.setHasFixedSize(true);
        binding.rvSnk.setLayoutManager(new LinearLayoutManager(getActivity()));
        binding.rvSnk.setAdapter(adapter);
        return binding.getRoot();
    }

    @Override
    public void onViewCreated(@NonNull View view, @Nullable Bundle savedInstanceState) {
        super.onViewCreated(view, savedInstanceState);
    }

    @Override
    public void onClickSnk(@NonNull List<? extends VoucherDetail.TermCondition> productSnk, int position) {
        SnkVoucherActivity.launchIntent(fragmentActivity, productSnk.get(position).getStyle(), productSnk.get(position).getValue());
    }
}