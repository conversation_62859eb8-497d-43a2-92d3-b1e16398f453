package id.co.bri.brimo.ui.activities.onboardingrevamp

import android.annotation.SuppressLint
import android.app.Activity
import android.content.Intent
import android.content.IntentFilter
import android.os.Build
import android.os.Bundle
import android.os.CountDownTimer
import android.util.Log
import android.view.KeyEvent
import android.view.View
import androidx.activity.OnBackPressedCallback
import androidx.activity.result.ActivityResultLauncher
import androidx.activity.result.contract.ActivityResultContracts
import androidx.core.content.ContextCompat
import androidx.core.text.HtmlCompat
import androidx.recyclerview.widget.GridLayoutManager
import com.google.android.gms.auth.api.phone.SmsRetriever
import com.google.gson.Gson
import id.co.bri.brimo.R
import id.co.bri.brimo.adapters.PinNumberAdapter
import id.co.bri.brimo.adapters.baseadapter.base.BasePinAdapter
import id.co.bri.brimo.adapters.pinadapter.OtpRevampAdapter
import id.co.bri.brimo.contract.IPresenter.onboardingrevamp.IOnboardingOtpPresenter
import id.co.bri.brimo.contract.IView.onboardingrevamp.IOnboardingOtpView
import id.co.bri.brimo.databinding.ActivityOnboardingOtpBinding
import id.co.bri.brimo.domain.config.Constant
import id.co.bri.brimo.domain.helpers.GeneralHelper
import id.co.bri.brimo.models.apimodel.request.onboardingrevamp.OnboardingResendReq
import id.co.bri.brimo.models.apimodel.request.onboardingrevamp.OnboardingValidateReq
import id.co.bri.brimo.models.apimodel.response.onboardingrevamp.OnboardingOtpRes
import id.co.bri.brimo.ui.activities.base.BaseActivity
import id.co.bri.brimo.ui.customviews.pin.InsertPinNumbers
import id.co.bri.brimo.ui.fragments.registrasi.BottomDialogKirimUlangFragment
import id.co.bri.brimo.util.broadcastreceiver.smsretriever.SmsRetrieverReceiver
import javax.inject.Inject

class OnboardingOtpActivity : BaseActivity(),
    IOnboardingOtpView,
    View.OnKeyListener,
    PinNumberAdapter.OnPinNumberListener,
    BasePinAdapter.PinAdapterListener,
    BottomDialogKirimUlangFragment.OnBackPressUlangi {

    companion object {
        private const val TAG = "OnboardingOtpActivity"
    }

    private val binding by lazy(LazyThreadSafetyMode.NONE) {  ActivityOnboardingOtpBinding.inflate(layoutInflater) }

    @Inject
    lateinit var presenter: IOnboardingOtpPresenter<IOnboardingOtpView>

    private var onboardingOtpResponse: OnboardingOtpRes? = null
    private var countDownTimer: CountDownTimer? = null
    private val second = 1000

    private var methodCheck: String? = null

    private var onboardingId: String = ""

    private var otpRevampAdapter: OtpRevampAdapter? = null
    private lateinit var pinNumberAdapter: PinNumberAdapter
    private lateinit var pinOtpLayoutManager: GridLayoutManager
    private lateinit var pinPadLayoutManager: GridLayoutManager

    private val smsRetrieverReceiver = SmsRetrieverReceiver()

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContentView(binding.root)

        onBackPressedDispatcher.addCallback(this, onBackPressedCallback)

        injectDependency()
        intentExtra()
        setupView()
        setEventAppsFlyerFirstInit()
        runSmsRetriever()
        setOtpSmsRetriever()
    }

    private fun setEventAppsFlyerFirstInit() {
        val eventValue: MutableMap<String, Any> = HashMap()
        eventValue[Constant.CUSTOMER_ID] = presenter.persistenceId
        trackAppsFlyerAnalyticEvent("openaccount_otp_phonenumber", eventValue)
    }

    private fun intentExtra() {
        onboardingOtpResponse = Gson().fromJson(intent.getStringExtra(Constant.GENRES), OnboardingOtpRes::class.java)
    }

    private fun injectDependency() {
        activityComponent.inject(this)
        presenter.view = this
        presenter.start()
        presenter.setUrlResend(GeneralHelper.getString(R.string.url_onboarding_resend_sms_v3))
        presenter.setUrlSend(GeneralHelper.getString(R.string.url_onboarding_validate_sms_v3))
        onboardingId = presenter.getDeviceId()
    }

    private fun setupView() {
        GeneralHelper.setToolbarRevamp(
            this,
            binding.toolbarRevamp.toolbar,
            GeneralHelper.getString(R.string.verifikasi_no_handphone_regis)
        )

        binding.layoutStep.imgStep1.setImageResource(R.drawable.bg_step_orange)
        binding.layoutStep.imgStep2.setImageResource(R.drawable.bg_step_orange)

        checkWaOrSms(onboardingOtpResponse?.method.orEmpty())

        otpRevampAdapter = OtpRevampAdapter(this, 3)
        otpRevampAdapter?.onCompleteOtpListener = { otp ->
            presenter.sendSendOtp(OnboardingValidateReq(onboardingId, otp))
        }

        pinNumberAdapter = PinNumberAdapter(InsertPinNumbers.getPinNumberList(this))
        pinOtpLayoutManager = GridLayoutManager(this, 6)
        pinPadLayoutManager = GridLayoutManager(this, 3)

        pinNumberAdapter.onPinNumberListener = this
        otpRevampAdapter?.setListener(this)
        binding.rvBox.layoutManager = pinOtpLayoutManager
        binding.rvBox.adapter = otpRevampAdapter
        binding.rvInputOtp.layoutManager = pinPadLayoutManager
        binding.rvInputOtp.adapter = pinNumberAdapter

        setTextTimer(onboardingOtpResponse?.expiredIinSecond ?: 0)

        binding.tvUlangi.setOnClickListener {
            bottomSheetResending()
        }
    }

    private fun runSmsRetriever() {
        val client = SmsRetriever.getClient(this).startSmsRetriever()
        client.addOnSuccessListener {
            if (!GeneralHelper.isProd()) Log.e(TAG, "Successfully running sms retriever")
        }

        client.addOnFailureListener {
            if (!GeneralHelper.isProd()) Log.e(TAG, "Failed running sms retriever")
        }
    }

    private fun setOtpSmsRetriever() {
        smsRetrieverReceiver.onOtpReceived = { result ->
            when (result) {
                is SmsRetrieverReceiver.OTPResult.OTPReceived -> otpRevampAdapter?.setOtp(result.otp)
                is SmsRetrieverReceiver.OTPResult.OTPNotReceived -> Log.e(TAG, "collectOtpResult: ${result.error}")
            }
        }
    }

    private fun bottomSheetResending() {
        val dialogFragment = BottomDialogKirimUlangFragment(this)
        dialogFragment.show(supportFragmentManager, "")
        dialogFragment.isCancelable = true
    }

    private fun setTextTimer(timer: Int) {
        val countDown: Int = second * timer
        countDownTimer?.cancel() // Cancel any existing count down
        countDownTimer = object : CountDownTimer(countDown.toLong(), second.toLong()) {
            override fun onTick(millisUntilFinished: Long) {
                val seconds: Int = millisUntilFinished.toInt() / second
                val timeFormat = GeneralHelper.getTimeFormat(seconds)
                binding.tvTimer.text = String.format(
                    resources.getString(R.string.countdown_otp00_00),
                    timeFormat[1], timeFormat[2]
                )

                binding.tvUlangi.setTextColor(
                    ContextCompat.getColor(
                        applicationContext,
                        R.color.neutral_light50
                    )
                )
                binding.tvUlangi.isEnabled = false
            }

            override fun onFinish() {
                binding.tvTimer.text = GeneralHelper.getString(R.string.time00_00)
                binding.tvUlangi.setTextColor(
                    ContextCompat.getColor(
                        applicationContext,
                        R.color.neutral_light10
                    )
                )
                binding.tvUlangi.isEnabled = true
            }
        }.start()
    }

    private fun checkWaOrSms(method: String) {
        val descStringRes = when (method) {
            Constant.WHATSAPP -> R.string.desc_regis_otp_wa
            else -> R.string.desc_regis_otp_sms
        }

        binding.tvDescOtp.text = HtmlCompat.fromHtml(
            String.format(
                GeneralHelper.getString(descStringRes),
                onboardingOtpResponse?.phone
            ), HtmlCompat.FROM_HTML_MODE_LEGACY
        )

        runSmsRetriever()
    }

    override fun onSuccessResend(onboardingOtpResponse: OnboardingOtpRes) {
        if (countDownTimer != null) countDownTimer?.cancel()
        this.onboardingOtpResponse = onboardingOtpResponse
        checkWaOrSms(onboardingOtpResponse.method.orEmpty())
        setTextTimer(onboardingOtpResponse.expiredIinSecond)

    }

    override fun onSuccessSend(stringResponse: String) {
        setEventAppsFlyerSuccessSendOTP()
        val intent = Intent(this, OnboardingVerifyEmailActivity::class.java)
        intent.putExtra(Constant.GENRES, stringResponse)
        startActivityIntent.launch(intent)
    }

    private fun setEventAppsFlyerSuccessSendOTP() {
        val eventValue: MutableMap<String, Any> = HashMap()
        eventValue[Constant.CUSTOMER_ID] = presenter.persistenceId
        trackAppsFlyerAnalyticEvent("openaccount_otp_phonenumber_success", eventValue)
    }

    override fun deletePin() {
        otpRevampAdapter?.deleteAllInsertedPin()
    }

    override fun onKey(v: View?, keyCode: Int, event: KeyEvent?): Boolean {
        return false
    }

    override fun onPinClicked(pinNumber: Int) {
        otpRevampAdapter?.addInsertedPin(pinNumber.toString())
    }

    override fun onDeleteClicked() {
        otpRevampAdapter?.deleteInsertedPin()
    }

    @SuppressLint("NotifyDataSetChanged")
    override fun notifyChanges() {
        otpRevampAdapter?.notifyDataSetChanged()
    }

    override fun onComplete(string: String) {
        // do nothing
    }

    override fun itemResending(method: String) {
        methodCheck = method
        presenter.sendResendOtp(OnboardingResendReq(onboardingId, method))
    }

    private var startActivityIntent: ActivityResultLauncher<Intent> = registerForActivityResult(
        ActivityResultContracts.StartActivityForResult()
    ) { result ->
        if (result.resultCode == Activity.RESULT_CANCELED && result.data != null) {
            setResult(RESULT_CANCELED, result.data)
            finish()
        }
    }

    private val onBackPressedCallback: OnBackPressedCallback =
        object : OnBackPressedCallback(true) {
            override fun handleOnBackPressed() {
                val intent = Intent()
                intent.putExtra(Constant.CHECK_POINT, 3)
                setResult(Activity.RESULT_CANCELED, intent)
                finish()
            }
        }

    override fun onStart() {
        super.onStart()

        val intentFilter = IntentFilter(SmsRetriever.SMS_RETRIEVED_ACTION)
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU) {
            registerReceiver(smsRetrieverReceiver, intentFilter, RECEIVER_EXPORTED)
        } else {
            @Suppress("UnspecifiedRegisterReceiverFlag")
            registerReceiver(smsRetrieverReceiver, intentFilter)
        }
    }

    override fun onStop() {
        super.onStop()
        unregisterReceiver(smsRetrieverReceiver)
    }

    override fun onDestroy() {
        presenter.stop()
        countDownTimer?.cancel()
        otpRevampAdapter = null
        super.onDestroy()
    }
}