package id.co.bri.brimo.models.apimodel.request;

import com.google.gson.annotations.Expose;
import com.google.gson.annotations.SerializedName;

public class UpdateEditAlias {

    @SerializedName("account")
    @Expose
    private String account;

    @SerializedName("alias")
    @Expose
    private String alias;

    public UpdateEditAlias(String account, String alias) {
        this.account = account;
        this.alias = alias;
    }

    public String getAccount() {
        return account;
    }

    public void setAccount(String account) {
        this.account = account;
    }

    public String getAlias() {
        return alias;
    }

    public void setAlias(String alias) {
        this.alias = alias;
    }
}
