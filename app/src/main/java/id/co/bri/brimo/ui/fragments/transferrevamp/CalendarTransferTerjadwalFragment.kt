package id.co.bri.brimo.ui.fragments.transferrevamp

import android.app.Dialog
import android.content.DialogInterface
import android.os.Bundle
import android.util.Log
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.TextView
import androidx.core.content.ContextCompat
import com.google.android.material.bottomsheet.BottomSheetBehavior
import com.google.android.material.bottomsheet.BottomSheetDialog
import com.google.android.material.bottomsheet.BottomSheetDialogFragment
import com.google.gson.Gson
import com.kizitonwose.calendarview.model.CalendarDay
import com.kizitonwose.calendarview.model.DayOwner
import com.kizitonwose.calendarview.ui.DayBinder
import com.kizitonwose.calendarview.ui.ViewContainer
import com.kizitonwose.calendarview.utils.next
import com.kizitonwose.calendarview.utils.previous
import com.kizitonwose.calendarview.utils.yearMonth
import id.co.bri.brimo.R
import id.co.bri.brimo.databinding.FragmentCalendarTransferTerjadwalBinding
import id.co.bri.brimo.domain.config.Constant
import id.co.bri.brimo.domain.helpers.GeneralHelper
import id.co.bri.brimo.domain.helpers.calendar.daysOfWeekFromLocale
import id.co.bri.brimo.domain.helpers.calendar.makeInVisible
import id.co.bri.brimo.domain.helpers.calendar.makeVisible
import id.co.bri.brimo.domain.helpers.calendar.setTextColorRes
import org.threeten.bp.DayOfWeek
import org.threeten.bp.LocalDate
import org.threeten.bp.YearMonth
import org.threeten.bp.temporal.TemporalAdjusters
import java.time.temporal.TemporalAdjusters.lastDayOfMonth

class CalendarTransferTerjadwalFragment(onSelectDate: OnSelectDate, type: String, aftType: String, calendarTitle: String,
                                        var onShow: (Boolean) -> Unit) : BottomSheetDialogFragment() {

    private val TAG = "SetCalendarFragment"

    private lateinit var binding: FragmentCalendarTransferTerjadwalBinding

    private val today = LocalDate.now()
    private val monthTitleFormatter = org.threeten.bp.format.DateTimeFormatter.ofPattern("MMMM")
    private val dateFormatter = org.threeten.bp.format.DateTimeFormatter.ofPattern("dd-MM-yyyy")

    private var tempDate: LocalDate? = null
    private var startDate: LocalDate? = null
    private var endDate: LocalDate? = null
    private var startDateString: String? = null
    private var endDateString: String? = null

    private var selectDate: OnSelectDate? = onSelectDate
    private var mType: String = type
    private var mAftType: String = aftType
    private var mTitle: String = calendarTitle

    private lateinit var daysOfWeek: Array<DayOfWeek>

    private var multipleDays = false

    private var getStartDate = false
    private var getEndDate = false
    private var maxToday = false
    private var debetDate = false
    private var mSelectedDate : LocalDate? = null

    private var threeMonthsBefore: LocalDate? = null
    private var threeMonthsAfter: LocalDate? = null

    private var minDate : LocalDate? = null
    private var maxDate : LocalDate? = null
    private var minDateString : String? = null
    private var maxDateString : String? = null
    private var mTempTextView : TextView? = null
    private var mTempRoundBgView : View? = null
    private var mLeftGreyArea : View? = null
    private var mRightGreyArea : View? = null
    private var mTempSelectedDate : LocalDate? = null
    private var mTempSelectedDateString : String? = null
    private var mWeeklyDay : Int? = null
    private var mCurrentDate: LocalDate = LocalDate.now()
    private var mStartBlockedDate: String? = null
    private var mEndBlockedDate: String? = null
    private var mIsUnlimitedEnable : Boolean? = false
    private var mIsUnlimitedChecked : Boolean = false

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setStyle(STYLE_NORMAL, R.style.CustomBottomSheetDialogThemeMaterial)
    }

    interface OnSelectDate {
        fun onDateselect(dateSelect: LocalDate, type: String, isUnlimited: Boolean)
    }

    override fun onCreateView(
        inflater: LayoutInflater, container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        (dialog as? BottomSheetDialog)?.behavior?.state = BottomSheetBehavior.STATE_EXPANDED
        // Inflate the layout for this fragment
        binding = FragmentCalendarTransferTerjadwalBinding.inflate(inflater, container, false)
        return binding.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)

        val bundle: Bundle? = arguments
        if (bundle != null){
            multipleDays = bundle.getBoolean(Constant.TAG_PICK_DATE)
            getStartDate = bundle.getBoolean(Constant.TAG_PICK_START_DATE)
            getEndDate = bundle.getBoolean(Constant.TAG_PICK_END_DATE)
            maxToday = bundle.getBoolean(Constant.TAG_MAX_TODAY)
            startDateString = bundle.getString(Constant.TAG_START_DATE)
            endDateString = bundle.getString(Constant.TAG_END_DATE)
            debetDate = bundle.getBoolean(Constant.TAG_DEBET_DATE)
            minDateString = bundle.getString(Constant.TAG_MIN_DATE)
            maxDateString = bundle.getString(Constant.TAG_MAX_DATE)
            mWeeklyDay = bundle.getInt(Constant.TAG_CURRENT_DAY)
            mTempSelectedDateString = bundle.getString(Constant.TAG_SELECTED_DATE)
            mIsUnlimitedChecked = bundle.getBoolean(Constant.TAG_IS_CHECKED_UNLIMITED)
            mIsUnlimitedEnable = bundle.getBoolean(Constant.TAG_IS_ENABLE_UNLIMITED)
        }

        daysOfWeek = daysOfWeekFromLocale()

        // set current Start Date
        if (startDateString != null && !startDateString.equals("")) {
            try {
                startDate = LocalDate.parse(startDateString)
            } catch (e: Exception) {
                if (!GeneralHelper.isProd()) {
                    Log.e(TAG, "startDate LocalDate.parse: ", e)
                }
            }
        }

        // set min date picker
        if (minDateString != null && !minDateString.equals("")) {
            try {
                minDate = LocalDate.parse(minDateString)
            } catch (e: Exception) {
                if (!GeneralHelper.isProd()) {
                    Log.e(TAG, "startDate LocalDate.parse: ", e)
                }
            }
        }

        // set min date picker
        if (maxDateString != null && !maxDateString.equals("")) {
            try {
                maxDate = LocalDate.parse(maxDateString)
            } catch (e: Exception) {
                if (!GeneralHelper.isProd()) {
                    Log.e(TAG, "startDate LocalDate.parse: ", e)
                }
            }
        }

        // set current End Date
        if (endDateString != null && !endDateString.equals("")) {
            try {
                endDate = LocalDate.parse(endDateString)
            } catch (e: Exception) {
                if (!GeneralHelper.isProd()) {
                    Log.e(TAG, "endDate LocalDate.parse: ", e)
                }
            }
        }

        // set current temp date
        if (mTempSelectedDateString != null && !mTempSelectedDateString.equals("")) {
            try {
                mTempSelectedDate = LocalDate.parse(mTempSelectedDateString)
            } catch (e: Exception) {
                if (!GeneralHelper.isProd()) {
                    Log.e(TAG, "endDate LocalDate.parse: ", e)
                }
            }
        }

        setupCalendar()
        setupButton()
        setupView()
    }

    private fun setupView(){
        if (mTitle.isNotEmpty()){
            binding.tvSearchTitle.text = mTitle
        }
        binding.checkboxSave.isChecked = mIsUnlimitedChecked
    }

    override fun onCreateDialog(savedInstanceState: Bundle?): Dialog {
        val dialog = BottomSheetDialog(requireContext(), theme)
        return dialog
    }

    private fun onCheckboxChange(isChecked: Boolean){
        mIsUnlimitedChecked = isChecked
        setupCalendarBinder()
        binding.checkboxSave.isChecked = isChecked
        checkDate(null)
    }

    private fun setupButton() {
        if  (mAftType.equals(Constant.AFT_SCHEDULE_WEEKLY) && mType.equals(Constant.DATE_PICKER_END)){
            binding.llCheckboxUnlimited.visibility = View.VISIBLE
        }
        binding.checkboxSave.setOnCheckedChangeListener { buttonView, isChecked ->
            onCheckboxChange(isChecked)
        }
        binding.tvUnlimitedCheckboxSave.setOnClickListener{binding.checkboxSave.toggle()}
        binding.btPilihDurasi.text = resources.getString(R.string.hinttanggal)

        binding.btPilihDurasi.setOnClickListener {
            val startDate = startDate
            if (mIsUnlimitedChecked){
                val selectedDate =
                if (mType == Constant.DATE_PICKER_END && minDate != null){
                    LocalDate.parse(
                        "2999-" + String.format(
                            "%02d",
                            minDate?.monthValue
                        ) + "-" + String.format("%02d", minDate?.dayOfMonth)
                    )
                }else{
                    LocalDate.parse(
                        "2999-"+"01"+"-01"
                    )
                }
                returnSingleDatePicked(selectedDate)
            }else{
                returnSingleDatePicked(startDate!!)
            }

        }
    }

    /**
     * Returning picked date value into previous activity using bundle
     */


    private fun returnSingleDatePicked(startDate: LocalDate) {
        mSelectedDate = startDate
        selectDate?.onDateselect(startDate, mType, mIsUnlimitedChecked)
        dismiss()
    }



    private fun setupCalendar() {
        setupCalendarView()
        setupCalendarNavigation()
    }


    private fun setupCalendarNavigation() {
        binding.ibPreviousMonthFrag.setOnClickListener {
            binding.calendarView.findFirstVisibleMonth()?.let {
                binding.calendarView.smoothScrollToMonth(it.yearMonth.previous)
            }
        }

        binding.ibNextMonthFrag.setOnClickListener {
            binding.calendarView.findFirstVisibleMonth()?.let {
                binding.calendarView.scrollToMonth(it.yearMonth.next)
            }
        }

        binding.ibPreviousYearFrag.setOnClickListener {
            binding.calendarView.findFirstVisibleMonth()?.let {
                binding.calendarView.scrollToMonth(it.yearMonth.minusYears(1))
            }
        }

        binding.ibNextYearFrag.setOnClickListener {
            binding.calendarView.findFirstVisibleMonth()?.let {
                binding.calendarView.scrollToMonth(it.yearMonth.plusYears(1))
            }
        }
        val currentDate = LocalDate.now()
        if (currentDate == currentDate.with(TemporalAdjusters.lastDayOfMonth())){
            binding.calendarView.scrollToMonth(currentDate.yearMonth.plusMonths(1))
        }
    }



    private fun setupCalendarView() {
        setupMonth()
        setupCalendarBinder()
    }

    private fun setupCalendarBinder() {
        class DayViewContainer(view: View) : ViewContainer(view) {
            // Will be set when this container is bound. See the dayBinder.
            lateinit var day: CalendarDay
            val textView = view.findViewById<TextView>(R.id.tvCalendarDay)
            val roundBgView = view.findViewById<View>(R.id.calendarDayView)
            val leftGreyArea = view.findViewById<View>(R.id.leftGreyArea)
            val rightGreyArea = view.findViewById<View>(R.id.rightGreyArea)

        }

        setupScrollListener()

        binding.calendarView.dayBinder = object : DayBinder<DayViewContainer> {
            override fun create(view: View): DayViewContainer {
                return DayViewContainer(view)
            }

            override fun bind(container: DayViewContainer, day: CalendarDay) {
                container.day = day
                val textView = container.textView
                val roundBgView = container.roundBgView
                val leftGreyArea = container.leftGreyArea
                val rightGreyArea = container.rightGreyArea
                day.date.yearMonth

                //Make default container to be empty before binding the dates in
                textView.text = day.day.toString()
                textView.background = null
                roundBgView.makeInVisible()
                leftGreyArea.makeInVisible()
                rightGreyArea.makeInVisible()


                textView.setOnClickListener{
                    if (mTempTextView != null && mTempRoundBgView != null && mLeftGreyArea != null && mRightGreyArea != null ){
                        mTempTextView?.background = null
                        mTempRoundBgView?.makeInVisible()
                        mLeftGreyArea?.makeInVisible()
                        mRightGreyArea?.makeInVisible()
                        if (day.owner == DayOwner.THIS_MONTH){
                            mTempTextView?.setTextColorRes(R.color.colorBlack)
                        }else{
                            mTempTextView?.setTextColorRes(R.color.greyColor)
                        }
                    }

                    if (mTempSelectedDate != day.date){
                        binding.calendarView.scrollToMonth(day.date.yearMonth)
                        setActiveDate(true, textView, leftGreyArea, rightGreyArea, roundBgView)
                        mTempTextView = textView
                        mTempRoundBgView = roundBgView
                        mLeftGreyArea = leftGreyArea
                        mRightGreyArea = rightGreyArea
                        mTempSelectedDate = day.date;
                        onCheckboxChange(false)
                    }else{
                        mTempSelectedDate = null
                        mTempTextView = null
                        setActiveDate(false, textView, leftGreyArea, rightGreyArea, roundBgView)
                    }

                    checkDate(day)
                }


                /*jika outdate*/
                setEnableDate(false, textView)

                if (day.date == mCurrentDate){
                    if (context == null) return
                    textView.background = ContextCompat.getDrawable(context!!, R.drawable.bg_circle_border)
                    roundBgView.makeInVisible()
                    leftGreyArea.makeInVisible()
                    rightGreyArea.makeInVisible()
                }


                if (day.owner != DayOwner.THIS_MONTH){
                    return
                }


                if (day.date <= mCurrentDate){
                    return
                }
//                // min date check
                if (minDate != null && day.date <= minDate){
                    return
                }
                // max date check
                if (maxDate != null && day.date >= maxDate){
                    return
                }

                if (mAftType.equals(Constant.AFT_SCHEDULE_WEEKLY) && mWeeklyDay != null && mWeeklyDay != 0){
                    if (day.date.dayOfWeek.value != mWeeklyDay){
                        return
                    }
                    if (maxDate != null && day.date >= maxDate){
                        return
                    }
                }



                setEnableDate(true, textView)
                /*set active date*/
                if (mTempSelectedDate != null && mTempSelectedDate?.equals(day.date) == true && !mIsUnlimitedChecked){
                    setActiveDate(true, textView, leftGreyArea, rightGreyArea, roundBgView)
                    mTempTextView = textView
                    mTempRoundBgView = roundBgView
                    mLeftGreyArea = leftGreyArea
                    mRightGreyArea = rightGreyArea
                    mTempSelectedDate = day.date;
                    checkDate(day)
                }

            }


        }
    }

    private fun setEnableDate(isEnable: Boolean, textView: TextView){
        if (isEnable){
            textView.setTextColorRes(R.color.neutral_dark40)
            textView.isClickable = true
            textView.isFocusable = true
        }else{
            textView.setTextColorRes(R.color.neutral_light40)
            textView.isClickable = false
            textView.isFocusable = false
        }
    }

    private fun checkDate(day : CalendarDay?){
        binding.btPilihDurasi.isEnabled = false
        binding.btPilihDurasi.setTextColor(ContextCompat.getColor(requireActivity(), R.color.neutral_light40))

        if (!mIsUnlimitedChecked){
            if (day == null){
                return
            }

            if (mTempSelectedDate == null){
                return
            }
        }

        mSelectedDate = day?.date

        startDate = day?.date
        //Formatting
        binding.btPilihDurasi.isEnabled = true
        binding.btPilihDurasi.setTextColor(ContextCompat.getColor(requireActivity(), R.color.whiteColor))
    }

    fun setActiveDate(isActive: Boolean, textView: TextView, leftGreyArea: View, rightGreyArea: View, roundBgView: View){
        if (isActive){
            textView.setTextColorRes(R.color.white)
            leftGreyArea.makeInVisible()
            rightGreyArea.makeInVisible()
            roundBgView.makeVisible()
            roundBgView.setBackgroundResource(R.drawable.example_4_single_selected_bg)
        }else{
            textView.setTextColorRes(R.color.black)
            textView.background = null
            roundBgView.makeInVisible()
            leftGreyArea.makeInVisible()
            rightGreyArea.makeInVisible()
        }
    }

    /**
     * Setup scroll listener of calendar view and change the month-year display according to its adapter position
     */
    private fun setupScrollListener() {
        binding.calendarView.monthScrollListener = { month ->
            binding.tvMonthFrag.text = monthTitleFormatter.format(month.yearMonth)
            binding.tvYearFrag.text = "${month.yearMonth.year}"
        }
    }

    private fun setupMonth() {
        var totalPreviousMonthsDisplayed: Long = 360
        var totalNextMonthsDisplayed: Long = 360
        val currentMonth = YearMonth.now()
        val startDate = startDate
        val endDate = endDate

        if (maxToday) {
            totalNextMonthsDisplayed = 0
        }

        if (debetDate) {
            totalNextMonthsDisplayed = 1
            totalPreviousMonthsDisplayed = 0
        }

        val startMonth = currentMonth.minusMonths(totalPreviousMonthsDisplayed)
        val endMonth = currentMonth.plusMonths(totalNextMonthsDisplayed)

        binding.calendarView.setup(startMonth, endMonth, daysOfWeek[1])

        if (getStartDate && startDate != null) {
            val starMonth = startDate.yearMonth
            binding.calendarView.scrollToMonth(starMonth)
        } else if (getEndDate && endDate != null) {
            val endMonthYear = endDate.yearMonth
            binding.calendarView.scrollToMonth(endMonthYear)
        } else {
            binding.calendarView.scrollToMonth(currentMonth)
        }
    }

    fun showSnackbarErrorMessage(message: String?) {
        GeneralHelper.showSnackBar(binding.content, message!!)
    }

    override fun onDismiss(dialog: DialogInterface) {
        onShow(false);
        super.onDismiss(dialog)
    }

    override fun onStart() {
        onShow(true)
        super.onStart()
    }
}