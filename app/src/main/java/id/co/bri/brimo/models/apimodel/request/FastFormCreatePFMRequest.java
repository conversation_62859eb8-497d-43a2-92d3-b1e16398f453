package id.co.bri.brimo.models.apimodel.request;

import com.google.gson.annotations.SerializedName;

public class FastFormCreatePFMRequest extends FastMenuRequest {
    @SerializedName("trx_type_pfm")
    private String trxTypePFM;

    public FastFormCreatePFMRequest(String username, String tokenKey) {
        super(username, tokenKey);
    }

    public FastFormCreatePFMRequest(FastMenuRequest request, String trxType) {
        super(request.getUsername(),request.getTokenKey());
        this.trxTypePFM = trxType;
    }

    public String getTrxTypePFM() {
        return trxTypePFM;
    }

    public void setTrxTypePFM(String trxTypePFM) {
        this.trxTypePFM = trxTypePFM;
    }
}
