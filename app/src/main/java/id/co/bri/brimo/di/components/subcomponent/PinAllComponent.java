package id.co.bri.brimo.di.components.subcomponent;

import dagger.Subcomponent;
import id.co.bri.brimo.adapters.PinNumberAdapter;
import id.co.bri.brimo.adapters.PinNumberAdapterNewSkin;
import id.co.bri.brimo.adapters.pinadapter.PinHiddenAdapter;
import id.co.bri.brimo.di.modules.fragment.PinAllModule;
import id.co.bri.brimo.di.scopes.PerForm;
import id.co.bri.brimo.ui.activities.BlokirKartuActivity;
import id.co.bri.brimo.ui.activities.KonfirmasiPinActivity;
import id.co.bri.brimo.ui.activities.LupaPinConfirmActivity;
import id.co.bri.brimo.ui.activities.LupaPinCreateActivity;
import id.co.bri.brimo.ui.activities.LupaPinKonfirmasiActivity;
import id.co.bri.brimo.ui.activities.LupaPinOtpActivity;
import id.co.bri.brimo.ui.activities.LupaPinVerifEmailActivity;
import id.co.bri.brimo.ui.activities.UbahPasswordOtpActivity;
import id.co.bri.brimo.ui.activities.UbahPinActivity;
import id.co.bri.brimo.ui.activities.UbahPinBaru2Activity;
import id.co.bri.brimo.ui.activities.UbahPinBaruActivity;
import id.co.bri.brimo.ui.activities.UbahPinValidasiActivity;
import id.co.bri.brimo.ui.activities.britamarencana.KonfirmasiDataRencanaActivity;
import id.co.bri.brimo.ui.activities.britamarencana.SyaratRencanaActivity;
import id.co.bri.brimo.ui.activities.changepinnewskin.ChangeNewPinActivity;
import id.co.bri.brimo.ui.activities.changepinnewskin.ChangeNewPinConfirmActivity;
import id.co.bri.brimo.ui.activities.changepinnewskin.ChangePinActivity;
import id.co.bri.brimo.ui.activities.pengelolaankartu.KonfirmUbahPinAtmActivity;
import id.co.bri.brimo.ui.activities.pengelolaankartu.UbahPinAtmActivity;
import id.co.bri.brimo.ui.activities.rdn.RdnOtpActivity;
import id.co.bri.brimo.ui.activities.transferinternasional.KonfirmasiTransferInternasionalActivity;
import id.co.bri.brimo.ui.fragments.PinFragment;
import id.co.bri.brimo.ui.fragments.PinFragmentNewSkin;
import id.co.bri.brimo.ui.fragments.PinFragmentRevamp;
import id.co.bri.brimo.ui.fragments.biometric.PinBiometricFragment;
import id.co.bri.brimo.ui.fragments.pengelolaankartu.InputConfirmPinDebitNewSkinFragment;
import id.co.bri.brimo.ui.fragments.pengelolaankartu.InputExistingPinNewSkinFragment;
import id.co.bri.brimo.ui.fragments.pengelolaankartu.InputNewPinDebitNewSkinFragment;
import id.co.bri.brimo.ui.fragments.pin.reskin.PinReskinFragment;

@PerForm
@Subcomponent(modules = {PinAllModule.class})
public interface PinAllComponent {
    void inject(PinFragment pinFragment);

    void inject(PinReskinFragment target);

    void inject(KonfirmasiPinActivity konfirmasiPinActivity);

    void inject(LupaPinKonfirmasiActivity lupaPinKonfirmasiActivity);

    void inject(LupaPinOtpActivity lupaPinOtpActivity);

    void inject(BlokirKartuActivity blokirKartuActivity);

    void inject(SyaratRencanaActivity syaratRencanaActivity);

    void inject(LupaPinVerifEmailActivity lupaPinVerifEmailActivity);

    void inject(LupaPinCreateActivity lupaPinCreateActivity);

    void inject(LupaPinConfirmActivity lupaPinConfirmActivity);

    void inject(KonfirmasiDataRencanaActivity konfirmasiDataRencanaActivity);

    void inject(KonfirmasiTransferInternasionalActivity konfirmasiTransferInternasionalActivity);

    PinNumberAdapter pinNumberAdapter();

    PinHiddenAdapter pinHiddenAdapter();

    PinNumberAdapterNewSkin pinNumberAdapterNewSkin();

    void inject(UbahPasswordOtpActivity ubahPasswordOtpActivity);

    void inject(UbahPinActivity ubahPinActivity);

    void inject(UbahPinBaru2Activity ubahPinBaru2Activity);

    void inject(UbahPinValidasiActivity ubahPinActivity);

    void inject(UbahPinBaruActivity ubahPinBaruActivity);

    void inject(RdnOtpActivity rdnOtpActivity);

    void inject(PinFragmentRevamp pinFragmentRevamp);

    void inject(UbahPinAtmActivity ubahPinAtmActivity);

    void inject(KonfirmUbahPinAtmActivity konfirmUbahPinAtmActivity);

    void inject(PinBiometricFragment pinBiometricFragment);

    void inject(ChangePinActivity changePinActivity);

    void inject(ChangeNewPinActivity changeNewPinActivity);

    void inject(ChangeNewPinConfirmActivity changeNewPinConfirmActivity);

    void inject(PinFragmentNewSkin pinFragmentNewSkin);

    void inject(InputExistingPinNewSkinFragment target);

    void inject(InputNewPinDebitNewSkinFragment target);

    void inject(InputConfirmPinDebitNewSkinFragment target);




}