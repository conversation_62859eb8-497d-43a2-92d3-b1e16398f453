package id.co.bri.brimo.models.apimodel.request;

import com.google.gson.annotations.Expose;
import com.google.gson.annotations.SerializedName;

public class FastPaymentRevampRequest extends FastMenuRequest {
    @SerializedName("reference_number")
    @Expose
    private String referenceNumber;
    @SerializedName("pin")
    @Expose
    private String pin;
    @SerializedName("pfm_category")
    @Expose
    private String pfmCategory;

    public FastPaymentRevampRequest(FastMenuRequest request, String refNum, String pin, String pfmCategory) {
        super(request.getUsername(), request.getTokenKey());
        this.referenceNumber = refNum;
        this.pin = pin;
        this.pfmCategory = pfmCategory;
    }

    public String getReferenceNumber() {
        return referenceNumber;
    }

    public void setReferenceNumber(String referenceNumber) {
        this.referenceNumber = referenceNumber;
    }

    public String getPin() {
        return pin;
    }

    public void setPin(String pin) {
        this.pin = pin;
    }

    public String getPfmCategory() {
        return pfmCategory;
    }

    public void setPfmCategory(String pfmCategory) {
        this.pfmCategory = pfmCategory;
    }
}
