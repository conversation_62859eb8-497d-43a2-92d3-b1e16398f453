package id.co.bri.brimo.util.singalarity.model

import org.json.JSONException
import org.json.JSONObject

data class SingalarityAccessToken(
    val tokenType: String,
    val accessToken: String,
    val oSecretHex: String
){
    fun convertToJson(): JSONObject? {
        accessToken?.let {
            val json = JSONObject()
            try {
                json.put("tokenType", tokenType)
                json.put("accessToken", accessToken)
                json.put("oSecretHex", oSecretHex)
                // Add other fields from SingalarityAccessToken as needed
            } catch (e: JSONException) {

                return null
            }
            return json
        }
        return null // or handle the case where accessToken is null
    }
}