package id.co.bri.brimo.data.repository.fastmenudefault;

import android.util.Log;

import java.util.ArrayList;
import java.util.List;


import id.co.bri.brimo.domain.config.MenuConfig;
import id.co.bri.brimo.models.daomodel.DashboardMenu.LanguageModel;
import id.co.bri.brimo.models.daomodel.FastMenuDefault;
import io.reactivex.Completable;
import io.reactivex.Maybe;
import io.reactivex.Observable;
import io.reactivex.Single;
import io.reactivex.android.schedulers.AndroidSchedulers;
import io.reactivex.observers.DisposableCompletableObserver;
import io.reactivex.schedulers.Schedulers;

/**
 * Created by user on 24/03/2021
 */
public class FastMenuDefaultRepository implements FastMenuDefaultSource {

    private static final String TAG = "FastMenuDefaultRepository";

    private DisposableCompletableObserver categoryDisposable;
    private FastMenuDefaultSource fastMenuSource;

    public FastMenuDefaultRepository(FastMenuDefaultSource categoryLocalSource) {
        this.fastMenuSource = categoryLocalSource;
    }

    @Override
    public Maybe<List<FastMenuDefault>> getFastMenuDefault() {
        return fastMenuSource.getFastMenuDefault()
                .switchIfEmpty(fastMenuSource.getFastMenuDefault());
    }

    @Override
    public Maybe<List<FastMenuDefault>> getAllFastMenuDefaultByPosition(boolean isToggledOn, boolean isAvailable) {
        return fastMenuSource.getAllFastMenuDefaultByPosition(isToggledOn, isAvailable)
                .switchIfEmpty(fastMenuSource.getAllFastMenuDefaultByPosition(isToggledOn, isAvailable));
    }

    @Override
    public Maybe<List<FastMenuDefault>> getAllFastMenuDefaultByName(boolean isToggledOff, boolean isAvailable) {
        return fastMenuSource.getAllFastMenuDefaultByName(isToggledOff, isAvailable)
                .switchIfEmpty(fastMenuSource.getAllFastMenuDefaultByName(isToggledOff, isAvailable));
    }

    @Override
    public Observable<List<FastMenuDefault>> getFastMenuDefaultByToggle(boolean isToggled) {
        return fastMenuSource.getFastMenuDefaultByToggle(isToggled)
                .switchIfEmpty(fastMenuSource.getFastMenuDefaultByToggle(isToggled));
    }

    @Override
    public Maybe<List<FastMenuDefault>> getAllFastMenuDefaultFinal() {
        return fastMenuSource.getAllFastMenuDefaultFinal()
                .switchIfEmpty(fastMenuSource.getAllFastMenuDefaultFinal());
    }

    @Override
    public Completable saveFastMenu(List<FastMenuDefault> fastMenus) {
        return fastMenuSource.saveFastMenu(fastMenus);
    }

    @Override
    public Completable deleteAll() {
        return fastMenuSource.deleteAll();
    }

    @Override
    public Completable updateFastMenuName(int id, LanguageModel languageModel) {
        return fastMenuSource.updateFastMenuName(id,languageModel);
    }


    protected Maybe<List<FastMenuDefault>> saveSampleFastMenu(){
        List<FastMenuDefault> fastMenuModel = new ArrayList<>();
        fastMenuModel.addAll(MenuConfig.fetchFastMenuDefault());
        categoryDisposable = new DisposableCompletableObserver() {
            @Override
            public void onComplete() {
                getFastMenuDefault();
                disposeObserver();
            }

            @Override
            public void onError(Throwable e) {
                /*
                if (!GeneralHelper.isProd())
                e.printStackTrace();

                 */
            }
        };

        fastMenuSource.saveFastMenu(fastMenuModel).subscribeOn(Schedulers.io())
                .observeOn(AndroidSchedulers.mainThread())
                .subscribe(categoryDisposable);



        return Maybe.just(fastMenuModel);

    }

    public void disposeObserver() {
        if (categoryDisposable != null && !categoryDisposable.isDisposed()) {
            categoryDisposable.dispose();
        }
    }
}
