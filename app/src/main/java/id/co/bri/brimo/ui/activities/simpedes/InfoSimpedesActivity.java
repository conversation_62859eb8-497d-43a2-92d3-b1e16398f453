package id.co.bri.brimo.ui.activities.simpedes;

import android.annotation.SuppressLint;
import android.app.Activity;
import android.content.Intent;
import android.os.Bundle;
import android.view.View;
import android.view.animation.AnimationUtils;

import androidx.annotation.Nullable;
import androidx.fragment.app.FragmentTransaction;
import androidx.swiperefreshlayout.widget.SwipeRefreshLayout;

import com.ethanhua.skeleton.Skeleton;
import com.ethanhua.skeleton.SkeletonScreen;
import com.google.gson.Gson;

import javax.inject.Inject;

import id.co.bri.brimo.R;
import id.co.bri.brimo.contract.IPresenter.simpedes.IInfoSimpedesPresenter;
import id.co.bri.brimo.contract.IView.simpedes.IInfoSimpedesView;
import id.co.bri.brimo.data.preference.BRImoPrefRepository;
import id.co.bri.brimo.databinding.ActivityInfoSimpedesBinding;
import id.co.bri.brimo.domain.config.Constant;
import id.co.bri.brimo.domain.helpers.GeneralHelper;
import id.co.bri.brimo.models.ImpianForm;
import id.co.bri.brimo.models.apimodel.response.BrifineCheckRes;
import id.co.bri.brimo.models.apimodel.response.BrifineFormRes;
import id.co.bri.brimo.models.apimodel.response.InfoSimpedesResponse;
import id.co.bri.brimo.models.apimodel.response.JenisAsuransiAmkkmResponse;
import id.co.bri.brimo.models.apimodel.response.ListRekeningResponse;
import id.co.bri.brimo.ui.activities.InfoRekeningActivity;
import id.co.bri.brimo.ui.activities.base.BaseActivity;
import id.co.bri.brimo.ui.customviews.dialog.DialogInformation;
import id.co.bri.brimo.ui.fragments.ProsesKlaimBottomFragment;

public class InfoSimpedesActivity extends BaseActivity implements
        SwipeRefreshLayout.OnRefreshListener,
        IInfoSimpedesView,
        View.OnClickListener,
        DialogInformation.OnActionClick {

    private ActivityInfoSimpedesBinding binding;

    @Inject
    IInfoSimpedesPresenter<IInfoSimpedesView> presenter;

    protected static final String TAG_ACCOUNT = "account_string";

    protected boolean isLoadingImpian = false;
    protected boolean isLoadingBrifine = false;
    protected boolean isLoadingAmkkm = false;

    protected SkeletonScreen skeletonScreenImpian;
    protected SkeletonScreen skeletonScreenBrifine;
    protected SkeletonScreen skeletonScreenProteksi;
    protected ListRekeningResponse.Account rekeningAccount;
    protected double totalSimpedes = 0;

    protected InfoSimpedesResponse infoSimpedesResponse;
    protected BrifineCheckRes brifineCheckResponse;
    protected JenisAsuransiAmkkmResponse jenisAsuransiAmkkmRes;

    protected BRImoPrefRepository brImoPrefRepository = new BRImoPrefRepository(this);

    public static void launchIntent(Activity caller, String account) {
        Intent intent = new Intent(caller, InfoSimpedesActivity.class);
        intent.putExtra(TAG_ACCOUNT, account);
        caller.startActivityForResult(intent, Constant.REQ_PAYMENT);
    }

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        binding = ActivityInfoSimpedesBinding.inflate(getLayoutInflater());
        setContentView(binding.getRoot());

        if (getIntent().getExtras().getString(TAG_ACCOUNT) != null)
            rekeningAccount = new Gson().fromJson(getIntent().getExtras().getString(TAG_ACCOUNT), ListRekeningResponse.Account.class);

        GeneralHelper.setToolbar(this, binding.toolbar.toolbar, GeneralHelper.getString(R.string.toolbar_simpedes));

        if (rekeningAccount != null) {
            binding.tvNomorRekening.setText(rekeningAccount.getAccountString());
            binding.tvNamaRekening.setText(rekeningAccount.getName());

            if (rekeningAccount.getSaldoReponse() != null) {
                if (rekeningAccount.getSaldoReponse().getBalance() != null) {
                    binding.tvTotalSaldoRekening.setText(binding.tvCurrency.getText().toString() + GeneralHelper.formatNominalIDR(rekeningAccount.getSaldoReponse().getBalance()));
                    totalSimpedes = rekeningAccount.getSaldoReponse().getBalance();
                    binding.tvTotalSaldoSimpedes.setText(GeneralHelper.formatNominalIDR(totalSimpedes));
                    updateTotalSaldo();
                }
            }
        }

        skeletonScreenImpian = Skeleton.bind(binding.layoutCvImpian)
                .shimmer(true)
                .angle(20)
                .duration(1200)
                .load(R.layout.item_skeleton_tabjan_info_s3f)
                .show();

        skeletonScreenBrifine = Skeleton.bind(binding.layoutCvBrifine)
                .shimmer(true)
                .angle(20)
                .duration(1200)
                .load(R.layout.item_skeleton_tabjan_info_s3f)
                .show();

        skeletonScreenProteksi = Skeleton.bind(binding.cvAsuransi)
                .shimmer(true)
                .angle(20)
                .duration(1200)
                .load(R.layout.item_skeleton_tabasrns_info_s3f)
                .show();

        injectDependency();

        binding.layoutCvImpian.setOnClickListener(this);
        binding.layoutCvBrifine.setOnClickListener(this);
        binding.btnTambah.setOnClickListener(this);
        binding.btnDaftarBrifine.setOnClickListener(this);
        binding.btnStatusBrifine.setOnClickListener(this);
        binding.btnBeliAsuransi.setOnClickListener(this);
        binding.cvAsuransi.setOnClickListener(this);
        binding.lySimpedes.setOnClickListener(this);

        binding.swipeRefreshRekening.setOnRefreshListener(this);
    }

    protected void updateTotalSaldo() {
        if (totalSimpedes < 0)
            binding.tvCurrency.setText("-Rp");
        else
            binding.tvCurrency.setText("Rp");
    }

    private void injectDependency() {
        getActivityComponent().inject(this);
        if (presenter != null) {
            presenter.setView(this);
            presenter.start();
            presenter.setUrlFormInfo(GeneralHelper.getString(R.string.url_s3f_form_detail));
            presenter.setUrlAddImpian(GeneralHelper.getString(R.string.url_s3f_add_impian_form));
            presenter.setUrlAddBrifine(GeneralHelper.getString(R.string.url_s3f_open_dplk_form));
            presenter.setUrlBrifineCheck(GeneralHelper.getString(R.string.url_s3f_dplk_check));
            presenter.setUrlProductAmkkm(GeneralHelper.getString(R.string.url_s3f_open_jenis_asuransi_amkkm));

            if (Boolean.TRUE.equals(rekeningAccount.getS3DreamPopupShow())) {
                presenter.getImpian(rekeningAccount.getAccount());
            } else {
                presenter.getImpian(rekeningAccount.getAccount());
                presenter.getBrifineCheck(rekeningAccount.getAccount());
                presenter.getProductAmkkm(rekeningAccount.getAccount());
            }

        }
    }

    @Override
    public void onClick(View v) {
        switch (v.getId()) {
            case R.id.layout_cv_impian:
                if (infoSimpedesResponse != null)
                    InfoImpianActivity.launchIntent(this, new Gson().toJson(infoSimpedesResponse));
                break;
            case R.id.layout_cv_brifine:
                if (brifineCheckResponse != null)
                    DetailBrifineActivity.launchIntent(this, new Gson().toJson(brifineCheckResponse));
                break;
            case R.id.btn_tambah:
                presenter.getAddImpian(rekeningAccount.getAccount());
                break;
            case R.id.btn_daftar_brifine:
                presenter.getAddBrifine(rekeningAccount.getAccount());
                break;
            case R.id.btn_beli_asuransi:
            case R.id.cv_asuransi:
                if (jenisAsuransiAmkkmRes != null)
                    ProductListAmkkmActivity.launchActivity(this, rekeningAccount.getAccount(), jenisAsuransiAmkkmRes);
                break;
            case R.id.btn_status_brifine:
                ProsesKlaimBottomFragment bottomFragment = new ProsesKlaimBottomFragment();
                bottomFragment.setCancelable(true);
                bottomFragment.show(getSupportFragmentManager(), "");
                break;
            case R.id.ly_simpedes:
                InfoRekeningActivity.launchIntent(this, rekeningAccount);
                break;
            default:
                break;
        }
    }

    @SuppressLint("SetTextI18n")
    @Override
    public void getDataInfo(InfoSimpedesResponse infoSimpedesRes) {
        if (infoSimpedesRes != null) {
            infoSimpedesResponse = infoSimpedesRes;

            if (Boolean.TRUE.equals(rekeningAccount.getS3DreamPopupShow())) {
                binding.tvDescTidakKetemu.setText(infoSimpedesRes.getDescription());
                binding.layoutButton.setVisibility(View.VISIBLE);
                binding.layoutTidakKetemu.setVisibility(View.VISIBLE);
                binding.layoutInfo.setVisibility(View.GONE);
                binding.swipeRefreshRekening.setEnabled(false);
            } else {
                if (skeletonScreenImpian != null) {
                    skeletonScreenImpian.hide();
                }
                binding.tvImpian.setVisibility(View.VISIBLE);
                binding.layoutAnimImpian.startAnimation(AnimationUtils.loadAnimation(getApplicationContext(), R.anim.slide_down_show));

                binding.tvDescImpian.setText(infoSimpedesRes.getDescription());
                binding.tvTotalSaldoImpian.setText(infoSimpedesRes.getTotalChildBalanceString());

                totalSimpedes += infoSimpedesRes.getTotalChildBalance();
                binding.tvTotalSaldoSimpedes.setText(GeneralHelper.formatNominalIDR(totalSimpedes));
                updateTotalSaldo();
            }
        }
    }

    @Override
    public void onExceptionImpian(String sDesc) {
        if (skeletonScreenImpian != null) {
            skeletonScreenImpian.hide();
        }

        binding.tvImpian.setVisibility(View.VISIBLE);
        binding.layoutAnimImpian.startAnimation(AnimationUtils.loadAnimation(getApplicationContext(), R.anim.slide_down_show));
        binding.tvDescImpian.setText(sDesc);
        binding.layoutCvImpian.setEnabled(false);
        binding.layoutDetailImpian.setVisibility(View.INVISIBLE);
    }

    @Override
    public void getDataBrifineCheck00(BrifineCheckRes brifineCheckRes) {
        if (brifineCheckRes != null) {
            brifineCheckResponse = brifineCheckRes;

            if (skeletonScreenBrifine != null) {
                skeletonScreenBrifine.hide();
            }

            binding.tvBrifine.setVisibility(View.VISIBLE);
            binding.layoutAnimBrifine.startAnimation(AnimationUtils.loadAnimation(getApplicationContext(), R.anim.slide_down_show));

            binding.tvDescBrifine.setText(brifineCheckRes.getDescription());
            binding.tvTotalSaldoBrifine.setText(brifineCheckRes.getBalanceString());

            totalSimpedes += brifineCheckRes.getBalance();
            binding.tvTotalSaldoSimpedes.setText(GeneralHelper.formatNominalIDR(totalSimpedes));
            updateTotalSaldo();
        }
    }

    @Override
    public void getDataBrifineCheckNF(BrifineCheckRes brifineCheckRes) {
        if (brifineCheckRes != null) {
            brifineCheckResponse = brifineCheckRes;
            if (skeletonScreenBrifine != null) {
                skeletonScreenBrifine.hide();
            }

            binding.tvDescBrifineBaru.setText(brifineCheckRes.getDescription());
            DialogInformation dialog = new DialogInformation(this, "Mulai investasi, yuk!", brifineCheckRes.getDescription(), this::onClickAction, "default_deposito");
            FragmentTransaction ft = getSupportFragmentManager().beginTransaction();
            ft.add(dialog, null);
            ft.commitAllowingStateLoss();

            binding.layoutAnimBrifine.setVisibility(View.GONE);
            binding.layoutBrifineDaftar.setVisibility(View.VISIBLE);
            binding.layoutBrifineDaftar.startAnimation(AnimationUtils.loadAnimation(getApplicationContext(), R.anim.slide_down_show));
        }
    }

    @Override
    public void onExceptionBrifine(String sDesc) {
        if (skeletonScreenBrifine != null) {
            skeletonScreenBrifine.hide();
        }

        binding.tvBrifine.setVisibility(View.VISIBLE);
        binding.layoutAnimBrifine.startAnimation(AnimationUtils.loadAnimation(getApplicationContext(), R.anim.slide_down_show));
        binding.tvDescBrifine.setText(sDesc);
        binding.layoutCvBrifine.setEnabled(false);
        binding.layoutDetailBrifine.setVisibility(View.INVISIBLE);
    }

    @Override
    public void getProductAmkkm(JenisAsuransiAmkkmResponse jenisAsuransiAmkkmResponse) {
        if (jenisAsuransiAmkkmResponse != null) {
            jenisAsuransiAmkkmRes = jenisAsuransiAmkkmResponse;

            if (skeletonScreenProteksi != null) {
                skeletonScreenProteksi.hide();
            }

            binding.tvDescAmkkm.setText(jenisAsuransiAmkkmResponse.getDesc());
            binding.tvProteksi.setVisibility(View.VISIBLE);
            binding.layoutAsuransi.setVisibility(View.VISIBLE);
            binding.layoutAnimAsusransi.startAnimation(AnimationUtils.loadAnimation(getApplicationContext(), R.anim.slide_down_show));
            binding.ivArrowRightBlue.setVisibility(View.VISIBLE);
        }
    }


    @Override
    public void getProductAmkkmNF(JenisAsuransiAmkkmResponse jenisAsuransiAmkkmResponse) {
        if (jenisAsuransiAmkkmResponse != null) {
            jenisAsuransiAmkkmRes = jenisAsuransiAmkkmResponse;

            binding.tvDescAmkkmBaru.setText(jenisAsuransiAmkkmResponse.getDesc());
            binding.layoutAmkkmDaftar.setVisibility(View.VISIBLE);
            binding.layoutAmkkmDaftar.startAnimation(AnimationUtils.loadAnimation(getApplicationContext(), R.anim.slide_down_show));
            binding.layoutAnimAsusransi.setVisibility(View.GONE);
        }
    }

    @Override
    public void onExceptionAmkkm(String sDesc) {
        if (skeletonScreenProteksi != null) {
            skeletonScreenProteksi.hide();
        }

        binding.tvDescAmkkm.setText(sDesc);
        binding.tvProteksi.setVisibility(View.VISIBLE);
        binding.layoutAsuransi.setVisibility(View.VISIBLE);
        binding.layoutAnimAsusransi.startAnimation(AnimationUtils.loadAnimation(getApplicationContext(), R.anim.slide_down_show));
        binding.cvAsuransi.setEnabled(false);
        binding.ivArrowRightBlue.setVisibility(View.GONE);
    }

    @Override
    public void completeImpian() {
        isLoadingImpian = true;
    }

    @Override
    public void completeBrifine() {
        isLoadingBrifine = true;
    }

    @Override
    public void completeAmkkm() {
        isLoadingAmkkm = true;
    }

    @Override
    public void getDataImpianForm(ImpianForm impianForm) {
        FormImpianActivity.launchIntent(this, rekeningAccount.getAccount(), new Gson().toJson(impianForm));
    }

    @Override
    public void getDataBrifineForm(BrifineFormRes brifineFormRes) {
        FormBrifineActivity.launchIntent(this, new Gson().toJson(brifineFormRes));
    }

    @Override
    protected void onActivityResult(int requestCode, int resultCode, @Nullable Intent data) {
        super.onActivityResult(requestCode, resultCode, data);
        if (requestCode == Constant.REQ_PAYMENT && resultCode == RESULT_OK) {
            setResult(RESULT_OK, data);
            this.finish();
        } else if (requestCode == ProductListAmkkmActivity.REQUESTCODE && resultCode == Activity.RESULT_CANCELED
                && data != null && data.getStringExtra(Constant.TAG_ERROR_MESSAGE) != null) {
            onException(data.getStringExtra(Constant.TAG_ERROR_MESSAGE));
        }
    }

    @Override
    public void onBackPressed() {
        if (Boolean.TRUE.equals(rekeningAccount.getS3DreamPopupShow())) {
            Intent returnIntent = new Intent();
            this.setResult(RESULT_OK, returnIntent);
            this.finish();
        } else
            super.onBackPressed();
    }

    @Override
    public void onClickAction() {
        presenter.getAddBrifine(rekeningAccount.getAccount());
    }

    @Override
    public void onRefresh() {
        if (isLoadingImpian) {
            if (infoSimpedesResponse == null) {
                skeletonScreenImpian.show();
                binding.tvImpian.setVisibility(View.GONE);
                presenter.getImpian(rekeningAccount.getAccount());
                isLoadingImpian = false;
            }
        }

        if (isLoadingBrifine) {
            if (brifineCheckResponse == null) {
                skeletonScreenBrifine.show();
                binding.tvBrifine.setVisibility(View.GONE);
                presenter.getBrifineCheck(rekeningAccount.getAccount());
                isLoadingBrifine = false;
            }
        }

        if (isLoadingAmkkm) {
            if (jenisAsuransiAmkkmRes == null) {
                skeletonScreenProteksi.show();
                binding.tvProteksi.setVisibility(View.GONE);
                presenter.getProductAmkkm(rekeningAccount.getAccount());
                isLoadingAmkkm = false;
            }
        }

        binding.swipeRefreshRekening.setRefreshing(false);
    }

    @Override
    public void viewSwipeRefresh(boolean isCheck) {
        if (binding.swipeRefreshRekening != null) {
            binding.swipeRefreshRekening.setRefreshing(isCheck);
        }
    }

    @Override
    protected void onDestroy() {
        super.onDestroy();
        binding = null;
    }
}