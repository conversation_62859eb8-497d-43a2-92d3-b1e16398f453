package id.co.bri.brimo.ui.activities.ibbiz;

import android.app.Activity;
import android.content.Intent;
import android.os.Bundle;
import android.text.Editable;
import android.text.InputFilter;
import android.text.Spanned;
import android.text.TextWatcher;
import android.view.View;
import androidx.annotation.Nullable;
import androidx.core.content.ContextCompat;
import javax.inject.Inject;
import id.co.bri.brimo.R;
import id.co.bri.brimo.contract.IPresenter.ibbiz.IDataPerusahaanPresenter;
import id.co.bri.brimo.contract.IView.ibbiz.IDataPerusahaanView;
import id.co.bri.brimo.databinding.ActivityDataPerusahaanBinding;
import id.co.bri.brimo.domain.config.Constant;
import id.co.bri.brimo.domain.helpers.GeneralHelper;
import id.co.bri.brimo.models.apimodel.response.KonfirmasiIbbizResponse;
import id.co.bri.brimo.ui.activities.base.BaseActivity;

public class DataPerusahaanActivity extends BaseActivity implements IDataPerusahaanView {

    private ActivityDataPerusahaanBinding binding;

    @Inject
    IDataPerusahaanPresenter<IDataPerusahaanView> presenter;

    protected static String sPhone;
    protected static String sEmail;
    protected static String sAccount;

    public static void launchIntent(Activity caller, String phone, String email, String account) {
        Intent intent = new Intent(caller, DataPerusahaanActivity.class);
        sPhone = phone;
        sEmail = email;
        sAccount = account;
        caller.startActivityForResult(intent, Constant.REQ_REGIS);
    }

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        binding = ActivityDataPerusahaanBinding.inflate(getLayoutInflater());
        setContentView(binding.getRoot());

        GeneralHelper.setToolbar(this, binding.toolbar.toolbar, GeneralHelper.getString(R.string.toolbar_qlola));

        injectDependency();
        setupViews();
    }

    private void injectDependency() {
        getActivityComponent().inject(this);
        if (presenter != null) {
            presenter.setView(this);
            presenter.start();
            presenter.setUrl(GeneralHelper.getString(R.string.url_ibbiz_confirmation));
        }
    }

    private void setupViews() {
        binding.etNoHandphone.setText(sPhone);
        binding.etEmail.setText(sEmail);

        disableEmojiInTitle();

        binding.etNamaPerusahaan.addTextChangedListener(new TextWatcher() {
            @Override
            public void beforeTextChanged(CharSequence s, int start, int count, int after) {
                // do nothing
            }

            @Override
            public void onTextChanged(CharSequence charSequence, int start, int before, int count) {
                if (charSequence.length() >= 5) {
                    binding.layoutAlertPerusahaan.setVisibility(View.GONE);
                    if (getRefferal().length() == 0 || getRefferal().length() == 8) {
                        binding.lineRefferal.setBackgroundColor(ContextCompat.getColor(DataPerusahaanActivity.this, R.color.color_grey_bb));
                        binding.layoutAlertPerusahaan.setVisibility(View.GONE);
                        enableButton(true);
                    } else {
                        binding.lineRefferal.setBackgroundColor(ContextCompat.getColor(DataPerusahaanActivity.this, R.color.red));
                        binding.layoutAlertPerusahaan.setVisibility(View.VISIBLE);
                        enableButton(false);
                    }
                } else {
                    enableButton(false);
                    if (charSequence.length() == 0)
                        binding.layoutAlertPerusahaan.setVisibility(View.GONE);
                    else
                        binding.layoutAlertPerusahaan.setVisibility(View.VISIBLE);
                }
            }

            @Override
            public void afterTextChanged(Editable s) {
                // do nothing
            }
        });

        binding.etKodeReferral.addTextChangedListener(new TextWatcher() {
            @Override
            public void beforeTextChanged(CharSequence s, int start, int count, int after) {
                // do nothing
            }

            @Override
            public void onTextChanged(CharSequence charSequence, int start, int before, int count) {
                if (charSequence.length() == 0 || charSequence.length() == 8) {
                    binding.lineRefferal.setBackgroundColor(ContextCompat.getColor(DataPerusahaanActivity.this, R.color.color_grey_bb));
                    binding.layoutAlertPerusahaan.setVisibility(View.GONE);
                } else {
                    binding.lineRefferal.setBackgroundColor(ContextCompat.getColor(DataPerusahaanActivity.this, R.color.red));
                    binding.layoutAlertPerusahaan.setVisibility(View.VISIBLE);
                }
            }

            @Override
            public void afterTextChanged(Editable s) {
                // do nothing
            }
        });

        binding.btLanjut.setOnClickListener(v -> presenter.sendDataConfirm(sAccount, getCorpName(), getRefferal()));
    }

    private void disableEmojiInTitle() {
        InputFilter emojiFilter = new InputFilter() {
            @Override
            public CharSequence filter(CharSequence source, int start, int end, Spanned dest, int dstart, int dend) {
                for (int index = start; index < end; index++) {
                    int type = Character.getType(source.charAt(index));

                    if (type == Character.SURROGATE || type == Character.OTHER_SYMBOL) {
                        return "";
                    }
                }
                return null;
            }
        };
        binding.etNamaPerusahaan.setFilters(new InputFilter[]{emojiFilter});
    }

    private void enableButton(boolean isEnable) {
        if (isEnable) {
            binding.btLanjut.setEnabled(true);
            binding.btLanjut.setAlpha(1);
        } else {
            binding.btLanjut.setEnabled(false);
            binding.btLanjut.setAlpha(0.3f);
        }
    }

    private String getCorpName() {
        return binding.etNamaPerusahaan.getText().toString();
    }

    private String getRefferal() {
        return binding.etKodeReferral.getText().toString();
    }

    @Override
    public void getDataSuccess(KonfirmasiIbbizResponse konfirmasiIbbizResponse) {
        KonfirmasiIbbizActivity.launchIntent(this, konfirmasiIbbizResponse);
    }

    @Override
    protected void onActivityResult(int requestCode, int resultCode, @Nullable Intent data) {
        super.onActivityResult(requestCode, resultCode, data);

        if (requestCode == Constant.REQ_REGIS && resultCode == RESULT_OK && data != null) {
            this.setResult(RESULT_OK, data);
            this.finish();
        } else if (requestCode == Constant.REQ_REGIS && resultCode == RESULT_CANCELED && data != null) {
            this.setResult(RESULT_CANCELED, data);
            this.finish();
        }
    }

    @Override
    protected void onDestroy() {
        super.onDestroy();
        binding = null;
    }
}