package id.co.bri.brimo.ui.activities.applyvcc

import android.content.Intent
import androidx.appcompat.app.AppCompatActivity
import id.co.bri.brimo.contract.IPresenter.applyvcc.IApplyVccLocalStoragePresenter
import id.co.bri.brimo.contract.IView.applyvcc.IApplyVccLocalStorageView
import id.co.bri.brimo.databinding.ActivityApplyVccPhotoSelfieBinding
import id.co.bri.brimo.domain.config.Constant
import id.co.bri.brimo.domain.helpers.ValidationHelper
import id.co.bri.brimo.models.apimodel.request.applyccrevamp.ApplyCcSubmitDataLocalRequest
import id.co.bri.brimo.ui.activities.base.BaseCameraActivity
import id.co.bri.brimo.ui.activities.base.CameraMode
import id.co.bri.brimo.ui.activities.base.ScreenSizePhone

import id.co.bri.brimo.util.extension.getBitmapFromPath
import id.co.bri.brimo.util.extension.gone
import id.co.bri.brimo.util.extension.visible
import javax.inject.Inject

class ApplyVccPhotoSelfieActivity : BaseCameraActivity() {

    private var hasRecord: Boolean = false
    private var isEdit: Boolean = false
    private var applyVccRequestModel = ApplyCcSubmitDataLocalRequest()

    private lateinit var binding: ActivityApplyVccPhotoSelfieBinding

    @Inject
    lateinit var iApplyVccLocalStoragePresenter: IApplyVccLocalStoragePresenter<IApplyVccLocalStorageView>

    companion object {
        private const val IS_EDIT = "IS_EDIT"

        fun launchIntent(caller: AppCompatActivity, isEdit: Boolean = false) {
            val intent = Intent(caller, ApplyVccPhotoSelfieActivity::class.java).apply {
                putExtra(IS_EDIT, isEdit)
            }
            caller.startActivity(intent)
        }
    }

    override fun setupFrontBackCamera() = CameraMode.FRONT_CAMERA_SQUARE

    override fun getPreviewView() = binding.camera

    override fun setContent() {
        binding = ActivityApplyVccPhotoSelfieBinding.inflate(layoutInflater)
        setContentView(binding.root)
        injectDependency()
        getData()
        setupView()
        showBottomGuideDialog()
    }

    override fun onPreviewPhoto() {
        previewPhoto()
    }

    override fun setQuality() = 100

    override fun setDimension() = DIMENSION

    override fun setSize() = ScreenSizePhone.EXTRA_SMALL

    override fun onRestart() {
        super.onRestart()
        if (hasRecord) {
            getData()
            applyVccRequestModel.selfieFilenameLocal.getBitmapFromPath()?.let { bitmap ->
                cropKtp = bitmap
                previewPhoto()
            }
        }
    }

    override fun onSuccessSavePhotoLocal(savedPath: String) {
        applyVccRequestModel.selfieFilenameLocal = savedPath
    }

    override fun onSuccessUploadSendPhotoToS3Minio(imgUrl: String) {
        hideProgress()
        gotoNextPageOrFinish()
    }

    override fun onFailedUploadSendPhotoToS3Minio(errorMsg: String) {
        hideProgress()
    }

    private fun getData() {
        applyVccRequestModel = iApplyVccLocalStoragePresenter.getApplyVccRequestLocal()
        intent?.apply {
            isEdit = getBooleanExtra(IS_EDIT, isEdit)
        }
    }

    private fun injectDependency() {
        activityComponent.inject(this)
    }

    private fun previewPhoto() {
        binding.capture.gone()
        binding.imgKtp.gone()
        binding.layoutPreview.visible()
        binding.imgPreview.visible()
        binding.layoutBtnPreview.visible()
        binding.imgPreview.setImageBitmap(cropKtp)
    }

    private fun repeatPhoto() {
        binding.capture.visible()
        binding.imgKtp.visible()
        binding.layoutPreview.gone()
        binding.imgPreview.gone()
        binding.layoutBtnPreview.gone()
        deletePhotoFromStorage()
        startCamera(binding.camera)
    }

    private fun setupView() {
        binding.toolbarActivityPhotoGeneral.setSupportActionBar(this)

        if (ValidationHelper.validateDoubleClick()) {
            binding.toolbarActivityPhotoGeneral.setNavigationEndOnClickListener { showBottomGuideDialog() }
            binding.capture.setOnClickListener { takePhoto(Constant.PHOTO_SELFIE_FILE_NAME) }
            binding.btnUlangi.setOnClickListener { repeatPhoto() }
            binding.btnGunakan.setOnClickListener {
                iApplyVccLocalStoragePresenter.setApplyVccRequestLocal(applyVccRequestModel)
                gotoNextPageOrFinish()
            }
        }
    }

    private fun gotoNextPageOrFinish() {
        hasRecord = true
        if (isEdit) finish()
        else ApplyVccConfirmActivity.launchIntent(this)
    }
}