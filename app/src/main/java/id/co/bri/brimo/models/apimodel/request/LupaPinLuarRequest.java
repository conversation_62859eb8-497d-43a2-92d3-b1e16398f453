package id.co.bri.brimo.models.apimodel.request;

import com.google.gson.annotations.Expose;
import com.google.gson.annotations.SerializedName;

public class LupaPinLuarRequest {
    @SerializedName("born_date")
    @Expose
    private String bornDate;
    @SerializedName("reference_number")
    @Expose
    private String refnum;

    public LupaPinLuarRequest(String bornDate, String refnum) {
        this.bornDate = bornDate;
        this.refnum = refnum;
    }

    public String getBornDate() {
        return bornDate;
    }

    public void setBornDate(String bornDate) {
        this.bornDate = bornDate;
    }

    public String getRefnum() {
        return refnum;
    }

    public void setRefnum(String refnum) {
        this.refnum = refnum;
    }
}