package id.co.bri.brimo.ui.activities.ssc

import android.app.Activity
import android.content.Intent
import android.os.Bundle
import android.text.Editable
import android.text.Html
import android.view.View
import androidx.core.text.HtmlCompat
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.google.gson.Gson
import com.google.gson.reflect.TypeToken
import id.co.bri.brimo.R
import id.co.bri.brimo.adapters.HeaderTransaksiAdapter
import id.co.bri.brimo.databinding.ActivityComplaintKartuKreditBinding
import id.co.bri.brimo.domain.config.Constant
import id.co.bri.brimo.domain.helpers.GeneralHelper
import id.co.bri.brimo.domain.helpers.ValidationHelper
import id.co.bri.brimo.models.DurasiModel
import id.co.bri.brimo.models.apimodel.request.ssc.CreateTicketCcBillReq
import id.co.bri.brimo.models.apimodel.request.ssc.CreateTicketCcPayReq
import id.co.bri.brimo.models.apimodel.response.*
import id.co.bri.brimo.models.apimodel.response.ssc.ComplaintInformasiResponse
import id.co.bri.brimo.models.apimodel.response.ssc.ComplaintMutasiResponse
import id.co.bri.brimo.models.apimodel.response.ssc.ComplaintTicketCreateRes
import id.co.bri.brimo.ui.activities.GeneralSyaratActivity
import id.co.bri.brimo.ui.customviews.dialog.DialogExitCustom
import id.co.bri.brimo.ui.customviews.edittext.NumberCardEditText

class ComplaintKartuKreditActivity : BaseComplaintActivity(),
    DialogExitCustom.DialogClickYesNoListener,
    NumberCardEditText.NumberCardCallback,
    View.OnClickListener {

    private lateinit var binding: ActivityComplaintKartuKreditBinding

    private var isTerm = false

    private var isCardNum: Boolean = false

    companion object {
        private const val TAG_TOOLBAR = "toolbar"
        private const val TAG_TRANSRESPONSE = "transaksi_response"
        private const val TAG_INFORESPONSE = "informasi_response"
        private const val TAG_ISSKIPMUTATION = "is_skip_mutation"
        private const val TAG_FEATURE_ID = "feature_id"
        private const val TAG_ACCOUNTS = "account"

        @JvmStatic
        fun launchIntentComplntCc(
            caller: Activity, sToolbar: String,
            informasiResponse: ComplaintInformasiResponse,
            transactionResponse: ComplaintMutasiResponse.Transaction
        ) {
            val intent = Intent(caller, ComplaintKartuKreditActivity::class.java)
            intent.putExtra(TAG_TOOLBAR, sToolbar)
            intent.putExtra(TAG_TRANSRESPONSE, Gson().toJson(transactionResponse))
            intent.putExtra(TAG_INFORESPONSE, Gson().toJson(informasiResponse))
            caller.startActivityForResult(intent, Constant.REQ_NON_PAYMENT)
        }

        @JvmStatic
        fun launchIntentComplntCcBill(
            caller: Activity, sToolbar: String,
            accounts: List<ListRekeningResponse.Account>,
            informasiResponse: ComplaintInformasiResponse,
            sFeatureId: String
        ) {
            val intent = Intent(caller, ComplaintKartuKreditActivity::class.java)
            intent.putExtra(TAG_TOOLBAR, sToolbar)
            intent.putExtra(TAG_ISSKIPMUTATION, true)
            intent.putExtra(TAG_ACCOUNTS, Gson().toJson(accounts))
            intent.putExtra(TAG_INFORESPONSE, Gson().toJson(informasiResponse))
            intent.putExtra(TAG_FEATURE_ID, sFeatureId)
            caller.startActivityForResult(intent, Constant.REQ_NON_PAYMENT)
        }
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        binding = ActivityComplaintKartuKreditBinding.inflate(layoutInflater)
        setContentView(binding.root)

        intentExtra()
        setToolbar()
        setupDataRes()
        setupViews()

    }

    override fun intentExtra() {
        if (intent.extras != null) {
            if (intent.hasExtra(InformasiTransaksiActivity.TAG_TOOLBAR))
                sToolbar = intent.getStringExtra(InformasiTransaksiActivity.TAG_TOOLBAR).toString()

            if (intent.hasExtra(InformasiTransaksiActivity.TAG_TRANSRESPONSE))
                transactionRes = Gson().fromJson(
                    intent.getStringExtra(InformasiTransaksiActivity.TAG_TRANSRESPONSE),
                    ComplaintMutasiResponse.Transaction::class.java
                )

            if (intent.hasExtra(InformasiTransaksiActivity.TAG_INFORESPONSE))
                informationRes = Gson().fromJson(
                    intent.getStringExtra(InformasiTransaksiActivity.TAG_INFORESPONSE),
                    ComplaintInformasiResponse::class.java
                )

            if (intent.hasExtra(TAG_ISSKIPMUTATION))
                isSkipMutation = intent.getBooleanExtra(TAG_ISSKIPMUTATION, false)

            if (intent.hasExtra(TAG_FEATURE_ID))
                sFeatureId = intent.getStringExtra(TAG_FEATURE_ID).toString()

            if (intent.hasExtra(TAG_ACCOUNTS)) {
                val collectionType =
                    object : TypeToken<Collection<ListRekeningResponse.Account>>() {}.type
                val gson = Gson()
                val jsonString = intent.extras?.getString(TAG_ACCOUNTS)
                val enums = gson.fromJson<Collection<ListRekeningResponse.Account>>(
                    jsonString,
                    collectionType
                )
                accountList!!.addAll(enums)
            }
        }
    }

    override fun setToolbar() {
        GeneralHelper.setToolbar(this, binding.toolbar.toolbar, sToolbar)
    }

    override fun setupDataRes() {
        if (transactionRes != null) sTrxDetail = transactionRes!!.detail

        if (informationRes != null) {
            if (informationRes!!.information.isNotEmpty())
                dataInformationList = informationRes!!.information as ArrayList<DataView>?

            if (informationRes!!.term.isNotEmpty())
                sTerm = informationRes!!.term
        }
    }

    override fun setupViews() {
        if (isSkipMutation)
            presenter.setUrlCreateTicket(GeneralHelper.getString(R.string.url_ss_complaint_ticket_create_non_mutation))
        else
            presenter.setUrlCreateTicket(GeneralHelper.getString(R.string.url_ss_complaint_ticket_create))

        onTransactionInformation()

        binding.layoutPermasalahanTerm.tvDescSyarat.text =
            Html.fromHtml(GeneralHelper.getString(R.string.syarat_ssc))

        showViews()

        binding.etAlamatEmail.addTextChangedListener(activityTextListener)
        binding.layoutPermasalahanTerm.etPermasalahan.addTextChangedListener(activityTextListener)

        binding.etJenisLaporan.isLongClickable = false
        binding.etTanggal.isLongClickable = false
        binding.etJenisLaporan.setOnClickListener(this)
        binding.etTanggal.setOnClickListener(this)
        binding.layoutPermasalahanTerm.layoutSyarat.setOnClickListener(this)
        binding.btnSubmit.setOnClickListener(this)

        binding.etNoKartuKredit.numberCardEditText(this, ' ', 19)
    }

    override fun recyclerTrxInfo(headerTransaksiAdapter: HeaderTransaksiAdapter) {
        binding.layoutInfoTrx.recyclerview.setHasFixedSize(true)
        binding.layoutInfoTrx.recyclerview.layoutManager = LinearLayoutManager(
            applicationContext, RecyclerView.VERTICAL, false
        )
        binding.layoutInfoTrx.recyclerview.adapter = headerTransaksiAdapter
    }

    override fun notFoundTrxInfo() {
        binding.layoutInfoTrx.layoutInfoTrx.visibility = View.GONE
    }

    override fun afterText(editable: Editable?) {

        if (binding.layoutPermasalahanTerm.etPermasalahan.toString()
                .isNotEmpty() && binding.layoutPermasalahanTerm.etPermasalahan.isFocused
        )
            binding.layoutPermasalahanTerm.tvLength.text = editable!!.length.toString() + "/200"

        validationButton()
    }

    override fun showViews() {
        if (isSkipMutation) {
            binding.layoutInfoTrx.layoutInfoTrx.visibility = View.GONE
            binding.layoutNoKartuKredit.visibility = View.VISIBLE
            binding.layoutJenisLaporan.visibility = View.GONE
            binding.layoutInformasi.visibility = View.VISIBLE
            binding.layoutAlamatEmail.visibility = View.VISIBLE
        } else {
            binding.layoutNoKartuKredit.visibility = View.VISIBLE
            binding.layoutJenisLaporan.visibility = View.GONE
            binding.layoutAlamatEmail.visibility = View.GONE
            binding.layoutTanggal.visibility = View.GONE
        }
    }

    override fun enableButton(isEnable: Boolean) {
        if (isEnable) {
            binding.btnSubmit.isEnabled = true
            binding.btnSubmit.alpha = 1f
        } else {
            binding.btnSubmit.isEnabled = false
            binding.btnSubmit.alpha = 0.3f
        }
    }

    override fun validationButton() {
        enableButton(false)
        if (isSkipMutation) {
            if (!isCardNum)
                return
            if (binding.etAlamatEmail.text.toString().isEmpty() ||
                !ValidationHelper.isValidEmail(binding.etAlamatEmail.text.toString())
            )
                return
            if (binding.layoutPermasalahanTerm.etPermasalahan.text.toString().isEmpty())
                return
            if (!isTerm)
                return
        } else {
            if (!isCardNum)
                return
            if (binding.layoutPermasalahanTerm.etPermasalahan.text.toString().isEmpty())
                return
            if (!isTerm)
                return
        }

        enableButton(true)
    }

    override fun onClick(v: View) {
        when (v.id) {
            binding.etTanggal.id -> setCalendar()
            binding.layoutPermasalahanTerm.layoutSyarat.id -> launchTerm()
            binding.btnSubmit.id -> buttonClickSendReq()
        }
    }

    private fun launchTerm() {
        GeneralSyaratActivity.launchIntentNoArrow(
            this,
            sTerm,
            true
        )
    }

    private fun setDuration(mDurasiModel: DurasiModel) {
        val dateString = mDurasiModel.startDateStringddMMyyyySlash
        binding.etTanggal.setText(dateString)
        sDate = mDurasiModel.startDateString
    }

    private fun showDialogEmail(email: String) {
        val dialogExitCustom = DialogExitCustom(
            this,
            GeneralHelper.getString(R.string.confirmation_email),
            HtmlCompat.fromHtml(
                String.format(
                    GeneralHelper.getString(R.string.confirm_email_cc), email
                ), HtmlCompat.FROM_HTML_MODE_LEGACY
            ),
            GeneralHelper.getString(R.string.ubah_email),
            GeneralHelper.getString(R.string.confirmation),
            false,
            false
        )
        val ft = supportFragmentManager.beginTransaction()
        ft.add(dialogExitCustom, null)
        ft.commitAllowingStateLoss()
    }

    override fun onClickBtnYes() {
        val createTicketCcPayReq = CreateTicketCcPayReq(
            binding.etNoKartuKredit.text.toString().replace(" ", ""),
            binding.etAlamatEmail.text.toString(),
            binding.layoutPermasalahanTerm.etPermasalahan.text.toString(),
            sFeatureId
        )
        presenter.sendCreateTicket(createTicketCcPayReq)
    }

    override fun onClickBtnNo() {
        // do nothing
    }

    override fun buttonClickSendReq() {
        if (isSkipMutation) {
            showDialogEmail(binding.etAlamatEmail.text.toString())
        } else {
            val createTicketCcBillReq = CreateTicketCcBillReq(
                binding.etNoKartuKredit.text.toString(),
                binding.layoutPermasalahanTerm.etPermasalahan.text.toString(),
                informationRes!!.referenceNumber
            )
            presenter.sendCreateTicket(createTicketCcBillReq)
        }
    }

    @Suppress("DEPRECATION")
    @Deprecated("Deprecated in Java")
    override fun onActivityResult(requestCode: Int, resultCode: Int, data: Intent?) {
        super.onActivityResult(requestCode, resultCode, data)

        if (requestCode == Constant.REQ_PETTUNJUK1 && data != null && resultCode == RESULT_OK) {
            isTerm = java.lang.Boolean.parseBoolean(data.getStringExtra("checkbox"))
            if (isTerm) {
                binding.layoutPermasalahanTerm.cbSyarat.setBackgroundResource(R.drawable.checkbox_on)
            } else {
                binding.layoutPermasalahanTerm.cbSyarat.setBackgroundResource(R.drawable.checkbox_off)
            }
            validationButton()
        }

        if (requestCode == Constant.REQ_CALENDAR && data != null && resultCode == RESULT_OK) {
            val durasiModel = DurasiModel(
                data.getIntExtra(Constant.START_DAY, 0),
                data.getIntExtra(Constant.START_MONTH, 0),
                data.getIntExtra(Constant.START_YEAR, 0)
            )
            setDuration(durasiModel)
            validationButton()
        }

        if (requestCode == Constant.REQ_NON_PAYMENT && data != null) {
            this.setResult(RESULT_CANCELED, data)
            finish()
        }
    }

    override fun onSuccessCreateTicket(response: ComplaintTicketCreateRes) {
        LaporanTransaksiActivity.launchIntent(this, sToolbar, response, Constant.CIAType.TYPE_COMPLAINT_IN_APPS_GENERAL)
    }

    override fun itemNumberCard(isCardNum: Boolean) {
        this.isCardNum = isCardNum
        validationButton()
    }
}