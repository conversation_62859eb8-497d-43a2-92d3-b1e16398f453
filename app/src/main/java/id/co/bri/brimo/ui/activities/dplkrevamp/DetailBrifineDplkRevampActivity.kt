package id.co.bri.brimo.ui.activities.dplkrevamp

import android.app.Activity
import android.content.ClipData
import android.content.ClipboardManager
import android.content.Intent
import android.os.Bundle
import android.widget.LinearLayout
import androidx.core.view.isVisible
import androidx.fragment.app.Fragment
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.swiperefreshlayout.widget.SwipeRefreshLayout
import androidx.viewpager.widget.ViewPager
import id.co.bri.brimo.R
import id.co.bri.brimo.adapters.CatatanKeuanganAdapter
import id.co.bri.brimo.adapters.dplkrevamp.DplkDetailViewAdapter
import id.co.bri.brimo.contract.IPresenter.dplkrevamp.IDetailBrifineDplkRevampPresenter
import id.co.bri.brimo.contract.IView.dplkrevamp.IDetailBrifineDplkRevampView
import id.co.bri.brimo.databinding.ActivityDetailBrifineDplkRevampBinding
import id.co.bri.brimo.domain.config.Constant
import id.co.bri.brimo.domain.helpers.GeneralHelper
import id.co.bri.brimo.models.ParameterKonfirmasiModel
import id.co.bri.brimo.models.ParameterModel
import id.co.bri.brimo.models.apimodel.request.dplkrevamp.DetailBrifineDplkRequest
import id.co.bri.brimo.models.apimodel.request.dplkrevamp.DetailKlaimDplkRequest
import id.co.bri.brimo.models.apimodel.request.dplkrevamp.KlaimDplkRequest
import id.co.bri.brimo.models.apimodel.response.GeneralConfirmationResponse
import id.co.bri.brimo.models.apimodel.response.QuestionResponse
import id.co.bri.brimo.models.apimodel.response.ReceiptRevampResponse
import id.co.bri.brimo.models.apimodel.response.dompetdigitalrevamp.InquiryDompetDigitalResponse
import id.co.bri.brimo.models.apimodel.response.dplkrevamp.DetailBrifineDplkResponse
import id.co.bri.brimo.models.apimodel.response.emas.SafetyModeDrawerResponse
import id.co.bri.brimo.ui.activities.DetailPusatBantuanActivity
import id.co.bri.brimo.ui.activities.base.BaseActivity
import id.co.bri.brimo.ui.activities.general.ReceiptStatusActivity
import id.co.bri.brimo.ui.customviews.dialog.DialogContinueCustom
import id.co.bri.brimo.ui.fragments.BaseFragment
import id.co.bri.brimo.ui.fragments.BottomFragmentSafteyMode
import id.co.bri.brimo.ui.fragments.bottomsheet.OpenBottomSheetGeneralFragment
import id.co.bri.brimo.ui.fragments.dplkrevamp.BottomKlaimBrifineFragment
import id.co.bri.brimo.ui.fragments.dplkrevamp.DetailBrifineDplkFragment
import id.co.bri.brimo.ui.fragments.dplkrevamp.MutasiOnDetailBrifineFragment
import id.co.bri.brimo.util.extension.fromJsonToObject
import id.co.bri.brimo.util.extension.fromObjectToJson
import javax.inject.Inject

class DetailBrifineDplkRevampActivity :
    BaseActivity(),
    IDetailBrifineDplkRevampView,
    DplkDetailViewAdapter.OnClickItemListener,
    SwipeRefreshLayout.OnRefreshListener,
    BottomFragmentSafteyMode.DialogDefaulListener,
    DialogContinueCustom.DialogContinueDefaultListener, ViewPager.OnPageChangeListener {

    private lateinit var binding: ActivityDetailBrifineDplkRevampBinding
    private lateinit var bottomKlaimBrifine: BottomKlaimBrifineFragment

    private var detailFragment: DetailBrifineDplkFragment = DetailBrifineDplkFragment()
    private var mutationFragment: MutasiOnDetailBrifineFragment = MutasiOnDetailBrifineFragment()
    private var titleList: MutableList<String>? = mutableListOf()
    private var fragmentList: MutableList<Fragment>? = mutableListOf()

    private var dplkDetailViewAdapter: DplkDetailViewAdapter? = null
    private var fragmentAdapter: CatatanKeuanganAdapter? = null
    private var linearLayout: LinearLayout? = null

    private var myClipboard: ClipboardManager? = null
    private var myClip: ClipData? = null
    private lateinit var inquiryRevampResponse: InquiryDompetDigitalResponse

    @Inject
    lateinit var presenter: IDetailBrifineDplkRevampPresenter<IDetailBrifineDplkRevampView>

    companion object {
        private lateinit var detailBrifineDplkResponse: DetailBrifineDplkResponse
        private const val TAG_INTENT_DETAIL_BRIFINE = "TAG_INTENT_DETAIL_BRIFINE"

        const val IS_FROM_RECEIPT = "IS_FROM_RECEIPT"

        @JvmStatic
        fun launchIntent(caller: Activity, response: DetailBrifineDplkResponse) {
            val intent = Intent(caller, DetailBrifineDplkRevampActivity::class.java)
            intent.putExtra(TAG_INTENT_DETAIL_BRIFINE, response.fromObjectToJson())
            caller.startActivityForResult(intent, Constant.REQ_PAYMENT)
        }
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        binding = ActivityDetailBrifineDplkRevampBinding.inflate(layoutInflater)
        setContentView(binding.root)
        setupView()
        injectDepedency()
        initListener()
    }

    private fun initListener() {
        binding.btnSubmit.setOnClickListener {
            presenter.getDataInquiryTopUp(detailBrifineDplkResponse.accountNumber)
        }

        binding.btnAjukanKlaim.setOnClickListener {
            if (detailBrifineDplkResponse.isSafetyModeOn) {
                OpenBottomSheetGeneralFragment.showDialogInformation(
                    fragmentManager = supportFragmentManager,
                    imgPath = "",
                    imgName = "",
                    titleTxt = detailBrifineDplkResponse.safetyModePopup.title,
                    subTitleTxt = detailBrifineDplkResponse.safetyModePopup.description,
                    btnFirstFunction = {
                    },
                    btnThirdFunction = {
                        presenter.getPusatBantuanSafety(Constant.ID_PUSAT_BANTUAN_SAFETY_MODE)
                    },
                    isClickableOutside = false,
                    firstBtnTxt = detailBrifineDplkResponse.safetyModePopup.firstButtonString,
                    thirdBtnTxt = detailBrifineDplkResponse.safetyModePopup.secondButtonString
                )
            } else {
                showBottomFragmentAjukanKlaim()
            }
        }
    }

    private fun injectDepedency() {
        activityComponent.inject(this)
        presenter.view = this
        presenter.setInquiryUrlTopUp(GeneralHelper.getString(R.string.url_dplk_inquiry_v3))
        presenter.setUrlDetailDplk(GeneralHelper.getString(R.string.url_detail_dplk))
        presenter.setUrlClaimDplk(getString(R.string.url_inquiry_konfirmasi_klaim_dplk))
        presenter.setUrlPusatBantuan(GeneralHelper.getString(R.string.url_safety_pusat_bantuan))
        presenter.getClaimDplkUrl(GeneralHelper.getString(R.string.url_detail_klaim_brifine))
        presenter.start()
    }

    private fun setupView() {
        binding.apply {
            intent.getStringExtra(TAG_INTENT_DETAIL_BRIFINE)?.fromJsonToObject(DetailBrifineDplkResponse())?.let {
                detailBrifineDplkResponse = it
            }
            GeneralHelper.setToolbarRevamp(
                this@DetailBrifineDplkRevampActivity,
                tbDetailDplk.toolbar,
                GeneralHelper.getString(R.string.txt_detail_brifine)
            )

            tvNoBrifine.text = detailBrifineDplkResponse.accountNumber
            tvTotalInvestasi.text = detailBrifineDplkResponse.balanceTotalString
            tvProfit.text =
                detailBrifineDplkResponse.returnTotalString + " " + detailBrifineDplkResponse.percentageString

            GeneralHelper.loadImageUrl(
                this@DetailBrifineDplkRevampActivity,
                detailBrifineDplkResponse.iconPath,
                binding.icBrifine,
                R.drawable.bri,
                0
            )

            swipeRefresh.setOnRefreshListener(this@DetailBrifineDplkRevampActivity)

            val compositionSize = detailBrifineDplkResponse.compotitionView.size
            val titleText: String = if (compositionSize > 1) {
                GeneralHelper.getString(R.string.txt_total_investasi)
            } else {
                GeneralHelper.getString(R.string.txt_nilai_investasi)
            }
            tvTitleSaldo.text = titleText

            detailFragment.setData(detailBrifineDplkResponse)
            mutationFragment.setData(detailBrifineDplkResponse)

            dplkDetailViewAdapter = DplkDetailViewAdapter(
                this@DetailBrifineDplkRevampActivity,
                detailBrifineDplkResponse.detailView,
                this@DetailBrifineDplkRevampActivity
            )
            binding.rvDetailView.adapter = dplkDetailViewAdapter
            binding.rvDetailView.layoutManager = LinearLayoutManager(
                this@DetailBrifineDplkRevampActivity,
                LinearLayoutManager.VERTICAL,
                false
            )

            //ViewPager1
            fragmentList = mutableListOf(detailFragment, mutationFragment)
            titleList = mutableListOf(
                GeneralHelper.getString(R.string.detail),
                GeneralHelper.getString(R.string.mutasi_title_bar)
            )

            fragmentAdapter = CatatanKeuanganAdapter(
                supportFragmentManager,
                this@DetailBrifineDplkRevampActivity,
                fragmentList,
                titleList

            )
            vpDetail.adapter = fragmentAdapter
            stDetail.setViewPager(vpDetail)
            stDetail.setOnPageChangeListener(this@DetailBrifineDplkRevampActivity)
            linearLayout = stDetail.getChildAt(0) as LinearLayout

            GeneralHelper.changeTabsFontBoldRevamp(
                this@DetailBrifineDplkRevampActivity,
                linearLayout,
                0
            )

            binding.btnAjukanKlaim.isVisible = detailBrifineDplkResponse.isClaimAllowed

            when {
                detailBrifineDplkResponse.returnTotal < 0.0f -> {
                    ivProfit.setImageResource(R.drawable.ic_arrow_circle_down)
                    rlHarga.setBackgroundResource(R.drawable.bg_red_border)
                }

                detailBrifineDplkResponse.returnTotal == 0.0f -> {
                    with(binding) {
                        ivProfit.setImageResource(R.drawable.ic_empty_porto_reksadana)
                        rlHarga.setBackgroundResource(R.drawable.bg_primary_90_border)
                    }
                }

                else -> {
                    with(binding) {
                        ivProfit.setImageResource(R.drawable.ic_arrow_circle_up)
                        rlHarga.setBackgroundResource(R.drawable.bg_green_border)
                    }
                }
            }


            ivIcCopy.setOnClickListener {
                myClipboard =
                    <EMAIL>(CLIPBOARD_SERVICE) as ClipboardManager
                myClip = ClipData.newPlainText(
                    GeneralHelper.getString(R.string.txt_text),
                    detailBrifineDplkResponse.accountNumber
                )
                myClip?.let {
                    myClipboard?.setPrimaryClip(it)
                }
                showSnackbarErrorMessage(
                    GeneralHelper.getString(R.string.txt_copy_brifine),
                    BaseFragment.ALERT_CONFIRM,
                    this@DetailBrifineDplkRevampActivity,
                    true
                )
            }
        }
    }

    override fun onActivityResult(requestCode: Int, resultCode: Int, data: Intent?) {
        super.onActivityResult(requestCode, resultCode, data)

        if (requestCode == Constant.REQ_AUTO_PAYMENT && data != null) {
            if (resultCode == RESULT_CANCELED) {
                val errorMessage = data.getStringExtra(Constant.TAG_ERROR_MESSAGE)
                showSnackbarErrorMessageRevamp(errorMessage, ALERT_ERROR, this, false)
            } else if (resultCode == RESULT_OK) {
                val successMessage = data.getStringExtra(Constant.TAG_MESSAGE)
                showSnackbarErrorMessageRevamp(successMessage, ALERT_CONFIRM, this, false)
                onRefresh()
                val intent = Intent()
                this.setResult(RESULT_OK, intent)
            } else {
                val errorMessage = data.getStringExtra(Constant.TAG_ERROR_MESSAGE)
                showSnackbarErrorMessageRevamp(errorMessage, ALERT_CONFIRM, this, false)
            }
        } else if (requestCode == Constant.REQ_PAYMENT && data != null) {
            if (resultCode == RESULT_CANCELED) {
                if (data.getStringExtra(Constant.TAG_ERROR_MESSAGE) != null) {
                    val msg = data.getStringExtra(Constant.TAG_ERROR_MESSAGE)
                    showSnackbarErrorMessageRevamp(msg, ALERT_ERROR, this, false)
                } else {
                    this.setResult(RESULT_CANCELED, data)
                    finish()
                }
            } else if (resultCode == RESULT_OK) {
                if (data.getBooleanExtra(IS_FROM_RECEIPT, false)) {
                    setResult(RESULT_OK, data)
                    finish()
                } else {
                    setResult(RESULT_OK)
                    finish()
                }
            } else {
                val intent = Intent()
                intent.putExtra(Constant.TAG_MESSAGE, data)
                this.setResult(RESULT_OK, intent)
                this.finish()
            }
        }
    }

    override fun onSuccessGetDetailBrfineDplk(response: DetailBrifineDplkResponse) {
        detailBrifineDplkResponse = response
        binding.swipeRefresh.isRefreshing = false
        setupView()
    }

    override fun onSuccessInquiryClaimDplk(response: GeneralConfirmationResponse) {
        bottomKlaimBrifine.dismiss()
        KonfirmasiInquiryGeneralActivity.launchIntent(
            this, response, setParameterKonfirmasi(),
            getString(
                R.string.url_submit_klaim_dplk
            )
        )
    }

    override fun onSuccessGetPusatBantuan(response: QuestionResponse) {
        DetailPusatBantuanActivity.launchIntent(this, response, response.topicName)
    }

    override fun onExceptionClaim(message: String) {
        bottomKlaimBrifine.dismiss()
    }

    override fun onSafetyMode(response: SafetyModeDrawerResponse) {
        val bottomFragmentSafteyMode = BottomFragmentSafteyMode(
            this,
            response.drawerContent.title,
            response.drawerContent.description,
            getString(R.string.txt_saya_mengerti),
            getString(R.string.txt_pelajari_lebih_lanjut_rekening),
            Constant.OPEN_ACCOUNT_S3f
        )
        bottomFragmentSafteyMode.show(supportFragmentManager, "")
    }

    override fun onSuccessGetInquiry(response: InquiryDompetDigitalResponse) {
        inquiryRevampResponse = response
        if (!inquiryRevampResponse.isAllowed) {
            val dialogContinueCustom = DialogContinueCustom(
                this,
                inquiryRevampResponse.invalidClaim.title,
                inquiryRevampResponse.invalidClaim.desc,
                GeneralHelper.getString(R.string.txt_button_dialog_process_claim_auto_payment_dplk),
                GeneralHelper.getString(R.string.kembali),
                false
            )
            val ft = this.supportFragmentManager.beginTransaction()
            ft.add(dialogContinueCustom, null)
            ft.commitAllowingStateLoss()
        } else {
            InquiryDplkRevampActivity.launchIntent(
                this,
                inquiryRevampResponse,
                GeneralHelper.getString(R.string.url_dplk_confirmation_v3),
                GeneralHelper.getString(R.string.url_dplk_pay_v3),
                isFromFastMenu,
                setParameter(),
                ""
            )
        }

    }

    override fun onSuccessGetHistoryDetailClaimDplk(data: ReceiptRevampResponse) {
        ReceiptStatusActivity.launchIntent(
            this, data.referenceNumber, data, false,
            Constant.TRX_TYPE_KLAIM_DPLK_DETAIL
        )
    }

    override fun onException(message: String?) {
        super.onException(message)
    }


    fun setParameterKonfirmasi(): ParameterKonfirmasiModel {
        val parameterKonfirmasiModel = ParameterKonfirmasiModel()
        parameterKonfirmasiModel.stringLabelTujuan = "Nomor Tujuan"
        parameterKonfirmasiModel.stringButtonSubmit = "Lanjutkan"
        return parameterKonfirmasiModel
    }

    private fun setParameter(): ParameterModel {
        val parameterModel = ParameterModel()
        parameterModel.stringLabelTujuan = GeneralHelper.getString(R.string.txt_nomor_brifine)
        parameterModel.stringButtonSubmit = GeneralHelper.getString(R.string.lanjutkan)
        parameterModel.defaultIcon = getDefaultIconResource()
        return parameterModel
    }

    private fun getDefaultIconResource(): Int {
        return R.drawable.ic_dplk
    }

    override fun onClickBtnInfo() {
        showBottomSheetInfo()
    }

    private fun showBottomSheetInfo() {
        OpenBottomSheetGeneralFragment.showDialogInformationWithNoImage(
            supportFragmentManager,
            "",
            "",
            detailBrifineDplkResponse.iuranInfo.title,
            detailBrifineDplkResponse.iuranInfo.description,
            {},
            true,
            GeneralHelper.getString(R.string.btn_tutup)
        )
    }

    override fun onRefresh() {
        presenter.getDetailDplk(DetailBrifineDplkRequest(detailBrifineDplkResponse.account, true))

    }

    fun showBottomFragmentAjukanKlaim() {
        bottomKlaimBrifine = BottomKlaimBrifineFragment(
            detailBrifineDplkResponse.claimDetail
        ) {
            presenter.getDataInquiryClaimDplk(KlaimDplkRequest(detailBrifineDplkResponse.account))
        }
        bottomKlaimBrifine.show(supportFragmentManager, "")
    }

    override fun onClickToSafety() {
        presenter.getPusatBantuanSafety(Constant.ID_PUSAT_BANTUAN_SAFETY_MODE)
    }

    override fun onClickContinueYes() {
        presenter.getDetailClaimBrifine(
            DetailKlaimDplkRequest(
                inquiryRevampResponse.invalidClaim.trxId,
                inquiryRevampResponse.invalidClaim.account
            )
        )
    }

    override fun onClickContinueNo() {
    }

    override fun onPageScrolled(position: Int, positionOffset: Float, positionOffsetPixels: Int) {
    }

    override fun onPageSelected(position: Int) {
        GeneralHelper.changeTabsFontBoldRevamp(this, linearLayout, position)
    }

    override fun onPageScrollStateChanged(state: Int) {
    }

}