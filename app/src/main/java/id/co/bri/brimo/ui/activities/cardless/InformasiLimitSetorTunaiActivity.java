package id.co.bri.brimo.ui.activities.cardless;

import android.app.Activity;
import android.content.Intent;
import android.os.Bundle;
import androidx.appcompat.app.AppCompatActivity;
import id.co.bri.brimo.R;
import id.co.bri.brimo.databinding.ActivityInformasiLimitSetorTunaiBinding;
import id.co.bri.brimo.domain.config.Constant;
import id.co.bri.brimo.domain.helpers.GeneralHelper;

public class InformasiLimitSetorTunaiActivity extends AppCompatActivity {

    private ActivityInformasiLimitSetorTunaiBinding binding;

    private static String mHtmlData;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        binding = ActivityInformasiLimitSetorTunaiBinding.inflate(getLayoutInflater());
        setContentView(binding.getRoot());

        GeneralHelper.setToolbar(this, binding.tbPetunjuk.toolbar, GeneralHelper.getString(R.string.txt_informasi_limit));
        GeneralHelper.setWebViewStandart(binding.webViewPetunjuk, "", mHtmlData);
    }

    public static void launchIntent(Activity caller, String htmlData) {
        mHtmlData = htmlData;
        Intent intent = new Intent(caller, InformasiLimitSetorTunaiActivity.class);
        caller.startActivityForResult(intent, Constant.REQ_PAYMENT);
    }

    @Override
    protected void onDestroy() {
        super.onDestroy();
        binding = null;
    }
}