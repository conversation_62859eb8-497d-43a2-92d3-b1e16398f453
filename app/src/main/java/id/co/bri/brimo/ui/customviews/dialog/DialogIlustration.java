package id.co.bri.brimo.ui.customviews.dialog;

import android.app.Activity;
import android.app.Dialog;
import android.content.Context;
import android.graphics.drawable.ColorDrawable;
import android.os.Bundle;
import android.util.Log;
import android.view.Window;
import android.widget.Button;
import android.widget.ImageView;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.fragment.app.DialogFragment;

import android.widget.TextView;

import com.google.gson.Gson;

import id.co.bri.brimo.R;
import id.co.bri.brimo.domain.helpers.GeneralHelper;

/**
 * Created by DTAP on 01/07/2022
 */
public class DialogIlustration extends DialogFragment {
    private Dialog alertDialog;
    private Button btnAksi;
    private TextView txtTitle, txtDetail;
    private ImageView ivClose, ivIlustration;
    private String mTitle, mDrawable, mDetail, mBtnTitle;
    private Context mContext;

    public DialogIlustration(Context context, String drawable, String title, String detail, String btnTitle) {
        this.mContext = context;
        this.mDrawable = drawable;
        this.mTitle = title;
        this.mDetail = detail;
        this.mBtnTitle = btnTitle;
    }

    @NonNull
    @Override
    public Dialog onCreateDialog(@Nullable Bundle savedInstanceState) {
        alertDialog = new Dialog(getActivity());
        alertDialog.setCanceledOnTouchOutside(false);
        alertDialog.getWindow().requestFeature(Window.FEATURE_NO_TITLE);
        alertDialog.getWindow().setBackgroundDrawable(new ColorDrawable(0));
        alertDialog.setContentView(R.layout.dialog_ilustration);
        alertDialog.setOnKeyListener((dialogInterface, i, keyEvent) -> false);
        alertDialog.show();
        init();
        txtTitle.setText(mTitle);
        txtDetail.setText(mDetail);
        ivIlustration.setImageResource(GeneralHelper.getImageId(mContext, mDrawable));
        btnAksi.setOnClickListener(view -> {
            dismiss();
        });
        ivClose.setOnClickListener(view ->{
            dismiss();
        });

        return alertDialog;
    }


    private void init() {
        btnAksi = alertDialog.findViewById(R.id.btn_aksi);
        ivClose = alertDialog.findViewById(R.id.iv_close_tutorial);
        ivIlustration = alertDialog.findViewById(R.id.iv_ilustration);
        txtTitle = alertDialog.findViewById(R.id.txt_title);
        txtDetail = alertDialog.findViewById(R.id.txt_detail);
    }
}
