package id.co.bri.brimo.ui.activities.bukaValas;


import android.app.Activity;
import android.content.Intent;
import android.os.Bundle;
import android.os.SystemClock;
import android.text.Editable;
import android.text.TextWatcher;
import android.view.View;

import androidx.annotation.Nullable;
import androidx.fragment.app.FragmentTransaction;

import java.math.BigInteger;
import java.util.ArrayList;
import java.util.List;

import javax.inject.Inject;

import id.co.bri.brimo.R;
import id.co.bri.brimo.contract.IPresenter.bukaValas.IInquiryBukaValasPresenter;
import id.co.bri.brimo.contract.IView.bukaValas.IInquiryBukaValasView;
import id.co.bri.brimo.databinding.ActivityInquirySetoranAwalValasBinding;
import id.co.bri.brimo.domain.config.Constant;
import id.co.bri.brimo.domain.helpers.GeneralHelper;
import id.co.bri.brimo.models.AccountModel;
import id.co.bri.brimo.models.ParameterKonfirmasiModel;
import id.co.bri.brimo.models.apimodel.request.KonfimasiBukaValasRequest;
import id.co.bri.brimo.models.apimodel.response.KonfirmasiBukaValasResponse;
import id.co.bri.brimo.models.apimodel.response.KursList;
import id.co.bri.brimo.models.apimodel.response.KursNego;
import id.co.bri.brimo.models.apimodel.response.ListKantorResponse;
import id.co.bri.brimo.models.apimodel.response.PilihRekeningValasResponse;
import id.co.bri.brimo.models.apimodel.response.TopBriva;
import id.co.bri.brimo.models.optionmodel.OptionGeneralModel;
import id.co.bri.brimo.ui.activities.InfoKursActivity;
import id.co.bri.brimo.ui.activities.base.BaseActivity;
import id.co.bri.brimo.ui.customviews.dialog.DialogInformation;
import id.co.bri.brimo.ui.fragments.ListRekeningFragment;
import id.co.bri.brimo.ui.fragments.PilihanGeneralFragment;
import id.co.bri.brimo.ui.fragments.SumberDanaFragment;


public class InquiryBukaValasActivity extends BaseActivity implements
        View.OnClickListener,
        IInquiryBukaValasView,
        SumberDanaFragment.SelectSumberDanaInterface,
        PilihanGeneralFragment.SelectPilihanGeneral,
        DialogInformation.OnActionClick {

    private ActivityInquirySetoranAwalValasBinding binding;
    private static final String TAG = "InquiryBukaValas";

    protected static String TAG_INQUIRY_DATA = "inquiry_data";

    protected List<Integer> mListFailed;
    protected List<AccountModel> mListAccountModel;
    protected List<KursNego> kursNegoModels;
    private List<KursList> kursLists;
    protected AccountModel sumberDana;

    protected static PilihRekeningValasResponse mPilihRekeningValas;
    protected static KursList mKursList;
    protected static String mUrlKonfirmasi;
    protected static String mUrlPayment;
    protected static ListKantorResponse.Office officeList1;

    private static List<TopBriva> topBrivaList;
    private OptionGeneralModel currentOptionModel = null;

    protected String saldoString = "";
    protected String defaultAkun;


    int counter = 0;
    double hasil = 0;
    double ask = 0;
    double bid = 0;
    Double saldo = 0.0;
    String rateAwal = "1";
    String msaldoString = "";
    String account_from, akunDefault;
    double debitAmount = 0;
    double creditAmount = 0;
    double minimum = 0;
    double maximum = 0;
    double hasilIDR = 0;
    double bidConvert = 0;
    double askConvert = 0;
    protected static String errorMessage = null;
    protected static int position1;

    protected boolean isSaldoHold;

    protected String pembandingDariSumberDana, pembandingDariPenerima;

    @Inject
    IInquiryBukaValasPresenter<IInquiryBukaValasView> presenter;

    public static void launchIntent(Activity caller, PilihRekeningValasResponse pilihRekeningValasResponse, KursList kursList, String konfrimasiUrl1, String paymentUrl1, ListKantorResponse.Office officeList) {
        Intent intent = new Intent(caller, InquiryBukaValasActivity.class);
        mPilihRekeningValas = pilihRekeningValasResponse;
        mUrlKonfirmasi = konfrimasiUrl1;
        mUrlPayment = paymentUrl1;
        mKursList = kursList;
        officeList1 = officeList;
        caller.startActivityForResult(intent, Constant.REQ_PAYMENT);
    }

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        binding = ActivityInquirySetoranAwalValasBinding.inflate(getLayoutInflater());
        setContentView(binding.getRoot());

        injectDependency();

        if (binding.tbInquiryToolbar.toolbar != null) {
            GeneralHelper.setToolbar(this, binding.tbInquiryToolbar.toolbar, "Britama Valas");
        }

        binding.itemLayoutBackground.setOnClickListener(this);
        binding.btnSubmit.setOnClickListener(this);
        binding.etJenisTujuan.setOnClickListener(this);
        binding.tvInfoKurs.setOnClickListener(this);
        binding.btnSubmit.setEnabled(false);
        binding.btnSubmit.setAlpha(0.3f);


    }

    protected void injectDependency() {
        getActivityComponent().inject(this);

        if (presenter != null) {
            presenter.setView(this);
            presenter.setUrlConfirmation(mUrlKonfirmasi);
            presenter.start();


        }
    }

    /**
     * Callback dari get saldo default dari BRImoPref
     *
     * @param saldoDefault    saldo dalam double
     * @param saldoStringPref saldo dalam bentuk string
     * @param defaultAcc      rekening default
     * @param isSaldoHold     saldo tertahan
     */
    @Override
    public void setDefaultSaldo(double saldoDefault, String saldoStringPref, String defaultAcc, boolean isSaldoHold) {
        // load default saldo dari preference
        saldoString = saldoStringPref;
        saldo = saldoDefault;
        defaultAkun = defaultAcc;

//        if (edNominal != null) {
//            setupInputError(edNominal);
//        }

        // saldo tertahan
        this.isSaldoHold = isSaldoHold;
        if (isSaldoHold) {
            binding.ivAlertSaldo.setVisibility(View.VISIBLE);
        } else binding.ivAlertSaldo.setVisibility(View.GONE);

        //set binding.itemLayoutBackground
        setupAccount(saldoDefault);
        binding.etValasFrom.addTextChangedListener(new TextWatcher() {
            @Override
            public void beforeTextChanged(CharSequence s, int start, int count, int after) {

            }

            @Override
            public void onTextChanged(CharSequence s, int start, int before, int count) {

            }

            @Override
            public void afterTextChanged(Editable s) {
                if (s.length() > 0) {
                    try {
                        creditAmount = binding.etValasFrom.getCurrencyDouble();
//
                        // kalau idr to valas
                        if (s.length() != 0) {
                            if (binding.etValasFrom.isFocused()) {
                                debitAmount = creditAmount / hasilIDR;

                                binding.etValasTo.setText(GeneralHelper.formatNominalIDR(debitAmount));
                                pengecekanMinimum();
                            }
                        }

                    } catch (Exception e) {

                    }

                } else {
                    binding.btnSubmit.setEnabled(false);
                    binding.btnSubmit.setAlpha((float) 0.3);

                }

            }
        });

        binding.etValasTo.addTextChangedListener(new TextWatcher() {
            @Override
            public void beforeTextChanged(CharSequence s, int start, int count, int after) {

            }

            @Override
            public void onTextChanged(CharSequence s, int start, int before, int count) {

            }

            @Override
            public void afterTextChanged(Editable s) {
                if (s.length() > 0) {

                    try {

                        debitAmount = binding.etValasTo.getCurrencyDouble();
                    } catch (Exception e) {

                    }
                    if (s.length() != 0) {
                        // kalau idr to valas
                        if (binding.etValasTo.isFocused()) {
                            hasilIDR = GeneralHelper.round(hasil, 2);
                            creditAmount = debitAmount * hasilIDR;
                            pengecekanMinimum();

                            binding.etValasFrom.setText(GeneralHelper.formatNominalIDR(creditAmount));

                        }
                    }

                } else {
                    binding.btnSubmit.setEnabled(false);
                    binding.btnSubmit.setAlpha((float) 0.3);

                }

            }
        });

        binding.etJenisTujuan.addTextChangedListener(new TextWatcher() {
            @Override
            public void beforeTextChanged(CharSequence s, int start, int count, int after) {

            }

            @Override
            public void onTextChanged(CharSequence s, int start, int before, int count) {

            }

            @Override
            public void afterTextChanged(Editable s) {
                if (s.length() > 0) {
                    pengecekanMinimum();
                } else {
                    binding.btnSubmit.setEnabled(false);
                    binding.btnSubmit.setAlpha((float) 0.3);
                }
            }
        });
    }

    protected void setupAccount(double saldoDefault) {
        if (mPilihRekeningValas == null)
            finish();

        //List Account
        sumberDana = new AccountModel();
        kursNegoModels = mPilihRekeningValas.getKursNegoList();
        kursLists = mPilihRekeningValas.getKursList();
        if (mPilihRekeningValas.getAccountList().size() > 0)
            mListAccountModel = mPilihRekeningValas.getAccountList();

        for (AccountModel accountModel : mListAccountModel) {
            if (accountModel.getIsDefault() == 1) {
                sumberDana = accountModel;
                break;
            } else {
                sumberDana = mListAccountModel.get(0);
            }
        }

        //jika get minimum tidak null
        if (sumberDana.getMinimumBalance() != null) {
            saldo = saldoDefault - sumberDana.getMinimumBalance();
        } else {
            saldo = saldoDefault;
        }

        if (isFromFastMenu) {
            binding.tvSaldo.setVisibility(View.GONE);
        } else {
            binding.tvSaldo.setVisibility(View.VISIBLE);

            if (sumberDana.getAcoount() != null) {
                if (sumberDana.getAcoount().equals(defaultAkun)) {
                    binding.tvSaldo.setText(String.format("%s%s", sumberDana.getCurrency(), saldoString));
//                    binding.tvSaldo.setText(GeneralHelper.formatNominalIDR(sumberDana.getCurrency(), saldoString));
                } else {
                    binding.tvSaldo.setText("-");
                }
            }

        }

        //jika string rekening tidak kosong
        if (sumberDana.getAcoountString() != null) {
            binding.tvNorek.setText(sumberDana.getAcoountString());
        } else {
            binding.tvNorek.setText("-");
        }

        //jika nama kosong
        if (sumberDana.getName() != null) {
            binding.tvInisial.setText(GeneralHelper.formatInitialName(sumberDana.getName()));
        } else {
            binding.tvInisial.setText("I");
        }
        binding.tvValasto.setText(mKursList.getCurrency());
        GeneralHelper.loadIconTransaction(
                this,
                mKursList.getIconPath(),
                mKursList.getIconName(),
                binding.ivEtTo,
                0);

        if (kursNegoModels != null) {
            if (sumberDana.getCurrency().contains("Rp")) {
                // kalau idr to valas
                pembandingDariSumberDana = mKursList.getCurrency() + "-" + "IDR";

                //kalau idr to valas
                for (KursNego kursNegoModel : kursNegoModels) {
                    if (kursNegoModel.getCode().contains(pembandingDariSumberDana)) {
                        ask = kursNegoModel.getValueAsk();
                    }
                }
                binding.tvValasCurrencyTo.setText(mKursList.getCurrency());
                binding.tvValasCurrencyFrom.setText("IDR");
                bidConvert = GeneralHelper.round(bid, 2);
                askConvert = GeneralHelper.round(ask, 2);
                hasilIDR = 1 * askConvert;
                hasil = 1 * ask;

//                Log.d("InquiryKonversiValas", );

                binding.tvValasFrom.setText(GeneralHelper.formatNominalRateValas(hasil));
            }
        }

        GeneralHelper.loadIconTransaction(
                this,
                sumberDana.getIcon_path(),
                sumberDana.getIcon_name(),
                binding.ivEtFrom,
                0);
        binding.tvValasFrom.setText("IDR");
        binding.tvValasTo.setText(rateAwal);


        binding.tvSaldoMinimum.setVisibility(View.GONE);
        binding.tvError.setVisibility(View.VISIBLE);
        binding.tvError.setTextColor(getResources().getColor(R.color.colorErrorMinTrx));
        binding.tvError.setText(String.format("Minimum setoran awal %s%s", mKursList.getCurrency(), GeneralHelper.formatNominalBiasa(Double.valueOf(mKursList.getMinimum_setoran()))));

//        tv_valas_to.setText(rateAwal);
//        etValasFrom.setEnabled(false);
//        etValasTo.setEnabled(false);
    }

    public void pengecekanMinimum() {

        minimum = Double.parseDouble(mKursList.getMinimum_setoran());
        maximum = Double.parseDouble(mPilihRekeningValas.getMaximumTransaction());

        if (isSaldoHold) {
            binding.tvSaldoMinimum.setVisibility(View.VISIBLE);
            binding.tvSaldoMinimum.setTextColor(getResources().getColor(R.color.red));
            binding.tvSaldoMinimum.setText(GeneralHelper.getString(R.string.saldo_tertahan));
        } else {
            binding.tvSaldoMinimum.setVisibility(View.GONE);
        }

        if (!binding.etJenisTujuan.getText().toString().equalsIgnoreCase("")) {
            binding.btnSubmit.setAlpha(1);
            binding.btnSubmit.setEnabled(true);

            if (BigInteger.valueOf((long) minimum).compareTo(BigInteger.valueOf((long) debitAmount)) <= 0) {
                binding.tvError.setVisibility(View.GONE);
                binding.btnSubmit.setAlpha(1);
                binding.btnSubmit.setEnabled(true);

                if (BigInteger.valueOf((long) creditAmount).compareTo(BigInteger.valueOf(saldo.longValue())) <= 0) {
                    binding.tvSaldoMinimum.setVisibility(View.GONE);
                    binding.btnSubmit.setAlpha(1);
                    binding.btnSubmit.setEnabled(true);

                    if (BigInteger.valueOf((long) maximum).compareTo(BigInteger.valueOf((long) creditAmount)) > 0) {
                        binding.tvError.setVisibility(View.GONE);
                        binding.btnSubmit.setAlpha(1);
                        binding.btnSubmit.setEnabled(true);

                    } else {
                        binding.tvSaldoMinimum.setVisibility(View.VISIBLE);
                        binding.tvSaldoMinimum.setTextColor(getResources().getColor(R.color.color_red_c83131));
                        binding.tvSaldoMinimum.setText(String.format("Jumlah maksimal setoran awal setara dengan %s%s", "Rp", GeneralHelper.formatNominalBiasa(Double.valueOf(mPilihRekeningValas.getMaximumTransaction()))));
                        binding.btnSubmit.setEnabled(false);
                        binding.btnSubmit.setAlpha((float) 0.3);
                    }

                } else {
                    binding.tvError.setVisibility(View.GONE);
                    binding.tvSaldoMinimum.setVisibility(View.VISIBLE);
                    binding.tvSaldoMinimum.setTextColor(getResources().getColor(R.color.red));
                    if (isSaldoHold)
                        binding.tvSaldoMinimum.setText(GeneralHelper.getString(R.string.saldo_tertahan));
                    else
                        binding.tvSaldoMinimum.setText("Saldo Anda tidak cukup");
                    binding.btnSubmit.setEnabled(false);
                    binding.btnSubmit.setAlpha((float) 0.3);
                }

            } else {
                binding.tvSaldoMinimum.setVisibility(View.GONE);
                binding.tvError.setVisibility(View.VISIBLE);
                binding.tvError.setTextColor(getResources().getColor(R.color.colorErrorMinTrx));
                binding.btnSubmit.setEnabled(false);
                binding.btnSubmit.setAlpha((float) 0.3);
                binding.tvError.setText(String.format("Minimum setoran awal %s%s", mKursList.getCurrency(), GeneralHelper.formatNominalBiasa(Double.valueOf(mKursList.getMinimum_setoran()))));

            }
        } else {
            binding.btnSubmit.setEnabled(false);
            binding.btnSubmit.setAlpha((float) 0.3);
            if (BigInteger.valueOf((long) minimum).compareTo(BigInteger.valueOf((long) debitAmount)) <= 0) {
                binding.tvError.setVisibility(View.GONE);


                if (BigInteger.valueOf((long) creditAmount).compareTo(BigInteger.valueOf(saldo.longValue())) <= 0) {
                    binding.tvSaldoMinimum.setVisibility(View.GONE);

                    if (BigInteger.valueOf((long) maximum).compareTo(BigInteger.valueOf((long) creditAmount)) > 0) {
                        binding.tvError.setVisibility(View.GONE);

                    } else {
                        binding.tvSaldoMinimum.setVisibility(View.VISIBLE);
                        binding.tvSaldoMinimum.setTextColor(getResources().getColor(R.color.color_red_c83131));
                        binding.tvSaldoMinimum.setText(String.format("Jumlah maksimal setoran awal setara dengan %s%s", "Rp", GeneralHelper.formatNominalBiasa(Double.valueOf(mPilihRekeningValas.getMaximumTransaction()))));
                    }
                } else {
                    binding.tvError.setVisibility(View.GONE);
                    binding.tvSaldoMinimum.setVisibility(View.VISIBLE);
                    binding.tvSaldoMinimum.setTextColor(getResources().getColor(R.color.red));
                    if (isSaldoHold)
                        binding.tvSaldoMinimum.setText(GeneralHelper.getString(R.string.saldo_tertahan));
                    else binding.tvSaldoMinimum.setText("Saldo Anda tidak cukup");
                }

            } else {
                binding.tvSaldoMinimum.setVisibility(View.GONE);
                binding.tvError.setVisibility(View.VISIBLE);
                binding.tvError.setTextColor(getResources().getColor(R.color.colorErrorMinTrx));
                binding.tvError.setText(String.format("Minimum setoran awal %s%s", mKursList.getCurrency(), GeneralHelper.formatNominalBiasa(Double.valueOf(mKursList.getMinimum_setoran()))));

            }
        }
    }


    public List<TopBriva> topBrivaList1() {
        List<TopBriva> list = new ArrayList<TopBriva>();
        for (int i = 0; i < mPilihRekeningValas.getOpenPurpose().size(); i++) {
            list.add(new TopBriva("", mPilihRekeningValas.getOpenPurpose().get(i), "", ""));
        }
//        list.add(new TopBriva("0","Tabungan","",""));
//        list.add(new TopBriva("1","Investasi","",""));
//        list.add(new TopBriva("2","Bisnis","",""));

        return list;
    }

    private List<OptionGeneralModel> fetchOptionList() {
        List<OptionGeneralModel> list = new ArrayList<OptionGeneralModel>();
        topBrivaList = topBrivaList1();
        if (topBrivaList != null) {
            for (int i = 0; i < topBrivaList.size(); i++) {
                list.add(new OptionGeneralModel(0, topBrivaList.get(i).getName(), topBrivaList.get(i).getIconName(), ""));
            }
        }

        return list;
    }

    @Override
    public void onClick(View view) {
        if (SystemClock.elapsedRealtime() - mLastClickTime < 1000) {
            return;
        }
        mLastClickTime = SystemClock.elapsedRealtime();
        int id = view.getId();
        switch (id) {
            case R.id.item_layout_background:
                counter++;

                if (mListAccountModel == null) {
                    GeneralHelper.showToast(this, GeneralHelper.getString(R.string.you_dont_have_any_accounts_yet));
                } else {
                    if (isFromFastMenu) {
                        ListRekeningFragment fragmentSumberDana = new ListRekeningFragment(mListAccountModel, this, counter, mListFailed);
                        fragmentSumberDana.show(getSupportFragmentManager(), Constant.TAG_PICK_ACCOUNT);

                    } else {
                        SumberDanaFragment fragmentSumberDana = new SumberDanaFragment(mListAccountModel, this, counter, mListFailed, false);
                        fragmentSumberDana.show(getSupportFragmentManager(), Constant.TAG_PICK_ACCOUNT);

                    }
                }
                break;
            case R.id.btnSubmit:
                onSubmit();
                break;
            case R.id.etJenisTujuan:
                PilihanGeneralFragment fragmentPilihan = new PilihanGeneralFragment(fetchOptionList(), this);
                fragmentPilihan.show(getSupportFragmentManager(), "");
                break;
            case R.id.tv_info_kurs:
                InfoKursActivity.launchIntent(this);
                break;
        }
    }


    @Override
    public void onException93(String message) {
        Intent returnIntent = new Intent();

        returnIntent.putExtra(Constant.TAG_ERROR_MESSAGE, message);

        this.setResult(RESULT_CANCELED, returnIntent);
        this.finish();
    }

    @Override
    public void onSelectSumberDana(AccountModel bankModel) {
        sumberDana = bankModel;
        account_from = sumberDana.getCurrency();

        binding.etValasFrom.setEnabled(true);
        binding.etValasTo.setEnabled(true);

        if (bankModel.getSaldoReponse() != null) {
            binding.tvSaldo.setText(String.format("%s%s", sumberDana.getCurrency(), sumberDana.getSaldoReponse().getBalanceString()));
            saldo = sumberDana.getSaldoReponse().getBalance() - sumberDana.getMinimumBalance();
            isSaldoHold = sumberDana.getSaldoReponse().isOnHold();
        } else {
            binding.tvSaldo.setText(String.format("%s%s", sumberDana.getCurrency(), "-"));
            saldo = 0.0;
            isSaldoHold = false;
        }

        // saldo tertahan
        if (bankModel.getSaldoReponse().isOnHold()) {
            binding.ivAlertSaldo.setVisibility(View.VISIBLE);
            dialogInfoSaldoHold();
        } else binding.ivAlertSaldo.setVisibility(View.GONE);

        binding.tvNorek.setText(sumberDana.getAcoountString());
        binding.tvNorek.setVisibility(View.VISIBLE);
        binding.tvSaldo.setVisibility(View.VISIBLE);

        binding.tvSaldoMinimum.setVisibility(View.GONE);
        binding.tvError.setVisibility(View.VISIBLE);
        binding.tvError.setTextColor(getResources().getColor(R.color.colorErrorMinTrx));
        binding.tvError.setText(String.format("Minimum setoran awal %s%s", mKursList.getCurrency(), GeneralHelper.formatNominalBiasa(Double.valueOf(mKursList.getMinimum_setoran()))));

        pengecekanMinimum();
    }

    private void dialogInfoSaldoHold() {
        DialogInformation dialog = new DialogInformation(this, "maaf_maaf",
                GeneralHelper.getString(R.string.title_popup_saldo_tertahan),
                GeneralHelper.getString(R.string.desc_popup_saldo_tertahan),
                GeneralHelper.getString(R.string.ok), this, true, false);
        FragmentTransaction ft = getSupportFragmentManager().beginTransaction();
        ft.add(dialog, null);
        ft.commitAllowingStateLoss();
    }

    @Override
    public void onSendFailedList(List<Integer> list) {
        this.mListFailed = list;
    }

    @Override
    public void onClick(int position, OptionGeneralModel optionModel) {
        currentOptionModel = optionModel;
        binding.etJenisTujuan.setText(currentOptionModel.getOptionName());

    }

    public void onSubmit() {
        String credit = String.valueOf(creditAmount);
        String debit = String.valueOf(debitAmount);
        String newCredit = credit.replaceAll(",", "\\.");
        String newDebit = debit.replaceAll(",", "\\.");
        presenter.getDataKonfirmasi(new KonfimasiBukaValasRequest(sumberDana.getAcoount(), mKursList.getCurrency(), officeList1.getName(), newCredit, mPilihRekeningValas.getReferenceNumber(), newDebit, currentOptionModel.getOptionName(), officeList1.getId()));


    }


    @Override
    public void onSuccesConfirmation(KonfirmasiBukaValasResponse brivaConfirmationResponse) {
        KonfirmasiBukaValasActivity.launchIntent(this, brivaConfirmationResponse, mUrlPayment, "Buka Valas", setParameterKonfirmasi(), isFromFastMenu, sumberDana);
    }

    @Override
    public ParameterKonfirmasiModel setParameterKonfirmasi() {
        ParameterKonfirmasiModel parameterKonfirmasiModel = new ParameterKonfirmasiModel();

        parameterKonfirmasiModel.setStringLabelTujuan("Setoran Awal");
        parameterKonfirmasiModel.setStringButtonSubmit("Konfirmasi");
        parameterKonfirmasiModel.setDefaultIcon(0);

        return parameterKonfirmasiModel;
    }

    @Override
    protected void onActivityResult(int requestCode, int resultCode, @Nullable Intent data) {
        super.onActivityResult(requestCode, resultCode, data);
        if (requestCode == Constant.REQ_PAYMENT) {
            if (resultCode == RESULT_OK) {
                this.setResult(RESULT_OK);
                this.finish();
            } else {
                this.setResult(RESULT_CANCELED, data);
                this.finish();

//                if (data != null) {
//                    if (!data.getStringExtra(Constant.TAG_ERROR_MESSAGE).isEmpty())
//                        this.finish();
//                }

            }
        }
    }


    @Override
    public void onClickAction() {
        // do nothing
    }

    @Override
    protected void onDestroy() {
        super.onDestroy();
        binding = null;
    }
}