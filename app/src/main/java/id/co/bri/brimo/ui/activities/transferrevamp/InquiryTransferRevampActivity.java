package id.co.bri.brimo.ui.activities.transferrevamp;

import android.annotation.SuppressLint;
import android.app.Activity;
import android.content.Intent;
import android.graphics.Color;
import android.os.Bundle;
import android.os.SystemClock;
import android.text.Editable;
import android.text.Html;
import android.text.TextWatcher;
import android.util.Log;
import android.view.View;
import android.view.inputmethod.InputMethodManager;
import android.widget.RadioGroup;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.core.content.ContextCompat;
import androidx.fragment.app.FragmentTransaction;

import com.google.android.material.datepicker.MaterialDatePicker;
import com.google.gson.Gson;

import org.threeten.bp.LocalDate;
import org.threeten.bp.format.DateTimeFormatter;

import java.math.BigInteger;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

import javax.inject.Inject;

import id.co.bri.brimo.R;
import id.co.bri.brimo.contract.IPresenter.transfer.IInquiryTransferRevampPresenter;
import id.co.bri.brimo.contract.IView.transfer.IInquiryTransferRevampView;
import id.co.bri.brimo.databinding.ActivityInquiryTransferRevampBinding;
import id.co.bri.brimo.domain.config.Constant;
import id.co.bri.brimo.domain.helpers.GeneralHelper;
import id.co.bri.brimo.domain.helpers.ValidationHelper;
import id.co.bri.brimo.domain.helpers.bubbleShowCaseView.BubbleShowCase;
import id.co.bri.brimo.domain.helpers.bubbleShowCaseView.BubbleShowCaseBuilder;
import id.co.bri.brimo.domain.helpers.calendar.CalendarHelper;
import id.co.bri.brimo.domain.helpers.textwatcher.AmountFormatWatcher;
import id.co.bri.brimo.models.AccountModel;
import id.co.bri.brimo.models.BillingDetailOpen;
import id.co.bri.brimo.models.DurasiModel;
import id.co.bri.brimo.models.ParameterKonfirmasiModel;
import id.co.bri.brimo.models.ParameterModel;
import id.co.bri.brimo.models.apimodel.request.InquiryTransferAliasRevampRequest;
import id.co.bri.brimo.models.apimodel.request.revamptransfer.ConfirmationTransferRevampRequest;
import id.co.bri.brimo.models.apimodel.request.revamptransfer.ConfirmationTransferRtgsRevampRequest;
import id.co.bri.brimo.models.apimodel.request.smarttransfer.RecommendAccountSmartTransferRequest;
import id.co.bri.brimo.models.apimodel.response.CityResponseRtgs;
import id.co.bri.brimo.models.apimodel.response.CodeValue;
import id.co.bri.brimo.models.apimodel.response.GeneralConfirmationResponse;
import id.co.bri.brimo.models.apimodel.response.InquiryRtgsCityResponse;
import id.co.bri.brimo.models.apimodel.response.InquiryTransferRevampResponse;
import id.co.bri.brimo.models.apimodel.response.MetodeTransfer;
import id.co.bri.brimo.models.apimodel.response.NonIndividuRtgsResponse;
import id.co.bri.brimo.models.apimodel.response.SaldoReponse;
import id.co.bri.brimo.models.apimodel.response.smarttransfer.SmartTransferRecommendedAcc;
import id.co.bri.brimo.models.apimodel.response.transferrevamp.AftConfirmationResponse;
import id.co.bri.brimo.models.apimodel.response.transferrevamp.UnavailableAftMethodResponse;
import id.co.bri.brimo.models.optionmodel.OptionSearchRevampModel;
import id.co.bri.brimo.ui.activities.KonfirmasiGeneralRevampActivity;
import id.co.bri.brimo.ui.activities.base.BaseActivity;
import id.co.bri.brimo.ui.customviews.dialog.DialogTransferAbnormal;
import id.co.bri.brimo.ui.fragments.FragmentMetode;
import id.co.bri.brimo.ui.fragments.SearchRevampFragment;
import id.co.bri.brimo.ui.fragments.SumberDanaFmFragmentRevamp;
import id.co.bri.brimo.ui.fragments.SumberDanaFragmentRevamp;
import id.co.bri.brimo.ui.fragments.bottomsheet.OpenBottomSheetSmartTransferFragment;
import id.co.bri.brimo.ui.fragments.transferrevamp.CalendarTransferTerjadwalFragment;
import id.co.bri.brimo.ui.fragments.transferrevamp.CalendarTransferTerjadwalMonthlyFragment;
import id.co.bri.brimo.ui.fragments.transferrevamp.CalendarTransferTerjadwalNoDayFragment;
import kotlin.Unit;


/**
 * x
 * Created by dresta on 10/02/2023
 * glossary :
 * AFT : automatic fund transfer
 */
public class InquiryTransferRevampActivity extends BaseActivity implements
        SumberDanaFragmentRevamp.SelectSumberDanaInterface,
        SumberDanaFmFragmentRevamp.SelectSumberDanaInterface,
        IInquiryTransferRevampView,
        View.OnClickListener,
        FragmentMetode.SelectMetode, SearchRevampFragment.SelectOptionInterface, DialogTransferAbnormal.OnClickOK,
        CalendarTransferTerjadwalFragment.OnSelectDate {

    private static final String TAG = "InquiryTransferRevampAc";
    private static final String TAG_RESPONSE = "response";
    private static final String TAG_PARAMETER_MODEL = "tag_parameter_model";
    protected static DurasiModel currentDuration = new DurasiModel(), currentDurationTemp = null;
    private static boolean mIsEditAft = false;
    private static boolean mIsCreateFromBanner = false;
    private static String mJourneyType = "";
    private final DateTimeFormatter dateFormatter = org.threeten.bp.format.DateTimeFormatter.ofPattern("dd/MM/yyyy");
    protected String minTrxString;
    protected String saldoString = "";
    protected String defaultAkun;
    Long minTrx = 0L;
    BillingDetailOpen openModel;
    BubbleShowCaseBuilder bubbleFirstTimeAft;
    @Inject
    IInquiryTransferRevampPresenter<IInquiryTransferRevampView> presenter;
    private InquiryTransferRevampResponse mInquiryTransferRevampResponse;
    private AccountModel model;
    private Double saldo = 0.0;
    private int counter = 0;
    private List<AccountModel> mListAccountModel = new ArrayList<>();
    private List<Integer> mListFailed;
    private long mLastClickTime = 0;
    private ActivityInquiryTransferRevampBinding binding;
    private String saveStr = "";
    private List<BillingDetailOpen> mbrivaOpenResponse = new ArrayList<>();
    private int mSelectedPos = 0;
    private MetodeTransfer mSelectedMetodeTransfer;
    private ParameterModel mParameterModel;
    private boolean isRtgs = false;
    private List<CityResponseRtgs> mCityResponseRtgsListTemp = new ArrayList<>();
    private InquiryRtgsCityResponse mInquiryRtgsCityResponse;
    private CityResponseRtgs mSelectedCity = null;
    private SmartTransferRecommendedAcc mSmartTransferRecommendedAcc = null;
    private RadioGroup radioGroup;
    private String wargaNegara;
    private List<UnavailableAftMethodResponse> unavailableAftMethodResponse = null;

    private String transferAmount;
    private String mAftFrequency = "", mAftDateStart = "", mAftDateEnd = "";
    private CalendarTransferTerjadwalFragment calendarFragment;
    private LocalDate selectedDateOnce, selectedDateStartWeekly, selectedDateEndWeekly, mSelectedMonthStart, mSelectedMonthEnd, selectedDateOnlyMonth;
    private int mSelectedDayWeekly = 0;
    private boolean saldoHold = false;

    private String rekNonBri = "";

    private MaterialDatePicker.Builder materialDateBuilder;
    private MaterialDatePicker materialDatePicker;
    private List<OptionSearchRevampModel> listDay = new ArrayList<>();
    private Boolean mIsUnlimited = false;
    private Boolean mIsUnlimitedEndDateMonthly = false;
    private Boolean mIsUnlimitedEndDateWeekly = false;
    private String dateNowString = CalendarHelper.getDateNowFormatRange();
    private String dateNowConverter = CalendarHelper.convertToNewFormat(dateNowString);
    private LocalDate dateNowLocalDate = LocalDate.parse(dateNowConverter);
    protected TextWatcher activityTextListener = new TextWatcher() {
        @Override
        public void beforeTextChanged(CharSequence charSequence, int i, int i1, int i2) {
            beforeText();
        }

        @Override
        public void onTextChanged(CharSequence charSequence, int i, int i1, int i2) {
            checkButton();
        }

        @Override
        public void afterTextChanged(Editable editable) {
            afterText(editable);
        }
    };
    private boolean isBottomSheetCalenderShow = false;

    public static void launchIntent(Activity caller, InquiryTransferRevampResponse inquiryTransferRevampResponse, boolean fromFastMenu, ParameterModel parameterModel) {
        Intent intent = new Intent(caller, InquiryTransferRevampActivity.class);
        intent.putExtra(TAG_RESPONSE, new Gson().toJson(inquiryTransferRevampResponse));
        intent.putExtra(TAG_PARAMETER_MODEL, new Gson().toJson(parameterModel));
        mIsEditAft = false;
        isFromFastMenu = fromFastMenu;
        mJourneyType = "";
        caller.startActivityForResult(intent, Constant.REQ_PAYMENT);
    }

    public static void launchIntent(Activity caller, InquiryTransferRevampResponse inquiryTransferRevampResponse, boolean fromFastMenu, ParameterModel parameterModel, boolean isEditAft, String journeyType, boolean isCreateFromBanner) {
        Intent intent = new Intent(caller, InquiryTransferRevampActivity.class);
        intent.putExtra(TAG_RESPONSE, new Gson().toJson(inquiryTransferRevampResponse));
        intent.putExtra(TAG_PARAMETER_MODEL, new Gson().toJson(parameterModel));
        mIsEditAft = isEditAft;
        mJourneyType = journeyType;
        mIsCreateFromBanner = isCreateFromBanner;
        isFromFastMenu = fromFastMenu;
        caller.startActivityForResult(intent, Constant.REQ_PAYMENT);
    }

    @Override
    protected void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        binding = ActivityInquiryTransferRevampBinding.inflate(getLayoutInflater());
        setContentView(binding.getRoot());
        if (getIntent().getExtras() != null) {
            parseIntent();
        }
        injectDependency();

        GeneralHelper.setToolbarRevamp(this, binding.tbTransfer.toolbar, GeneralHelper.getString(R.string.masukkan_nominal));

        setupView();
        //checkSmartTransferParams();
        setupCustomView();

    }

    private void parseIntent() {
        if (getIntent().getExtras() != null) {
            if (getIntent().hasExtra(TAG_RESPONSE)) {
                mInquiryTransferRevampResponse = new Gson().fromJson(getIntent().getExtras().getString(TAG_RESPONSE), InquiryTransferRevampResponse.class);
            }
            if (getIntent().hasExtra(TAG_PARAMETER_MODEL)) {
                mParameterModel = new Gson().fromJson(getIntent().getExtras().getString(TAG_PARAMETER_MODEL), ParameterModel.class);
            }
        }
    }

    private void setupView() {
        model = new AccountModel();

        try {
            mListAccountModel = mInquiryTransferRevampResponse.getAccountList();
            mbrivaOpenResponse = mInquiryTransferRevampResponse.getBillingDetailOpen();
            for (BillingDetailOpen billingDetailOpen : mbrivaOpenResponse) {
                openModel = billingDetailOpen;
            }

            for (AccountModel accountModel : mListAccountModel) {
                if (accountModel.getIsDefault() == 1) {
                    model = accountModel;
                    break;
                } else {
                    model = mListAccountModel.get(0);
                }
            }

        } catch (Exception e) {
            // donothing
        }

        unavailableAftMethodResponse = mInquiryTransferRevampResponse.getUnavailableAftMethod();

        //set minimum
        if (mInquiryTransferRevampResponse.getMinimumTransaction() != null && mInquiryTransferRevampResponse.getMinimumTransactionString() != null) {
            minTrx = mInquiryTransferRevampResponse.getMinimumTransaction();
            minTrxString = mInquiryTransferRevampResponse.getMinimumTransactionString();
        }

        binding.btnLanjut.setOnClickListener(this);
        binding.tvGanti.setOnClickListener(this);
        binding.etMetode.setOnClickListener(this);
        binding.etKota.setOnClickListener(this);
        binding.llPilihSumberDana.setOnClickListener(this);
        //binding.rlSmartTransfer.setOnClickListener(this);
        binding.switchSave.setOnCheckedChangeListener((compoundButton, b) -> {
            if (b) {
                saveStr = openModel.getTitle();
                binding.rlSaveAs.setVisibility(View.VISIBLE);
                binding.etSaveAs.setText(saveStr);
            } else {
                saveStr = "";
                binding.rlSaveAs.setVisibility(View.GONE);
                binding.etSaveAs.setText("");
            }
        });
        binding.rbWni.setChecked(true);

        setupSaved();

        binding.etNominal.addTextChangedListener(activityTextListener);
        binding.etNominal.addTextChangedListener(new AmountFormatWatcher(binding.etNominal, null, false));

        binding.tilNominal.setHelperText(String.format(GeneralHelper.getString(R.string.min_tf_label), minTrxString));


        binding.etKota.addTextChangedListener(activityTextListener);
        binding.etNote.addTextChangedListener(activityTextListener);
        binding.etNamaNorek.addTextChangedListener(activityTextListener);
        binding.etAlamat.addTextChangedListener(activityTextListener);
        binding.etBerita.addTextChangedListener(activityTextListener);
        binding.etSaveAs.addTextChangedListener(activityTextListener);
        binding.etAlamat.setOnFocusChangeListener((view, b) -> checkButton());
        binding.etBerita.setOnFocusChangeListener((view, b) -> checkButton());
        addListenerOnButton();

        if (mInquiryTransferRevampResponse.getMetodeTransfer().size() == 0) {
            binding.vSeparator1.setVisibility(View.GONE);
            binding.rlMetode.setVisibility(View.GONE);
        } else {
            mSelectedMetodeTransfer = mInquiryTransferRevampResponse.getMetodeTransfer().get(0);
            onClick(mSelectedMetodeTransfer, 0);
            binding.rlMetode.setVisibility(View.VISIBLE);
            binding.vSeparator1.setVisibility(View.VISIBLE);
        }

        /*note_enable (boolean)*/
        if (Boolean.TRUE.equals(mInquiryTransferRevampResponse.getNoteEnable())) {
            binding.rlNote.setVisibility(View.VISIBLE);
        } else {
            binding.rlNote.setVisibility(View.GONE);
        }

        if (!mInquiryTransferRevampResponse.getNote().isEmpty()) {
            binding.etNote.setText(mInquiryTransferRevampResponse.getNote());
        }

        if (model.getAcoountString() != null) {
            binding.tvNoRek.setText(model.getAcoountString());
        } else {
            binding.tvNoRek.setText("-");
        }

        try {
            if (openModel.getTitle() != null && !openModel.getTitle().isEmpty()) {
                binding.llTopSource.setVisibility(View.VISIBLE);
                binding.llTopSourceEditable.setVisibility(View.GONE);
                binding.tvRecipientName.setText(openModel.getTitle());
                binding.tvBankName.setText(openModel.getSubtitle());
                binding.tvAccNum.setText(openModel.getDescription());
            } else {
                binding.llTopSource.setVisibility(View.GONE);
                binding.llTopSourceEditable.setVisibility(View.VISIBLE);
                binding.tvBankNameEditable.setText(openModel.getSubtitle());
                binding.tvAccNumberEditable.setText(openModel.getDescription());
            }

        } catch (Exception e) {
            // do nothing
        }

        if (model.getAlias() != null) {
            if (model.getAlias().isEmpty()) {
                binding.tvAliasRek.setText(model.getName());
            } else {
                binding.tvAliasRek.setText(model.getAlias());
            }
        } else {
            binding.tvAliasRek.setText(model.getName());
        }

        if (model.getImagePath() != null) {
            if (!model.getImagePath().isEmpty()) {
                GeneralHelper.loadImageUrl(this, model.getImagePath(), binding.ivIconRek, R.drawable.bri, 0);
            } else {
                binding.ivIconRek.setImageResource(R.drawable.bri);
            }
        } else {
            binding.ivIconRek.setImageResource(R.drawable.bri);
        }

        if (openModel.getTitle() != null) {
            binding.tvInisialPenerima.setText(GeneralHelper.formatInitialName(openModel.getTitle()));
        } else {
            binding.tvInisialPenerima.setText("I");
        }

        if (!isFromFastMenu) {
            setupViewAft();
            if (mIsEditAft) {
                setupEditAft();
            }
            if (mIsCreateFromBanner) {
                setupUiCreateFromBanner();
            }
        } else {
            binding.rlSwitchTransferTerjadwal.setVisibility(View.GONE);
            checkIsAftAvailable();
        }

        checkButton();
        binding.etNominal.setOnFocusChangeListener((view, b) -> {
            if (b) {
                binding.etNominal.setHint("0");
            } else {
                binding.etNominal.setHint("Rp 0");
            }
        });

        requestFocus();

        // setupfast menu
        if (isFromFastMenu) {
            binding.tvSaldoRek.setVisibility(View.GONE);
        }
    }

    private void setupCustomView() {
        switch (mJourneyType) {
            case FormTransferAliasRevampActivity.mJourneyTypeList.ListAft:
                binding.switchTransferTerjadwal.setChecked(mInquiryTransferRevampResponse.isAftEnable());
                GeneralHelper.setToolbarRevamp(this, binding.tbTransfer.toolbar, GeneralHelper.getString(R.string.toolbar_create_aft));
                break;
        }
    }

    private void setupSaved() {
        if (!mIsEditAft || mIsCreateFromBanner) {
            if (isFromFastMenu || !mInquiryTransferRevampResponse.getSaved().equalsIgnoreCase("")) {
                if (!mInquiryTransferRevampResponse.getSaved().equalsIgnoreCase("")) {
                    binding.clInfo.setVisibility(View.VISIBLE);
                    binding.savedAs.setText(String.format(GeneralHelper.getString(R.string.added_to_saved), mInquiryTransferRevampResponse.getSaved()));
                }
                binding.rlFavorit.setVisibility(View.GONE);
                saveStr = "";
            } else if (mInquiryTransferRevampResponse.getSaved().equalsIgnoreCase("")) {
                binding.rlFavorit.setVisibility(View.VISIBLE);
            }
        } else {
            binding.rlFavorit.setVisibility(View.GONE);
        }
    }

    private void setupUiCreateFromBanner() {
        binding.switchTransferTerjadwal.setChecked(true);
        binding.rlInfoAft.setVisibility(View.GONE);
        binding.rlTransferTerjadwal.setVisibility(View.VISIBLE);
    }

    private void setupEditAft() {
        GeneralHelper.setToolbarRevamp(this, binding.tbTransfer.toolbar, GeneralHelper.getString(R.string.change_detail_aft));
        binding.switchTransferTerjadwal.setChecked(true);
        binding.rlInfoAft.setVisibility(View.GONE);
        binding.rlTransferTerjadwal.setVisibility(View.VISIBLE);
        binding.etNominal.setText(mInquiryTransferRevampResponse.getAmount().toString());
        setupSelectedFrequency(mInquiryTransferRevampResponse.getSelectedFrequency());


        if (mInquiryTransferRevampResponse.getSelectedAccount() != null) {
            onSelectSumberDana(mInquiryTransferRevampResponse.getSelectedAccount());
        }
        if (mInquiryTransferRevampResponse.getAftDateStop().isEmpty() || mInquiryTransferRevampResponse.getAftDateStart().isEmpty()) {
            return;
        }

        resetAft();

        LocalDate startDateLocal = getParseDate(mInquiryTransferRevampResponse.getAftDateStart());

        LocalDate stopDateLocal = getParseDate(CalendarHelper.getDateNowFormat("dd/MM/yyyy"));

        if (!mInquiryTransferRevampResponse.getAftDateStop()
                .equalsIgnoreCase(Constant.UNLIMITED_DATE_FLAG)) {
            stopDateLocal = getParseDate(mInquiryTransferRevampResponse.getAftDateStop());
        }


        if (mInquiryTransferRevampResponse.getAftDateStop().equals(Constant.UNLIMITED_DATE_FLAG)) {
            switch (mInquiryTransferRevampResponse.getSelectedFrequency()) {
                case Constant.AFT_SCHEDULE_WEEKLY:
                    mIsUnlimitedEndDateWeekly = true;
                    break;
                case Constant.AFT_SCHEDULE_MONTHLY:
                    mIsUnlimitedEndDateMonthly = true;
                    break;
                case Constant.AFT_SCHEDULE_ONCE:
                    stopDateLocal = getParseDate(mInquiryTransferRevampResponse.getAftDateStop());
                    break;
            }
        } else {
            switch (mInquiryTransferRevampResponse.getSelectedFrequency()) {
                case Constant.AFT_SCHEDULE_WEEKLY:
                    mIsUnlimitedEndDateWeekly = false;
                    stopDateLocal = getParseDate(mInquiryTransferRevampResponse.getAftDateStop());
                    break;
                case Constant.AFT_SCHEDULE_MONTHLY:
                    mIsUnlimitedEndDateMonthly = false;
                    stopDateLocal = getParseDate(mInquiryTransferRevampResponse.getAftDateStop());
                    break;
                case Constant.AFT_SCHEDULE_ONCE:
                    stopDateLocal = getParseDate(mInquiryTransferRevampResponse.getAftDateStop());
                    break;
            }
        }


        String monthTwoDigsStart = CalendarHelper.convertMont(String.format("%02d", startDateLocal.getMonthValue()));
        String monthTwoDigsStop = CalendarHelper.convertMont(String.format("%02d", stopDateLocal.getMonthValue()));
        ;

        switch (mInquiryTransferRevampResponse.getSelectedFrequency()) {
            case Constant.AFT_SCHEDULE_ONCE: {
                binding.etDatePickerOnce.setText(startDateLocal.getDayOfMonth() + " " + monthTwoDigsStart + " " + startDateLocal.getYear());
                selectedDateOnce = startDateLocal;
                break;
            }
            case Constant.AFT_SCHEDULE_WEEKLY: {
                selectedDateStartWeekly = startDateLocal;
                selectedDateEndWeekly = stopDateLocal;
                // set placeholder for day of week, getDayOfWeek() (mon = 1, sunday = 7)
                binding.etChooseDay.setText(CalendarHelper.dayBahasa[selectedDateStartWeekly.getDayOfWeek().getValue() - 1]);
                binding.etChooseDayStart.setText(startDateLocal.getDayOfMonth() + " " + monthTwoDigsStart + " " + startDateLocal.getYear());
                String textPlaceholder = (mIsUnlimitedEndDateWeekly) ? GeneralHelper.getString(R.string.unlimited_date_aft) : stopDateLocal.getDayOfMonth() + " " + monthTwoDigsStop + " " + stopDateLocal.getYear();
                binding.etChooseDayEnd.setText(textPlaceholder);
                break;
            }
            case Constant.AFT_SCHEDULE_MONTHLY: {
                mSelectedMonthStart = startDateLocal;
                mSelectedMonthEnd = stopDateLocal;
                String day = String.valueOf(startDateLocal.getDayOfMonth());
                binding.etChooseDateMonthly.setText(day);
                binding.etChooseMonthStart.setText(CalendarHelper.convertMont(String.format("%02d", startDateLocal.getMonthValue())) + " " + startDateLocal.getYear());
                String textPlaceholder = (mIsUnlimitedEndDateMonthly) ? GeneralHelper.getString(R.string.unlimited_date_aft) : CalendarHelper.convertMont(String.format("%02d", stopDateLocal.getMonthValue())) + " " + stopDateLocal.getYear();
                binding.etChooseMonthEnd.setText(textPlaceholder);
                break;
            }
        }
        onChangeFrekuensi();
    }

    private void resetAft() {
        selectedDateOnce = null;
        selectedDateStartWeekly = null;
        selectedDateEndWeekly = null;
        mSelectedMonthStart = null;
        mSelectedMonthEnd = null;
        selectedDateOnlyMonth = null;
        mIsUnlimitedEndDateWeekly = false;
        mIsUnlimitedEndDateMonthly = false;
        mAftDateStart = "";
        mAftDateEnd = "";

    }

    private void setupSelectedFrequency(String selectedFrequency) {
        mAftFrequency = selectedFrequency;
        for (int i = 0; i < mInquiryTransferRevampResponse.getFrequencyList().size(); i++) {
            if (mAftFrequency.equals(mInquiryTransferRevampResponse.getFrequencyList().get(i).getCode())) {
                binding.etFrekuensiTransferTerjadwal.setText(mInquiryTransferRevampResponse.getFrequencyList().get(i).getValue());
            }
        }
    }

    private void setupViewAft() {
        checkIsAftAvailable();

        listDay = new ArrayList<>();
        for (int i = 0; i < CalendarHelper.dayBahasa.length; i++) {
            listDay.add(new OptionSearchRevampModel("", CalendarHelper.dayBahasa[i], "", 0, i + 1, String.valueOf((i + 1)), false));
        }

        binding.etFrekuensiTransferTerjadwal.setOnClickListener(this);
        // once
        binding.etDatePickerOnce.setOnClickListener(this);

        // monthly
        binding.etChooseDateMonthly.setOnClickListener(this);
        binding.etChooseMonthStart.setOnClickListener(this);
        binding.etChooseMonthEnd.setOnClickListener(this);
        if (mInquiryTransferRevampResponse.getAftTerm() != null && !mInquiryTransferRevampResponse.getAftTerm().isEmpty()) {
            binding.tvAftTerm.setText(Html.fromHtml(mInquiryTransferRevampResponse.getAftTerm()));
            binding.mcvInfoTransferTerjadwal.setVisibility(View.VISIBLE);
        }

        materialDateBuilder = MaterialDatePicker.Builder.datePicker();

        // picker
        materialDatePicker = materialDateBuilder.build();
        if (mInquiryTransferRevampResponse.getFrequencyList() != null && fetchListFrekuensi().size() > 0) {
            mAftFrequency = fetchListFrekuensi().get(0).getCodeModel();
            binding.etFrekuensiTransferTerjadwal.setText(fetchListFrekuensi().get(0).getOptionName());
            onChangeFrekuensi();
        }
        binding.etChooseDay.setOnClickListener(this);
        binding.etChooseDayStart.setOnClickListener(this);
        binding.etChooseDayEnd.setOnClickListener(this);
        binding.etChooseDateMonthly.setOnClickListener(this);
    }

    private void checkIsAftAvailable() {
        if (!mInquiryTransferRevampResponse.isAftEnable()) {
            setBlurBannerTransferTerjadwal();
        } else {
            if (!mInquiryTransferRevampResponse.getMetodeTransfer().isEmpty() && unavailableAftMethodResponse != null) {
                for (UnavailableAftMethodResponse response : unavailableAftMethodResponse) {
                    if (mSelectedMetodeTransfer.getCode().equalsIgnoreCase(response.getCode())) {
                        setBlurBannerTransferTerjadwal();
                        setBannerTitleAndDesc(response.getBanner().getTitle(), response.getBanner().getDesc());
                        break;
                    } else {
                        setAftEnableView();
                    }
                }
            } else {
                setAftEnableView();
            }
        }
    }

    private void setAftEnableView() {
        showBubbleAft();
        binding.switchTransferTerjadwal.setClickable(true);
        binding.rlInfoAft.setAlpha(1f);
        binding.switchTransferTerjadwal.setOnCheckedChangeListener((buttonView, isChecked) -> {
            if (isChecked) {
                binding.rlTransferTerjadwal.setVisibility(View.VISIBLE);
            } else {
                binding.rlTransferTerjadwal.setVisibility(View.GONE);
            }
            checkButton();
        });
        if (mInquiryTransferRevampResponse.getAftBanner() != null) {
            setBannerTitleAndDesc(mInquiryTransferRevampResponse.getAftBanner().getTitle(), mInquiryTransferRevampResponse.getAftBanner().getDesc());
        }
    }

    private void setBannerTitleAndDesc(String title, String desc) {
        binding.titleLayoutTransferTerjadwal.setText(title);
        binding.descLayoutTransferTerjadwal.setText(desc);
    }

    private void setBlurBannerTransferTerjadwal() {
        binding.rlInfoAft.setAlpha(0.3f);
        binding.switchTransferTerjadwal.setOnCheckedChangeListener(null);
        binding.switchTransferTerjadwal.setClickable(false);
        binding.switchTransferTerjadwal.setChecked(false);
        binding.rlTransferTerjadwal.setVisibility(View.GONE);
        if (mInquiryTransferRevampResponse.getAftBanner() != null) {
            setBannerTitleAndDesc(mInquiryTransferRevampResponse.getAftBanner().getTitle(), mInquiryTransferRevampResponse.getAftBanner().getDesc());
        }
    }

    private void showBubbleAft() {
        if (!presenter.getPrefIsFirstTimeAft()) return;
        if (FormTransferAliasRevampActivity.mJourneyTypeList.ListAft.equals(mJourneyType)) return;
        bubbleFirstTimeAft = new BubbleShowCaseBuilder(this) //Activity instance
                .title(GeneralHelper.getString(R.string.bubble_aft_title)) //Any title for the bubble view
                .description(GeneralHelper.getString(R.string.bubble_aft_desc))
                .backgroundColor(Color.WHITE)
                .textColor(GeneralHelper.getColor(R.color.neutral_dark40))
                .textColorDesc(GeneralHelper.getColor(R.color.neutral_dark10))
                .targetView(binding.rlInfoAft)
                .buttonTitle(GeneralHelper.getString(R.string.ok))
                .arrowPosition(BubbleShowCase.ArrowPosition.BOTTOM);
        bubbleFirstTimeAft.setIsAnimated(false);
        bubbleFirstTimeAft.show();
        presenter.setPrefIsFirstTimeAft(false);
    }

    private void showMonthPickerMonthly(boolean isStart) {
        String typePicker = isStart ? Constant.DATE_PICKER_START : Constant.DATE_PICKER_END;
        String title = isStart ? binding.tvLabelMonthPickerStart.getText().toString() : binding.tvLabelMonthPickerEnd.getText().toString();
        CalendarTransferTerjadwalMonthlyFragment calendarFragment = new CalendarTransferTerjadwalMonthlyFragment(typePicker, title);
        calendarFragment.onDateSelect((selectedDate, type, isUnlimited) -> {

            if (!isStart) mIsUnlimitedEndDateMonthly = isUnlimited;
            @SuppressLint("DefaultLocale")
            String textPlaceholder = (isUnlimited) ? GeneralHelper.getString(R.string.unlimited_date_aft) : CalendarHelper.convertMont(String.format("%02d", selectedDate.getMonthValue())) + " " + selectedDate.getYear();
            if (Constant.DATE_PICKER_START.equals(type)) {
                binding.etChooseMonthStart.setText(textPlaceholder);
                mSelectedMonthStart = selectedDate;
                mAftDateStart = dateFormatter.format(mSelectedMonthStart);
            } else {
                binding.etChooseMonthEnd.setText(textPlaceholder);
                if (isUnlimited) mSelectedMonthEnd = LocalDate.now();
                else mSelectedMonthEnd = selectedDate;
                mAftDateEnd = dateFormatter.format(mSelectedMonthEnd);
            }

            checkButton();
            return null;
        });

        Bundle args = new Bundle();
        String currMonthYear = CalendarHelper.getDateNowFormatRange();
        String convertCurrMonthYear = CalendarHelper.convertToNewFormat(currMonthYear);
        LocalDate localMinDate = LocalDate.parse(convertCurrMonthYear);

        currentDuration = new DurasiModel(localMinDate.getDayOfMonth(), localMinDate.getMonthValue(), localMinDate.getYear(), localMinDate.getDayOfMonth(), localMinDate.getMonthValue(), localMinDate.getYear());
        if (currentDuration.getStartDateString() != null && !currentDuration.getStartDateString().isEmpty() && currentDuration.getStartMonth() > 0) {
            args.putString(Constant.TAG_CURRENT_MONTHYEAR_AFT, currentDuration.getStartDateString());
        }

        String selectedDate = binding.etChooseDateMonthly.getText().toString();
        if (!selectedDate.isEmpty()) {
            args.putString(Constant.TAG_SELECTED_DATE_PICKED_MONTH, selectedDate);
        }

        if (isStart) {
            if (mSelectedMonthStart != null) args.putString(Constant.TAG_MONTHDATE_MONTHYEAR_AFT, mSelectedMonthStart.toString());
        } else {
            if (mSelectedMonthEnd != null) args.putString(Constant.TAG_MONTHDATE_MONTHYEAR_AFT, mSelectedMonthEnd.toString());
        }

        //only enable/show unlimited checkbox when month picker end
        args.putBoolean(Constant.TAG_IS_ENABLE_UNLIMITED, !isStart);
        if (isStart) {
            args.putBoolean(Constant.TAG_IS_CHECKED_UNLIMITED, false);
        } else {
            args.putBoolean(Constant.TAG_IS_CHECKED_UNLIMITED, mIsUnlimitedEndDateMonthly);
        }

        if (mSelectedMonthStart != null && !isStart) args.putString(Constant.TAG_MIN_MONTHYEAR_AFT, mSelectedMonthStart.toString());
        if (mSelectedMonthEnd != null && !mIsUnlimitedEndDateMonthly && isStart) args.putString(Constant.TAG_MAX_MONTHYEAR_AFT, mSelectedMonthEnd.toString());

        args.putBoolean(Constant.TAG_TERJADWAL_IS_EDIT, mIsEditAft);


        calendarFragment.setArguments(args);
        calendarFragment.setCancelable(true);
        calendarFragment.show(getSupportFragmentManager(), String.valueOf(Constant.REQ_CALENDAR));
    }

    private void showChooseDay() {
        SearchRevampFragment SearchRevampFragment = new SearchRevampFragment(listDay, this::onSelectedDayWeekly, "", GeneralHelper.getString(R.string.aft_day_list_title), false, true);
        SearchRevampFragment.show(getSupportFragmentManager(), "");
    }

    private void onSelectedDayWeekly(OptionSearchRevampModel selected) {
        mSelectedDayWeekly = selected.getPositionModel();
        binding.etChooseDay.setText(selected.getOptionName());
        resetVarAft();
    }

    private void resetVarAft() {
        binding.etChooseDayStart.setText("");
        binding.etChooseDayEnd.setText("");
        mAftDateStart = "";
        mAftDateEnd = "";
    }

    private void showDatePickerWeekly(boolean isStart) {
        String typePicker = isStart ? Constant.DATE_PICKER_START : Constant.DATE_PICKER_END;
        String title = isStart ? binding.tvChooseDayStartWeekly.getText().toString() : binding.tvChooseDayEndWeekly.getText().toString();
        calendarFragment = new CalendarTransferTerjadwalFragment((dateSelect, type, isUnlimited) -> onDatePickedWeekly(isStart, dateSelect, isUnlimited), typePicker, mAftFrequency, title, (value) -> {
            isBottomSheetCalenderShow = value;
            return Unit.INSTANCE;
        });

        Bundle args = new Bundle();
        if (currentDuration != null) {
            String minDate = CalendarHelper.getDateNowFormatRange();
            String convertMinDate = CalendarHelper.convertToNewFormat(minDate);
            LocalDate localMinDate;
            localMinDate = LocalDate.parse(convertMinDate);

            currentDuration = new DurasiModel(localMinDate.getDayOfMonth(), localMinDate.getMonthValue(), localMinDate.getYear(), localMinDate.getDayOfMonth(), localMinDate.getMonthValue(), localMinDate.getYear());

            if (currentDuration.getStartDateString() != null && !currentDuration.getStartDateString().isEmpty() && currentDuration.getStartMonth() > 0) {
                args.putString(Constant.TAG_MIN_DATE, currentDuration.getStartDateString());
                if (!isStart && selectedDateStartWeekly != null) {
                    localMinDate = selectedDateStartWeekly;
                    currentDuration = new DurasiModel(localMinDate.getDayOfMonth(), localMinDate.getMonthValue(), localMinDate.getYear(), localMinDate.getDayOfMonth(), localMinDate.getMonthValue(), localMinDate.getYear());
                    args.putString(Constant.TAG_MIN_DATE, selectedDateStartWeekly.toString());
                }
            }

            args.putBoolean(Constant.TAG_IS_ENABLE_UNLIMITED, !isStart);
            if (isStart) {
                args.putBoolean(Constant.TAG_IS_CHECKED_UNLIMITED, false);
            } else {
                args.putBoolean(Constant.TAG_IS_CHECKED_UNLIMITED, mIsUnlimitedEndDateWeekly);
            }

            if (isStart && selectedDateEndWeekly != null) {
                localMinDate = selectedDateEndWeekly;
                currentDuration = new DurasiModel(localMinDate.getDayOfMonth(), localMinDate.getMonthValue(), localMinDate.getYear(), localMinDate.getDayOfMonth(), localMinDate.getMonthValue(), localMinDate.getYear());
                args.putString(Constant.TAG_MAX_DATE, selectedDateEndWeekly.toString());
            }

            args.putInt(Constant.TAG_CURRENT_DAY, mSelectedDayWeekly);

            checkButton();
        }
        if (selectedDateStartWeekly != null && isStart) {
            args.putString(Constant.TAG_SELECTED_DATE, selectedDateStartWeekly.toString());
        }

        if (selectedDateEndWeekly != null && !isStart && !mIsUnlimitedEndDateWeekly) {
            args.putString(Constant.TAG_SELECTED_DATE, selectedDateEndWeekly.toString());
        }

        args.putBoolean(Constant.TAG_PICK_END_DATE, false);
        args.putBoolean(Constant.TAG_PICK_DATE, false);
        args.putBoolean(Constant.TAG_MAX_TODAY, false);
        calendarFragment.setArguments(args);

        calendarFragment.setCancelable(true);
        calendarFragment.show(getSupportFragmentManager(), String.valueOf(Constant.REQ_CALENDAR));
    }

    @SuppressLint("SetTextI18n")
    private void onDatePickedWeekly(boolean isStart, LocalDate dateSelect, boolean isUnlimited) {
        mIsUnlimitedEndDateWeekly = isUnlimited;
        @SuppressLint("DefaultLocale") String monthTwoDigs = CalendarHelper.convertMont(String.format("%02d", dateSelect.getMonthValue()));
        mSelectedDayWeekly = dateSelect.getDayOfWeek().getValue();
        if (listDay.get(dateSelect.getDayOfWeek().getValue() - 1) != null) {
            binding.etChooseDay.setText(listDay.get(dateSelect.getDayOfWeek().getValue() - 1).getOptionName());
        }
        if (isStart) {
            selectedDateStartWeekly = dateSelect;
            mAftDateStart = dateFormatter.format(selectedDateStartWeekly);
            binding.etChooseDayStart.setText(dateSelect.getDayOfMonth() + " " + monthTwoDigs + " " + dateSelect.getYear());
        } else {
            selectedDateEndWeekly = dateSelect;
            mAftDateEnd = dateFormatter.format(selectedDateEndWeekly);
            String textPlaceholder = (mIsUnlimitedEndDateWeekly) ? GeneralHelper.getString(R.string.unlimited_date_aft) : dateSelect.getDayOfMonth() + " " + monthTwoDigs + " " + dateSelect.getYear();
            binding.etChooseDayEnd.setText(textPlaceholder);
        }

        checkButton();
    }

    private void requestFocus() {
        binding.etNominal.requestFocus();
        binding.etNominal.setEnabled(true);

        InputMethodManager inputMethodManager = (InputMethodManager) getSystemService(INPUT_METHOD_SERVICE);
        binding.etNominal.requestFocus();
        inputMethodManager.showSoftInput(binding.etNominal, 0);
    }

    private void injectDependency() {
        getActivityComponent().inject(this);
        if (presenter != null) {
            presenter.setView(this);
            presenter.start();
            presenter.setUrlKonfirmasiAft(GeneralHelper.getString(R.string.url_aft_confirmation_create));
            if (mIsEditAft) {
                presenter.setUrlKonfirmasiEditAft(GeneralHelper.getString(R.string.url_aft_confirm_edit));
            }
            if (isFromFastMenu) {
                presenter.setUrlListCity(GeneralHelper.getString(R.string.url_list_city_rtgs_fm_v3));
            } else {
                presenter.setUrlListCity(GeneralHelper.getString(R.string.url_list_city_rtgs_v3));
//                presenter.setInquiryUrl(GeneralHelper.getString(R.string.url_transfer_inquiry_v3));
//                presenter.setUrlRecommendSmartTransfer(GeneralHelper.getString(R.string.smart_transfer_recommend_account_url));
            }
        }
    }

    public ParameterKonfirmasiModel setParameterKonfirmasi() {
        ParameterKonfirmasiModel parameterKonfirmasiModel = new ParameterKonfirmasiModel();

        parameterKonfirmasiModel.setStringLabelTujuan(mParameterModel.getStringLabelTujuan());
        parameterKonfirmasiModel.setStringButtonSubmit(GeneralHelper.getString(R.string.confirmation));
        parameterKonfirmasiModel.setDefaultIcon(mParameterModel.getDefaultIcon());

        if(mParameterModel.getSmartTransfer() != null) {
            parameterKonfirmasiModel.setSmartTransfer(mParameterModel.getSmartTransfer());
            parameterKonfirmasiModel.setAccOffUs(mParameterModel.getAccOffUs());
            parameterKonfirmasiModel.setAccOffUsCode(mParameterModel.getAccOffUsCode());
        }

        if(isFromFastMenu){
            parameterKonfirmasiModel.setUsingC2(true);
        }

        return parameterKonfirmasiModel;
    }

    @Override
    public void onSelectSumberDana(AccountModel bankModel) {
        model = bankModel;

        checkBalance(bankModel);

        binding.tvNoRek.setText(model.getAcoountString());
        if (bankModel.getAlias() != null) {
            if (bankModel.getAlias().isEmpty()) {
                binding.tvAliasRek.setText(bankModel.getName());
            } else {
                binding.tvAliasRek.setText(bankModel.getAlias());
            }
        } else {
            binding.tvAliasRek.setText(bankModel.getName());
        }

        if (bankModel.getImagePath() != null) {
            if (!bankModel.getImagePath().isEmpty()) {
                GeneralHelper.loadImageUrl(this, bankModel.getImagePath(), binding.ivIconRek, R.drawable.bri, 0);
            } else {
                binding.ivIconRek.setImageResource(R.drawable.bri);
            }
        } else {
            binding.ivIconRek.setImageResource(R.drawable.bri);
        }

        if (!isFromFastMenu) {
            if (model.getSaldoReponse() != null) {
                // cek saldo tertahan
                if (model.getSaldoReponse().isOnHold())
                    binding.ivAlertSaldo.setVisibility(View.VISIBLE);
                else binding.ivAlertSaldo.setVisibility(View.GONE);
            }
        }

        checkButton();
    }

    private void checkBalance(AccountModel bankModel) {
        if (mIsEditAft && bankModel.getSaldoReponse() == null) {
            SaldoReponse saldoReponse = new SaldoReponse();
            saldoReponse.setBalance(mInquiryTransferRevampResponse.getSelectedAccount().getBalance());
            saldoReponse.setBalanceString(mInquiryTransferRevampResponse.getSelectedAccount().getBalanceString());
            bankModel.setCurrency(mInquiryTransferRevampResponse.getSelectedAccount().getCurrency());
            bankModel.setSaldoReponse(saldoReponse);
        }
        if (bankModel.getSaldoReponse() != null) {
            String saldoStr = String.valueOf(model.getSaldoReponse().getBalanceString());
            binding.tvSaldoRek.setText(GeneralHelper.formatNominalIDR(model.getCurrency(), saldoStr));
            if (bankModel.getSaldoReponse().getBalance() != null) {
                saldo = bankModel.getSaldoReponse().getBalance();
                if (model.getMinimumBalance() != null) {
                    saldo = bankModel.getSaldoReponse().getBalance() - model.getMinimumBalance();
                }
            } else {
                binding.tvSaldoRek.setText(String.format("%s%s", bankModel.getCurrency(), "-"));
                saldo = 0.0;
            }
        } else {
            binding.tvSaldoRek.setText(String.format("%s%s", bankModel.getCurrency(), "-"));
            saldo = 0.0;
        }
    }

    @Override
    protected void onResume() {
        super.onResume();
        injectDependency();

        if (mInquiryTransferRevampResponse == null) {
            finish();
        }
        checkButton();
    }

    @Override
    protected void onDestroy() {
        if (presenter != null) {
            presenter.stop();
        }
        binding = null;
        isFromFastMenu = false;
        super.onDestroy();
    }

    @Override
    public void onBackPressed() {
        presenter.stop();
        super.onBackPressed();
    }

    private void checkButton() {
        binding.btnLanjut.setEnabled(false);
        binding.btnLanjut.setTextColor(GeneralHelper.getColor(R.color.neutral_light60));
        if (isRtgs) {
            binding.tilNominal.setHelperText(String.format(GeneralHelper.getString(R.string.min_tf_label_rtgs), minTrxString));

            if (binding.etAlamat.getText().length() < 6 && binding.etAlamat.getText().length() != 0) {
                binding.tilAlamat.setHelperText(GeneralHelper.getString(R.string.min_address_length));
                binding.tilAlamat.setHelperTextColor(ContextCompat.getColorStateList(this, R.color.error80));
                binding.etAlamat.setBackgroundResource(R.drawable.state_input_error);
            } else {
                binding.tilAlamat.setHelperText(GeneralHelper.getString(R.string.info_input_address));
                binding.tilAlamat.setHelperTextColor(ContextCompat.getColorStateList(this, R.color.neutral_light60));
                binding.etAlamat.setBackgroundResource(R.drawable.state_input_normal);
            }

            if (binding.etBerita.getText().length() < 6 && binding.etBerita.getText().length() != 0) {
                binding.tilBerita.setHelperText(GeneralHelper.getString(R.string.min_note_length));
                binding.tilBerita.setHelperTextColor(ContextCompat.getColorStateList(this, R.color.error80));
                binding.etBerita.setBackgroundResource(R.drawable.state_input_error);
            } else {
                binding.tilBerita.setHelperText(GeneralHelper.getString(R.string.helper_text_berita_rtgs));
                binding.tilBerita.setHelperTextColor(ContextCompat.getColorStateList(this, R.color.neutral_light60));
                binding.etBerita.setBackgroundResource(R.drawable.state_input_normal);
            }
        } else {
            binding.tilNominal.setHelperText(String.format(GeneralHelper.getString(R.string.min_tf_label), minTrxString));
        }
        binding.tilNominal.setHelperTextColor(ContextCompat.getColorStateList(getApplicationContext(), R.color.neutral_light80));

        String nominalStr = binding.etNominal.getText().toString();
        String nominalStrClr = binding.etNominal.getText().toString().isEmpty() ? "0" : binding.etNominal.getText().toString().replace(".", "");
        if (nominalStr.isEmpty()) {
            return;
        }
        if (!ValidationHelper.validateAmountString(nominalStr)) {
            return;
        }

        // jika nominal dibandingkan dgn min trx lebih kecil (compare to < 0 ) kurang dari min trx
        if (BigInteger.valueOf(Long.parseLong(nominalStrClr)).compareTo(BigInteger.valueOf(minTrx)) < 0) {
            if (saldo.longValue() <= 0) {
                binding.tilNominal.setHelperText(String.format(GeneralHelper.getString(R.string.min_tf_label), minTrxString));
                binding.tilNominal.setHelperTextColor(ContextCompat.getColorStateList(getApplicationContext(), R.color.neutral_light80));
                return;
            }
            return;
        }

        if (!isFromFastMenu && !binding.switchTransferTerjadwal.isChecked()) {
            if (saldo.longValue() <= 0) {
                binding.tilNominal.setHelperText(GeneralHelper.getString(R.string.saldo_anda_tidak_cukup));
                binding.tilNominal.setHelperTextColor(ContextCompat.getColorStateList(getApplicationContext(), R.color.red));
                return;
            }

            // jika nominal dibandingkan dgn saldo (lebih besar) (compare to > 0 ) melebihi saldo
            if (BigInteger.valueOf(Long.parseLong(nominalStrClr)).compareTo(BigInteger.valueOf(saldo.longValue())) > 0) {
                binding.tilNominal.setHelperText(GeneralHelper.getString(R.string.saldo_anda_tidak_cukup));
                binding.tilNominal.setHelperTextColor(ContextCompat.getColorStateList(getApplicationContext(), R.color.red));
                return;
            }
        }
        if (binding.switchTransferTerjadwal.isChecked()) {
            // jika validateAft false, balikin
            if (!validateAft()) {
                return;
            }
        }
        /*pengecekan rtgs*/
        String etKotaStr = binding.etKota.getText().toString();
        String etAlamatStr = binding.etAlamat.getText().toString();
        String etBeritaStr = binding.etBerita.getText().toString();
        if (isRtgs) {
            if (binding.etAlamat.getText().length() < 6 || binding.etBerita.getText().length() < 6) {
                return;
            }
            if (etKotaStr.isEmpty()) {
                return;
            }
            if (etAlamatStr.isEmpty()) {
                return;
            }
            if (etBeritaStr.isEmpty()) {
                return;
            }
        }


        // jika metode transfer yg dipilih tidak aktif
        if (mSelectedMetodeTransfer != null) {
            if (!mSelectedMetodeTransfer.getActive()) {
                return;
            }
        }

        if (binding.switchSave.isChecked()) {
            if (binding.etSaveAs.getText().length() <= 0) {
                return;
            }
        }

        /*getDestinationName*/
        String namaNorekStr = binding.etNamaNorek.getText().toString();
        if (openModel.getTitle() != null && openModel.getTitle().isEmpty()) {
            if (namaNorekStr.isEmpty()) {
                return;
            }
        }

        binding.btnLanjut.setEnabled(true);
        binding.btnLanjut.setTextColor(GeneralHelper.getColor(R.color.neutral_light10));
    }

    private Boolean validateAft() {
        if (mAftFrequency.equals(Constant.AFT_SCHEDULE_ONCE)) {
            if (binding.etDatePickerOnce.getText().toString().isEmpty()) {
                return false;
            }
        }
        if (mAftFrequency.equals(Constant.AFT_SCHEDULE_WEEKLY)) {
            if (binding.etChooseDay.getText().toString().isEmpty()) {
                return false;
            }
            if (binding.etChooseDayStart.getText().toString().isEmpty()) {
                return false;
            }
            if (binding.etChooseDayEnd.getText().toString().isEmpty()) {
                return false;
            }
        }
        if (mAftFrequency.equals(Constant.AFT_SCHEDULE_MONTHLY)) {
            if (binding.etChooseDateMonthly.getText().toString().isEmpty()) {
                return false;
            }
            if (binding.etChooseMonthStart.getText().toString().isEmpty()) {
                return false;
            }

            //validate backdate and if edit can continue if its same date
            if (mIsEditAft && mSelectedMonthStart != null && mSelectedMonthStart.compareTo(dateNowLocalDate) <= 0) {
                LocalDate startDateValidate = getParseDate(mInquiryTransferRevampResponse.getAftDateStart());
                if (!startDateValidate.toString().equals(mSelectedMonthStart.toString())) {
                    return false;
                }
            }


            // Check if endMonth already passed
            if (mIsEditAft && mSelectedMonthEnd != null && mSelectedMonthEnd.compareTo(dateNowLocalDate) <= 0) {
                // Only when unlimited flag is not set
                if (!mIsUnlimitedEndDateMonthly) {
                    return false;
                }
            }

            if (binding.etChooseMonthEnd.getText().toString().isEmpty()) {
                return false;
            }
        }
        return true;
    }

    @Override
    public void onSendFailedList(List<Integer> list) {
        mListFailed = list;
    }

    @Override
    public void onException(String message) {
        if (GeneralHelper.isContains(Constant.LIST_TYPE_GAGAL, message)) {
            GeneralHelper.showDialogGagalBack(this, message);
        } else {
            showSnackbarErrorMessageRevamp(message, ALERT_ERROR, this, false);
            Intent returnIntent = new Intent();
            returnIntent.putExtra(Constant.TAG_ERROR_MESSAGE, message);
            this.setResult(RESULT_CANCELED, returnIntent);
        }
    }

    @Override
    public void onClick(View view) {
        if (isBottomSheetCalenderShow) return;
        if (SystemClock.elapsedRealtime() - mLastClickTime < 1000) {
            return;
        }
        mLastClickTime = SystemClock.elapsedRealtime();
        if (view.getId() == binding.btnLanjut.getId()) {
            onClickSubmit();
        }
        if (view.getId() == binding.tvGanti.getId() || view.getId() == binding.llPilihSumberDana.getId()) {
            onClickChangeSof();
        }
        if (view.getId() == binding.etKota.getId()) {
            onClickChangeCity();
        }
        if (view.getId() == binding.etMetode.getId()) {
            onClickChangeMethod();
        }
        if (view.getId() == binding.etFrekuensiTransferTerjadwal.getId()) {
            onCLickFrekuensiAft();
        }
        if (view.getId() == binding.etChooseMonthStart.getId()) {
            showMonthPickerMonthly(true);
        }
        if (view.getId() == binding.etChooseMonthEnd.getId()) {
            showMonthPickerMonthly(false);
        }
        if (view.getId() == binding.etDatePickerOnce.getId()) {
            showDatePickerOnce();
        }
        if (view.getId() == binding.etChooseDay.getId()) {
            showChooseDay();
        }
        if (view.getId() == binding.etChooseDayStart.getId()) {
            showDatePickerWeekly(true);
        }
        if (view.getId() == binding.etChooseDayEnd.getId()) {
            showDatePickerWeekly(false);
        }
        if (view.getId() == binding.etChooseDateMonthly.getId()) {
            showDatePickerNoDay();
        }
//        if (view.getId() == binding.rlSmartTransfer.getId()) {
//            showKonfirmasiAlihTransfer(mSmartTransferRecommendedAcc);
//        }
    }

    private void onCLickFrekuensiAft() {
        // bottom fragment frekuensi
        SearchRevampFragment SearchRevampFragment = new SearchRevampFragment(fetchListFrekuensi(), optionSearchRevampModel -> {
            mAftFrequency = optionSearchRevampModel.getCodeModel();
            binding.etFrekuensiTransferTerjadwal.setText(optionSearchRevampModel.getOptionName());
            onChangeFrekuensi();
        }, "", GeneralHelper.getString(R.string.label_frekuensi), false, false);
        SearchRevampFragment.show(getSupportFragmentManager(), "");
    }

    private void onClickSubmit() {
        String nominalStr = binding.etNominal.getText().toString().isEmpty() ? "0" : binding.etNominal.getText().toString().replace(".", "");
        String note = binding.etNote.getText().toString();
        // jika tf ke BRI maka code metode dikosongkan
        String selectedMetodeStr = (mSelectedMetodeTransfer == null) ? "" : mSelectedMetodeTransfer.getCode();
        // custom inquiry untuk rtgs

        String saveAsStr = binding.etSaveAs.getText().toString();
        //jika edit text nama tersimpan kosong, ambil dari nama rekening tujuan
        saveAsStr = saveAsStr.isEmpty() ? openModel.getTitle() : saveAsStr;

        saveStr = (binding.switchSave.isChecked()) ? saveAsStr : "";

        if (binding.switchTransferTerjadwal.isChecked()) {
            String reqDateEnd = mAftDateEnd;
            if (((mAftFrequency.equals(Constant.AFT_SCHEDULE_WEEKLY) && mIsUnlimitedEndDateWeekly) || (mAftFrequency.equals(Constant.AFT_SCHEDULE_MONTHLY) && mIsUnlimitedEndDateMonthly))) {
                reqDateEnd = "unlimited";
            }
            selectedMetodeStr = (mSelectedMetodeTransfer == null) ? GeneralHelper.getString(R.string.transfer_method_aft_bri) : mSelectedMetodeTransfer.getCode();
            if (mIsEditAft && mInquiryTransferRevampResponse.getIdAft() != null) {
                presenter.getEditKonfirmasiAft(mInquiryTransferRevampResponse.getReferenceNumber(), model.getAcoount(), nominalStr, selectedMetodeStr, note, mAftFrequency, mAftDateStart, reqDateEnd, mInquiryTransferRevampResponse.getIdAft());
            } else {
                presenter.getDataKonfirmasiAft(mInquiryTransferRevampResponse.getReferenceNumber(), model.getAcoount(), nominalStr, selectedMetodeStr, note, mAftFrequency, mAftDateStart, reqDateEnd, saveStr);
            }
        } else {
            if (mSelectedMetodeTransfer != null && mSelectedMetodeTransfer.getCode().equalsIgnoreCase(Constant.FLAG_METHOD_RTGS)) {
                onRtgsSelected(nominalStr, selectedMetodeStr, note);
            } else {
                if (isFromFastMenu) {
                    presenter.setUrlKonfirmasi(GeneralHelper.getString(R.string.url_transfer_confirmation_fm_v3));
                } else {
                    presenter.setUrlKonfirmasi(GeneralHelper.getString(R.string.url_transfer_confirmation_v3));
                }
                presenter.getDataKonfirmasi(mInquiryTransferRevampResponse.getReferenceNumber(), model.getAcoount(), nominalStr, saveStr, isFromFastMenu, selectedMetodeStr, note);
            }
        }
    }

    private void showDatePickerNoDay() {
        if (mAftFrequency.isEmpty()) return;
        CalendarTransferTerjadwalNoDayFragment calendarFragment = new CalendarTransferTerjadwalNoDayFragment(this::handleDatePickerNoDay, (value) -> {
            isBottomSheetCalenderShow = value;
            return Unit.INSTANCE;
        });

        Bundle args = new Bundle();

        //TODO("Ini jangan dihapus")
        /*if (mSelectedMonthStart != null) args.putString(Constant.TAG_MIN_MONTHYEAR_AFT, new Gson().toJson(mSelectedMonthStart));
        if (mSelectedMonthEnd != null) args.putString(Constant.TAG_MAX_MONTHYEAR_AFT, new Gson().toJson(mSelectedMonthEnd));
        if (selectedMonthDateOnly == 0) {
            if (selectedMonthDate != null) args.putString(Constant.TAG_SELECTED_DATE, selectedMonthDate.toString());
        } else {
            LocalDate localDate;
            if (selectedMonthDate == null) {
                localDate = LocalDate.now().withDayOfMonth(selectedMonthDateOnly);
            } else {
                localDate = selectedMonthDate;
            }
            DateTimeFormatter dateTimeFormatter = DateTimeFormatter.ofPattern(DateTimeFormat.YYYY_MM_DD_STRIP.getFormat());
            String format = localDate.format(dateTimeFormatter);
            args.putString(Constant.TAG_SELECTED_DATE, format);
        }*/

        if (mSelectedMonthStart != null) {
            args.putString(Constant.TAG_MIN_MONTHYEAR_AFT, new Gson().toJson(mSelectedMonthStart));
        }

        if (mSelectedMonthEnd != null) {
            args.putString(Constant.TAG_MAX_MONTHYEAR_AFT, new Gson().toJson(mSelectedMonthEnd));
        }

        if (selectedDateOnlyMonth != null) {
            args.putString(Constant.TAG_SELECTED_DATE, selectedDateOnlyMonth.toString());
        }

        calendarFragment.setArguments(args);
        calendarFragment.setCancelable(true);
        calendarFragment.show(getSupportFragmentManager(), String.valueOf(Constant.REQ_CALENDAR));
    }

    private void handleDatePickerNoDay(LocalDate selectedDate) {
        String day = String.valueOf(selectedDate.getDayOfMonth());
        selectedDateOnlyMonth = LocalDate.now().withDayOfMonth(Integer.parseInt(day));

        if (mSelectedMonthStart != null) {
            mSelectedMonthStart = mSelectedMonthStart.withDayOfMonth(Integer.parseInt(day));
            mAftDateStart = dateFormatter.format(mSelectedMonthStart);
        }

        if (mSelectedMonthEnd != null && !mIsUnlimitedEndDateMonthly) {
            mSelectedMonthEnd = mSelectedMonthEnd.withDayOfMonth(Integer.parseInt(day));
            mAftDateEnd = dateFormatter.format(mSelectedMonthEnd);
        }

        binding.etChooseDateMonthly.setText(day);

        checkButton();
    }

    private void showDatePickerOnce() {
        // show datepicker
        if (mAftFrequency.isEmpty()) return;
        calendarFragment = new CalendarTransferTerjadwalFragment(this, Constant.DATE_PICKER_START, mAftFrequency, "",
                (value) -> {
                    isBottomSheetCalenderShow = value;
                    return Unit.INSTANCE;
                });

        Bundle args = new Bundle();
        if (currentDuration != null) {
            String minDate = CalendarHelper.getDateNowFormatRange();
            String convertMinDate = CalendarHelper.convertToNewFormat(minDate);
            LocalDate localMinDate = LocalDate.parse(convertMinDate);

            currentDuration = new DurasiModel(localMinDate.getDayOfMonth(), localMinDate.getMonthValue(), localMinDate.getYear(), localMinDate.getDayOfMonth(), localMinDate.getMonthValue(), localMinDate.getYear());

            if (currentDuration.getStartDateString() != null && !currentDuration.getStartDateString().isEmpty() && currentDuration.getStartMonth() > 0) {
                args.putString(Constant.TAG_MIN_DATE, currentDuration.getStartDateString());
            }
        }

        if (selectedDateOnce != null) {
            args.putString(Constant.TAG_SELECTED_DATE, selectedDateOnce.toString());
        }
        checkButton();

        args.putBoolean(Constant.TAG_PICK_END_DATE, false);
        args.putBoolean(Constant.TAG_PICK_DATE, false);
        args.putBoolean(Constant.TAG_MAX_TODAY, false);
        calendarFragment.setArguments(args);

        calendarFragment.setCancelable(true);
        calendarFragment.show(getSupportFragmentManager(), String.valueOf(Constant.REQ_CALENDAR));
    }

    private void onChangeFrekuensi() {
        mAftDateStart = "";
        mAftDateEnd = "";
        switch (mAftFrequency) {
            case Constant.AFT_SCHEDULE_ONCE: {
                if (selectedDateOnce != null) {
                    mAftDateStart = dateFormatter.format(selectedDateOnce);
                    mAftDateEnd = mAftDateStart;
                }
                binding.llAftOnce.setVisibility(View.VISIBLE);
                binding.llAftWeekly.setVisibility(View.GONE);
                binding.llAftMonthly.setVisibility(View.GONE);
                checkButton();
                break;
            }
            case Constant.AFT_SCHEDULE_WEEKLY: {
                if (selectedDateStartWeekly != null) {
                    mAftDateStart = dateFormatter.format(selectedDateStartWeekly);
                }
                if (selectedDateEndWeekly != null) {
                    mAftDateEnd = dateFormatter.format(selectedDateEndWeekly);
                }
                binding.llAftOnce.setVisibility(View.GONE);
                binding.llAftWeekly.setVisibility(View.VISIBLE);
                binding.llAftMonthly.setVisibility(View.GONE);
                checkButton();
                break;
            }
            case Constant.AFT_SCHEDULE_MONTHLY: {
                if (mSelectedMonthStart != null) {
                    mAftDateStart = dateFormatter.format(mSelectedMonthStart);
                }

                if (mSelectedMonthEnd != null) {
                    mAftDateEnd = dateFormatter.format(mSelectedMonthEnd);
                }

                binding.llAftOnce.setVisibility(View.GONE);
                binding.llAftWeekly.setVisibility(View.GONE);
                binding.llAftMonthly.setVisibility(View.VISIBLE);
                checkButton();
                break;
            }
            default: {
                break;
            }
        }

    }

    private void onRtgsSelected(String nominalStr, String selectedMetodeStr, String note) {
        if (isFromFastMenu) {
            presenter.setUrlKonfirmasi(GeneralHelper.getString(R.string.url_transfer_confirmation_rtgs_fm_v3));
        } else {
            presenter.setUrlKonfirmasi(GeneralHelper.getString(R.string.url_transfer_confirmation_rtgs_v3));
        }
        String benefCityCodeStr = mSelectedCity.getSandiKota();
        String benefCityNameStr = mSelectedCity.getNamaKota();
        String benefProvinceSandiStr = mSelectedCity.getSandiPropinsi();
        String benefAddressStr = binding.etAlamat.getText().toString();
        String benefNoteStr = binding.etBerita.getText().toString();
        String destinationNameStr = (!openModel.getTitle().isEmpty()) ? openModel.getTitle() : binding.etNamaNorek.getText().toString();
        presenter.getDataKonfirmasiRtgs(new ConfirmationTransferRtgsRevampRequest(
                new ConfirmationTransferRevampRequest(mInquiryTransferRevampResponse.getReferenceNumber(), model.getAcoount(), nominalStr, saveStr, selectedMetodeStr, note),
                benefCityCodeStr, benefCityNameStr, benefProvinceSandiStr, benefAddressStr, benefNoteStr, wargaNegara, destinationNameStr
        ), isFromFastMenu);
    }

    public void addListenerOnButton() {
        radioGroup = binding.rgWargaNegara;
        int radioButtonID = radioGroup.getCheckedRadioButtonId();
        if (radioButtonID == binding.rbWna.getId()) {
            wargaNegara = "WNA";
        } else if (radioButtonID == binding.rbWni.getId()) {
            wargaNegara = "WNI";
        }

    }

    private void onClickChangeSof() {
        counter++;
        if (mListAccountModel == null) {
            GeneralHelper.showToast(this, GeneralHelper.getString(R.string.no_account_list));
        } else {
            String nominalStr = binding.etNominal.getText().toString().isEmpty() ? "0" : binding.etNominal.getText().toString().replace(".", "");
            long nominalLong = Long.parseLong(nominalStr);
            if (isFromFastMenu) {
                SumberDanaFmFragmentRevamp fragmentSumberDanaFm = new SumberDanaFmFragmentRevamp(mListAccountModel, counter, nominalLong, this, isFromFastMenu);
                fragmentSumberDanaFm.show(getSupportFragmentManager(), "");
            } else {
                SumberDanaFragmentRevamp fragmentSumberDana = new SumberDanaFragmentRevamp(mListAccountModel, counter, nominalLong, this, mListFailed, isFromFastMenu);
                fragmentSumberDana.show(getSupportFragmentManager(), "");
            }
        }
    }

    private void onClickChangeCity() {
        SearchRevampFragment SearchRevampFragment = new SearchRevampFragment(fetchList(), this, GeneralHelper.getString(R.string.placeholder_search_city), GeneralHelper.getString(R.string.title_search_city), true, true);
        SearchRevampFragment.show(getSupportFragmentManager(), "");
    }

    private void onClickChangeMethod() {
        FragmentMetode fragmentMetode = new FragmentMetode(
                mInquiryTransferRevampResponse.getMetodeTransfer(),
                this,
                mSelectedPos,
                false,
                unavailableAftMethodResponse,
                binding.switchTransferTerjadwal.isChecked(),
                mJourneyType
        );
        fragmentMetode.show(getSupportFragmentManager(), "");
    }

    private List<OptionSearchRevampModel> fetchList() {
        List<OptionSearchRevampModel> optionSearchRevampModelArrayList = new ArrayList<>();
        if (mCityResponseRtgsListTemp != null) {
            for (int i = 0; i < mCityResponseRtgsListTemp.size(); i++) {
                CityResponseRtgs cityResponseRtgs = mCityResponseRtgsListTemp.get(i);
                optionSearchRevampModelArrayList.add(new OptionSearchRevampModel("", cityResponseRtgs.getNamaKota(), "", 0, i, cityResponseRtgs.getSandiKota(), false));
            }
        }
        return optionSearchRevampModelArrayList;
    }


    private List<OptionSearchRevampModel> fetchListFrekuensi() {
        List<OptionSearchRevampModel> optionSearchRevampModels = new ArrayList<>();
        if (mInquiryTransferRevampResponse.getFrequencyList() != null) {
            for (int i = 0; i < mInquiryTransferRevampResponse.getFrequencyList().size(); i++) {
                CodeValue codeValue = mInquiryTransferRevampResponse.getFrequencyList().get(i);
                optionSearchRevampModels.add(new OptionSearchRevampModel("", codeValue.getValue(), "", 0, i, codeValue.getCode(), false));
            }
        }
        return optionSearchRevampModels;
    }

    @Override
    public void onGetDataKonfirmasi(GeneralConfirmationResponse generalConfirmationResponse) {
        String mUrlPayment;
        if (isFromFastMenu) {
            if (isRtgs) {
                mUrlPayment = GeneralHelper.getString(R.string.url_transfer_payment_rtgs_fm_v3);
            } else {
                mUrlPayment = GeneralHelper.getString(R.string.url_transfer_payment_fm_v3);
            }
        } else {
            if (isRtgs) {
                mUrlPayment = GeneralHelper.getString(R.string.url_transfer_payment_rtgs_v3);
            } else {
                mUrlPayment = GeneralHelper.getString(R.string.url_transfer_payment_v3);
            }
        }
        KonfirmasiGeneralRevampActivity.launchIntent(this, generalConfirmationResponse, mUrlPayment, GeneralHelper.getString(R.string.toolbar_confirmation), setParameterKonfirmasi(), isFromFastMenu, true);
    }

    @Override
    public void onSuccessGetListCity(InquiryRtgsCityResponse inquiryRtgsCityResponse) {
        mCityResponseRtgsListTemp = inquiryRtgsCityResponse.getListKota();
        mInquiryRtgsCityResponse = inquiryRtgsCityResponse;
        setupMinTrx();
        checkButton();
    }

    @Override
    public void onFailedGetListCity() {
        isRtgs = false;
        binding.rlRtgs.setVisibility(View.GONE);
        if (mInquiryTransferRevampResponse.getMetodeTransfer().size() > 0) {
            onClick(mInquiryTransferRevampResponse.getMetodeTransfer().get(0), 0);
        }
    }

    @Override
    public void setDefaultSaldo(double saldoDefault, String saldoStringPref, String defaultAcc, boolean saldoHoldPref) {
        // load default saldo dari preference
        saldoString = saldoStringPref;
        saldo = saldoDefault;
        defaultAkun = defaultAcc;
        saldoHold = saldoHoldPref;

        //set layout
        setupAccount(saldoDefault);
    }

    protected void setupAccount(double saldoDefault) {
        if (mInquiryTransferRevampResponse == null)
            finish();

        //List Account
        try {
            model = new AccountModel();
            if (mInquiryTransferRevampResponse.getAccountList() != null && mInquiryTransferRevampResponse.getAccountList().size() > 0)
                mListAccountModel = mInquiryTransferRevampResponse.getAccountList();

            for (AccountModel accountModel : mListAccountModel) {
                if (accountModel.getIsDefault() == 1) {
                    model = accountModel;
                    break;
                } else {
                    model = mListAccountModel.get(0);
                }
            }
        } catch (Exception e) {
            if (!GeneralHelper.isProd()) {
                Log.e(TAG, "setupAccount: ", e);
            }
        }
        //jika get minimum tidak null
        if (model.getMinimumBalance() != null) {
            saldo = saldoDefault - model.getMinimumBalance();
        } else {
            saldo = saldoDefault;
        }

        if (isFromFastMenu) {
            binding.tvSaldoRek.setVisibility(View.GONE);
        } else {
            binding.tvSaldoRek.setVisibility(View.VISIBLE);

            if (model.getAcoount() != null) {
                if (model.getAcoount().equals(defaultAkun)) {
                    if (!saldoString.equals("-")) {
                        binding.tvSaldoRek.setText(GeneralHelper.formatNominalIDR(model.getCurrency(), saldoString));
                    } else {
                        binding.tvSaldoRek.setText("-");
                    }
                } else {
                    binding.tvNoRek.setText("-");
                    binding.tvSaldoRek.setText("-");
                }
            }

        }

        //jika string rekening tidak kosong
        if (model.getAcoountString() != null) {
            binding.tvNoRek.setText(model.getAcoountString());
        } else {
            binding.tvNoRek.setText("-");
        }

        if (!isFromFastMenu) {
            if (model.getSaldoReponse() != null) {
                // cek saldo tertahan
                if (model.getSaldoReponse().isOnHold())
                    binding.ivAlertSaldo.setVisibility(View.VISIBLE);
                else binding.ivAlertSaldo.setVisibility(View.GONE);
            }
        }

        if (!isFromFastMenu) {
            if (saldoHold) binding.ivAlertSaldo.setVisibility(View.VISIBLE);
            else binding.ivAlertSaldo.setVisibility(View.GONE);
        }

        checkButton();
    }

    @Override
    protected void onActivityResult(int requestCode, int resultCode, @Nullable Intent data) {
        super.onActivityResult(requestCode, resultCode, data);
        if (requestCode == Constant.REQ_PAYMENT) {
            if (resultCode == RESULT_OK) {
                this.setResult(RESULT_OK, data);
                this.finish();
            } else if (resultCode == RESULT_FIRST_USER) {
                this.setResult(RESULT_FIRST_USER, data);
            } else {
                this.setResult(RESULT_CANCELED, data);
                this.finish();
            }
        }
    }

    private void setupMinTrx() {
        if (isRtgs && mInquiryRtgsCityResponse != null) {
            minTrx = Long.parseLong(mInquiryRtgsCityResponse.getMinimumTransaction());
            minTrxString = String.format("Rp%s", GeneralHelper.formatNominal(mInquiryRtgsCityResponse.getMinimumTransaction()));
        } else {
            minTrx = mInquiryTransferRevampResponse.getMinimumTransaction();
            minTrxString = mInquiryTransferRevampResponse.getMinimumTransactionString();
        }
    }

    @Override
    public void onClick(MetodeTransfer metodeTransfer, int selectedPos) {
        mSelectedPos = selectedPos;
        mSelectedMetodeTransfer = metodeTransfer;
        checkIsAftAvailable();
        if (mSelectedMetodeTransfer.getCode().equalsIgnoreCase(Constant.FLAG_METHOD_RTGS)) {
            /*hit list city rtgs*/
            if (mCityResponseRtgsListTemp.size() == 0) {
                presenter.getListCity(mInquiryTransferRevampResponse.getBicCode(), isFromFastMenu);
            }
            isRtgs = true;
            binding.rlRtgs.setVisibility(View.VISIBLE);
        } else {
            isRtgs = false;
            binding.rlRtgs.setVisibility(View.GONE);
        }
        if (!mSelectedMetodeTransfer.getActive()) {
            binding.tilMetode.setHelperText(mSelectedMetodeTransfer.getForm());
        } else {
            binding.tilMetode.setHelperText("");
        }
        setupMinTrx();
        checkButton();
        binding.etMetode.setText(metodeTransfer.getTitle());
    }

    @Override
    public void onSelectOption(OptionSearchRevampModel optionSearchRevampModel) {
        if (optionSearchRevampModel != null) {
            mSelectedCity = mCityResponseRtgsListTemp.get(optionSearchRevampModel.getPositionModel());
            binding.etKota.setText(optionSearchRevampModel.getOptionName());
            checkButton();
        }
    }

    @Override
    public void onException93(String message) {
        Intent returnIntent = new Intent();

        returnIntent.putExtra(Constant.TAG_ERROR_MESSAGE, message);

        this.setResult(RESULT_CANCELED, returnIntent);
        this.finish();
    }

    @Override
    public void onException03(NonIndividuRtgsResponse messageResponse) {
        isRtgs = false;
        DialogTransferAbnormal dialog = new DialogTransferAbnormal(this, messageResponse.getMessageResponse().getTitle(), messageResponse.getMessageResponse().getDesc());
        FragmentTransaction ft = this.getSupportFragmentManager().beginTransaction();
        ft.add(dialog, null);
        ft.commitAllowingStateLoss();
    }

    @Override
    public void onSuccessGetDataKonfirmasiAft(AftConfirmationResponse aftConfirmationResponse) {
        String urlSubmit = (mIsEditAft) ? GeneralHelper.getString(R.string.url_aft_pay_edit) : GeneralHelper.getString(R.string.url_aft_pay_create);
        KonfirmasiAftActivity.launchIntent(this, aftConfirmationResponse, urlSubmit, GeneralHelper.getString(R.string.toolbar_confirmation), setParameterKonfirmasi(), isFromFastMenu, mIsEditAft, mJourneyType);
    }

    @Override
    public void onSuccessGetReccomendSmartTransfer(SmartTransferRecommendedAcc smartTransferRecommendedAcc) {
        mSmartTransferRecommendedAcc = smartTransferRecommendedAcc;
        //showSmartTransfer();
    }

    @Override
    public void onSuccessGetInquiry(InquiryTransferRevampResponse inquiryTransferRevampResponse) {
        mParameterModel.setSmartTransfer(true);
        mParameterModel.setAccOffUs(mSmartTransferRecommendedAcc.getAccountBankLain().getAccount());
        mParameterModel.setAccOffUsCode(mSmartTransferRecommendedAcc.getAccountBankLain().getKode());
        InquiryTransferRevampActivity.launchIntent(
                this,
                inquiryTransferRevampResponse,
                isFromFastMenu,
                mParameterModel,
                false,
                mJourneyType,
                mIsCreateFromBanner
        );
        setResult(RESULT_OK);
        this.finish();
    }

    @Override
    public void clickOk() {

    }

    @SuppressLint({"DefaultLocale", "SetTextI18n"})
    @Override
    public void onDateselect(@NonNull LocalDate dateSelect, @NonNull String type, boolean isUnlimited) {
        mIsUnlimitedEndDateWeekly = isUnlimited;
        if (type.equalsIgnoreCase(Constant.DATE_PICKER_START)) {
            selectedDateOnce = dateSelect;
            mAftDateStart = dateFormatter.format(selectedDateOnce);
            mAftDateEnd = mAftDateStart;
        }
        String monthTwoDigs = CalendarHelper.convertMont(String.format("%02d", dateSelect.getMonthValue()));

        binding.etDatePickerOnce.setText(dateSelect.getDayOfMonth() + " " + monthTwoDigs + " " + dateSelect.getYear());
        checkButton();
    }

    private LocalDate getParseDate(String value) {
        return LocalDate.parse(CalendarHelper.changeFormatCalendar("dd/MM/yyyy", "yyyy-MM-dd", value));
    }

//    private void showSmartTransfer() {
//        binding.rlContainerSmartTransfer.setVisibility(View.VISIBLE);
//    }

//    private void showKonfirmasiAlihTransfer(SmartTransferRecommendedAcc recommendedAcc){
//        OpenBottomSheetSmartTransferFragment.INSTANCE.showDialogConfirmation(
//                getSupportFragmentManager(),
//                recommendedAcc.getAccountBankLain().getLogo(),
//                recommendedAcc.getAccountBankBri().getLogo(),
//                recommendedAcc.getAccountBankLain().getSname(),
//                recommendedAcc.getAccountBankBri().getSname(),
//                recommendedAcc.getAccountBankLain().getNamaBank(),
//                GeneralHelper.getString(R.string.bank_bri_title_case),
//                recommendedAcc.getAccountBankLain().getAccount(),
//                recommendedAcc.getAccountBankBri().getAccount(),
//                GeneralHelper.getString(R.string.title_confirm_switch_destination),
//                GeneralHelper.getString(R.string.desc_confirm_switch_destination),
//                createKotlinFunction0(firstBtnFunction),
//                createKotlinFunction0(secondBtnFunction),
//                true,
//                "Lanjutkan",
//                "Batal",
//                false
//        );
//    }
//
//    Runnable firstBtnFunction = () -> {
//        presenter.getDataInquiry(new InquiryTransferAliasRevampRequest(Constant.FLAG_BANK_BRI_CODE, mSmartTransferRecommendedAcc.getAccountBankBri().getAccount()));
//    };
//
//    Runnable secondBtnFunction = () -> {
//
//    };
//
//    private void checkSmartTransferParams() {
//        if(mParameterModel != null) {
//            boolean mIsSmartTransfer = mParameterModel.getSmartTransfer() != null ? mParameterModel.getSmartTransfer() : false;
//            String mAccOffUsCode = mParameterModel.getAccOffUsCode() != null ? mParameterModel.getAccOffUsCode() : "";
//            if (mIsSmartTransfer) {
//                GeneralHelper.showSnackBarGreenRevamp(findViewById(R.id.content), GeneralHelper.getString(R.string.txt_sukses_switch_rekening));
//            } else {
//                if (!isFromFastMenu && !mAccOffUsCode.equals(Constant.FLAG_BANK_BRI_CODE)) {
//                    rekNonBri = openModel.getDescription().replaceAll("\\s", "");
//                    presenter.getRecommendSmartTransfer(new RecommendAccountSmartTransferRequest(rekNonBri, model.getAcoount(), mAccOffUsCode));
//                }
//            }
//        }
//    }
}