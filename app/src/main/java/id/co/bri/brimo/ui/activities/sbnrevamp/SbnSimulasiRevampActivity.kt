package id.co.bri.brimo.ui.activities.sbnrevamp

import android.app.Activity
import android.content.Context
import android.content.Intent
import android.os.Bundle
import android.view.KeyEvent
import android.view.View
import android.view.inputmethod.EditorInfo
import android.view.inputmethod.InputMethodManager
import android.widget.EditText
import androidx.core.content.ContextCompat
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.ethanhua.skeleton.Skeleton
import com.ethanhua.skeleton.SkeletonScreen
import id.co.bri.brimo.R
import id.co.bri.brimo.adapters.DataTransaksiRevampAdapter
import id.co.bri.brimo.adapters.sbnrevamp.SbnDataViewAdapter
import id.co.bri.brimo.contract.IPresenter.sbnrevamp.ISimulasiSbnPresenter
import id.co.bri.brimo.contract.IView.sbnrevamp.ISimulasiSbnView
import id.co.bri.brimo.databinding.ActivitySbnSimulasiRevampBinding
import id.co.bri.brimo.domain.config.Constant
import id.co.bri.brimo.domain.helpers.GeneralHelper
import id.co.bri.brimo.domain.helpers.textwatcher.AmountFormatWatcher
import id.co.bri.brimo.models.apimodel.request.esbn.RegisKemenkeuRequest
import id.co.bri.brimo.models.apimodel.request.sbnrevamp.SbnSimulasiRequest
import id.co.bri.brimo.models.apimodel.response.esbn.BeliSbnResponse
import id.co.bri.brimo.models.apimodel.response.esbn.DashboardDataSbnResponse
import id.co.bri.brimo.models.apimodel.response.esbn.DialogData
import id.co.bri.brimo.models.apimodel.response.esbn.EsbnExceptionResponse
import id.co.bri.brimo.models.apimodel.response.esbn.regisESBN.EsbnProductBriefResponse
import id.co.bri.brimo.models.apimodel.response.rdn.RdnOnBoardingResponse
import id.co.bri.brimo.models.apimodel.response.rdn.RdnOnCheckpointResponse
import id.co.bri.brimo.models.apimodel.response.sbnrevamp.NotFoundSbnModel
import id.co.bri.brimo.models.apimodel.response.sbnrevamp.SbnSimulasiResponse
import id.co.bri.brimo.ui.activities.base.BaseActivity
import id.co.bri.brimo.ui.activities.rdn.CekStatusRdnActivity
import id.co.bri.brimo.ui.activities.rdn.DashboardRDNActivity
import id.co.bri.brimo.ui.activities.rdn.DescriptionRdnProductActivity
import id.co.bri.brimo.ui.activities.rdnrevamp.dashboard.DashboardRdnRevampActivity
import id.co.bri.brimo.ui.activities.sbn.DetailBeliSbnActivity
import id.co.bri.brimo.ui.activities.sbn.regisSbn.EsbnProductBriefRegisActivity
import id.co.bri.brimo.ui.customviews.edittext.KeyboardEditText
import id.co.bri.brimo.ui.fragments.bottomsheet.OpenBottomSheetGeneralFragment
import id.co.bri.brimo.ui.fragments.esbn.BottomFragmentRegKemenkeu
import id.co.bri.brimo.ui.fragments.rdn.CustomBottomDialogFragment
import id.co.bri.brimo.ui.fragments.sbnrevamp.BottomFragmentSbnKemenkeu
import java.math.BigInteger
import javax.inject.Inject


class SbnSimulasiRevampActivity : BaseActivity(), ISimulasiSbnView,View.OnClickListener, CustomBottomDialogFragment.DialogDefaultListener,BottomFragmentSbnKemenkeu.OnFragmentInteractionListener, BottomFragmentRegKemenkeu.BottomDialogDefaultListener {

    private lateinit var binding: ActivitySbnSimulasiRevampBinding
    private var nominalStrClr: String? = "0"
    private var skeletonScreen: SkeletonScreen? = null
    private var dataTransaksiRevampAdapter: DataTransaksiRevampAdapter? = null
    private var sbnDataViewAdapter : SbnDataViewAdapter? = null

    @Inject
    lateinit var presenter : ISimulasiSbnPresenter<ISimulasiSbnView>
    companion object {
        private var mResponse : SbnSimulasiResponse? = null
        private var mIdSeri : Int? = null
        var mAccount : String? = ""
        var mSid :String?= ""
        var mSre : String?= ""
        @JvmStatic
        fun launchIntent(caller: Activity, response: SbnSimulasiResponse, idSeri : Int,account :String?, sid : String?, sre : String?) {
            val intent = Intent(caller, SbnSimulasiRevampActivity::class.java)
            mResponse = response
            mIdSeri = idSeri
            mAccount = account
            mSid = sid
            mSre = sre
            caller.startActivityForResult(intent, Constant.REQ_PAYMENT)
        }
    }
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        binding = ActivitySbnSimulasiRevampBinding.inflate(layoutInflater)
        setContentView(binding.root)
        injecetDepedency()
        setupView()
    }
    private fun injecetDepedency() {
        activityComponent.inject(this)
        presenter.view = this
        presenter.setUrlSimulasiSbn(GeneralHelper.getString(R.string.url_simulasi_sbn))
        presenter.setUrlBeliSbn(GeneralHelper.getString(R.string.url_get_detail_offer))
        presenter.setUrlValidateUser(GeneralHelper.getString(R.string.url_sbn_validate_user))
        presenter.setUrlRegistrasiSbn(GeneralHelper.getString(R.string.url_product_brief_esbn))
        presenter.setUrlProductsBrief(GeneralHelper.getString(R.string.url_rdn_onboarding_new))
        presenter.setUrlKemenkeu(GeneralHelper.getString(R.string.url_kemenkeu))
        presenter.start()
    }

    private fun setupView() {
        binding.apply {
            val responseSbnDetail = mResponse?.sbnDetailView
            GeneralHelper.setToolbarRevamp(this@SbnSimulasiRevampActivity, tbSimulasiSbn.toolbar, GeneralHelper.getString(R.string.tb_simulasi_sbn))
            GeneralHelper.loadImageUrl(this@SbnSimulasiRevampActivity, mResponse?.sbnDetailView?.iconPath, ivIcon, 0, 0)
            setAdapter()
            initView(responseSbnDetail)
            setEditText()
            setKeyboard()
        }

    }

    private fun setKeyboard() {
        window.decorView.viewTreeObserver.addOnGlobalFocusChangeListener { oldFocus, newFocus ->
            if (newFocus != null && newFocus is EditText) {
                // Fokus berpindah ke EditText, keyboard mungkin muncul
            } else {
                presenter.getDataSimulasiSbn(SbnSimulasiRequest(mResponse?.sbnDetailView?.title!!, nominalStrClr!!))

            }
        }
    }

    private fun setEditText() {
        binding.etNominal.setOnFocusChangeListener { _, b ->
            if (b) {
                binding.etNominal.hint = ""
            } else {
                binding.etNominal.hint = "Rp"
            }
        }
        binding.btnSubmit.isEnabled = false
        binding.btnSubmit.setTextColor(GeneralHelper.getColor(R.color.neutral_light60))

        binding.etNominal.addTextChangedListener(activityTextListener)
        binding.etNominal.addTextChangedListener(
                AmountFormatWatcher(
                        binding.etNominal,
                        null,
                        false
                )
        )

    }

    fun initView(responseSbnDetail: SbnSimulasiResponse.SbnDetailView?) {
            binding.apply {
                tvTitleSource.text = responseSbnDetail?.title
                tvSubtitleSource.text = responseSbnDetail?.subtitle
                tvHargaImbal.text = mResponse?.sbnReturnPerMonthView?.subtitle
                tvDescImbal.text = mResponse?.sbnReturnPerMonthView?.description
                tvImbalPerbulan.text = mResponse?.sbnReturnPerMonthView?.title
                tvTotalEstimasi.text = mResponse?.totalSbnReturn?.subtitle
                tvNilaiInvestasi.text = mResponse?.estimatedInvestmentTime
                tvStatus.text = responseSbnDetail?.description
                btnSubmit.setOnClickListener(this@SbnSimulasiRevampActivity)
                GeneralHelper.setWebViewStandart(binding.wvInformation, "", mResponse?.information?.desc)
                val desc = responseSbnDetail?.description?.trim()?.lowercase()
                if (desc?.equals(GeneralHelper.getString(R.string.akan_berakhir_txt), ignoreCase = true) == true) {
                    llStatus.visibility = View.VISIBLE

                    tvStatus.setTextColor(
                            ContextCompat.getColor(
                                    this@SbnSimulasiRevampActivity,
                                    R.color.semanticRed80
                            )
                    )
                    llStatus.setBackgroundResource(R.drawable.background_red10)
                }

                if (desc?.equals(GeneralHelper.getString(R.string.mendatang_txt), ignoreCase = true) == true) {
                    llStatus.visibility = View.VISIBLE
                    tvStatus.setTextColor(
                            ContextCompat.getColor(
                                    this@SbnSimulasiRevampActivity,
                                    R.color.semanticGreen80
                            )
                    )
                    llStatus.setBackgroundResource(R.drawable.background_green10)
                }

                if (desc?.equals(GeneralHelper.getString(R.string.berakhir_txt), ignoreCase = true) == true) {
                    llStatus.visibility = View.VISIBLE
                    tvStatus.setTextColor(
                            ContextCompat.getColor(
                                    this@SbnSimulasiRevampActivity,
                                    R.color.neutralLight60
                            )
                    )
                    llStatus.setBackgroundResource(R.drawable.background_neutrallight20)
                }
            }
        binding.tilNominal.helperText = mResponse?.minimumInvestView?.minimumDesc
    }

    private fun setAdapter() {
        binding.apply {
            rvDetailPayment.setHasFixedSize(true)
            rvDetailPayment.layoutManager = LinearLayoutManager(applicationContext, RecyclerView.HORIZONTAL, false)
            if (mResponse?.detailDataView != null) {
                sbnDataViewAdapter = SbnDataViewAdapter(mResponse?.detailDataView!!, this@SbnSimulasiRevampActivity)
                rvDetailPayment.adapter = sbnDataViewAdapter
            }
            rvAmountPayment.setHasFixedSize(true)
            rvAmountPayment.layoutManager = LinearLayoutManager(applicationContext, RecyclerView.VERTICAL, false)
            if (mResponse?.amountDataView != null) {
                dataTransaksiRevampAdapter = DataTransaksiRevampAdapter(mResponse?.amountDataView, this@SbnSimulasiRevampActivity)
                rvAmountPayment.adapter = dataTransaksiRevampAdapter
            }
        }

    }


    fun callPresenterKeyboard(){

        val inputMethodManager = getSystemService(Context.INPUT_METHOD_SERVICE) as InputMethodManager

        binding.etNominal.setOnEditorActionListener { _, actionId, event ->
            if (actionId == EditorInfo.IME_ACTION_DONE ||
                    actionId == EditorInfo.IME_ACTION_NEXT ||
                    actionId == EditorInfo.IME_ACTION_SEND ||
                    (event != null && event.action == KeyEvent.ACTION_DOWN &&
                            event.keyCode == KeyEvent.KEYCODE_ENTER)) {
                // Sembunyikan keyboard
                inputMethodManager.hideSoftInputFromWindow(binding.etNominal.windowToken, 0)
                    presenter.getDataSimulasiSbn(SbnSimulasiRequest(mResponse?.sbnDetailView?.title!!, nominalStrClr!!))
                true
            } else {
                false
            }
        }
        binding.etNominal.listener = object : KeyboardEditText.Listener {
            override fun onImeBack(editText: KeyboardEditText) {
                presenter.getDataSimulasiSbn(SbnSimulasiRequest(mResponse?.sbnDetailView?.title!!, nominalStrClr!!))

            }
        }

    }
    override fun changeText(charSequence: CharSequence?, i: Int, i1: Int, i2: Int) {
        super.changeText(charSequence, i, i1, i2)
        nominalStrClr = if (binding.etNominal.text.toString().isEmpty()) "0"
        else binding.etNominal.text.toString().replace(".", "")

        checkButton()
    }

    override fun onShowSkeleton(isShow: Boolean) {
       if (isShow){
           skeletonScreen = Skeleton.bind(binding.llAditionalData)
                   .shimmer(true)
                   .angle(20)
                   .duration(1200).color(R.color.white)
                   .load(R.layout.skeleton_nominal_investas)
                   .show()
       }else{
           skeletonScreen?.hide()
       }
    }



    override fun onSuccessSimulasiSbn(response: SbnSimulasiResponse) {
        binding.apply {

          if (response.detailDataView != null) {
              sbnDataViewAdapter = SbnDataViewAdapter(response.detailDataView, this@SbnSimulasiRevampActivity)
              binding.rvDetailPayment.adapter = sbnDataViewAdapter
            }

          if (response.amountDataView != null) {
                dataTransaksiRevampAdapter = DataTransaksiRevampAdapter(response.amountDataView, this@SbnSimulasiRevampActivity)
                binding.rvAmountPayment.adapter = dataTransaksiRevampAdapter
            }
            tvHargaImbal.text = response.sbnReturnPerMonthView?.subtitle
            tvDescImbal.text = response.sbnReturnPerMonthView?.description
            tvTotalEstimasi.text =response.totalSbnReturn?.subtitle
            tvNilaiInvestasi.text = response.estimatedInvestmentTime

        }

    }

    override fun onException12(message : String) {
        skeletonScreen?.hide()
        showSnackbarErrorMessageRevamp(
                message,
                ALERT_ERROR,
                this@SbnSimulasiRevampActivity,
                false
        )
    }

    override fun onSuccessGetDetail(beliSbnResponse: BeliSbnResponse?) {
        DetailBeliSbnActivity.launchIntent(this@SbnSimulasiRevampActivity, beliSbnResponse,nominalStrClr)
    }

    override fun onSuccessGetValidateUser(idSeri: Int) {
        mIdSeri?.let { presenter.getDetailOffer(it) }
    }

    override fun onExceptionNotFound(data: NotFoundSbnModel) {
        val customBottomDialogFragment = CustomBottomDialogFragment(this, data.title, data.desc, getString(R.string.ok), data.icon, data.iconPath, false)
        customBottomDialogFragment.show(this.supportFragmentManager, "")
    }

    override fun onExceptionRegisKemenkeu(response: NotFoundSbnModel?) {
        val bottomFragmentSbnKemenkeu = BottomFragmentSbnKemenkeu(this, response!!,this)
        bottomFragmentSbnKemenkeu.show(this.supportFragmentManager, "")
    }

    override fun onException02(response: EsbnExceptionResponse) {
        val customBottomDialogFragment = CustomBottomDialogFragment(this, response.title, response.description, GeneralHelper.getString(R.string.baiklah), response.imageName, response.imagePath, false,false, this)
        customBottomDialogFragment.show(supportFragmentManager, customBottomDialogFragment.getTag())
    }

    override fun onExceptionR4(response: NotFoundSbnModel?) {
        presenter.getSbnRegisData()
    }

    override fun onSuccessGetRegisSbn(response: EsbnProductBriefResponse) {
        EsbnProductBriefRegisActivity.launchIntent(this, response, false)
    }

    override fun onSuccessGetRegisRdnS1(response: DashboardDataSbnResponse) {
        val btnTitle = GeneralHelper.getString(R.string.buka_rdn_txt)
        showBottomSheetRegistrasi(
                response,
                btnTitle,
                action = {presenter.getOnProductBrief()}
        )
    }

    override fun onSuccessGetRegisRdnS2(response: DashboardDataSbnResponse) {
        val btnTitle = GeneralHelper.getString(R.string.string_button_01_sbn_to_rdn)
        showBottomSheetRegistrasi(
                response,
                btnTitle,
                action = { DashboardRdnRevampActivity.launchIntent(this)}
        )
    }

    override fun onSuccessGetSbnException(response: EsbnExceptionResponse) {
        OpenBottomSheetGeneralFragment.showDialogInformation(
                fragmentManager = supportFragmentManager,
                imgPath = response.imagePath,
                imgName = response.imageName,
                titleTxt = response.title,
                subTitleTxt = response.description,
                btnFirstFunction = {},
                isClickableOutside = false,
                firstBtnTxt = GeneralHelper.getString(R.string.ok)
        )
    }

    override fun onCheckPointRegisSbn(response: RdnOnCheckpointResponse) {
        CekStatusRdnActivity.launchIntent(this, response)
    }

    override fun onSuccessProductRdn(response: RdnOnBoardingResponse) {
        DescriptionRdnProductActivity.rdnOnBoardingResponse = response
        DescriptionRdnProductActivity.launchIntent(this)
    }

    override fun onSuccessKemenkeu(response: DashboardDataSbnResponse) {
        val customBottomDialogFragment = CustomBottomDialogFragment(this, response.dialogData.title, response.dialogData.description, GeneralHelper.getString(R.string.ok), "", response.dialogData.imageUrl, false)
        customBottomDialogFragment.show(supportFragmentManager, customBottomDialogFragment.tag)
    }

    private fun showBottomSheetRegistrasi(response: DashboardDataSbnResponse, btnTitle: String, action: () -> Unit = {}) {
        OpenBottomSheetGeneralFragment.showDialogInformationWithAction(
                fragmentManager = supportFragmentManager,
                imgPath = response.dialogData.imageUrl,
                imgName = "",
                titleTxt = response.dialogData.title,
                subTitleTxt = response.dialogData.description,
                btnFirstFunction = { action() },
                btnThirdFunction = {},
                isClickableOutside = true,
                firstBtnTxt = btnTitle,
                thirdBtnTxt = getString(R.string.nanti_saja)
        )
    }

    fun checkButton(){
        if (BigInteger.valueOf(nominalStrClr!!.toLong()) < BigInteger.valueOf(mResponse!!.minimumInvestView?.minimumInvestFloat!!.toLong())) {
            binding.btnSubmit.isEnabled = false
            binding.btnSubmit.setTextColor(GeneralHelper.getColor(R.color.neutral_light60))
            binding.tilNominal.helperText = mResponse?.minimumInvestView?.minimumDesc
            binding.tilNominal.setHelperTextColor(resources.getColorStateList(R.color.semanticRed80))
        }
        else if(nominalStrClr!!.toLong()% mResponse!!.mutltipleOrder!! !=0L){
            binding.btnSubmit.isEnabled = false
            binding.btnSubmit.setTextColor(GeneralHelper.getColor(R.color.neutral_light60))
            binding.tilNominal.helperText ="Nominal harus berkelipatan "+mResponse?.mutltipleOrderString
            binding.tilNominal.setHelperTextColor(resources.getColorStateList(R.color.semanticRed80))
        }
        else{
            binding.tilNominal.helperText = mResponse?.minimumInvestView?.minimumDesc
            binding.tilNominal.setHelperTextColor(resources.getColorStateList(R.color.neutral_light80))
            callPresenterKeyboard()
            binding.btnSubmit.isEnabled = true
            binding.btnSubmit.setTextColor(GeneralHelper.getColor(R.color.neutral_light10))
        }

    }


    override fun onClick(p0: View?) {
        mIdSeri?.let { presenter.getValidateUser(it) }

    }

    override fun onActivityResult(requestCode: Int, resultCode: Int, data: Intent?) {
        super.onActivityResult(requestCode, resultCode, data)
        if (requestCode == Constant.REQ_PAYMENT && data != null) {
            if (resultCode == RESULT_OK) {
                setResult(RESULT_OK, data)
                finish()
            }else {
                setResult(RESULT_CANCELED, data)
            }
        }
    }

    override fun onClickDialog() {
    }

    override fun onFragmentInteraction() {
        val rdnSidBottomFragment = BottomFragmentRegKemenkeu(this, GeneralHelper.getString(R.string.batal))
        rdnSidBottomFragment.show(supportFragmentManager, "")
    }

    override fun onClickOk(account: String?, sre: String?, sid: String?) {
        presenter.getKemenkeuData(RegisKemenkeuRequest(mAccount, mSid, mSre))
    }

    override fun onCLickNanti() {
    }

}