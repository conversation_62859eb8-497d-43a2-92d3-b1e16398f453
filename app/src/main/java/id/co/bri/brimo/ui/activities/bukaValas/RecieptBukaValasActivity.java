package id.co.bri.brimo.ui.activities.bukaValas;

import android.Manifest;
import android.app.Activity;
import android.content.ActivityNotFoundException;
import android.content.Intent;
import android.content.pm.PackageManager;
import android.content.pm.ResolveInfo;
import android.graphics.Bitmap;
import android.net.Uri;
import android.os.Bundle;
import android.os.Environment;
import android.os.Handler;
import android.util.Log;
import android.view.MenuItem;
import android.view.MotionEvent;
import android.view.View;
import android.view.ViewGroup;
import android.view.ViewTreeObserver;
import android.view.animation.Animation;
import android.widget.Toast;

import androidx.annotation.NonNull;
import androidx.appcompat.widget.Toolbar;
import androidx.core.app.ActivityCompat;
import androidx.core.content.ContextCompat;
import androidx.core.content.FileProvider;
import androidx.recyclerview.widget.GridLayoutManager;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.google.gson.Gson;
import com.wajahatkarim3.easyflipview.EasyFlipView;

import java.io.File;
import java.io.FileOutputStream;
import java.util.ArrayList;
import java.util.List;

import javax.inject.Inject;

import id.co.bri.brimo.BuildConfig;
import id.co.bri.brimo.R;
import id.co.bri.brimo.adapters.DataTransaksiAdapter;
import id.co.bri.brimo.adapters.HeaderTransaksiAdapter;
import id.co.bri.brimo.adapters.TotalTransaksiAdapter;
import id.co.bri.brimo.adapters.WatermarkAdapter;
import id.co.bri.brimo.contract.IPresenter.general.IGeneralReceiptPresenter;
import id.co.bri.brimo.contract.IView.general.IGeneralReceiptView;
import id.co.bri.brimo.databinding.ActivityRecieptBukaValasBinding;
import id.co.bri.brimo.domain.config.Constant;
import id.co.bri.brimo.domain.helpers.GeneralHelper;
import id.co.bri.brimo.domain.helpers.calendar.CalendarHelper;
import id.co.bri.brimo.domain.helpers.image.ImageHelper;
import id.co.bri.brimo.models.apimodel.response.DataView;
import id.co.bri.brimo.models.apimodel.response.InquiryReceiptResponse;
import id.co.bri.brimo.models.apimodel.response.InquiryRevampReceiptResponse;
import id.co.bri.brimo.models.apimodel.response.PendingResponse;
import id.co.bri.brimo.models.apimodel.response.ValidateResponse;
import id.co.bri.brimo.models.apimodel.response.lifestyle.PatternLifestyleTrackingResponse;
import id.co.bri.brimo.models.apimodel.response.lifestyle.UrlTrackingMobelanjaResponse;
import id.co.bri.brimo.models.apimodel.response.smartrecom.SmartRecomResponse;
import id.co.bri.brimo.models.apimodel.response.smarttransfer.SmartTransferAccountListConsentResponse;
import id.co.bri.brimo.models.apimodel.response.smarttransfer.SmartTransferConfirmAccBinding;
import id.co.bri.brimo.models.apimodel.response.smarttransfer.SmartTransferGeneralResponse;
import id.co.bri.brimo.models.apimodel.response.voucher.TutorialVoucherResponse;
import id.co.bri.brimo.ui.activities.DashboardIBActivity;
import id.co.bri.brimo.ui.activities.base.BaseActivity;

public class RecieptBukaValasActivity extends BaseActivity implements
        HeaderTransaksiAdapter.clickCopyListener,
        ActivityCompat.OnRequestPermissionsResultCallback,
        EasyFlipView.OnFlipAnimationListener,
        Toolbar.OnMenuItemClickListener,
        IGeneralReceiptView,
        View.OnClickListener {

    private ActivityRecieptBukaValasBinding binding;

    private static final String TAG = "ReceiptActivity";

    //untuk save instance state
    private static String TAG_PENDING = "pending_data";
    private String TAG_VALIDATE = "validate_data";

    ImageHelper imageHelper;

    String footer = "";

    private ArrayList<DataView> allDataView = new ArrayList<DataView>();
    private ArrayList<DataView> minimumDataView = new ArrayList<DataView>();
    private boolean isShowAll = false;
    private boolean isNoLihatLebih = false;
    private boolean isShared = false;
    private boolean isReadytoShare = false;
    private Bitmap bm;

    private PendingResponse mPendingrespon;
    private ValidateResponse mvalidateResponse;

    private final List<Integer> watermarkList = new ArrayList<>();

    @Inject
    IGeneralReceiptPresenter<IGeneralReceiptView> receiptPresenter;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        binding = ActivityRecieptBukaValasBinding.inflate(getLayoutInflater());
        setContentView(binding.getRoot());
        overridePendingTransition(R.anim.bottom_up, R.anim.nothing);

        imageHelper = new ImageHelper(this);

        //parsing data intent
        if (getIntent().getExtras() != null) {
            parseDataIntent(getIntent().getExtras());
        }

        //remove name toolbar
        GeneralHelper.setToolbar(this, binding.tbReceipt.toolbar, "");

        //show menu toolbar
        binding.tbReceipt.toolbar.inflateMenu(R.menu.menu);
        binding.tbReceipt.toolbar.setOnMenuItemClickListener(this);

        //inject presenter
        injectDependency();

        parseDataReceipt();
        onSetLayoutHeight();
        onWatermark();

        binding.btnReceipt.setOnClickListener(this);
        binding.llLhtLebih.lihatLebih.setOnClickListener(this);
        binding.llLhtLebih.lihatSedikit.setOnClickListener(this);
        binding.llLhtLebih.lihatLebih.setOnClickListener(this);
        binding.btnShare.setOnClickListener(this);
        binding.flipFooterReceipt.setOnFlipListener(this);
        binding.flipDetailReceipt.setOnFlipListener(this);

        checkPermission();

    }

    private void onSetLayoutHeight() {
        ViewTreeObserver viewTreeObserver = binding.layoutTicketView.getViewTreeObserver();
        viewTreeObserver.addOnGlobalLayoutListener(new ViewTreeObserver.OnGlobalLayoutListener() {
            @Override
            public void onGlobalLayout() {
                binding.layoutTicketView.getViewTreeObserver().removeGlobalOnLayoutListener(this);
                int height = binding.layoutTicketView.getMeasuredHeight();

                ViewGroup.LayoutParams params = binding.rvWatermark.getLayoutParams();
                params.height = height;
                binding.rvWatermark.setLayoutParams(params);

                Log.d(TAG, "onWatermark: " + height);
            }
        });
    }

    private void onWatermark() {
        for (int i = 0; i < 200; i++) {
            watermarkList.add(i);
        }

        binding.rvWatermark.setHasFixedSize(true);
        binding.rvWatermark.setLayoutManager(new GridLayoutManager(this, 4, RecyclerView.VERTICAL, false) {
            @Override
            public boolean canScrollVertically() {
                return false;
            }
        });
        WatermarkAdapter watermarkAdapter = new WatermarkAdapter(this, watermarkList);
        binding.rvWatermark.setAdapter(watermarkAdapter);
    }

    protected void parseDataReceipt() {
        if (mPendingrespon != null) {

            binding.rvDateReceipt.setHasFixedSize(true);
            binding.rvDateReceipt.setLayoutManager(new LinearLayoutManager(getApplicationContext(), RecyclerView.VERTICAL, false));
            DataTransaksiAdapter dataTransaksiAdapter = new DataTransaksiAdapter(mPendingrespon.getHeaderDataView(), RecieptBukaValasActivity.this);
            binding.rvDateReceipt.setAdapter(dataTransaksiAdapter);

            // Data Transaksi
            parseDataViewTransaction();

            // Detail Transaksi
//                rvNominal.setHasFixedSize(true);
//                rvNominal.setLayoutManager(new LinearLayoutManager(getApplicationContext(),RecyclerView.VERTICAL,false));
//                HeaderTransaksiAdapter headerTransaksiAdapter3=new HeaderTransaksiAdapter(mPendingrespon.getAmountDataView(),RecieptBukaValasActivity.this);
//                headerTransaksiAdapter3.setAdapterClickCopyListener(this);
//                rvNominal.setAdapter(headerTransaksiAdapter3);

            // Total Transaksi
            binding.rvTotalReceipt.setHasFixedSize(true);
            binding.rvTotalReceipt.setLayoutManager(new LinearLayoutManager(getApplicationContext(), RecyclerView.VERTICAL, false));
            TotalTransaksiAdapter totalTransaksiAdapter = new TotalTransaksiAdapter(mPendingrespon.getTotalDataView(), RecieptBukaValasActivity.this);
            binding.rvTotalReceipt.setAdapter(totalTransaksiAdapter);

            footer = mPendingrespon.getFooter();

            if (footer != null) {
                if (footer.equals("")) {
                    footer = mPendingrespon.getFooterHtml();
                    if (footer != null) {
                        binding.wvFooter.setVisibility(View.VISIBLE);
                        GeneralHelper.setWebViewReceipt(binding.wvFooter, "", footer);
                        //                        footerPulsa.setText(Html.fromHtml(footer));
                        //                        footerPulsa.setVisibility(View.VISIBLE);
                    } else
                        binding.tvFootNote.setVisibility(View.GONE);
                } else {
                    binding.tvFootNote.setVisibility(View.VISIBLE);
                    //                    footerPulsa.setTextAlignment(View.TEXT_ALIGNMENT_CENTER);
                    binding.tvFootNote.setText(footer);
                }
            } else {
                binding.tvFootNote.setVisibility(View.GONE);
            }

            if (mPendingrespon.getTitle() != null) {
                binding.tvTitle.setText(mPendingrespon.getTitle());
            }

            if (mPendingrespon.getTitleImage() != null) {
                int imageId = this.getResources().getIdentifier(mPendingrespon.getTitleImage(), "drawable", this.getPackageName());
                binding.imgCeklist.setImageResource(imageId);
            }

        } else {
            Log.d("kosong", "Null");
        }

    }

    protected void injectDependency() {
        getActivityComponent().inject(this);

        if (receiptPresenter != null) {
            receiptPresenter.setView(this);
            receiptPresenter.start();
        }
    }

    /**
     * Method digunakan untuk meng-extract data Intent
     *
     * @param extras Bundle savedInstanceState
     */
    protected void parseDataIntent(Bundle extras) {
        if (extras != null) {
            String pendingTemp = extras.getString(TAG_PENDING);
            if (pendingTemp != null) {
                mPendingrespon = new Gson().fromJson(pendingTemp, PendingResponse.class);
            }

            String validationTemp = extras.getString(TAG_VALIDATE);
            if (pendingTemp != null) {
                mvalidateResponse = new Gson().fromJson(validationTemp, ValidateResponse.class);
            }
        }
    }

    /**
     * Get return Intent untuk Dashboard activity
     *
     * @return
     */
    public static Intent setResultReceipt(PendingResponse response) {
        Intent intentReturn = new Intent();

        try {
            if (response.getTitleImage() != null) {
                if (response.getTitleImage().contains("00")) {
                    intentReturn.putExtra(Constant.REQUEST_RECEIPT, true);
                    return intentReturn;
                } else {
                    intentReturn.putExtra(Constant.REQUEST_RECEIPT, false);
                    return intentReturn;
                }
            } else {
                intentReturn.putExtra(Constant.REQUEST_RECEIPT, true);
                return intentReturn;
            }
        } catch (Exception e) {
            intentReturn.putExtra(Constant.REQUEST_RECEIPT, true);
            return intentReturn;
        }


    }

    private void checkPermission() {
        int permissionCheck = ContextCompat.checkSelfPermission(this, Manifest.permission.WRITE_EXTERNAL_STORAGE);

        if (permissionCheck != PackageManager.PERMISSION_GRANTED) {
            ActivityCompat.requestPermissions(this, new String[]{Manifest.permission.WRITE_EXTERNAL_STORAGE}, Constant.REQUEST_WRITE_STATE);
        }
    }


    private void parseDataViewTransaction() {
        allDataView = mPendingrespon.getDataViewTransaction();
        minimumDataView = mPendingrespon.getMinimDataTransaction();

        try {

            if (allDataView.size() <= mPendingrespon.getRowDataShow() || mPendingrespon.getRowDataShow() == 0) {
                isNoLihatLebih = true;
                binding.llLhtLebih.lihatLebih.setVisibility(View.GONE);

                lihatLebih();
            } else {
                isNoLihatLebih = false;
                binding.rvDetailReceipt2.setHasFixedSize(true);
                binding.rvDetailReceipt2.setLayoutManager(new LinearLayoutManager(getApplicationContext(), RecyclerView.VERTICAL, false));
                HeaderTransaksiAdapter headerTransaksiAdapter2 = new HeaderTransaksiAdapter(minimumDataView, RecieptBukaValasActivity.this);
                headerTransaksiAdapter2.setAdapterClickCopyListener(this);
                binding.rvDetailReceipt2.setAdapter(headerTransaksiAdapter2);

                binding.rvDetailReceipt.setVisibility(View.GONE);
                binding.rvDetailReceipt2.setVisibility(View.VISIBLE);
                binding.llLhtLebih.lihatLebih.setVisibility(View.VISIBLE);

                lihatSedikit();
            }

            //load default detail transaksi
            binding.rvDetailReceipt.setHasFixedSize(true);
            binding.rvDetailReceipt.setLayoutManager(new LinearLayoutManager(getApplicationContext(), RecyclerView.VERTICAL, false));
            HeaderTransaksiAdapter headerTransaksiAdapter1 = new HeaderTransaksiAdapter(allDataView, RecieptBukaValasActivity.this);
            headerTransaksiAdapter1.setAdapterClickCopyListener(this);
            binding.rvDetailReceipt.setAdapter(headerTransaksiAdapter1);

            binding.rvDetailReceipt3.setHasFixedSize(true);
            binding.rvDetailReceipt3.setLayoutManager(new LinearLayoutManager(getApplicationContext(), RecyclerView.VERTICAL, false));
            binding.rvDetailReceipt3.setAdapter(headerTransaksiAdapter1);

        } catch (Exception e) {
        }
    }

    protected void lihatLebih() {

        animationFadeOut.setAnimationListener(new Animation.AnimationListener() {
            @Override
            public void onAnimationStart(Animation animation) {

            }

            @Override
            public void onAnimationEnd(Animation animation) {
                binding.rvDetailReceipt.setVisibility(View.VISIBLE);
                binding.llLhtLebih.lihatSedikit.setVisibility(View.VISIBLE);
                binding.rvDetailReceipt2.setVisibility(View.GONE);
                binding.llLhtLebih.lihatLebih.setVisibility(View.GONE);
            }

            @Override
            public void onAnimationRepeat(Animation animation) {

            }
        });

        binding.llLhtLebih.lihatLebih.startAnimation(animationFadeOut);
        binding.rvDetailReceipt2.startAnimation(animationFadeOut);

        isShowAll = true;
    }

    /**
     * Share receipt
     */
    protected void shareReceipt() {
           /* if (!isShowAll) {
                animationFadeOut.reset();
                animationFadeOut.setAnimationListener(new Animation.AnimationListener() {
                    @Override
                    public void onAnimationStart(Animation animation) {
                        rvDetailMaksimum.setVisibility(View.VISIBLE);
                        lihatSedikit.setVisibility(View.GONE);
                        rvDetailMinimum.setVisibility(View.GONE);
                        lihatLebih.setVisibility(View.GONE);
                        isShowAll = true;
                    }

                    @Override
                    public void onAnimationEnd(Animation animation) {
                        shareImage(generateImage());
                        isShared = true;
                    }

                    @Override
                    public void onAnimationRepeat(Animation animation) {

                    }
                });

                rvDetailMinimum.startAnimation(animationFadeOut);
            } else {

            */

        shareImage(generateImage());
        isShared = true;
        //}
    }

    /**
     * Generate File Image
     *
     * @return
     */
    private File generateImage() {
        File file = null;
        if (binding.receiptShare != null)
            bm = imageHelper.getBitmapFromView(binding.receiptShare, binding.receiptShare.getChildAt(0).getHeight(), binding.receiptShare.getChildAt(0).getWidth());
        else
            bm = imageHelper.getBitmapFromView(binding.layoutReceipt, binding.layoutReceipt.getChildAt(0).getHeight(), binding.layoutReceipt.getChildAt(0).getWidth());
        file = saveBitmap(bm, generateNameReceipt());

        return file;
    }

    /**
     * Method untuk digunakan untuk generate Nama File BRImo
     *
     * @return tag name FIle Receipt
     */
    protected String generateNameReceipt() {
        String tag = "";
        String dateTime = CalendarHelper.getCurrentTimeReceipt();
        try {
            tag = Constant.TAG_START_NAME + dateTime + Constant.TAG_END_NAME;
        } catch (Exception e) {

            if (!GeneralHelper.isProd())
                Log.e(TAG, "generateNameReceipt: ", e);

            tag = Constant.TAG_START_NAME + Constant.TAG_END_NAME;
        }

        return tag;
    }

    protected void lihatSedikit() {

        animationFadeOut.setAnimationListener(new Animation.AnimationListener() {
            @Override
            public void onAnimationStart(Animation animation) {

            }

            @Override
            public void onAnimationEnd(Animation animation) {
                binding.rvDetailReceipt2.setVisibility(View.VISIBLE);
                binding.rvDetailReceipt.setVisibility(View.GONE);
                binding.llLhtLebih.lihatLebih.setVisibility(View.VISIBLE);
                binding.llLhtLebih.lihatSedikit.setVisibility(View.GONE);

            }

            @Override
            public void onAnimationRepeat(Animation animation) {

            }
        });

        binding.rvDetailReceipt.startAnimation(animationFadeOut);
        binding.llLhtLebih.lihatSedikit.startAnimation(animationFadeOut);

        isShowAll = false;
    }

    protected void toggleLihat() {
        if (!isShowAll) {
            lihatLebih();
        } else {
            lihatSedikit();
        }
    }

    private static File saveBitmap(Bitmap bm, String fileName) {
        final String path = Environment.getExternalStorageDirectory().getAbsolutePath() + Constant.URI_DOWNLOAD;
        File dir = new File(path);
        if (!dir.exists())
            dir.mkdirs();
        File file = new File(dir, fileName);

        try {
            FileOutputStream fOut = new FileOutputStream(file);
            bm.compress(Bitmap.CompressFormat.PNG, 90, fOut);
            fOut.flush();
            fOut.close();
        } catch (Exception e) {

        }

        return file;
    }

    @Override
    public boolean onMenuItemClick(MenuItem item) {
        if (item.getItemId() == R.id.share_item) {

            if (mPendingrespon.getShare()) {
                binding.flipLogoReceipt.flipTheView();
                binding.flipFooterReceipt.flipTheView();

                if (!isNoLihatLebih)
                    binding.flipDetailReceipt.flipTheView();
            }
        }
        return true;
    }


    /**
     * Generate Share INtent to Another APPS
     *
     * @param file file image receipt
     */
    private void shareImage(File file) {
        Uri uri = FileProvider.getUriForFile(this, BuildConfig.APPLICATION_ID + ".fileprovider", file);
        Intent intent = new Intent();
        intent.setAction(Intent.ACTION_SEND);
        intent.setType("image/*");

        intent.putExtra(android.content.Intent.EXTRA_SUBJECT, "");
        intent.putExtra(android.content.Intent.EXTRA_TEXT, "");
        intent.putExtra(Intent.EXTRA_STREAM, uri);
        intent.addFlags(Intent.FLAG_GRANT_READ_URI_PERMISSION);

        Intent chooser = Intent.createChooser(intent, "Download and share receipt");

        List<ResolveInfo> resInfoList = this.getPackageManager().queryIntentActivities(chooser, PackageManager.MATCH_DEFAULT_ONLY);

        for (ResolveInfo resolveInfo : resInfoList) {
            String packageName = resolveInfo.activityInfo.packageName;
            this.grantUriPermission(packageName, uri, Intent.FLAG_GRANT_WRITE_URI_PERMISSION | Intent.FLAG_GRANT_READ_URI_PERMISSION);
        }

        try {
            startActivity(chooser);
        } catch (ActivityNotFoundException e) {
            Toast.makeText(this, "Aplikasi tidak tersedia", Toast.LENGTH_SHORT).show();
        }
    }


    public static void launchIntent(Activity caller, PendingResponse pendingResponse) {
        Intent intent = new Intent(caller, RecieptBukaValasActivity.class);
        intent.putExtra(TAG_PENDING, new Gson().toJson(pendingResponse));

        caller.startActivityForResult(intent, Constant.REQ_PAYMENT);
        caller.setResult(RESULT_OK, setResultReceipt(pendingResponse));
        caller.finish();
    }

    public static void launchIntentFMpulsa(Activity caller, PendingResponse pendingResponse, boolean isFromFastMenu) {
        Intent intent = new Intent(caller, RecieptBukaValasActivity.class);
        intent.putExtra(TAG_PENDING, new Gson().toJson(pendingResponse));

        caller.startActivityForResult(intent, Constant.REQ_PAYMENT);
        caller.setResult(RESULT_OK, setResultReceipt(pendingResponse));
        caller.finish();
    }

    @Override
    public void onClick(View view) {
        int id = view.getId();
        switch (id) {
            case R.id.btn_receipt:
                onBackPressed();
                break;
            case R.id.lihat_sedikit:
            case R.id.lihat_lebih:
                toggleLihat();
                onSetLayoutHeight();
                break;
            case R.id.btn_share:
                binding.rvDetailReceipt.setVisibility(View.VISIBLE);
                binding.flipLogoReceipt.flipTheView();
                binding.flipFooterReceipt.flipTheView();
                onSetLayoutHeight();
                if (!isNoLihatLebih)
                    binding.flipDetailReceipt.flipTheView();
                break;
            default:
                break;
        }

    }

    @Override
    public void onBackPressed() {
        overridePendingTransition(R.anim.nothing, R.anim.bottom_down);
        DashboardIBActivity.launchIntent(this);
        this.finish();

    }


    @Override
    public void onResume() {
        super.onResume();
        onSetLayoutHeight();

        if (isShared) {
            parseDataViewTransaction();
            binding.flipLogoReceipt.flipTheView();
            binding.flipFooterReceipt.flipTheView();

            if (!isNoLihatLebih)
                binding.flipDetailReceipt.flipTheView();

            isShared = false;
        }
    }

    public void hideLihatLebih() {
        binding.llLhtLebih.lihatSedikit.setVisibility(View.GONE);
        binding.llLhtLebih.lihatLebih.setVisibility(View.GONE);
        binding.llLhtLebih.lihatLebih.setVisibility(View.GONE);
        if (binding.llLhtLebih.lihatLebih.getVisibility() == View.GONE && binding.llLhtLebih.lihatSedikit.getVisibility() == View.GONE)
            isReadytoShare = true;
    }

    /**
     * Callback ketika animasi Easy FLIP
     *
     * @param easyFlipView
     * @param newCurrentSide
     */
    @Override
    public void onViewFlipCompleted(EasyFlipView easyFlipView, EasyFlipView.FlipState newCurrentSide) {
        if (easyFlipView.getId() == R.id.flipFooterReceipt) {
            if (newCurrentSide.equals(EasyFlipView.FlipState.BACK_SIDE)) {
                try {
                    isReadytoShare = true;
                    if (isReadytoShare) {
                        new Handler().postDelayed(new Runnable() {
                            public void run() {
                                shareReceipt();
                                isReadytoShare = false;
                            }
                        }, 500);
                    }
                } catch (Exception e) {
                    Log.e(TAG, "onMenuItemClick: ", e);
                }
            }
        }
        onSetLayoutHeight();
    }

    @Override
    public boolean dispatchTouchEvent(MotionEvent event) {
        return super.dispatchTouchEvent(event);
    }

    /**
     * Save data response Payment
     *
     * @param outState
     */
    @Override
    protected void onSaveInstanceState(@NonNull Bundle outState) {
        super.onSaveInstanceState(outState);

        if (mPendingrespon != null) {
            try {
                outState.putString(TAG_PENDING, new Gson().toJson(mPendingrespon));
            } catch (Exception e) {

                if (!GeneralHelper.isProd())
                    Log.e(TAG, "onSaveInstanceState: ", e);
            }
        }

        if (mvalidateResponse != null) {
            try {
                outState.putString(TAG_VALIDATE, new Gson().toJson(mvalidateResponse));
            } catch (Exception e) {
                if (!GeneralHelper.isProd())
                    Log.e(TAG, "onSaveInstanceState: ", e);
            }
        }
    }


    /**
     * Extract data response Payment for view response
     *
     * @param savedInstanceState
     */
    @Override
    protected void onRestoreInstanceState(@NonNull Bundle savedInstanceState) {
        super.onRestoreInstanceState(savedInstanceState);

        if (savedInstanceState != null) {
            String pendingTemp = savedInstanceState.getString(TAG_PENDING);
            if (pendingTemp != null) {
                mPendingrespon = new Gson().fromJson(pendingTemp, PendingResponse.class);
            }

            String validationTemp = savedInstanceState.getString(TAG_VALIDATE);
            if (pendingTemp != null) {
                mvalidateResponse = new Gson().fromJson(validationTemp, ValidateResponse.class);
            }
        }

    }


    @Override
    protected void onDestroy() {
//                receiptPresenter.stop();
        binding = null;
        super.onDestroy();
    }

    /**
     * Callback ketika click copy dari Adapter HeaderTransaksiAdapter utk menampilkan Snackbar
     *
     * @param fieldName field Name
     * @param position  position item
     */
    @Override
    public void onClickCopy(String fieldName, int position) {
        showSnackbarErrorMessage("Nomor stroom/token Anda berhasil disalin", ALERT_CONFIRM, this, false);
    }

    @Override
    public void onSuccessGetTutorial(TutorialVoucherResponse tutorialVoucherResponse) {
        // do nothing
    }

    @Override
    public void onSuccessGetUrlTrackingMobelanja(UrlTrackingMobelanjaResponse urlTrackingMobelanjaResponse) {
        // do nothing
    }

    @Override
    public void onSuccessGetSmartRecom(SmartRecomResponse smartRecomResponse) {
        // do nothing
    }

    @Override
    public void onFailedGetSmartRecom() {
        //do nothing
    }

    @Override
    public void onSuccessGetInquiry(InquiryReceiptResponse inquiryReceiptResponse) {
        //do nothing
    }

    @Override
    public void onSuccessGetInquiryRevamp(InquiryRevampReceiptResponse inquiryRevampReceiptResponse) {
        //do nothing
    }
    @Override
    public void onShowBottomSheetCcAsSof() {
        //do nothing
    }

    @Override
    public void onSuccessChangeSof(String desc) {
        //do nothing
    }

    @Override
    public void onSuccessCheckSimilaritySmartTransfer(SmartTransferGeneralResponse checkSimilarityResponse) {
        //do nothing
    }

    @Override
    public void onSucessGetAccountListConsent(SmartTransferAccountListConsentResponse smartTransferAccountListConsentResponse) {
        //do nothing
    }

    @Override
    public void onSuccessSmartTransferManageUserConsent(SmartTransferConfirmAccBinding smartTransferConfirmAccBinding) {
        //do nothing
    }

    @Override
    public void onSuccessGetTrackingPattern(PatternLifestyleTrackingResponse patternLifestyleTrackingResponse) {
        //do nothing
    }

    @Override
    public void onExceptionEticketNotIssued() {
        //do nothing
    }
}