package id.co.bri.brimo.ui.activities.transferinternasional;

import android.app.Activity;
import android.content.ActivityNotFoundException;
import android.content.ClipData;
import android.content.ClipboardManager;
import android.content.Intent;
import android.content.pm.PackageManager;
import android.content.pm.ResolveInfo;
import android.graphics.Bitmap;
import android.graphics.Color;
import android.net.Uri;
import android.os.Bundle;
import android.os.Environment;
import android.os.Handler;
import android.util.Log;
import android.view.View;
import android.view.ViewGroup;
import android.view.ViewTreeObserver;
import android.view.animation.Animation;
import android.widget.Toast;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.core.app.ActivityCompat;
import androidx.core.content.FileProvider;
import androidx.recyclerview.widget.GridLayoutManager;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.google.firebase.crashlytics.FirebaseCrashlytics;
import com.google.gson.Gson;
import com.wajahatkarim3.easyflipview.EasyFlipView;

import java.io.File;
import java.io.FileOutputStream;
import java.util.ArrayList;
import java.util.List;

import id.co.bri.brimo.BuildConfig;
import id.co.bri.brimo.R;
import id.co.bri.brimo.adapters.DataTransaksiAdapter;
import id.co.bri.brimo.adapters.DataTransaksiGreyAdapter;
import id.co.bri.brimo.adapters.DetailPengirimAdapter;
import id.co.bri.brimo.adapters.HeaderTransaksiAdapter;
import id.co.bri.brimo.adapters.TotalTransaksiAdapter;
import id.co.bri.brimo.adapters.WatermarkAdapter;
import id.co.bri.brimo.contract.IView.general.IGeneralReceiptView;
import id.co.bri.brimo.databinding.ActivityReceiptTransferInternationalBinding;
import id.co.bri.brimo.domain.config.Constant;
import id.co.bri.brimo.domain.helpers.GeneralHelper;
import id.co.bri.brimo.domain.helpers.calendar.CalendarHelper;
import id.co.bri.brimo.domain.helpers.image.ImageHelper;
import id.co.bri.brimo.models.apimodel.response.DataView;
import id.co.bri.brimo.models.apimodel.response.InquiryReceiptResponse;
import id.co.bri.brimo.models.apimodel.response.InquiryRevampReceiptResponse;
import id.co.bri.brimo.models.apimodel.response.ReceiptInternasionalResponse;
import id.co.bri.brimo.models.apimodel.response.RestResponse;
import id.co.bri.brimo.models.apimodel.response.ValidateResponse;
import id.co.bri.brimo.models.apimodel.response.lifestyle.PatternLifestyleTrackingResponse;
import id.co.bri.brimo.models.apimodel.response.lifestyle.UrlTrackingMobelanjaResponse;
import id.co.bri.brimo.models.apimodel.response.smartrecom.SmartRecomResponse;
import id.co.bri.brimo.models.apimodel.response.smarttransfer.SmartTransferAccountListConsentResponse;
import id.co.bri.brimo.models.apimodel.response.smarttransfer.SmartTransferConfirmAccBinding;
import id.co.bri.brimo.models.apimodel.response.smarttransfer.SmartTransferGeneralResponse;
import id.co.bri.brimo.models.apimodel.response.voucher.TutorialVoucherResponse;
import id.co.bri.brimo.ui.activities.base.BaseActivity;

public class ReceiptTransferInternationalActivity extends BaseActivity implements
        HeaderTransaksiAdapter.clickCopyListener,
        ActivityCompat.OnRequestPermissionsResultCallback,
        EasyFlipView.OnFlipAnimationListener,
        IGeneralReceiptView,
        View.OnClickListener {

    private ActivityReceiptTransferInternationalBinding binding;

    private static final String TAG_PENDING = "pending_data";

    ImageHelper imageHelper;

    String footer = "";

    private boolean isShowAll = false;
    private boolean isNoLihatLebih = false;
    private boolean isShared = false;
    private boolean isReadytoShare = false;

    private ReceiptInternasionalResponse mPendingrespon;
    private ClipboardManager myClipboard;

    private final List<Integer> watermarkList = new ArrayList<>();

    public static void launchIntent(Activity caller, ReceiptInternasionalResponse pendingResponse) {
        Intent intent = new Intent(caller, ReceiptTransferInternationalActivity.class);
        intent.putExtra(TAG_PENDING, new Gson().toJson(pendingResponse));
        caller.startActivityForResult(intent, Constant.REQ_PAYMENT);
        caller.setResult(RESULT_OK, setResultReceipt(pendingResponse));
    }

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        overridePendingTransition(R.anim.bottom_up, R.anim.nothing);
        binding = ActivityReceiptTransferInternationalBinding.inflate(getLayoutInflater());
        setContentView(binding.getRoot());

        imageHelper = new ImageHelper(this);

        //parsing data intent
        if (getIntent().getExtras() != null) {
            parseDataIntent(getIntent().getExtras());
        }

        //remove name toolbar
        GeneralHelper.setToolbar(this, binding.tbReceipt.toolbar, "");

        //show menu toolbar
        if (mPendingrespon.getShare() != null) {
            if (mPendingrespon.getShare()) {
                binding.btnShare.setVisibility(View.VISIBLE);
            } else {
                binding.btnShare.setVisibility(View.GONE);
            }
        } else {
            binding.btnShare.setVisibility(View.GONE);
        }

        //
        parseDataReceipt();
        onSetLayoutHeight();
        onWatermark();

        binding.btnReceipt.setOnClickListener(this);
        binding.llLhtLebih.lihatLebihReceipt.setOnClickListener(this);
        binding.llLhtLebih.lihatSedikit.setOnClickListener(this);
        binding.llLhtLebih.lihatLebih.setOnClickListener(this);
        binding.flipFooterReceipt.setOnFlipListener(this);
        binding.flipDetailReceipt.setOnFlipListener(this);
        binding.btnLacak.setOnClickListener(this);
        binding.btnCopyUetr.setOnClickListener(this);
        binding.btnShare.setOnClickListener(this);

        myClipboard = (ClipboardManager) getSystemService(CLIPBOARD_SERVICE);
    }

    private void onSetLayoutHeight() {
        ViewTreeObserver viewTreeObserver = binding.layoutTicketView.getViewTreeObserver();
        viewTreeObserver.addOnGlobalLayoutListener(new ViewTreeObserver.OnGlobalLayoutListener() {
            @Override
            public void onGlobalLayout() {
                binding.layoutTicketView.getViewTreeObserver().removeGlobalOnLayoutListener(this);
                int height = binding.layoutTicketView.getMeasuredHeight();

                ViewGroup.LayoutParams params = binding.rvWatermark.getLayoutParams();
                params.height = height;
                binding.rvWatermark.setLayoutParams(params);
            }
        });
    }

    /**
     * method untuk memberikan watermark
     */
    private void onWatermark() {
        for (int i = 0; i < 200; i++) {
            watermarkList.add(i);
        }

        binding.rvWatermark.setHasFixedSize(true);
        binding.rvWatermark.setLayoutManager(new GridLayoutManager(this, 4, RecyclerView.VERTICAL, false) {
            @Override
            public boolean canScrollVertically() {
                return false;
            }
        });
        WatermarkAdapter watermarkAdapter = new WatermarkAdapter(this, watermarkList);
        binding.rvWatermark.setAdapter(watermarkAdapter);
    }

    @Override
    public void onClick(View view) {
        int id = view.getId();
        switch (id) {
            case R.id.btn_receipt:
                setResult(RESULT_OK);
                finish();
                break;
            case R.id.lihat_sedikit:
            case R.id.lihat_lebih:
                toggleLihat();
                onSetLayoutHeight();
                break;
            case R.id.btn_lacak:
                startActivity(new Intent(Intent.ACTION_VIEW, Uri.parse(mPendingrespon.getUrlTracker())));
                break;
            case R.id.btn_copy_uetr:
                ClipData myClip = ClipData.newPlainText("Data", mPendingrespon.getEutrDataView().getValue());
                myClipboard.setPrimaryClip(myClip);
                Toast.makeText(this, mPendingrespon.getEutrDataView().getName() + " berhasil disalin", Toast.LENGTH_LONG).show();
                break;
            case R.id.btn_share:
                binding.flipLogoReceipt.flipTheView();
                binding.flipFooterReceipt.flipTheView();

                if (!isNoLihatLebih)
                    binding.flipDetailReceipt.flipTheView();
                break;
            default:
                break;
        }

    }

    /**
     * Method digunakan untuk meng-extract data Intent
     *
     * @param extras Bundle savedInstanceState
     */
    protected void parseDataIntent(Bundle extras) {
        if (extras != null) {
            String pendingTemp = extras.getString(TAG_PENDING);
            if (pendingTemp != null) {
                mPendingrespon = new Gson().fromJson(pendingTemp, ReceiptInternasionalResponse.class);
            }
        }
    }

    protected void parseDataReceipt() {
        if (mPendingrespon != null) {

            //Pengirim
            binding.tvPenerima.setText("Penerima");
            binding.tvPengirim.setText("Pengirim");

            //UETR
            if (mPendingrespon.getEutrDataView().getValue().equals("")) {
                binding.rlCard.setVisibility(View.GONE);
                binding.btnLacak.setVisibility(View.GONE);
            } else {
                binding.tvUetrTitle.setText(mPendingrespon.getEutrDataView().getName());
                binding.tvUetr.setText(mPendingrespon.getEutrDataView().getValue());
            }


            binding.rvPengirim.setHasFixedSize(true);
            binding.rvPengirim.setLayoutManager(new LinearLayoutManager(this, RecyclerView.VERTICAL, false));

            DetailPengirimAdapter detailPengirimAdapter = new DetailPengirimAdapter(mPendingrespon.getSenderSumaryDataView());
            binding.rvPengirim.setAdapter(detailPengirimAdapter);

            binding.rvPenerima.setHasFixedSize(true);
            binding.rvPenerima.setLayoutManager(new LinearLayoutManager(this, RecyclerView.VERTICAL, false));

            DetailPengirimAdapter detailPenerimaAdapter = new DetailPengirimAdapter(mPendingrespon.getBenefSumaryDataView());
            binding.rvPenerima.setAdapter(detailPenerimaAdapter);

            binding.rvDateReceipt.setHasFixedSize(true);
            binding.rvDateReceipt.setLayoutManager(new LinearLayoutManager(getApplicationContext(), RecyclerView.VERTICAL, false));
            DataTransaksiAdapter dataTransaksiAdapter = new DataTransaksiAdapter(mPendingrespon.getHeaderDataView(), ReceiptTransferInternationalActivity.this);
            binding.rvDateReceipt.setAdapter(dataTransaksiAdapter);

            // Data Transaksi
            parseDataViewTransaction();

            // Detail Transaksi
            binding.rvNominalReceipt.setHasFixedSize(true);
            binding.rvNominalReceipt.setLayoutManager(new LinearLayoutManager(getApplicationContext(), RecyclerView.VERTICAL, false));
            DataTransaksiGreyAdapter headerTransaksiAdapter3 = new DataTransaksiGreyAdapter(mPendingrespon.getAmountDataView(), ReceiptTransferInternationalActivity.this);
//            headerTransaksiAdapter3.setAdapterClickCopyListener(this);
            binding.rvNominalReceipt.setAdapter(headerTransaksiAdapter3);

            binding.rvNominalDiterima.setHasFixedSize(true);
            binding.rvNominalDiterima.setLayoutManager(new LinearLayoutManager(getApplicationContext(), RecyclerView.VERTICAL, false));
            DataTransaksiGreyAdapter headerTransaksiAdapter4 = new DataTransaksiGreyAdapter(mPendingrespon.getAmountReceiveDataView(), ReceiptTransferInternationalActivity.this);
//            headerTransaksiAdapter3.setAdapterClickCopyListener(this);
            binding.rvNominalDiterima.setAdapter(headerTransaksiAdapter4);



            // Total Transaksi
            binding.rvTotalReceipt.setHasFixedSize(true);
            binding.rvTotalReceipt.setLayoutManager(new LinearLayoutManager(getApplicationContext(), RecyclerView.VERTICAL, false));
            TotalTransaksiAdapter totalTransaksiAdapter = new TotalTransaksiAdapter(mPendingrespon.getTotalDataView(), ReceiptTransferInternationalActivity.this);
            binding.rvTotalReceipt.setAdapter(totalTransaksiAdapter);

            footer = mPendingrespon.getFooterHtml();
            if (footer != null) {
                binding.wvFooter.setVisibility(View.VISIBLE);
                binding.wvFooter.setBackgroundColor(Color.TRANSPARENT);
                GeneralHelper.setWebViewReceipt(binding.wvFooter, "", footer);
//                        footerPulsa.setText(Html.fromHtml(footer));
//                        footerPulsa.setVisibility(View.VISIBLE);
            } else
                binding.tvFootNote.setVisibility(View.GONE);

            if (mPendingrespon.getTitle() != null) {
                binding.tvTitle.setText(mPendingrespon.getTitle());
            }

            if (mPendingrespon.getTitleImage() != null) {
                String titleImage;
                if (mPendingrespon.getTitleImage().equalsIgnoreCase("receipt_58")) {
                    titleImage = "receipt_68";
                } else titleImage = mPendingrespon.getTitleImage();
                int imageId = this.getResources().getIdentifier(titleImage, "drawable", this.getPackageName());
                binding.imgCeklist.setImageResource(imageId);
            }

        } else {
            Log.d("kosong", "Null");
        }

    }

    private void parseDataViewTransaction() {
        ArrayList<DataView> allDataView = mPendingrespon.getDataViewTransaction();
        ArrayList<DataView> minimumDataView = mPendingrespon.getMinimDataTransaction();

        try {


            if (allDataView.size() <= mPendingrespon.getRowDataShow() || mPendingrespon.getRowDataShow() == 0) {
                isNoLihatLebih = true;
                binding.llLhtLebih.lihatLebih.setVisibility(View.GONE);

                lihatLebih();
            } else {
                isNoLihatLebih = false;
                binding.rvDetailReceipt2.setHasFixedSize(true);
                binding.rvDetailReceipt2.setLayoutManager(new LinearLayoutManager(getApplicationContext(), RecyclerView.VERTICAL, false));
                HeaderTransaksiAdapter headerTransaksiAdapter2 = new HeaderTransaksiAdapter(minimumDataView, ReceiptTransferInternationalActivity.this);
                headerTransaksiAdapter2.setAdapterClickCopyListener(this);
                binding.rvDetailReceipt2.setAdapter(headerTransaksiAdapter2);

                binding.rvDetailReceipt.setVisibility(View.GONE);
                binding.rvDetailReceipt2.setVisibility(View.VISIBLE);
                binding.llLhtLebih.lihatLebih.setVisibility(View.VISIBLE);

                lihatSedikit();
            }

            //load default detail transaksi
            binding.rvDetailReceipt.setHasFixedSize(true);
            binding.rvDetailReceipt.setLayoutManager(new LinearLayoutManager(getApplicationContext(), RecyclerView.VERTICAL, false));
            HeaderTransaksiAdapter headerTransaksiAdapter1 = new HeaderTransaksiAdapter(allDataView, ReceiptTransferInternationalActivity.this);
            headerTransaksiAdapter1.setAdapterClickCopyListener(this);
            binding.rvDetailReceipt.setAdapter(headerTransaksiAdapter1);

            binding.rvDetailReceipt3.setHasFixedSize(true);
            binding.rvDetailReceipt3.setLayoutManager(new LinearLayoutManager(getApplicationContext(), RecyclerView.VERTICAL, false));
            binding.rvDetailReceipt3.setAdapter(headerTransaksiAdapter1);

        } catch (Exception e) {
            // do nothing
        }
    }

    protected void requestPermission() {

        if (!hasPermissions(this, PERMISSIONS)) {
            ActivityCompat.requestPermissions(this, PERMISSIONS, PERMISSIONS_ALL);
        } else {
            try {
                shareImage(generateImage());
                isShared = true;
                isReadytoShare = false;
            } catch (Exception e) {
                FirebaseCrashlytics.getInstance().recordException(e);
            }
        }
    }

    /**
     * Generate Share INtent to Another APPS
     *
     * @param file file image receipt
     */
    private void shareImage(File file) {
        Uri uri = FileProvider.getUriForFile(this,
                BuildConfig.APPLICATION_ID + ".fileprovider",
                file);
        Intent intent = new Intent();
        intent.setAction(Intent.ACTION_SEND);
        intent.setType("image/*");

        intent.putExtra(android.content.Intent.EXTRA_SUBJECT, "");
        intent.putExtra(android.content.Intent.EXTRA_TEXT, "");
        intent.putExtra(Intent.EXTRA_STREAM, uri);
        intent.addFlags(Intent.FLAG_GRANT_READ_URI_PERMISSION);

        Intent chooser = Intent.createChooser(intent, "Download and share receipt");

        List<ResolveInfo> resInfoList = this.getPackageManager().queryIntentActivities(chooser, PackageManager.MATCH_DEFAULT_ONLY);

        for (ResolveInfo resolveInfo : resInfoList) {
            String packageName = resolveInfo.activityInfo.packageName;
            this.grantUriPermission(packageName, uri, Intent.FLAG_GRANT_WRITE_URI_PERMISSION | Intent.FLAG_GRANT_READ_URI_PERMISSION);
        }

        try {
            startActivity(chooser);
        } catch (ActivityNotFoundException e) {
            Toast.makeText(this, "Aplikasi tidak tersedia", Toast.LENGTH_SHORT).show();
        }
    }

    protected void toggleLihat() {
        if (!isShowAll) {
            lihatLebih();
        } else {
            lihatSedikit();
        }
    }

    /**
     * Generate File Image
     *
     * @return
     */
    @Nullable
    private File generateImage() {
        try {
            File file;
            Bitmap bm;
            if (binding.receiptShare != null) bm = imageHelper.getBitmapFromView(binding.receiptShare, binding.receiptShare.getChildAt(0).getHeight(), binding.receiptShare.getChildAt(0).getWidth());
            else bm = imageHelper.getBitmapFromView(binding.layoutReceipt, binding.layoutReceipt.getChildAt(0).getHeight(), binding.layoutReceipt.getChildAt(0).getWidth());
            file = saveBitmap(bm, generateNameReceipt());
            return file;
        } catch (Exception e) {
            FirebaseCrashlytics.getInstance().recordException(e);
            return null;
        }
    }


    /**
     * Method untuk digunakan untuk generate Nama File BRImo
     *
     * @return tag name FIle Receipt
     */
    protected String generateNameReceipt() {
        String tag = "";
        String dateTime = CalendarHelper.getCurrentTimeReceipt();
        try {
            tag = Constant.TAG_START_NAME + dateTime + Constant.TAG_END_NAME;
        } catch (Exception e) {
            if (!GeneralHelper.isProd())
                tag = Constant.TAG_START_NAME + Constant.TAG_END_NAME;
        }

        return tag;
    }

    private static File saveBitmap(Bitmap bm, String fileName) {
        final String path = Environment.getExternalStorageDirectory().getAbsolutePath() + Constant.URI_DOWNLOAD;
        File dir = new File(path);
        if (!dir.exists())
            dir.mkdirs();
        File file = new File(dir, fileName);

        try {
            FileOutputStream fOut = new FileOutputStream(file);
            bm.compress(Bitmap.CompressFormat.PNG, 90, fOut);
            fOut.flush();
            fOut.close();
        } catch (Exception e) {
            // do nothing
        }

        return file;
    }

    protected void lihatSedikit() {

        animationFadeOut.setAnimationListener(new Animation.AnimationListener() {
            @Override
            public void onAnimationStart(Animation animation) {
                // do nothing
            }

            @Override
            public void onAnimationEnd(Animation animation) {
                binding.rvDetailReceipt2.setVisibility(View.VISIBLE);
                binding.rvDetailReceipt.setVisibility(View.GONE);
                binding.llLhtLebih.lihatLebih.setVisibility(View.VISIBLE);
                binding.llLhtLebih.lihatSedikit.setVisibility(View.GONE);

            }

            @Override
            public void onAnimationRepeat(Animation animation) {
                // do nothing
            }
        });

        binding.rvDetailReceipt.startAnimation(animationFadeOut);
        binding.llLhtLebih.lihatSedikit.startAnimation(animationFadeOut);

        isShowAll = false;
    }

    @Override
    protected void onResume() {
        super.onResume();
        if (isShared) {
            parseDataViewTransaction();
            binding.flipLogoReceipt.flipTheView();
            binding.flipFooterReceipt.flipTheView();

            if (!isNoLihatLebih)
                binding.flipDetailReceipt.flipTheView();

            isShared = false;
        }
        onSetLayoutHeight();
    }

    protected void lihatLebih() {

        animationFadeOut.setAnimationListener(new Animation.AnimationListener() {
            @Override
            public void onAnimationStart(Animation animation) {
                // do nothing
            }

            @Override
            public void onAnimationEnd(Animation animation) {
                binding.rvDetailReceipt.setVisibility(View.VISIBLE);
                binding.llLhtLebih.lihatSedikit.setVisibility(View.VISIBLE);
                binding.rvDetailReceipt2.setVisibility(View.GONE);
                binding.llLhtLebih.lihatLebih.setVisibility(View.GONE);
            }

            @Override
            public void onAnimationRepeat(Animation animation) {
                // do nothing
            }
        });

        binding.llLhtLebih.lihatLebih.startAnimation(animationFadeOut);
        binding.rvDetailReceipt2.startAnimation(animationFadeOut);

        isShowAll = true;
    }

    @Override
    public void onViewFlipCompleted(EasyFlipView easyFlipView, EasyFlipView.FlipState newCurrentSide) {
        if (easyFlipView.getId() == R.id.flipFooterReceipt
                && newCurrentSide.equals(EasyFlipView.FlipState.BACK_SIDE)) {
            try {
                isReadytoShare = true;
                if (isReadytoShare) {
                    new Handler().postDelayed(this::requestPermission, 500);
                }
            } catch (Exception e) {
                // do nothing
            }
        }

        onSetLayoutHeight();
    }

    @Override
    public void onClickCopy(String message, int position) {
        showSnackbarErrorMessage("Nomor Anda berhasil disalin", ALERT_CONFIRM, this, false);
    }

    @Override
    public void onRequestPermissionsResult(int requestCode, @NonNull String[] permissions, @NonNull int[] grantResults) {
        super.onRequestPermissionsResult(requestCode, permissions, grantResults);
        boolean grantAll = true;
        if (grantResults.length > 0) {
            for (int i = 0; i < grantResults.length; i++) {
                if (grantResults[i] != PackageManager.PERMISSION_GRANTED) {
                    grantAll = false;
                    break;
                }
            }
        }

        if (!grantAll) {
            showAlertFinish(getString(R.string.notes_need_permission_resi));
        } else {
            requestPermission();
        }
    }

    /**
     * Get return Intent untuk Dashboard activity
     *
     * @return
     */
    public static Intent setResultReceipt(ReceiptInternasionalResponse response) {
        Intent intentReturn = new Intent();

        try {
            if (response.getTitleImage() != null) {
                if (response.getTitleImage().contains("00")) {
                    intentReturn.putExtra(Constant.REQUEST_RECEIPT, true);
                    return intentReturn;
                } else {
                    intentReturn.putExtra(Constant.REQUEST_RECEIPT, false);
                    return intentReturn;
                }
            } else {
                intentReturn.putExtra(Constant.REQUEST_RECEIPT, true);
                return intentReturn;
            }
        } catch (Exception e) {
            intentReturn.putExtra(Constant.REQUEST_RECEIPT, true);
            return intentReturn;
        }
    }

    @Override
    public void onBackPressed() {
        super.onBackPressed();
        overridePendingTransition(R.anim.nothing, R.anim.bottom_down);
    }

    @Override
    public void onSuccessGetTutorial(TutorialVoucherResponse tutorialVoucherResponse) {
        //do nothing
    }

    @Override
    public void onSuccessGetUrlTrackingMobelanja(UrlTrackingMobelanjaResponse urlTrackingMobelanjaResponse) {
        // do nothing
    }

    @Override
    protected void onDestroy() {
        super.onDestroy();
        binding = null;
    }

    @Override
    public void onSuccessGetSmartRecom(SmartRecomResponse smartRecomResponse) {
        //do nothing
    }

    @Override
    public void onFailedGetSmartRecom() {
        //do nothing
    }

    @Override
    public void onSuccessGetInquiry(InquiryReceiptResponse inquiryReceiptResponse) {
        //do nothing
    }

    @Override
    public void onSuccessGetInquiryRevamp(InquiryRevampReceiptResponse inquiryRevampReceiptResponse) {
        //do nothing
    }

    @Override
    public void onShowBottomSheetCcAsSof() {
        //do nothing
    }

    @Override
    public void onSuccessChangeSof(String desc) {
        //do nothing
    }

    @Override
    public void onSuccessCheckSimilaritySmartTransfer(SmartTransferGeneralResponse checkSimilarityResponse) {
        //do nothing
    }

    @Override
    public void onSucessGetAccountListConsent(SmartTransferAccountListConsentResponse smartTransferAccountListConsentResponse) {
        //do nothing
    }

    @Override
    public void onSuccessSmartTransferManageUserConsent(SmartTransferConfirmAccBinding smartTransferConfirmAccBinding) {
        //do nothing
    }

    @Override
    public void onSuccessGetTrackingPattern(PatternLifestyleTrackingResponse patternLifestyleTrackingResponse) {
        //do nothing
    }

    @Override
    public void onExceptionEticketNotIssued() {
        //do nothing
    }
}