package id.co.bri.brimo.ui.activities.emas

import android.app.Activity
import android.content.Intent
import android.os.Bundle
import android.os.SystemClock
import android.view.View
import androidx.recyclerview.widget.LinearLayoutManager
import id.co.bri.brimo.R
import id.co.bri.brimo.adapters.DataviewRevampAdapter
import id.co.bri.brimo.databinding.ActivityReceiptGagalRegistrasiBinding
import id.co.bri.brimo.domain.config.Constant
import id.co.bri.brimo.domain.helpers.GeneralHelper
import id.co.bri.brimo.models.apimodel.response.emas.ReceiptGagalEmasResponse
import id.co.bri.brimo.ui.activities.DashboardIBActivity
import id.co.bri.brimo.ui.activities.PusatBantuanActivity
import id.co.bri.brimo.ui.activities.base.BaseActivity

class ReceiptGagalRegistrasiActivity : BaseActivity(), View.OnClickListener {
    lateinit var binding : ActivityReceiptGagalRegistrasiBinding

    companion object{
        var dataMaster: ReceiptGagalEmasResponse? = null
        @JvmStatic
        fun launchIntent(caller: Activity, data: ReceiptGagalEmasResponse) {
            val intent = Intent(caller, ReceiptGagalRegistrasiActivity::class.java)
            dataMaster = data
            caller.startActivityForResult(intent, Constant.REQ_PAYMENT)
        }
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        binding = ActivityReceiptGagalRegistrasiBinding.inflate(layoutInflater)
        setContentView(binding.root)

        setupView(dataMaster!!)
    }

    private fun setupView(response: ReceiptGagalEmasResponse){
        GeneralHelper.setWebViewStandart(binding.webview, "", response.subtitle)
        binding.webview.setBackgroundColor(0)
        binding.rvDetailPending.layoutManager = LinearLayoutManager(this)
        binding.rvNominalPending.layoutManager = LinearLayoutManager(this)
        binding.rvSetoranAwal.layoutManager = LinearLayoutManager(this)
        binding.rvTotalPending.layoutManager = LinearLayoutManager(this)

        var adapterHeader = DataviewRevampAdapter(response.headerDataView, this)
        var adapterTransaction = DataviewRevampAdapter(response.transactionDataView, this)
        var adapterAmount= DataviewRevampAdapter(response.amountDataView, this)
        var adapterTotal = DataviewRevampAdapter(response.totalDataView, this)

        binding.rvDetailPending.adapter = adapterHeader
        binding.rvNominalPending.adapter = adapterTransaction
        binding.rvSetoranAwal.adapter = adapterAmount
        binding.rvTotalPending.adapter = adapterTotal

        binding.btnPending.setOnClickListener(this)
        binding.llPusatBantuan.setOnClickListener(this)
    }

    override fun onClick(p0: View?) {
        if (SystemClock.elapsedRealtime() - mLastClickTime < 1000){
            return
        }
        mLastClickTime = SystemClock.elapsedRealtime()

        when(p0!!.id){
            R.id.btn_pending->{
                DashboardIBActivity.launchIntent(this)
            }

            R.id.ll_pusat_bantuan->{
                PusatBantuanActivity.launchIntent(this)
            }
        }
    }

}