package id.co.bri.brimo.models.apimodel.request;

import com.google.gson.annotations.Expose;
import com.google.gson.annotations.SerializedName;

public class FastSetorTunaiPaymentRequest extends FastMenuRequest {
    @SerializedName("account_number")
    @Expose
    private String account_number;
    @SerializedName("pin")
    @Expose
    private String pin;
    @SerializedName("pfm_category")
    @Expose
    private String pfmCategory;
    @SerializedName("note")
    @Expose
    private String note;

    public FastSetorTunaiPaymentRequest(FastMenuRequest request, String account_number, String pin, String pfmCategory, String note) {
        super(request.getUsername(), request.getTokenKey());
        this.account_number = account_number;
        this.pin = pin;
        this.pfmCategory = pfmCategory;
        this.note = note;
    }

    public String getAccount_number() {
        return account_number;
    }

    public void setAccount_number(String account_number) {
        this.account_number = account_number;
    }

    public String getPin() {
        return pin;
    }

    public void setPin(String pin) {
        this.pin = pin;
    }

    public String getPfmCategory() {
        return pfmCategory;
    }

    public void setPfmCategory(String pfmCategory) {
        this.pfmCategory = pfmCategory;
    }

    public String getNote() {
        return note;
    }

    public void setNote(String note) {
        this.note = note;
    }
}
