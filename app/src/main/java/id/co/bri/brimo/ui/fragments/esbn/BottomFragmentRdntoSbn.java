package id.co.bri.brimo.ui.fragments.esbn;

import android.annotation.SuppressLint;
import android.os.Bundle;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;

import androidx.fragment.app.DialogFragment;

import com.google.android.material.bottomsheet.BottomSheetDialogFragment;

import id.co.bri.brimo.R;
import id.co.bri.brimo.databinding.FragmentBottomRdntoSbnBinding;
import id.co.bri.brimo.models.apimodel.response.rdn.RdnMoveSbnResponse;


public class BottomFragmentRdntoSbn extends BottomSheetDialogFragment {

    private FragmentBottomRdntoSbnBinding binding;

    private BottomDialogDefaultListener dialogDefaulListener;


    private String stButton;
    private RdnMoveSbnResponse model;


    @SuppressLint("ValidFragment")
    public BottomFragmentRdntoSbn() {
        // Required empty public constructor
    }

    public BottomFragmentRdntoSbn(BottomDialogDefaultListener dialogDefaulListener2, RdnMoveSbnResponse model, String stButton) {
        this.model = model;
        this.dialogDefaulListener = dialogDefaulListener2;
        this.stButton = stButton;
    }

    @Override
    public void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setStyle(DialogFragment.STYLE_NORMAL, R.style.CustomBottomSheetDialogTheme);

    }

    @Override
    public View onCreateView(LayoutInflater inflater, ViewGroup container,
                             Bundle savedInstanceState) {
        binding = FragmentBottomRdntoSbnBinding.inflate(inflater, container, false);

        binding.tvTitle.setText(model.getOfferSbnDrawer().getTitle());
        binding.tvDesc.setText(model.getOfferSbnDrawer().getDescription());
        binding.btnYes.setText(stButton);

        binding.btnYes.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                if (dialogDefaulListener != null) {
                    dialogDefaulListener.onClickYesRdnToSbn(model);
                }
                dismiss();
            }
        });

        binding.btnNo.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View view) {
                if (dialogDefaulListener != null) {
                    dialogDefaulListener.onClickNoRdnToSbn(model);
                }
                dismiss();
            }
        });
        return binding.getRoot();
    }


    public interface BottomDialogDefaultListener {
        void onClickYesRdnToSbn(RdnMoveSbnResponse rdnMoveSbnResponse);

        void onClickNoRdnToSbn(RdnMoveSbnResponse rdnMoveSbnResponse);
    }
}