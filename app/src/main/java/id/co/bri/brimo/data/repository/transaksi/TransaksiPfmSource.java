package id.co.bri.brimo.data.repository.transaksi;


import id.co.bri.brimo.models.CatatanAnggaranModel;
import id.co.bri.brimo.models.CatatanPengeluaranModel;
import id.co.bri.brimo.models.TransaksiModel;
import id.co.bri.brimo.models.daomodel.AmountCategory;
import id.co.bri.brimo.models.daomodel.Transaksi;

import java.util.Date;
import java.util.List;

import io.reactivex.Completable;
import io.reactivex.Maybe;
import io.reactivex.Single;

public interface TransaksiPfmSource {

    Single<Transaksi> getFindId(long idTransaksi);

    Maybe<List<CatatanAnggaranModel>> getTotalTransaksiCategoryByMonth(String type, String bulan, String year);

    Maybe<List<CatatanPengeluaranModel>> getTotalPengeluaranCategory(String type,String starDate,String endDate, String startMonth, String endMonth, String year);

    Maybe<List<TransaksiModel>> getTransaksiPfmByDate(String type);

    Single<Long> getTransaksiPfmByCategory(long idCategory, Date startDate, Date endDate);

    Single<Long> saveTransaksiPfm(Transaksi transaksi);

    Maybe<Long> getTotalAmount(String type, String startMonth, String endMonth, String year);

    Maybe<Long> getTotalAmountPFM(String type,String starDay,String endDay, String startMonth, String endMonth, String year);

    Single<AmountCategory> getTotalTransaksiByMonth(String type, String bulan);

    Completable deleteTransaksiById(long id);

    Single<Integer> updateTransaksiPfm(long categoryId, long anggaranId, String trxName, String phone, String type,
                                       String username, long amount, Date date, long refNum, long paymentType, long transaksiId);

//    Maybe<List<TransaksiModel>> getListPFM(String type);
}