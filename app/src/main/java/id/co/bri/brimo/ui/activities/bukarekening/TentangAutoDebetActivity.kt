package id.co.bri.brimo.ui.activities.bukarekening

import android.app.Activity
import android.content.Intent
import androidx.appcompat.app.AppCompatActivity
import android.os.Bundle
import id.co.bri.brimo.R
import id.co.bri.brimo.databinding.ActivityTentangAutoDebetBinding
import id.co.bri.brimo.domain.config.Constant
import id.co.bri.brimo.domain.helpers.GeneralHelper

class TentangAutoDebetActivity : AppCompatActivity() {
    private lateinit var binding : ActivityTentangAutoDebetBinding

    companion object {
        lateinit var data : String
        var mToolbar = ""

        @JvmStatic
        fun launchIntent(caller: Activity, text : String, toolbarText : String) {
            val intent = Intent(caller, TentangAutoDebetActivity::class.java)
            data = text
            mToolbar = toolbarText
            caller.startActivityForResult(intent, Constant.REQ_BUKA_REKENING)
        }
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        binding = ActivityTentangAutoDebetBinding.inflate(layoutInflater)
        setContentView(binding.root)

        setView()
    }

    private fun setView(){
        GeneralHelper.setToolbarRevamp(this, binding.tbRek.toolbar, mToolbar)

        GeneralHelper.setWebViewStandart(binding.webview, "", data)
    }

}