package id.co.bri.brimo.ui.fragments.travel;

import android.annotation.SuppressLint;
import android.content.Context;
import android.os.Bundle;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.recyclerview.widget.GridLayoutManager;

import id.co.bri.brimo.adapters.FasilitasBusAdapter;
import id.co.bri.brimo.databinding.FragmentDetailFiturBinding;
import id.co.bri.brimo.models.apimodel.response.RouteDetail;
import id.co.bri.brimo.ui.fragments.BaseFragment;

public class DetailFiturFragment extends BaseFragment {

    private FragmentDetailFiturBinding binding;

    private static RouteDetail mRouteDetail;
    private FasilitasBusAdapter fasilitasBusAdapter;

    @SuppressLint("ValidFragment")
    public DetailFiturFragment() {
        // Required empty public constructor
    }

    public static DetailFiturFragment newInstance(RouteDetail routeDetail) {
        DetailFiturFragment fragment = new DetailFiturFragment();
        mRouteDetail = routeDetail;
        return fragment;
    }

    @Override
    public void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
    }

    @Override
    public View onCreateView(LayoutInflater inflater, ViewGroup container,
                             Bundle savedInstanceState) {
        binding = FragmentDetailFiturBinding.inflate(inflater, container, false);
        return binding.getRoot();
    }

    @Override
    public void onAttach(Context context) {
        super.onAttach(context);
    }

    @SuppressLint("SetTextI18n")
    @Override
    public void onViewCreated(@NonNull View view, @Nullable Bundle savedInstanceState) {
        super.onViewCreated(view, savedInstanceState);

        binding.tvBusName.setText(mRouteDetail.getProviderCommercialName());
        binding.tvBusCode.setText(mRouteDetail.getBusTripCode());
        binding.tvBusClass.setText(mRouteDetail.getSeatClass());
        binding.tvJumlahSeat.setText(mRouteDetail.getSeatCapacity().toString());
        binding.tvFormatSeat.setText(mRouteDetail.getSeatLayout());
        binding.tvModel.setText(mRouteDetail.getFleetName());

        binding.rvFasilitas.setLayoutManager(new GridLayoutManager(getContext(), 1, GridLayoutManager.HORIZONTAL, false));
        fasilitasBusAdapter = new FasilitasBusAdapter(mRouteDetail.getFacilities());
        binding.rvFasilitas.setAdapter(fasilitasBusAdapter);
    }

    @Override
    public void onDestroyView() {
        binding = null;
        super.onDestroyView();
    }
}
