package id.co.bri.brimo.ui.activities.bukaValas;


import android.app.Activity;
import android.content.Intent;
import android.os.Bundle;
import android.view.View;
import androidx.recyclerview.widget.LinearLayoutManager;
import com.ethanhua.skeleton.Skeleton;
import com.ethanhua.skeleton.SkeletonScreen;
import java.util.ArrayList;
import java.util.List;
import javax.inject.Inject;
import id.co.bri.brimo.R;
import id.co.bri.brimo.adapters.ListJenisTabunganAdapter;
import id.co.bri.brimo.contract.IPresenter.bukaValas.IPilihTabunganValasPresenter;
import id.co.bri.brimo.contract.IView.bukaValas.IPilihTabunganValasView;
import id.co.bri.brimo.databinding.ActivityPilihJenisTabunganValasBinding;
import id.co.bri.brimo.domain.config.Constant;
import id.co.bri.brimo.domain.helpers.GeneralHelper;
import id.co.bri.brimo.models.apimodel.response.bukarekening.JenisTabunganResponse;
import id.co.bri.brimo.ui.activities.base.BaseActivity;
import id.co.bri.brimo.ui.fragments.FragmentBottomDialog;

public class PilihJenisTabunganValas extends BaseActivity implements IPilihTabunganValasView, View.OnClickListener, ListJenisTabunganAdapter.onClickItem {

    private ActivityPilihJenisTabunganValasBinding binding;

    @Inject
    IPilihTabunganValasPresenter<IPilihTabunganValasView> presenter;

    private SkeletonScreen skeletonScreen;
    private ListJenisTabunganAdapter jnsTabunganAdapter;
    private List<JenisTabunganResponse.Product> productList = new ArrayList<>();
    protected List<JenisTabunganResponse.Tab> tabList = new ArrayList<>();

    public static void launchIntent(Activity caller){
        Intent intent = new Intent(caller, PilihJenisTabunganValas.class);
        caller.startActivity(intent);
    }


    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        binding = ActivityPilihJenisTabunganValasBinding.inflate(getLayoutInflater());
        setContentView(binding.getRoot());

        injectDependency();
        GeneralHelper.setToolbar(this, binding.tbToolbar.toolbar, "Buka Rekening");

        jnsTabunganAdapter = new ListJenisTabunganAdapter(productList, getApplicationContext(), this);
        LinearLayoutManager layoutManager = new LinearLayoutManager(this);
        binding.recyclerView.setHasFixedSize(true);
        binding.recyclerView.setLayoutManager(layoutManager);
        binding.recyclerView.setAdapter(jnsTabunganAdapter);

        skeletonScreen = Skeleton.bind(binding.recyclerView)
                .adapter(jnsTabunganAdapter)
                .shimmer(true)
                .angle(20)
                .frozen(false)
                .duration(1200)
                .load(R.layout.item_skeleton_list_jenis_tabungan)
                .show(); //default count is 10
    }

    private void injectDependency() {
        getActivityComponent().inject(this);
        if (presenter != null) {
            presenter.setView(this);
            presenter.start();
            presenter.setUrl(GeneralHelper.getString(R.string.url_jenis_tabungan));
            presenter.getJenisTabungan();
        }
    }

    @Override
    public void onClick(View view) {

    }

    public void onClickItem(int position) {
        RincianProdukValas.launchIntent(this, productList, position);
    }

    @Override
    public void onClickTabungan(int position) {
        onClickItem(position);
//        else GeneralHelper.showBottomDialog(PilihJenisTabunganActivity.this, Constant.COMING_SOON);
    }

    @Override
    public void onSuccessGetData(JenisTabunganResponse jenisTabunganResponse) {
        skeletonScreen.hide();
        productList = jenisTabunganResponse.getProduk();

        jnsTabunganAdapter.setItems(productList);
        jnsTabunganAdapter.notifyDataSetChanged();
    }

    @Override
    public void onFailedData() {
        FragmentBottomDialog fragmentBottomDialog = new FragmentBottomDialog(this,
                Constant.SERVER_UNDER_MAINTENANCE,
                GeneralHelper.getString(R.string.title_server_under_maintenance),
                GeneralHelper.getString(R.string.desc_server_under_maintenance),
                Constant.IMAGE_SERVER_UNDER_MAINTENANCE, false, () -> presenter.getJenisTabungan());
        fragmentBottomDialog.setCancelable(false);
        fragmentBottomDialog.show(getSupportFragmentManager(), "");
    }

    @Override
    protected void onDestroy() {
        super.onDestroy();
        binding = null;
    }

//    @Override
//    protected void onActivityResult(int requestCode, int resultCode, @Nullable Intent data) {
//        super.onActivityResult(requestCode, resultCode, data);
//        if (requestCode == Constant.REQ_PAYMENT) {
//            if (resultCode == RESULT_OK) {
//                this.setResult(RESULT_OK);
//                this.finish();
//            } else {
//                this.setResult(RESULT_CANCELED, data);
//                if (data != null) {
//                    if (!data.getStringExtra(Constant.TAG_ERROR_MESSAGE).isEmpty())
//                        this.finish();
//                }
//
//            }
//        }
//    }
}