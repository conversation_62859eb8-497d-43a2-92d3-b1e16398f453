package id.co.bri.brimo.ui.activities.emas

import android.app.Activity
import android.content.Intent
import android.os.Bundle
import android.widget.LinearLayout
import androidx.fragment.app.Fragment
import androidx.viewpager.widget.ViewPager
import id.co.bri.brimo.R
import id.co.bri.brimo.adapters.CatatanKeuanganAdapter
import id.co.bri.brimo.databinding.ActivityRiwayatTransaksiEmasBinding
import id.co.bri.brimo.domain.config.Constant
import id.co.bri.brimo.domain.helpers.GeneralHelper
import id.co.bri.brimo.models.apimodel.response.emas.RiwayatAmbilFisikEmasResponse
import id.co.bri.brimo.models.apimodel.response.emas.RiwayatTransaksiEmasResponse
import id.co.bri.brimo.ui.activities.base.BaseActivity
import id.co.bri.brimo.ui.fragments.emas.RiwayatAmbilFisikEmasFragment
import id.co.bri.brimo.ui.fragments.emas.RiwayatBeliEmasFragment
import id.co.bri.brimo.ui.fragments.emas.RiwayatJualEmasFragment

class RiwayatTransaksiEmasActivity : BaseActivity(), ViewPager.OnPageChangeListener {
    lateinit var binding: ActivityRiwayatTransaksiEmasBinding

    private var catatanKeuanganFragmentAdapter: CatatanKeuanganAdapter? = null
    private var fragmentList: MutableList<Fragment>? = mutableListOf()
    private var titleList: MutableList<String>? = mutableListOf()
    private var lyTabs: LinearLayout? = null

    companion object{
        private var mResponse: RiwayatTransaksiEmasResponse? = null
        private var mResponseRiwayatAmbilFisik : RiwayatAmbilFisikEmasResponse? = null
        @JvmStatic
        fun launchIntent(caller: Activity, response: RiwayatTransaksiEmasResponse, responseAmbilFisik: RiwayatAmbilFisikEmasResponse) {
            val intent = Intent(caller, RiwayatTransaksiEmasActivity::class.java)
            mResponse = response
            mResponseRiwayatAmbilFisik = responseAmbilFisik
            caller.startActivityForResult(intent, Constant.REQ_PAYMENT)
        }
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        binding = ActivityRiwayatTransaksiEmasBinding.inflate(layoutInflater)
        setContentView(binding.root)
        setupView()

    }

    private fun setupView(){
        GeneralHelper.setToolbar(this, binding.tbCatatanKeuangan.toolbar, GeneralHelper.getString(R.string.tb_txt_riwayat_emas))
        fragmentList!!.add(RiwayatBeliEmasFragment(mResponse!!,this))
        fragmentList!!.add(RiwayatJualEmasFragment(mResponse!!,this))
        fragmentList!!.add(RiwayatAmbilFisikEmasFragment(this, mResponseRiwayatAmbilFisik))

        titleList!!.add(GeneralHelper.getString(R.string.txt_pembelian))
        titleList!!.add(GeneralHelper.getString(R.string.txt_penjualan))
        titleList!!.add(getString(R.string.txt_ambil_fisik))

        catatanKeuanganFragmentAdapter = CatatanKeuanganAdapter(supportFragmentManager, this, fragmentList, titleList)
        binding.vpCatatanKeuangan.adapter = catatanKeuanganFragmentAdapter
        binding.tabCatatanKeuangan.setViewPager( binding.vpCatatanKeuangan)

        //Add bold effect on selected tab
        binding.tabCatatanKeuangan.setOnPageChangeListener(this)
        lyTabs = binding.tabCatatanKeuangan.getChildAt(0) as LinearLayout

        //change default style toolbar font
        GeneralHelper.changeTabsFontBoldRevamp(this, lyTabs, 0)

    }

    override fun onPageScrolled(position: Int, positionOffset: Float, positionOffsetPixels: Int) {
    }

    override fun onPageSelected(position: Int) {
        GeneralHelper.changeTabsFontBoldRevamp(this, lyTabs, position)
    }

    override fun onPageScrollStateChanged(state: Int) {
    }

    override fun onBackPressed() {
        val i = Intent()
        setResult(RESULT_CANCELED, i)
        finish()
    }

    override fun onActivityResult(requestCode: Int, resultCode: Int, data: Intent?) {
        super.onActivityResult(requestCode, resultCode, data)
        //melanjutkan activity result ke fragment onresultActivity
        for (fragment in supportFragmentManager.fragments) {
            fragment.onActivityResult(requestCode, resultCode, data)
        }
    }

}