package id.co.bri.brimo.ui.fragments.deposito

import android.annotation.SuppressLint
import android.app.Activity
import android.graphics.Color
import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.RelativeLayout
import androidx.core.text.HtmlCompat
import androidx.core.view.doOnLayout
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.swiperefreshlayout.widget.SwipeRefreshLayout
import androidx.swiperefreshlayout.widget.SwipeRefreshLayout.OnRefreshListener
import id.co.bri.brimo.R
import id.co.bri.brimo.adapters.dashboardInvestasi.FaqInvestasiAdapter
import id.co.bri.brimo.adapters.depositorevamp.ListDepositoRevampAdapter
import id.co.bri.brimo.contract.IPresenter.depositorevamp.IOnBoardingDepositoRevampPresenter
import id.co.bri.brimo.contract.IView.depositorevamp.IOnBoardingDepositoRevampView
import id.co.bri.brimo.databinding.FragmentListDepositoBinding
import id.co.bri.brimo.domain.config.FontConfig
import id.co.bri.brimo.domain.helpers.GeneralHelper
import id.co.bri.brimo.domain.helpers.bubbleShowCaseView.BubbleShowCase
import id.co.bri.brimo.domain.helpers.bubbleShowCaseView.BubbleShowCaseBuilder
import id.co.bri.brimo.domain.helpers.bubbleShowCaseView.BubbleShowCaseSequence
import id.co.bri.brimo.models.CategoryRekeningModel
import id.co.bri.brimo.models.apimodel.response.dashboardInvestasi.InvenstasiFaqResponse
import id.co.bri.brimo.models.apimodel.response.depositorevamp.GetListDepositoResponse
import id.co.bri.brimo.ui.activities.LupaPinActivity
import id.co.bri.brimo.ui.customviews.dialog.DialogExitCustom
import id.co.bri.brimo.ui.fragments.BaseFragment
import id.co.bri.brimo.ui.fragments.PinFragment


class ListDepositoFragment(
    var presenter: IOnBoardingDepositoRevampPresenter<IOnBoardingDepositoRevampView>,
    private var dataList: List<GetListDepositoResponse.AccountList>,
    private var faqList: List<GetListDepositoResponse.Faq>,
    private var itemListener: OnRefreshListener,
    private var mPosition: Int,
    private var onBubleCaseListener: OnBubleCaseListener
) : BaseFragment(), OnRefreshListener,
    View.OnClickListener,
    DialogExitCustom.DialogClickYesNoListener, PinFragment.SendPin {

    private var _binding: FragmentListDepositoBinding? = null
    private val binding get() = _binding!!
    private lateinit var listDepositoAdapter: ListDepositoRevampAdapter
    private var infoDepositoAdapter: FaqInvestasiAdapter? = null
    private val listBubleCase: MutableList<BubbleShowCaseBuilder> = arrayListOf()

    private lateinit var activity: Activity

    interface OnRefreshListener {
        fun onRefreshDashboard(swipeRefreshLayout: SwipeRefreshLayout)
    }

    interface OnBubleCaseListener {
        fun onReadyShowBubleCase()
    }

    override fun onCreateView(
        inflater: LayoutInflater, container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        // Inflate the layout for this fragment
        _binding = FragmentListDepositoBinding.inflate(inflater, container, false)
        activity = requireActivity()

        return binding.root
    }

    @SuppressLint("SetTextI18n")
    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)

        setupView()
        initListBubleCase()
        binding.swipeRefresh.setOnRefreshListener(this)
        binding.btnRekeningBaru.setOnClickListener(this)
        binding.btnPerbaruiRekening.setOnClickListener(this)
    }

    private fun initListBubleCase() {
        binding.rvDeposito.doOnLayout {
            val view =
                binding.rvDeposito.findViewHolderForAdapterPosition(0)?.itemView?.findViewById<RelativeLayout>(
                    R.id.rl_back
                )

            view?.apply {
                val bubleCaseCard = getBubbleShowCase(
                    GeneralHelper.getString(R.string.deposito_revamp_bubble_case_card_title_fragment),
                    GeneralHelper.getString(R.string.deposito_revamp_bubble_case_card_desc_fragment),
                    this,
                    true
                )
                listBubleCase.add(bubleCaseCard)
            }

            val bubleCaseAddButton = getBubbleShowCase(
                GeneralHelper.getString(R.string.deposito_revamp_bubble_case_title_fragment),
                GeneralHelper.getString(R.string.deposito_revamp_bubble_case_desc_fragment),
                binding.layoutButton
            )
            listBubleCase.add(bubleCaseAddButton)
            onBubleCaseListener.onReadyShowBubleCase()
        }
    }

    fun setupView() {
        val mListResponse: ArrayList<InvenstasiFaqResponse.Faq> = arrayListOf()
        faqList.forEach {
            val item = InvenstasiFaqResponse.Faq()
            item.header = it.title.toString()
            item.detail = it.description
            item.name = it.title
            mListResponse.add(item)
        }
        binding.tvTotalSaldo.text = dataList[mPosition].totalBalanceString
        listDepositoAdapter = ListDepositoRevampAdapter(
            requireActivity(),
            dataList[mPosition]
        )
        binding.rvDeposito.adapter = listDepositoAdapter
        binding.rvDeposito.layoutManager =
            LinearLayoutManager(requireContext(), LinearLayoutManager.VERTICAL, false)
        infoDepositoAdapter = FaqInvestasiAdapter(requireContext(), mListResponse.toList())
        binding.rvInfoDeposito.adapter = infoDepositoAdapter
        binding.rvInfoDeposito.layoutManager =
            LinearLayoutManager(requireContext(), LinearLayoutManager.VERTICAL, false)
        binding.btnScrollTop.setOnClickListener {
            binding.scrlView.fullScroll(binding.scrlView.top)
        }
        binding.btnScrollTop.visibility = View.GONE
        binding.scrlView.viewTreeObserver.addOnScrollChangedListener {
            val scrollY = binding.scrlView.scrollY
            val scrollViewHeight = binding.scrlView.height
            val contentHeight = binding.scrlView.getChildAt(0).height

            if (scrollY + scrollViewHeight >= contentHeight * 0.6) {
                binding.btnScrollTop.visibility = View.VISIBLE
            } else {
                binding.btnScrollTop.visibility = View.GONE
            }
        }


    }

    override fun onRefresh() {
        itemListener.onRefreshDashboard(binding.swipeRefresh)
    }

    override fun onClick(p0: View?) {
        when (p0?.id) {
            R.id.btn_rekening_baru -> presenter.getTermData()
            R.id.btn_perbarui_rekening -> {
                val dialogExitCustom = DialogExitCustom(
                    this,
                    GeneralHelper.getString(R.string.title_dialog_tampilkan_deposito),
                    HtmlCompat.fromHtml(
                        String.format(
                            GeneralHelper.getString(R.string.desc_dialog_lakukan_pembaruan)
                        ), HtmlCompat.FROM_HTML_MODE_LEGACY
                    ),
                    GeneralHelper.getString(R.string.cancel),
                    GeneralHelper.getString(R.string.button_text_tampil_deposito_perbarui),
                    true,
                    true
                )
                val ft = requireActivity()?.supportFragmentManager?.beginTransaction()
                if (ft != null) {
                    ft.add(dialogExitCustom, null)
                    ft.commitAllowingStateLoss()
                }

            }
        }
    }

    private fun addBubbleShowCase() {
        if (mPosition != 0) return
        BubbleShowCaseSequence()
            .addShowCases(listBubleCase)
            .show()
    }

    private fun getBubbleShowCase(
        title: String,
        message: String,
        view: View,
        showSkip: Boolean? = false
    ): BubbleShowCaseBuilder {
        val bubleCase = BubbleShowCaseBuilder(activity) //Activity instance
            .title(title) //Any title for the bubble view
            .description(message)
            .backgroundColor(Color.WHITE)
            .textColor(Color.BLACK)
            .buttonTitle(GeneralHelper.getString(R.string.close))
            .titleTextSize(18)
            .targetView(view)
            .setFontTitle(FontConfig.BRI_BOLD)
            .setFontText(FontConfig.BRI_MEDIUM)
            .textColorDesc(GeneralHelper.getColor(R.color.neutral_dark10))
            .arrowPosition(BubbleShowCase.ArrowPosition.BOTTOM)

        if (showSkip == true) {
            bubleCase
                .arrowPosition(BubbleShowCase.ArrowPosition.TOP)
                .buttonTitle(GeneralHelper.getString(R.string.berikutnya))
                .enableViewButtonSkip(true)
                .textViewLewati(GeneralHelper.getString(R.string.lewati))
        }
        return bubleCase
    }

    fun getListBubleCase() = listBubleCase

    override fun onClickBtnYes() {
        val pinFragment = PinFragment(activity, this)
        pinFragment.show()
    }

    override fun onClickBtnNo() {
        //do nothing
    }

    override fun onSendPinComplete(pin: String?) {
        if (pin != null) {
            val categoryRekeningModel = CategoryRekeningModel(
                GeneralHelper.getString(R.string.model_categroy_id_check_pin),
                GeneralHelper.getString(R.string.model_categroy_name_check_pin),
                GeneralHelper.getString(R.string.model_categroy_code_check_pin)
            )
            presenter.getRekeningDepositoUpdate(
                GeneralHelper.getString(R.string.url_deposito_revamp_get_acccount_list),
                categoryRekeningModel.categoryCode,
                pin, false
            )
        }
    }

    override fun onLupaPin() {
        LupaPinActivity.launchIntent(activity)
    }

}