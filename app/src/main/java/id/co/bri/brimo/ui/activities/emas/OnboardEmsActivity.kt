package id.co.bri.brimo.ui.activities.emas

import android.app.Activity
import android.content.Intent
import android.os.Bundle
import android.os.SystemClock
import android.view.View
import androidx.fragment.app.Fragment
import com.google.gson.Gson
import id.co.bri.brimo.R
import id.co.bri.brimo.adapters.baseadapter.ViewPagerAdapter
import id.co.bri.brimo.databinding.ActivityOnboardingEmasBinding
import id.co.bri.brimo.domain.config.Constant
import id.co.bri.brimo.domain.helpers.GeneralHelper
import id.co.bri.brimo.models.apimodel.response.emas.OnboardData
import id.co.bri.brimo.models.apimodel.response.emas.OnboardingSliderResponse
import id.co.bri.brimo.ui.activities.base.BaseActivity
import id.co.bri.brimo.ui.fragments.emas.OnboardSliderFragment

open class OnboardEmsActivity : BaseActivity(), View.OnClickListener {
    lateinit var binding : ActivityOnboardingEmasBinding


    private val TAG_ONBOARDING = "onboarding"
    private val TAG_FRAGMENT = "fragment"
    private val TAG_IMAGE = "image"

    private val AUTO_SCROLL_THRESHOLD_IN_MILLI = 3500
    private var adapterSLider: ViewPagerAdapter? = null


    companion object{
        private lateinit var onBoardData : OnboardingSliderResponse
        @JvmStatic
        fun launchIntent(caller: Activity, data : OnboardingSliderResponse) {
            val intent = Intent( caller, OnboardEmsActivity::class.java)
            onBoardData = data
            caller.startActivityForResult(intent, Constant.REQ_BUKA_REKENING)
        }
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        binding = ActivityOnboardingEmasBinding.inflate(layoutInflater)
        setContentView(binding.root)

        createDumyy(onBoardData)
    }

    private fun setupView(listData: ArrayList<OnboardData>){
        adapterSLider = ViewPagerAdapter(supportFragmentManager)
        GeneralHelper.setToolbar(this, binding.tbRdn.toolbar, GeneralHelper.getString(R.string.tb_regis_emas))

        for (x in listData){
            adapterSLider!!.addFragment(
                    addFragment(x),
                    ""
            )
        }

        binding.vpRdn.adapter = adapterSLider
        binding.vpRdn.startAutoScroll()
        binding.vpRdn.interval = AUTO_SCROLL_THRESHOLD_IN_MILLI.toLong()
        binding.vpRdn.setAutoScrollDurationFactor(6.0)
        binding.vpRdn.setCycle(true)
        binding.vpRdn.isStopScrollWhenTouch = true

        binding.dotsIndicator.setViewPager(binding.vpRdn)


        binding.btnLanjut.setOnClickListener(this)
    }

    private fun addFragment(onBoarding: OnboardData): Fragment {
        val bundle = Bundle()
        bundle.putString(TAG_FRAGMENT, Gson().toJson(onBoarding))
        val fragment = OnboardSliderFragment()
        fragment.arguments = bundle
        return fragment
    }

    private fun createDumyy(onBoarding: OnboardingSliderResponse){
        onBoardData = onBoarding

        setupView(onBoardData.onboardData)
    }

    override fun onClick(p0: View?) {
        if (SystemClock.elapsedRealtime() - mLastClickTime < 1000){
            return
        }
        mLastClickTime = SystemClock.elapsedRealtime()

        when (p0!!.id){
            R.id.btn_lanjut->{
                ProductBriefEmasActivity.launchIntent(this, onBoardData.productBrief!!)
            }
        }
    }

    override fun onActivityResult(requestCode: Int, resultCode: Int, data: Intent?) {
        super.onActivityResult(requestCode, resultCode, data)
        if (requestCode == Constant.REQ_BUKA_REKENING && resultCode == Constant.REQ_UPDATE && data != null) {
            this.setResult(Constant.REQ_UPDATE, data)
            finish()
        }else if (requestCode == Constant.REQ_BUKA_REKENING &&resultCode == RESULT_OK) {
            setResult(RESULT_OK, data)
            finish()
        }else if (requestCode == Constant.REQ_UPDATE && resultCode == RESULT_OK && data != null) {
            this.setResult(Constant.REQ_UPDATE, data)
            finish()
        }else if (resultCode == RESULT_OK && data != null) {
            this.setResult(Constant.REQ_UPDATE, data)
            finish()
        }
        else if(resultCode == RESULT_FIRST_USER && data != null){
            this.setResult(RESULT_FIRST_USER, data)
            finish()
        }
    }

    override fun onBackPressed() {
        val i = Intent()
        setResult(RESULT_FIRST_USER, i)
        finish()
    }
}