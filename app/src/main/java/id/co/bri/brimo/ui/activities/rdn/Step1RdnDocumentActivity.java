package id.co.bri.brimo.ui.activities.rdn;

import android.app.Activity;
import android.content.Intent;
import android.os.Bundle;
import android.view.View;

import androidx.annotation.Nullable;
import androidx.fragment.app.FragmentTransaction;

import id.co.bri.brimo.databinding.ActivityStep1RdnDocumentBinding;
import id.co.bri.brimo.domain.config.Constant;
import id.co.bri.brimo.domain.helpers.GeneralHelper;
import id.co.bri.brimo.ui.activities.base.BaseActivity;
import id.co.bri.brimo.ui.customviews.dialog.DialogExitCustom;

public class Step1RdnDocumentActivity extends BaseActivity implements View.OnClickListener, DialogExitCustom.DialogDefaultListener {

    private ActivityStep1RdnDocumentBinding binding;

    private static final String TAG_RESPONSE = "response";
    Intent i;

    public static void launchIntent(Activity caller, String response) {
        Intent i = new Intent(caller, Step1RdnDocumentActivity.class);
        i.putExtra(TAG_RESPONSE, response);
        caller.startActivityForResult(i, Constant.REQ_PAYMENT);
    }

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        binding = ActivityStep1RdnDocumentBinding.inflate(getLayoutInflater());
        setContentView(binding.getRoot());

        i = new Intent();

        GeneralHelper.setToolbar(this, binding.toolbar.toolbar, "Buka Rekening Efek - RDN");
        binding.btnSubmit.setOnClickListener(this);
    }

    @Override
    public void onClick(View view) {
        Step2RdnKtpInfromationActivity.launchIntent(this, getIntent().getStringExtra(TAG_RESPONSE));
    }

    @Override
    public void onBackPressed() {
        DialogExitCustom dialogExitCustom = new DialogExitCustom(this::onClickYes, "Yakin Ingin Menghentikan Pembukaan?", "Kamu akan dikembalikan ke halaman awal Rekening Dana Nasabah. Seluruh proses pembukaan RDN-mu sudah disimpan dan dapat kamu lanjutkan di lain waktu. ");
        FragmentTransaction ft = this.getSupportFragmentManager().beginTransaction();
        ft.add(dialogExitCustom, null);
        ft.commitAllowingStateLoss();
    }

    @Override
    public void onClickYes() {
        setResult(RESULT_CANCELED, getIntent());
        this.finish();
    }

    @Override
    protected void onActivityResult(int requestCode, int resultCode, @Nullable Intent data) {
        super.onActivityResult(requestCode, resultCode, data);
        if (resultCode == RESULT_OK) {
            if (data != null) {
                setResult(RESULT_OK, data);
            } else {
                setResult(RESULT_OK);
            }
            finish();
        } else if (requestCode == Constant.REQ_PAYMENT && data == null) {
            setResult(RESULT_CANCELED);
            finish();
        } else if (requestCode == Constant.REQ_REGIS) {
            if (resultCode == Activity.RESULT_OK) {
                setResult(RESULT_OK);
            } else {
                setResult(RESULT_CANCELED, data);
            }
            finish();
        }
    }

    @Override
    protected void onDestroy() {
        super.onDestroy();
        binding = null;
    }
}