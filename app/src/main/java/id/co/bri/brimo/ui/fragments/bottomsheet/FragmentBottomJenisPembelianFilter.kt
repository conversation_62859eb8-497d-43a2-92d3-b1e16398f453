package id.co.bri.brimo.ui.fragments.bottomsheet

import android.app.Dialog
import android.os.Bundle
import android.os.Handler
import android.os.Looper
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.view.WindowManager
import android.widget.FrameLayout
import com.google.android.material.bottomsheet.BottomSheetBehavior
import com.google.android.material.bottomsheet.BottomSheetBehavior.STATE_EXPANDED
import com.google.android.material.bottomsheet.BottomSheetDialog
import com.google.android.material.bottomsheet.BottomSheetDialogFragment
import id.co.bri.brimo.R

class FragmentBottomJenisPembelianFilter(
    private var customViedId: View,
    private var cancelable: Boolean = false,
    private var onTouchOutsideCancelable: Boolean = false,
    private var onDraggable: Boolean = false,
    var onDismiss: (<PERSON>olean) -> Unit
) : BottomSheetDialogFragment() {

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setStyle(STYLE_NORMAL, R.style.CustomBottomSheetDialogThemeInput)
    }

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View = customViedId.rootView

    override fun onCreateDialog(savedInstanceState: Bundle?): Dialog {
        val dialog = super.onCreateDialog(savedInstanceState)
        dialog.window?.setSoftInputMode(WindowManager.LayoutParams.SOFT_INPUT_STATE_ALWAYS_VISIBLE)
        dialog.apply {
            setOnShowListener {
                Handler(Looper.getMainLooper()).post {
                    val bottomSheet = (dialog as? BottomSheetDialog)?.findViewById<View>(R.id.design_bottom_sheet) as? FrameLayout
                    val behavior = bottomSheet?.let { BottomSheetBehavior.from(it) }
                    behavior?.isDraggable = onDraggable
                    behavior?.state = STATE_EXPANDED
                }
            }
            setCancelable(cancelable)
            setCanceledOnTouchOutside(onTouchOutsideCancelable)
        }
        return dialog
    }

    fun onDismissBottomSheet(resetCheckedCheckbox: Boolean) {
        onDismiss(resetCheckedCheckbox)
        dismissNow()
    }
}