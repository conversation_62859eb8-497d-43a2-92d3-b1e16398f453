package id.co.bri.brimo.ui.activities.dplkrevamp

import android.app.Activity
import android.content.Intent
import android.os.Build
import android.os.Bundle
import android.text.Editable
import android.text.InputFilter
import android.text.Selection
import android.text.TextWatcher
import android.view.View
import androidx.recyclerview.widget.LinearLayoutManager
import id.co.bri.brimo.R
import id.co.bri.brimo.adapters.RekomendasiAdapter
import id.co.bri.brimo.contract.IPresenter.dplkrevamp.ISettingAutoPaymentPresenter
import id.co.bri.brimo.contract.IView.dplkrevamp.ISettingAutoPaymentView
import id.co.bri.brimo.databinding.ActivitySettingAutoPaymentBinding
import id.co.bri.brimo.domain.config.Constant
import id.co.bri.brimo.domain.helpers.GeneralHelper
import id.co.bri.brimo.domain.helpers.ValidationHelper
import id.co.bri.brimo.models.AccountModel
import id.co.bri.brimo.models.apimodel.request.dplkrevamp.EditAutoPaymentRequest
import id.co.bri.brimo.models.apimodel.response.bukarekening.RecomInitialDepositItem
import id.co.bri.brimo.models.apimodel.response.dplkrevamp.InquirySettingAutoPaymentResponse
import id.co.bri.brimo.models.apimodel.response.dplkrevamp.SubmitAutoPaymentResponse
import id.co.bri.brimo.ui.activities.LupaPinActivity
import id.co.bri.brimo.ui.activities.base.BaseActivity
import id.co.bri.brimo.ui.fragments.PinFragment
import id.co.bri.brimo.ui.fragments.SetCalenderAFTFragment
import id.co.bri.brimo.ui.fragments.SumberDanaFragmentRevamp
import org.threeten.bp.LocalDate
import java.util.function.Consumer
import javax.inject.Inject

class SettingAutoPaymentActivity : BaseActivity(), RekomendasiAdapter.OnItemClickListener,
    SetCalenderAFTFragment.OnSelectDate, ISettingAutoPaymentView, PinFragment.SendPin,
    SumberDanaFragmentRevamp.SelectSumberDanaInterface {

    private lateinit var binding: ActivitySettingAutoPaymentBinding


    lateinit var calendarFragment: SetCalenderAFTFragment

    private var saldoNominal: Double = 0.0
    private var adapter: RekomendasiAdapter? = null
    private var minNominal: Double = 0.0
    private var maxNominal: Double = 0.0
    private var minNominalStr = ""
    private var maxNominalStr = ""
    private var model: AccountModel? = null
    private var saldo = 0.0
    private var isActive = false
    private var isRecom = false
    private var selectedDate = ""
    private var mSaldoString: String? = null
    private var mSaldo: Double = 0.0
    private var counter = 0
    private var mNominal: Double = 0.0
    private var mDefaultAkun: String? = null
    private var mListAccountModel: List<AccountModel>? = null
    private var mListFailed: List<Int>? = mutableListOf()
    private var recomList: List<RecomInitialDepositItem> = listOf()
    private var headerString =
        "<head><meta name='viewport' content='width=device-width, initial-scale=1.0, maximum-scale=1.0, minimum-scale=1.0, user-scalable=no'></head>"

    @Inject
    lateinit var presenter: ISettingAutoPaymentPresenter<ISettingAutoPaymentView>


    companion object {
        lateinit var mResponse: InquirySettingAutoPaymentResponse

        @JvmStatic
        fun launchIntent(caller: Activity, response: InquirySettingAutoPaymentResponse) {
            val intent = Intent(caller, SettingAutoPaymentActivity::class.java)
            mResponse = response
            caller.startActivityForResult(intent, Constant.REQ_AUTO_PAYMENT)
        }
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        binding = ActivitySettingAutoPaymentBinding.inflate(layoutInflater)
        setContentView(binding.root)
        injectDependency()
        setupView()
        initListener()
    }

    private fun initListener() {
        binding.etPilihTanggal.setOnClickListener { selectDate() }
        binding.btnSubmit.setOnClickListener {
            val pinFragment = PinFragment(this, this)
            pinFragment.show()
        }
        binding.lyInc.llSumberDana.setOnClickListener {
            counter++
            val fragmentSumberDanaNew = SumberDanaFragmentRevamp(
                mListAccountModel,
                counter,
                GeneralHelper.clearingAmountSigned("Rp" + binding.etNominal.text.toString())
                    .toLong(),
                this,
                mListFailed,
                false,
                true
            )
            fragmentSumberDanaNew.show(supportFragmentManager, Constant.TAG_PICK_ACCOUNT)
        }
    }

    private fun setupView() {
        setListenerEdittext()
        GeneralHelper.setToolbarRevamp(
            this@SettingAutoPaymentActivity, binding.tbInquiryAutoPayment.toolbar, getString(
                R.string.tb_atur_autopayment
            )
        )
        binding.btnSubmit.isEnabled = false
        binding.btnSubmit.setTextColor(GeneralHelper.getColor(R.color.neutral_light60))
        setupAccount()
        setupTextResponse()
        setupAdapterRekomendasi()
        binding.txtMinimal.setTextColor(GeneralHelper.getColor(R.color.neutralLight80))
        binding.etNominal.setOnFocusChangeListener { view, b ->
            isActive = b
            setBackgorundLayout()
            if (b) {
                isRecom = false
            }
        }

        binding.tvAutopaymentInfo.visibility = View.VISIBLE
        binding.wvDialogInfo.visibility = View.VISIBLE

    }

    private fun setupTextResponse() {
        maxNominal = mResponse.autopaymentDetail.maximalDeposit.deposit
        minNominal = mResponse.autopaymentDetail.minimalDeposit.deposit
        minNominalStr = mResponse.autopaymentDetail.minimalDeposit.depositString
        maxNominalStr = mResponse.autopaymentDetail.maximalDeposit.depositString
        mListAccountModel = mResponse.autopaymentDetail.accountList
        binding.tvAutopaymentInfo.text = mResponse.autopaymentDetail.editDateInfo
        binding.etPilihTanggal.setText(mResponse.autopaymentDetail.autopayment.dateString)
        binding.etNominal.setText(mResponse.autopaymentDetail.nominalInfo.nominalString)
        saldoNominal = mResponse.autopaymentDetail.nominalInfo.nominalFloat
        selectedDate = mResponse.autopaymentDetail.autopayment.date.toString()
        binding.txtMinimal.text = minNominalStr
        GeneralHelper.setWebView(
            binding.wvDialogInfo, "", headerString + mResponse.autopaymentDetail.autopaymentInfo
        )

    }

    private fun setupAdapterRekomendasi() {
        recomList = mResponse.autopaymentDetail.denomList
        adapter = RekomendasiAdapter(this, recomList, this)

        binding.rvRecomendation.layoutManager =
            LinearLayoutManager(this, LinearLayoutManager.HORIZONTAL, false)
        binding.rvRecomendation.adapter = adapter
    }

    private fun injectDependency() {
        activityComponent.inject(this)
        presenter.view = this
        presenter.setUrlInquiryAutoPayment(getString(R.string.url_inquiry_edit_auto_payment))
        presenter.setUrlEditAutoPayment(getString(R.string.url_edit_auto_payment))
        presenter.start()
    }

    override fun onItemClick(position: Int) {
        isRecom = true
        binding.etNominal.setText(String.format("%.0f", recomList[position].recomFloat.toFloat()))

        clearSelected()
        recomList[position].isSelected = true
        adapter?.notifyDataSetChanged()
    }

    override fun onSuccessInquiryAutoPayment(response: InquirySettingAutoPaymentResponse) {


    }


    private fun clearSelected() {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.N) {
            recomList.forEach(Consumer { p: RecomInitialDepositItem ->
                p.isSelected = false
            })
        } else {
            for (p in recomList) {
                p.isSelected = false
            }
        }
        adapter?.notifyDataSetChanged()
    }


    private fun setBackgorundLayout() {
        if (isActive) {
            binding.lyInputNominal.setBackgroundResource(R.drawable.background_cardview_state_stroked_primary80_radius10)
        }
    }

    override fun onSuccessInquiryEditAutoPayment(response: SubmitAutoPaymentResponse) {
        val data = Intent()
        data.putExtra(Constant.TAG_MESSAGE, response.message)
        setResult(RESULT_OK, data)
        finish()
    }

    override fun onException12(desc: String) {
        val intent = Intent()
        intent.putExtra(Constant.TAG_ERROR_MESSAGE, desc)
        setResult(RESULT_CANCELED, intent)
        finish()
    }

    override fun setDefaultSaldo(
        saldo: Double, saldoString: String?, account: String?, saldoHold: Boolean?
    ) {
        mSaldoString = saldoString
        mSaldo = saldo
        mDefaultAkun = account
    }

    override fun onExceptionTrxExpired(message: String) {
        val i = Intent()
        i.putExtra(Constant.TAG_ERROR_MESSAGE, message)
        setResult(RESULT_CANCELED, i)
        finish()
    }

    fun setupAccount() {
        //List Account
        if (mResponse.autopaymentDetail.accountList.isNotEmpty()) {
            mListAccountModel = mResponse.autopaymentDetail.accountList
        }

        for (accountModel in mListAccountModel!!) {
            if (accountModel.acoount.equals(mResponse.autopaymentDetail.account)) {
                model = accountModel
                break
            } else {
                model = mListAccountModel!![0]
            }
        }

        if (mResponse.autopaymentDetail.existingSourceAcc.alias == "") {
            binding.lyInc.tvAliasRek.text = GeneralHelper.getString(R.string.belum_ada_alias_text)
        } else {
            binding.lyInc.tvAliasRek.text = mResponse.autopaymentDetail.existingSourceAcc.alias
        }
        binding.lyInc.tvNoRek.text = mResponse.autopaymentDetail.existingSourceAcc.accountString
        binding.lyInc.tvSaldoRek.text =
            mResponse.autopaymentDetail.existingSourceAcc.aftBalanceString
        GeneralHelper.loadImageUrl(
            this,
            mResponse.autopaymentDetail.existingSourceAcc.imagePath,
            binding.lyInc.ivIconRek,
            R.drawable.bri,
            0
        )
        saldo = 0.0
        validationButton()
    }

    private fun validationButton() {
        if (selectedDate.isNotEmpty() && saldoNominal >= minNominal && saldoNominal < maxNominal && !model?.acoount.isNullOrEmpty()) {
            if (isDataTheSame()) {
                binding.btnSubmit.isEnabled = false
                binding.btnSubmit.setTextColor(GeneralHelper.getColor(R.color.neutral_light60))
            } else {
                binding.btnSubmit.isEnabled = true
                binding.btnSubmit.setTextColor(GeneralHelper.getColor(R.color.whiteColor))
            }
        } else {
            binding.btnSubmit.isEnabled = false
            binding.btnSubmit.setTextColor(GeneralHelper.getColor(R.color.neutral_light60))
        }
    }

    override fun onSelect(dateSelect: LocalDate) {
        selectedDate = dateSelect.dayOfMonth.toString()
        binding.etPilihTanggal.setText(getString(R.string.txt_setiap_tanggal) + dateSelect.dayOfMonth.toString())
        validationButton()

    }


    private fun selectDate() {
        calendarFragment = SetCalenderAFTFragment(this)
        val args = Bundle()
        args.putBoolean(Constant.TAG_DAY_OF_DATE, true)
        calendarFragment.arguments = args
        calendarFragment.isCancelable = true
        calendarFragment.show(supportFragmentManager, "")
    }

    override fun onSelectSumberDana(bankModel: AccountModel?) {
        model = bankModel

        if (model?.saldoReponse != null) {
            binding.lyInc.tvSaldoRek.text = GeneralHelper.formatNominalIDR(
                model?.currency, model!!.saldoReponse.balanceString

            )
            saldo = model!!.saldoReponse.balance
            validationButton()

        } else {
            binding.lyInc.tvSaldoRek.text = String.format("%s%s", model?.currency, "-")
            saldo = 0.0
        }

        binding.lyInc.tvNoRek.text = model?.acoountString
        if (model?.alias != null) {
            if (model?.alias.equals("")) {
                binding.lyInc.tvAliasRek.text =
                    GeneralHelper.getString(R.string.belum_ada_alias_text)
            } else {
                binding.lyInc.tvAliasRek.text = model?.alias
            }
        } else {
            binding.lyInc.tvAliasRek.text = GeneralHelper.getString(R.string.belum_ada_alias_text)
        }

        if (model?.imagePath != null) {
            if (!model?.imagePath.equals("", ignoreCase = true)) {
                GeneralHelper.loadImageUrl(
                    this, model?.imagePath, binding.lyInc.ivIconRek, R.drawable.bri, 0
                )
            } else {
                binding.lyInc.ivIconRek.setImageResource(R.drawable.bri)
            }
        } else {
            binding.lyInc.ivIconRek.setImageResource(R.drawable.bri)
        }

        if (!isFromFastMenu) {
            if (bankModel?.saldoReponse?.isOnHold == true) binding.lyInc.ivAlertSaldo.visibility =
                View.VISIBLE
            else binding.lyInc.ivAlertSaldo.visibility = View.GONE
        }


        validationButton()
    }

    private fun isDataTheSame(): Boolean {
        val autopaymentDetail = mResponse.autopaymentDetail
        val mSelectedDate = mResponse.autopaymentDetail.autopayment.date.toString()
        val saldoNominal = saldoNominal
        val modelAccount = model?.acoount

        if (autopaymentDetail == null) return true

        val nominalInfo = autopaymentDetail.nominalInfo.nominalFloat
        val account = autopaymentDetail.account

        return (selectedDate == mSelectedDate && saldoNominal == nominalInfo && modelAccount == account)
    }


    private fun setListenerEdittext() {
        binding.etNominal.filters = arrayOf<InputFilter>(InputFilter.LengthFilter(13))
        binding.etNominal.addTextChangedListener(object : TextWatcher {
            override fun afterTextChanged(s: Editable?) {
                if (binding.etNominal.text.toString() == "" || s.isNullOrEmpty()) {
                    binding.lyInputNominal.setBackgroundResource(R.drawable.background_cardview_state_stroked)
                    binding.etNominal.setText("0")
                }
                saldoNominal =
                    GeneralHelper.clearingAmountSigned("Rp" + binding.etNominal.text.toString())
                        .toDouble()
                try {
                    binding.etNominal.removeTextChangedListener(this)
                    var value: String = binding.etNominal.text.toString()
                    if (value == "") {
                        value = "0"
                    }
                    var str = value.replace(Constant.CURRENCY.toRegex(), "")
                        .replace(regex = "\\.".toRegex(), replacement = "")
                    str = getDecimalFormattedString(str)
                    value = str
                    binding.etNominal.setText(value)
                    Selection.setSelection(binding.etNominal.text, value.length)
                    binding.lyInputNominal.setBackgroundResource(R.drawable.background_cardview_state_stroked_primary80)
                    if (saldoNominal < minNominal) {
                        binding.txtMinimal.text =
                            mResponse.autopaymentDetail.minimalDeposit.depositString
                        binding.txtMinimal.setTextColor(GeneralHelper.getColor(R.color.error80))
                    } else if (saldoNominal > maxNominal) {
                        binding.txtMinimal.text =
                            mResponse.autopaymentDetail.maximalDeposit.depositString
                        binding.txtMinimal.setTextColor(GeneralHelper.getColor(R.color.error80))
                    } else {
                        binding.txtMinimal.text =
                            mResponse.autopaymentDetail.minimalDeposit.depositString
                        binding.txtMinimal.setTextColor(GeneralHelper.getColor(R.color.neutralLight80))
                    }
                    clearSelected()
                    binding.etNominal.addTextChangedListener(this)
                } catch (ex: Exception) {
                    binding.etNominal.addTextChangedListener(this)
                }
                validationButton()
            }

            override fun beforeTextChanged(s: CharSequence?, start: Int, count: Int, after: Int) {}

            override fun onTextChanged(s: CharSequence?, start: Int, before: Int, count: Int) {
                validationButton()
            }
        })

        ValidationHelper.disableCopyPaste(binding.etNominal)
    }

    private fun getDecimalFormattedString(value: String): String {
        return GeneralHelper.formatNominal(value)
    }


    override fun onSendFailedList(list: MutableList<Int>?) {
        mListFailed = list
    }

    override fun onSendPinComplete(pin: String?) {
        model?.acoount?.let { account ->
            pin?.let { pin ->
                presenter.editAutoPayment(
                    EditAutoPaymentRequest(
                        selectedDate,
                        saldoNominal,
                        mResponse.referenceNumber,
                        account,
                        pin,
                    )
                )
            }
        }
    }

    override fun onLupaPin() {
        LupaPinActivity.launchIntent(this)
    }
}