package id.co.bri.brimo.ui.customviews.dialog;

import android.app.Dialog;
import android.graphics.drawable.ColorDrawable;
import android.os.Bundle;
import android.view.Window;
import android.widget.Button;
import android.widget.ImageView;
import android.widget.TextView;

import id.co.bri.brimo.R;
import id.co.bri.brimo.domain.helpers.GeneralHelper;
import id.co.bri.brimo.models.apimodel.response.DataList;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.fragment.app.DialogFragment;

/**
 * Created by PSD Mobile on 15/09/2020
 */

public class DialogDetail extends DialogFragment {
    private Dialog alertDialog;
    private Button btnBeli;
    private ImageView ivClose;
    private TextView titleDetail;
    private TextView contentDetail;
    private TextView tvAmount;
    private DataList itemDataList;
    private DetailClickListener clickListener;


    public DialogDetail(DataList itemDataList, DetailClickListener clickListener) {
        this.itemDataList = itemDataList;
        this.clickListener = clickListener;
    }


    @NonNull
    @Override
    public Dialog onCreateDialog(@Nullable Bundle savedInstanceState) {
        alertDialog = new Dialog(getActivity());
        alertDialog.setCanceledOnTouchOutside(false);
        alertDialog.getWindow().requestFeature(Window.FEATURE_NO_TITLE);
        alertDialog.getWindow().setBackgroundDrawable(new ColorDrawable(0));
        alertDialog.setContentView(R.layout.dialog_detail_paket);
        alertDialog.setOnKeyListener((dialogInterface, i, keyEvent) -> false);
        alertDialog.show();

        initView();

        initContent();

        btnBeli.setOnClickListener(view -> {
            if (clickListener != null) {
                clickListener.onClickBuy(itemDataList);
            }
            dismiss();
        });

        ivClose.setOnClickListener(view -> dismiss());
        return alertDialog;
    }

    private void initContent() {
        if (itemDataList != null) {
            titleDetail.setText(itemDataList.getTitle());
            contentDetail.setText(itemDataList.getDetail());
            tvAmount.setText(String.format("Rp%s", GeneralHelper.formatNominal(itemDataList.getAmountString())));
        }
    }

    private void initView() {

        ivClose = alertDialog.findViewById(R.id.iv_close_tutorial);
        titleDetail = alertDialog.findViewById(R.id.tv_title_detail);
        contentDetail = alertDialog.findViewById(R.id.tv_content_detail);
        tvAmount = alertDialog.findViewById(R.id.tv_total_pembayaran_pulsa);
        btnBeli = alertDialog.findViewById(R.id.btnSubmitPulsa);
    }

    public interface DetailClickListener {
        void onClickBuy(DataList itemData);
    }
}
