package id.co.bri.brimo.ui.activities.dashboardInvestasi

import android.app.Activity
import android.content.Intent
import android.graphics.Color
import android.os.Build
import android.os.Bundle
import android.view.GestureDetector
import android.view.MotionEvent
import android.view.View
import android.view.WindowManager
import com.bumptech.glide.Glide
import com.teresaholfeld.stories.StoriesProgressView
import id.co.bri.brimo.databinding.ActivityStoryInvestasiBinding
import id.co.bri.brimo.domain.config.Constant
import id.co.bri.brimo.ui.activities.dplk.InfoDplkActivity
import id.co.bri.brimo.ui.activities.base.BaseActivity
import id.co.bri.brimo.ui.activities.bukarekening.TabunganActivity
import id.co.bri.brimo.ui.activities.deposito.DepositoActivity
import id.co.bri.brimo.ui.activities.dplkrevamp.DashboardDplkRevampActivity
import id.co.bri.brimo.ui.activities.emas.DashboardEmasActivity
import id.co.bri.brimo.ui.activities.portofolioksei.DashboardKseiActivity
import id.co.bri.brimo.ui.activities.sbn.DashboardESBNActivity
import id.co.bri.brimo.ui.activities.sbnrevamp.DashboardSbnRevampActivity

class StoryInvestasiActivity : BaseActivity(), StoriesProgressView.StoriesListener {

    private lateinit var binding : ActivityStoryInvestasiBinding

    private var storiesProgressView: StoriesProgressView? = null
    private var counter = 0
    private val durations = longArrayOf(500L, 1000L, 1500L, 4000L, 5000L, 1000)

    private var pressTime = 0L
    private var limit = 500L
    private lateinit var gestureDetector: GestureDetector


    companion object{
        private var mListContent : List<String> = mutableListOf()
        private var mColor : String? = null
        private var mMappingRoute : String? = null

        fun launchIntent(caller : Activity, contentResponse: List<String>, color :String, mappingRoute : String){
            val intent = Intent(caller, StoryInvestasiActivity::class.java)
            mListContent = contentResponse
            mColor = color
            mMappingRoute = mappingRoute
            caller.startActivityForResult(intent, Constant.REQ_PAYMENT)

        }
    }

    private val onTouchListener = View.OnTouchListener { v, event ->
        when (event.action) {
            MotionEvent.ACTION_DOWN -> {
                pressTime = System.currentTimeMillis()
                storiesProgressView?.pause()
                return@OnTouchListener false
            }
            MotionEvent.ACTION_UP -> {
                val now = System.currentTimeMillis()
                storiesProgressView?.resume()

                return@OnTouchListener limit < now - pressTime
            }
        }
        false
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        binding = ActivityStoryInvestasiBinding.inflate(layoutInflater)
        setContentView(binding.root)


        //set blue bar
        if (Build.VERSION.SDK_INT >= 21) {
            val window = window
            window.addFlags(WindowManager.LayoutParams.FLAG_DRAWS_SYSTEM_BAR_BACKGROUNDS)
            window.clearFlags(WindowManager.LayoutParams.FLAG_TRANSLUCENT_STATUS)
            window.statusBarColor = Color.parseColor(mColor)
        }
        binding.flStory.setBackgroundColor(Color.parseColor(mColor))
        storiesProgressView = binding.stories // Initialize storiesProgressView
        storiesProgressView?.setStoriesCount(mListContent.size)
        storiesProgressView?.setStoryDuration(3000L)

        storiesProgressView?.setStoriesListener(this)
        
        // Load images from URLs and add them to the stories list
        storiesProgressView?.startStories(counter)
        loadStory(mListContent[counter])

    }

    private fun onLastStoryClicked(){
        if (mMappingRoute == "deposito_dashboard"){
            val newIntent = Intent(this, DepositoActivity::class.java)
            this.startActivityForResult(newIntent, Constant.REQ_PAYMENT)
        }else if (mMappingRoute == "emas_dashboard" || mMappingRoute == "emas_open"){
            DashboardEmasActivity.launchIntent(this,false)
        }
        else if (mMappingRoute == "sbn_dashboard" || mMappingRoute == "sbn_open"){
            DashboardSbnRevampActivity.launchIntent(this, false,false)
        }
        else if (mMappingRoute == "dplk_dashboard"){
            DashboardDplkRevampActivity.launchIntent(this)
        }
        else if (mMappingRoute == "ksei_dashboard"){
            DashboardKseiActivity.launchIntent(this,false)
        }
        else if (mMappingRoute == "rencana_dashboard"){
            TabunganActivity.launchIntent(this)
        }
    }

    override fun onNext() {
        loadStory(mListContent[++counter])
    }

    override fun onPrev() {
        if (counter - 1 < 0) return
        loadStory(mListContent[--counter])
    }

    override fun onComplete() {
        finish()
    }

    override fun onDestroy() {
        // Very important !
        storiesProgressView?.destroy()
        super.onDestroy()
    }

    private fun loadStory(url: String) {
        Glide.with(this)
                .load(url)
                .into(binding.ivImage)
    }
}