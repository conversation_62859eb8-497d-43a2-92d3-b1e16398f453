package id.co.bri.brimo.ui.activities.portofolioksei

import android.app.Activity
import android.content.Intent
import android.os.Bundle
import android.util.Log
import android.view.View
import id.co.bri.brimo.R
import id.co.bri.brimo.databinding.ActivityRegistrasiKseiBinding
import id.co.bri.brimo.domain.config.Constant
import id.co.bri.brimo.domain.helpers.GeneralHelper
import id.co.bri.brimo.models.apimodel.response.portofolioksei.RegistrasiResponse
import id.co.bri.brimo.ui.activities.base.BaseActivity
import id.co.bri.brimo.ui.fragments.BottomFragmentKsei

class RegistrasiKseiActivity : BaseActivity(), View.OnClickListener {

    var binding: ActivityRegistrasiKseiBinding? = null
    var bottomFragmentKsei : BottomFragmentKsei? = null
    companion object{
        var mResponse : RegistrasiResponse? = null
        var mDrawer : Boolean? = null
        var mIdString : String? = null
        @JvmStatic
        fun launchIntent(caller : Activity, response: RegistrasiResponse, drawer : Boolean, idString : String){
            val intent = Intent(caller, RegistrasiKseiActivity::class.java)
            mResponse = response
            mDrawer = drawer
            mIdString = idString
            caller.startActivityForResult(intent, Constant.REQ_PAYMENT)
        }
    }
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        binding = ActivityRegistrasiKseiBinding.inflate(layoutInflater)
        setContentView(binding!!.root)
        setupView()
    }

    private fun setupView() {
        binding!!.toolbarText.text =GeneralHelper.getString(R.string.tb_ksei)
        binding!!.btnSubmit.setOnClickListener(this)
        binding!!.toolbarImage.setOnClickListener(this)
    }

    override fun onClick(p0: View?) {
        when(p0!!.id){
            R.id.btnSubmit -> {
                if (mDrawer!!){
                    bottomFragmentKsei = BottomFragmentKsei.newInstance(this, mResponse!!, mIdString!!)
                    bottomFragmentKsei!!.show(supportFragmentManager , "")
                }else{
                    SyaratRegisKseiActivity.launchIntent(this, mResponse!!.snk)
                }
            }

            R.id.toolbar_image ->{
                onBackPressed()
            }

        }


    }
    override fun onActivityResult(requestCode: Int, resultCode: Int, data: Intent?) {
        super.onActivityResult(requestCode, resultCode, data)
        if (requestCode == Constant.REQ_PAYMENT) {
            if (resultCode == RESULT_OK && data != null) {
                setResult(RESULT_OK, data)
            } else if (resultCode == RESULT_CANCELED && data != null && data.hasExtra(Constant.TAG_ERROR_MESSAGE)) {
                if (data.getStringExtra(Constant.TAG_ERROR_MESSAGE) != null) {
                    showSnackbarErrorMessageRevamp(data.getStringExtra(Constant.TAG_ERROR_MESSAGE), ALERT_ERROR,this,false)
                }
            }else{
                setResult(RESULT_CANCELED, data)
                finish()
            }
        }
    }


}