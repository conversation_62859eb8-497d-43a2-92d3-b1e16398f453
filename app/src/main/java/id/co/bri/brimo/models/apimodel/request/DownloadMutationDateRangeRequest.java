package id.co.bri.brimo.models.apimodel.request;

import com.google.gson.annotations.SerializedName;

public class DownloadMutationDateRangeRequest {
    @SerializedName("start_date")
    private String startDate;

    @SerializedName("end_date")
    private String endDate;

    @SerializedName("account_number")
    private String accountNumber;

    @SerializedName("file_type")
    private String type;

    @SerializedName("filter")
    private String filter;

    public DownloadMutationDateRangeRequest(String startDate, String endDate, String accountNumber, String type, String filter) {
        this.startDate = startDate;
        this.endDate = endDate;
        this.accountNumber = accountNumber;
        this.type = type;
        this.filter = filter;
    }

    public String getStartDate() {
        return startDate;
    }

    public void setStartDate(String startDate) {
        this.startDate = startDate;
    }

    public String getEndDate() {
        return endDate;
    }

    public void setEndDate(String endDate) {
        this.endDate = endDate;
    }

    public String getAccountNumber() {
        return accountNumber;
    }

    public void setAccountNumber(String accountNumber) {
        this.accountNumber = accountNumber;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public String getFilter() {
        return filter;
    }

    public void setFilter(String filter) {
        this.filter = filter;
    }
}
