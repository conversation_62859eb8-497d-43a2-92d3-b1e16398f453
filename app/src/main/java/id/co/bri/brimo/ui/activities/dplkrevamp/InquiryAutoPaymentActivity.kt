package id.co.bri.brimo.ui.activities.dplkrevamp

import android.annotation.SuppressLint
import android.app.Activity
import android.content.Intent
import android.os.Build
import android.os.Bundle
import android.text.Editable
import android.text.InputFilter
import android.text.Selection
import android.text.TextWatcher
import android.util.Log
import android.view.View
import android.view.WindowManager
import androidx.fragment.app.FragmentTransaction
import androidx.recyclerview.widget.LinearLayoutManager
import id.co.bri.brimo.R
import id.co.bri.brimo.adapters.RekomendasiAdapter
import id.co.bri.brimo.contract.IPresenter.dplkrevamp.IInquiryAutoPaymentPresenter
import id.co.bri.brimo.contract.IView.dplkrevamp.IInquiryAutoPaymentView
import id.co.bri.brimo.databinding.ActivityInquiryAutoPaymentBinding
import id.co.bri.brimo.domain.config.Constant
import id.co.bri.brimo.domain.helpers.GeneralHelper
import id.co.bri.brimo.domain.helpers.ValidationHelper
import id.co.bri.brimo.models.AccountModel
import id.co.bri.brimo.models.apimodel.request.dplkrevamp.SubmitAutoPaymentRequest
import id.co.bri.brimo.models.apimodel.response.bukarekening.RecomInitialDepositItem
import id.co.bri.brimo.models.apimodel.response.dplkrevamp.InquiryAutoPaymentResponse
import id.co.bri.brimo.models.apimodel.response.dplkrevamp.SubmitAutoPaymentResponse
import id.co.bri.brimo.ui.activities.base.BaseActivity
import id.co.bri.brimo.ui.customviews.dialog.DialogExitCustom
import id.co.bri.brimo.ui.fragments.PinFragment
import id.co.bri.brimo.ui.fragments.SetCalenderAFTFragment
import id.co.bri.brimo.ui.fragments.SumberDanaFragmentRevamp
import org.threeten.bp.LocalDate
import java.util.function.Consumer
import javax.inject.Inject

class InquiryAutoPaymentActivity : BaseActivity(),
    RekomendasiAdapter.OnItemClickListener,
    SetCalenderAFTFragment.OnSelectDate,
    DialogExitCustom.DialogClickYesNoListener,
    IInquiryAutoPaymentView,
    PinFragment.SendPin,
    SumberDanaFragmentRevamp.SelectSumberDanaInterface{

    lateinit var binding : ActivityInquiryAutoPaymentBinding

    lateinit var calendarFragment: SetCalenderAFTFragment

    private var saldoNominal : Double = 0.0
    private var adapter : RekomendasiAdapter? = null
    private var minNominal : Double = 0.0
    private var maxNominal : Double = 0.0
    private var model: AccountModel? = null
    private var saldo = 0.0
    private var isActive = false
    private var isRecom = false
    private var selectedDate = ""
    private var mSaldoString : String? = null
    private var mSaldo : Double = 0.0
    private var counter = 0
    private var mNominal : Double = 0.0
    private var mDefaultAkun : String? = null
    private var mListAccountModel: List<AccountModel>? = null
    private var mListFailed: List<Int>? = mutableListOf()


    @Inject
    lateinit var presenter: IInquiryAutoPaymentPresenter<IInquiryAutoPaymentView>


    companion object{
        private lateinit var mResponse : InquiryAutoPaymentResponse

        @JvmStatic
        fun launchIntent(caller: Activity, responseData : InquiryAutoPaymentResponse) {
            val intent = Intent(caller, InquiryAutoPaymentActivity::class.java)
            mResponse = responseData
            caller.startActivityForResult(intent, Constant.REQ_AUTO_PAYMENT)
        }
    }


    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        binding = ActivityInquiryAutoPaymentBinding.inflate(layoutInflater)
        setContentView(binding.root)
        injectDependency()
        setupView()
        initListener()
    }

    private fun injectDependency() {
        activityComponent.inject(this)
        presenter.view = this
        presenter.setUrlSubmitAutoPayment(getString(R.string.url_submit_auto_payment_dplk_revamp))
        presenter.start()
    }


    private fun initListener() {
        binding.etPilihTanggal.setOnClickListener { selectDate() }
        binding.btnSubmit.setOnClickListener {
            val dialogExitCustom = DialogExitCustom(
                this,
                mResponse.autopaymentPopup!!.title!!,
                mResponse.autopaymentPopup!!.description!!,
                GeneralHelper.getString(R.string.batal2),
                GeneralHelper.getString(R.string.ya_lanjutkan),true,true
            )
            val ft: FragmentTransaction =
                this.getSupportFragmentManager().beginTransaction()
            ft.add(dialogExitCustom, null)
            ft.commitAllowingStateLoss()
        }
        binding.lyInc.llSumberDana.setOnClickListener {
            counter++
            val fragmentSumberDanaNew = SumberDanaFragmentRevamp(mListAccountModel, counter, GeneralHelper.clearingAmountSigned("Rp"+binding.etNominal.text.toString()).toLong(), this,mListFailed, false,true)
            fragmentSumberDanaNew.show(supportFragmentManager, Constant.TAG_PICK_ACCOUNT)
        }
    }

    private fun setupView() {
        //set blue bar
        setStatusColor(R.color.toolbar_blue)
        GeneralHelper.setToolbarRevamp(this@InquiryAutoPaymentActivity,binding.tbInquiryAutoPayment.toolbar,getString(R.string.tb_autopayment))

        maxNominal = mResponse.autopaymentDetail!!.maximalDeposit!!.deposit!!
        minNominal = mResponse.autopaymentDetail!!.minimalDeposit!!.deposit!!
        mListAccountModel = mResponse.autopaymentDetail!!.accountList

        binding.txtMinimal.setText(mResponse.autopaymentDetail!!.minimalDeposit!!.depositString)
        binding.txtMinimal.setTextColor(GeneralHelper.getColor(R.color.neutralLight80))

        adapter = RekomendasiAdapter(this, mResponse.autopaymentDetail!!.denomList!!, this)
        binding.rvRecomendation.layoutManager = LinearLayoutManager(this, LinearLayoutManager.HORIZONTAL, false)
        binding.rvRecomendation.adapter = adapter
        clearSelected()

        binding.etNominal.setOnFocusChangeListener { view, b ->
            isActive = b
            setBackgorundLayout()
            if (b){
                isRecom = false
            }
        }
        setListenerEdittext()
    }

    @SuppressLint("NotifyDataSetChanged")
    override fun onItemClick(position: Int) {
        isRecom = true
        binding.etNominal.setText(String.format("%.0f", mResponse.autopaymentDetail!!.denomList!![position].recomFloat.toFloat()))
        saldoNominal = mResponse.autopaymentDetail!!.denomList!![position].recomFloat.toDouble()

        clearSelected()
        mResponse.autopaymentDetail!!.denomList!![position].isSelected = true
        adapter!!.notifyDataSetChanged()
        validationButton()
    }

    @SuppressLint("NotifyDataSetChanged")
    private fun clearSelected() {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.N) {
            mResponse.autopaymentDetail!!.denomList!!.forEach(Consumer { p: RecomInitialDepositItem ->
                p.isSelected = false
            })
        } else {
            for (p in mResponse.autopaymentDetail!!.denomList!!) {
                p.isSelected = false
            }
        }
        adapter!!.notifyDataSetChanged()
    }

    fun setListenerEdittext(){
        binding.txtMinimal.setTextColor(GeneralHelper.getColor(R.color.neutralLight80))
        binding.etNominal.filters = arrayOf<InputFilter>(InputFilter.LengthFilter(13))
        binding.etNominal.addTextChangedListener(object: TextWatcher {
            override fun afterTextChanged(s: Editable?) {

            }

            override fun beforeTextChanged(s: CharSequence?, start: Int, count: Int, after: Int) { }

            override fun onTextChanged(s: CharSequence?, start: Int, before: Int, count: Int) {
                clearSelected()
                if (binding.etNominal.text.toString() == ""){
                    binding.lyInputNominal.setBackgroundResource(R.drawable.background_cardview_state_stroked)
                    binding.etNominal.setText("0")
                }
                saldoNominal = GeneralHelper.clearingAmountSigned("Rp"+binding.etNominal.text.toString()).toDouble()
                try {
                    binding.etNominal.removeTextChangedListener(this)
                    var value: String = binding.etNominal.text.toString()
                    if (value == ""){
                        value = "0"
                    }
                    var str = value.replace(Constant.CURRENCY.toRegex(), "")
                        .replace(regex = "\\.".toRegex(), replacement = "")
                    str = getDecimalFormattedString(str)
                    value = str
                    binding.etNominal.setText(value)
                    Selection.setSelection(binding.etNominal.text, value.length)
                    binding.lyInputNominal.setBackgroundResource(R.drawable.background_cardview_state_stroked_primary80_radius10)

                    binding.etNominal.addTextChangedListener(this)
                } catch (ex: Exception) {
                    binding.etNominal.addTextChangedListener(this)
                }
                validationButton()
            }
        })


        ValidationHelper.disableCopyPaste(binding.etNominal)
        ValidationHelper.disableCopyPaste(binding.etNominal)
    }

    private fun getDecimalFormattedString(value: String): String {
        return GeneralHelper.formatNominal(value)
    }

    private fun validationButton() {
        validateNominal()
        if (
            selectedDate.isNotEmpty()
            && saldoNominal >= minNominal
            && saldoNominal <= maxNominal
            && !model!!.acoount.isNullOrEmpty()) {
                binding.btnSubmit.isEnabled = true
                binding.btnSubmit.setTextColor(GeneralHelper.getColor(R.color.whiteColor))
            } else {
                binding.btnSubmit.isEnabled = false
                binding.btnSubmit.setTextColor(GeneralHelper.getColor(R.color.neutral_light60))
            }
    }

    private fun validateNominal(){
        if (saldoNominal < minNominal){
            binding.txtMinimal.text = mResponse.autopaymentDetail?.minimalDeposit?.depositString
            binding.txtMinimal.setTextColor(GeneralHelper.getColor(R.color.error80))
        }else if (saldoNominal > maxNominal){
            binding.txtMinimal.text = mResponse.autopaymentDetail?.maximalDeposit?.depositString
            binding.txtMinimal.setTextColor(GeneralHelper.getColor(R.color.error80))
        }else{
            binding.txtMinimal.text = mResponse.autopaymentDetail?.minimalDeposit?.depositString
            binding.txtMinimal.setTextColor(GeneralHelper.getColor(R.color.neutralLight80))
        }
    }

    override fun onSelect(dateSelect: LocalDate) {
        selectedDate = dateSelect.dayOfMonth.toString()
        binding.etPilihTanggal.setText(getString(R.string.txt_setiap_tanggal) + " " +dateSelect.dayOfMonth.toString())
        validationButton()
    }

    private fun selectDate(){
        calendarFragment = SetCalenderAFTFragment(this)
        val args = Bundle()
        args.putBoolean(Constant.TAG_DAY_OF_DATE, true)
        calendarFragment.arguments = args
        calendarFragment.isCancelable = true
        calendarFragment.show(supportFragmentManager,"")
    }

    override fun onClickBtnYes() {
        val pinFragment = PinFragment(this, this)
        pinFragment.show()
    }

    override fun onClickBtnNo() {
        // do nothing
    }


    override fun onSuccessInquirySubmitAutoPayment(response: SubmitAutoPaymentResponse) {
        val data = Intent()
        data.putExtra(Constant.TAG_MESSAGE, response.message)
        setResult(RESULT_OK,data)
        finish()
        //
    }

    override fun onExceptionTrxExpired(desc: String) {
        val data = Intent()
        data.putExtra(Constant.TAG_ERROR_MESSAGE, desc)
        setResult(RESULT_CANCELED,data)
        finish()
    }

    override fun setDefaultSaldo(
        saldo: Double,
        saldoString: String?,
        account: String?,
        saldoHold: Boolean?
    ) {
        mSaldoString = saldoString!!
        mSaldo = saldo
        mDefaultAkun = account!!
        setupAccount(mSaldo)
    }

    fun setupAccount(saldoDoubled : Double) {
        //List Account
        if (mResponse.autopaymentDetail!!.accountList!!.isNotEmpty()) {
            mListAccountModel = mResponse.autopaymentDetail!!.accountList
        }

        //get account default
        for (accountModel in mListAccountModel!!) {
            if (accountModel.isDefault == 1) {
                model = accountModel
                break
            } else {
                model = mListAccountModel!![0]

            }
        }

        if (model!!.imagePath != null) {
            if (!model!!.imagePath.equals("", ignoreCase = true)) {
                GeneralHelper.loadImageUrl(
                    this,
                    model!!.imagePath,
                    binding.lyInc.ivIconRek,
                    R.drawable.bri,
                    0
                )
            } else {
                binding.lyInc.ivIconRek.setImageResource(R.drawable.bri)
            }
        } else {
            binding.lyInc.ivIconRek.setImageResource(R.drawable.bri)
        }

        if (model!!.acoountString != null) {
            binding.lyInc.tvNoRek.text = model!!.acoountString
        }
        if (model!!.alias != null && !model!!.alias.equals("")) {
            binding.lyInc.tvAliasRek.text = model!!.alias
        }else{
            binding.lyInc.tvAliasRek.text = GeneralHelper.getString(R.string.belum_ada_alias_text)
        }

        if (model!!.acoount != null) {
            val saldoText: String = saldoDoubled.toString()
            if (saldoText != "") {
                saldo = saldoDoubled
                binding.lyInc.tvSaldoRek.text = "Rp."+GeneralHelper.formatNominal(saldoDoubled)
            }
            if (model!!.acoount == mDefaultAkun) {
                binding.lyInc.tvSaldoRek.text = GeneralHelper.formatNominalIDR(model!!.currency, saldo)
            }else {
                binding.lyInc.tvSaldoRek.text = "-"
                binding.lyInc.tvAliasRek.text = "- "
                saldo = 0.0
                model!!.acoount = null
            }
        }


        validationButton()
    }



    override fun onSendPinComplete(pin: String?) {
        presenter.submitAutoPayment(
            SubmitAutoPaymentRequest(
                selectedDate,
                pin!!,
                saldoNominal,
                mResponse.refNum,
                model!!.acoount
            )
        )
    }

    override fun onLupaPin() {
//        do nothing
    }

    private fun setBackgorundLayout(){
        if (isActive){
            binding.lyInputNominal.setBackgroundResource(R.drawable.background_cardview_state_stroked_primary80_radius10)
        }
    }
    override fun onSelectSumberDana(bankModel: AccountModel?) {
        model = bankModel

        if (model?.saldoReponse != null) {
            binding.lyInc.tvSaldoRek.text = GeneralHelper.formatNominalIDR(
                model?.currency,
                model!!.saldoReponse.balanceString

            )
            saldo = model!!.saldoReponse.balance
            validationButton()

        } else {
            binding.lyInc.tvSaldoRek.text = String.format("%s%s", model?.currency, "-")
            saldo = 0.0
        }

        binding.lyInc.tvNoRek.text = model?.acoountString
        if (model?.alias != null){
            if (model?.alias.equals("")){
                binding.lyInc.tvAliasRek.text = GeneralHelper.getString(R.string.belum_ada_alias_text)
            }else{
                binding.lyInc.tvAliasRek.text = model?.alias
            }
        }else{
            binding.lyInc.tvAliasRek.text = GeneralHelper.getString(R.string.belum_ada_alias_text)
        }

        if (model?.imagePath != null) {
            if (!model?.imagePath.equals("", ignoreCase = true)) {
                GeneralHelper.loadImageUrl(
                    this,
                    model?.imagePath,
                    binding.lyInc.ivIconRek,
                    R.drawable.bri,
                    0
                )
            } else {
                binding.lyInc.ivIconRek.setImageResource(R.drawable.bri)
            }
        } else {
            binding.lyInc.ivIconRek.setImageResource(R.drawable.bri)
        }

        if (!isFromFastMenu) {
            if (bankModel?.saldoReponse?.isOnHold == true) binding.lyInc.ivAlertSaldo.visibility = View.VISIBLE
            else binding.lyInc.ivAlertSaldo.visibility = View.GONE
        }
    }

    override fun onSendFailedList(list: MutableList<Int>?) {
        mListFailed = list
    }

    override fun onActivityResult(requestCode: Int, resultCode: Int, data: Intent?) {
        super.onActivityResult(requestCode, resultCode, data)

        if (requestCode == Constant.REQ_PAYMENT) {
            if (resultCode == RESULT_OK) {
                this.setResult(RESULT_OK, data)
                finish()
            } else {
                this.setResult(RESULT_CANCELED, data)
                finish()
            }
        } else if (requestCode == Constant.REQ_FORGET_PIN) {
            if (resultCode == RESULT_OK) {
                this.setResult(RESULT_OK)
                finish()
            }
        }
    }


}