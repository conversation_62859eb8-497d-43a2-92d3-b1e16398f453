package id.co.bri.brimo.ui.activities.virtualdebitcard

import android.graphics.Rect
import android.os.Bundle
import android.text.Editable
import android.text.InputFilter
import android.text.InputType
import android.text.TextWatcher
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.EditText
import com.google.android.material.bottomsheet.BottomSheetDialogFragment
import id.co.bri.brimo.R
import id.co.bri.brimo.databinding.FragmentEditLabelVdcBinding
import id.co.bri.brimo.domain.helpers.ValidationHelper
import java.util.regex.Pattern

class EditLabelVDCFragment(
    private val label: String,
    private var title: String
) : BottomSheetDialogFragment() {

    private var _binding: FragmentEditLabelVdcBinding? = null
    private val binding: FragmentEditLabelVdcBinding
        get() = _binding!!
    private var labelCardName = ""
    private val minLengthLabelName = 3
    private var isKeyboardVisible = false

    private var editLabelListener: (String) -> Unit = { _ -> }

    fun setEditLabelListener(listener: (String) -> Unit) {
        editLabelListener = listener
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setStyle(STYLE_NORMAL, R.style.BottomSheetDialogStyle)
    }

    override fun onCreateView(
        inflater: LayoutInflater, container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View? {
        super.onCreateView(inflater, container, savedInstanceState)
        _binding = FragmentEditLabelVdcBinding.inflate(inflater, container, false)
        return binding.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)

        binding.tvTitle.text = title
        binding.tvLabel.text = label

        binding.ivClear.setOnClickListener {
            binding.etCardName.text.clear()
        }

        view.viewTreeObserver.addOnGlobalLayoutListener {
            val r = Rect()
            view.getWindowVisibleDisplayFrame(r)
            val screenHeight = view.rootView.height
            val keypadHeight = screenHeight - r.bottom

            if (keypadHeight > screenHeight * 0.15) { // arbitrary threshold
                isKeyboardVisible = true
                binding.llNewLabel.setBackgroundResource(R.drawable.bg_border_primary_blue_80)
                binding.tvCounter.setTextColor(resources.getColor(R.color.primaryBlue80))
                binding.tvMax.setTextColor(resources.getColor(R.color.primaryBlue80))
            } else {
                isKeyboardVisible = false
                binding.llNewLabel.setBackgroundResource(R.drawable.bg_grey_border)
                binding.tvCounter.setTextColor(resources.getColor(R.color.neutralLight30))
                binding.tvMax.setTextColor(resources.getColor(R.color.neutralLight30))
            }
        }

        binding.etCardName.addTextChangedListener(object : TextWatcher {
            override fun beforeTextChanged(charSequence: CharSequence, i: Int, i1: Int, i2: Int) {}
            override fun onTextChanged(charSequence: CharSequence, i: Int, i1: Int, i2: Int) {
                binding.etCardName.setTextColor(resources.getColor(R.color.neutralDark40))
                if (binding.etCardName.text.isNullOrEmpty()) disableButton() else enableButton()
            }

            override fun afterTextChanged(editable: Editable) {
                val currentLength = editable.length
                val isLengthValid = editable.length >= minLengthLabelName
                if (isLengthValid) {
                    enableButton()
                } else {
                    disableButton()
                }
                binding.tvCounter.text = currentLength.toString()
                labelCardName = binding.etCardName.text.toString()
            }
        })

        binding.etCardName.inputType = InputType.TYPE_CLASS_TEXT
        ValidationHelper.setAlphanumericInput(binding.etCardName)

        binding.btnSubmit.setOnClickListener {
            updateLabel()
        }
    }

    override fun onDestroyView() {
        _binding = null
        super.onDestroyView()
    }

    fun enableButton() {
        binding.btnSubmit.apply {
            isEnabled = true
            alpha = 1f
        }
    }

    fun disableButton() {
        binding.btnSubmit.apply {
            isEnabled = false
            alpha = 0.3f
        }
    }

    private fun updateLabel() {
        editLabelListener(labelCardName)
        dismiss()
    }
}