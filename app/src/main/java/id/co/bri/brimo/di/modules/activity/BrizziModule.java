package id.co.bri.brimo.di.modules.activity;

import dagger.Module;
//import id.co.bri.sdk.Brizzi;

@Module
public class BrizziModule {






/*
    @Provides
    @PerForm
    public Brizzi provideBrizzi(Context sontek, BrizziHelper helper) {
        Brizzi brizzi = Brizzi.getInstance();
        brizzi.Init(helper.getBrizziToken(),helper.getBrizziSecret());
        brizzi.setNfcAdapter(sontek);
        return brizzi;
    }

 */

}
