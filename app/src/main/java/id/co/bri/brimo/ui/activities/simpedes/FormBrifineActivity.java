package id.co.bri.brimo.ui.activities.simpedes;

import android.app.Activity;
import android.content.Intent;
import android.os.Bundle;
import android.os.SystemClock;
import android.view.View;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;

import com.google.gson.Gson;

import org.threeten.bp.LocalDate;

import java.util.ArrayList;
import java.util.List;

import javax.inject.Inject;

import id.co.bri.brimo.R;
import id.co.bri.brimo.contract.IPresenter.simpedes.IFormBrifinePresenter;
import id.co.bri.brimo.contract.IView.simpedes.IFormBrifineView;
import id.co.bri.brimo.databinding.ActivityFormBrifineBinding;
import id.co.bri.brimo.domain.config.Constant;
import id.co.bri.brimo.domain.helpers.GeneralHelper;
import id.co.bri.brimo.domain.helpers.calendar.CalendarHelper;
import id.co.bri.brimo.domain.helpers.textwatcher.AmountFormatWatcher;
import id.co.bri.brimo.models.DurasiModel;
import id.co.bri.brimo.models.apimodel.response.BrifineFormRes;
import id.co.bri.brimo.models.apimodel.response.InquirySimpedesBrifineResponse;
import id.co.bri.brimo.models.optionmodel.OptionGeneralModel;
import id.co.bri.brimo.ui.activities.base.BaseActivity;
import id.co.bri.brimo.ui.fragments.PilihGeneralNoLogoFragment;
import id.co.bri.brimo.ui.fragments.PilihanJenisBrifineFragment;
import id.co.bri.brimo.ui.fragments.SetCalendarFragment;

public class FormBrifineActivity extends BaseActivity implements
        IFormBrifineView,
        View.OnClickListener,
        SetCalendarFragment.OnSelectDate,
        AmountFormatWatcher.onAmountChange, PilihanJenisBrifineFragment.OnClickItem {

    private ActivityFormBrifineBinding binding;

    @Inject
    IFormBrifinePresenter<IFormBrifineView> presenter;

    protected static final String TAG_RESPONSE = "response";

    protected DurasiModel durasiModel = null;

    protected BrifineFormRes brifineFormRes;

    protected PilihGeneralNoLogoFragment penghasilanFragment = null;
    protected PilihGeneralNoLogoFragment pekerjaanFragment = null;
    protected PilihanJenisBrifineFragment jenisFragment = null;

    protected String sAccount;
    protected String sProduct;
    protected String sIncomeCode;
    protected String sJobCode;
    protected String sSubscriptionCode;
    protected String sSubscriptionAmount = "";
    protected String sFirstDate;

    protected Long lAmount = null;


    public static void launchIntent(Activity caller, String response) {
        Intent intent = new Intent(caller, FormBrifineActivity.class);
        intent.putExtra(TAG_RESPONSE, response);
        caller.startActivityForResult(intent, Constant.REQ_PAYMENT);
    }

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        binding = ActivityFormBrifineBinding.inflate(getLayoutInflater());
        setContentView(binding.getRoot());

        if (getIntent().getExtras().getString(TAG_RESPONSE) != null)
            brifineFormRes = new Gson().fromJson(getIntent().getExtras().getString(TAG_RESPONSE), BrifineFormRes.class);

        GeneralHelper.setToolbar(this, binding.toolbar.toolbar, GeneralHelper.getString(R.string.toolbar_brifine));

        injectDependency();
        setupView();
    }

    private void injectDependency() {
        getActivityComponent().inject(this);
        if (presenter != null) {
            presenter.setView(this);
            presenter.start();
            presenter.setUrlInquiry(GeneralHelper.getString(R.string.url_s3f_open_dplk_inquiry));
        }
    }

    private void setupView() {
        if (brifineFormRes != null) {
            sAccount = brifineFormRes.getAccountNumber();
            binding.tvNominalIuran.setText(String.format(GeneralHelper.getString(R.string.minimal_iuran), brifineFormRes.getMinimumIuranString()));

            penghasilanFragment = new PilihGeneralNoLogoFragment(fetchOptionListPenghasilan(brifineFormRes.getIncomeList()), (position, optionModel) -> {
                sIncomeCode = brifineFormRes.getIncomeList().get(position).getCode();
                binding.etPenghasilan.setText(brifineFormRes.getIncomeList().get(position).getValue());
                validationText();
            });

            pekerjaanFragment = new PilihGeneralNoLogoFragment(fetchOptionListPekerjaan(brifineFormRes.getJobList()), (position, optionModel) -> {
                sJobCode = brifineFormRes.getJobList().get(position).getCode();
                binding.etPekerjaan.setText(brifineFormRes.getJobList().get(position).getValue());
                validationText();
            });

            jenisFragment = new PilihanJenisBrifineFragment(this, brifineFormRes.getProductList());

            binding.etNominalIuran.addTextChangedListener(new AmountFormatWatcher(binding.etNominalIuran, this, false));
            binding.etTanggal.setOnClickListener(this);
            binding.etPenghasilan.setOnClickListener(this);
            binding.etPekerjaan.setOnClickListener(this);
            binding.btTambah.setOnClickListener(this);
            binding.etJenisBrifine.setOnClickListener(this);
        }
    }

    private List<OptionGeneralModel> fetchOptionListPekerjaan(List<BrifineFormRes.CodeValue> pekerjaaanList) {
        List<OptionGeneralModel> list = new ArrayList<>();

        if (pekerjaaanList != null) {
            for (int i = 0; i < pekerjaaanList.size(); i++) {
                list.add(new OptionGeneralModel(0, pekerjaaanList.get(i).getValue(), "", ""));
            }
        }
        return list;
    }

    private List<OptionGeneralModel> fetchOptionListPenghasilan(List<BrifineFormRes.CodeValue> penghasilanList) {
        List<OptionGeneralModel> list = new ArrayList<>();

        if (penghasilanList != null) {
            for (int i = 0; i < penghasilanList.size(); i++) {
                list.add(new OptionGeneralModel(0, penghasilanList.get(i).getValue(), "", ""));
            }
        }
        return list;
    }

    private void validationText() {
        if (binding.etPenghasilan.length() != 0) {
            if (lAmount != null) {
                if (lAmount < brifineFormRes.getMinimumIuran()) {
                    validationButton(false);
                } else {
                    validationButton(binding.etNominalIuran.length() != 0 && binding.etTanggal.length() != 0 && sProduct != null);
                }
            }
        } else
            validationButton(false);
    }

    private void validationButton(boolean isEnable) {

        if (isEnable) {
            binding.btTambah.setAlpha(1);
            binding.btTambah.setEnabled(true);
        } else {
            binding.btTambah.setAlpha(0.3f);
            binding.btTambah.setEnabled(false);
        }
    }

    @Override
    public void onClick(View v) {
        if (SystemClock.elapsedRealtime() - mLastClickTime < 1000) {
            return;
        }
        mLastClickTime = SystemClock.elapsedRealtime();
        switch (v.getId()) {
            case R.id.et_tanggal:
                SetCalendarFragment calendarFragment;
                calendarFragment = new SetCalendarFragment(this::onSelect);
                Bundle args = new Bundle();
                args.putBoolean(Constant.TAG_DEBET_DATE, true);
                calendarFragment.setArguments(args);

                calendarFragment.setCancelable(true);
                calendarFragment.show(getSupportFragmentManager(), "");
                break;
            case R.id.et_penghasilan:
                if (penghasilanFragment != null) {
                    penghasilanFragment.show(getSupportFragmentManager(), "");
                    penghasilanFragment.setCancelable(true);
                }
                break;
            case R.id.et_pekerjaan:
                if (pekerjaanFragment != null) {
                    pekerjaanFragment.show(getSupportFragmentManager(), "");
                    pekerjaanFragment.setCancelable(true);
                }
                break;
            case R.id.bt_tambah:
                presenter.getInquiryData(sAccount, sProduct, sIncomeCode, sJobCode, sSubscriptionAmount, sFirstDate, true);
                break;
            case R.id.et_jenis_brifine:
                if (jenisFragment != null) {
                    jenisFragment.show(getSupportFragmentManager(), "");
                    jenisFragment.setCancelable(true);
                }
                break;
            default:
                break;
        }
    }

    public void setDurasi(DurasiModel durasi) {
        this.durasiModel = durasi;
        String tanggalString = durasiModel.getStartDatePfmStringddMMyyyy().substring(0, 3) +
                CalendarHelper.convertMont(durasiModel.getStartDatePfmStringddMMyyyy().substring(3, 5)) +
                durasiModel.getStartDatePfmStringddMMyyyy().substring(5, 10);

        binding.etTanggal.setText(tanggalString);
        binding.tvAutoDebetSelanjutnya.setText(getResources().getString(R.string.auto_debet_selanjutnya, tanggalString.substring(0, 3)));
        binding.tvAutoDebetSelanjutnya.setVisibility(View.VISIBLE);

        sFirstDate = durasiModel.getStartDateString();
        validationText();
    }

    @Override
    public void onSelect(@NonNull LocalDate dateSelect) {
        durasiModel = new DurasiModel(
                dateSelect.getDayOfMonth(),
                dateSelect.getMonthValue(),
                dateSelect.getYear());
        setDurasi(durasiModel);
    }

    @Override
    public void onAmountChange(String amount) {
        // Do nothing
        if (amount.length() != 0) {
            binding.tvCurrency.setVisibility(View.VISIBLE);
            lAmount = Long.parseLong(amount.replace(".", "")
                    .replace("Rp", "").replace("-", ""));
            sSubscriptionAmount = String.valueOf(lAmount);

            if (lAmount < brifineFormRes.getMinimumIuran())
                binding.tvNominalIuran.setVisibility(View.VISIBLE);
            else
                binding.tvNominalIuran.setVisibility(View.GONE);
        } else {
            binding.tvCurrency.setVisibility(View.GONE);
            sSubscriptionAmount = "";
            binding.tvNominalIuran.setVisibility(View.GONE);
        }

        validationText();
    }

    @Override
    public void setAmountListener() {
        // Do nothing
    }

    @Override
    public void getInquirySuccess(InquirySimpedesBrifineResponse generalInquiryResponse) {
        InquiryBrifineActivity.launchIntent(this, new Gson().toJson(generalInquiryResponse));
    }

    @Override
    protected void onActivityResult(int requestCode, int resultCode, @Nullable Intent data) {
        super.onActivityResult(requestCode, resultCode, data);

        if (requestCode == Constant.REQ_PAYMENT) {
            if (resultCode == RESULT_OK) {
                this.setResult(RESULT_OK, data);
                this.finish();
            } else {
                this.setResult(RESULT_CANCELED, data);
                this.finish();
            }
        }
    }

    @Override
    public void setItemClick(BrifineFormRes.Product option) {
        binding.etJenisBrifine.setText(option.getTitle());
        sProduct = option.getCode();
        validationText();
    }

    @Override
    protected void onDestroy() {
        super.onDestroy();
        binding = null;
    }
}