package id.co.bri.brimo.models.apimodel.request;

import com.google.gson.annotations.Expose;
import com.google.gson.annotations.SerializedName;

public class PaymentAmkkmRequest {
    @SerializedName("reference_number")
    @Expose
    private String refNum;
    @SerializedName("pin")
    @Expose
    private String pin;

    public PaymentAmkkmRequest(String refNum, String pin) {
        this.refNum = refNum;
        this.pin = pin;
    }

    public String getRefNum() {
        return refNum;
    }

    public void setRefNum(String refNum) {
        this.refNum = refNum;
    }

    public String getPin() {
        return pin;
    }

    public void setPin(String pin) {
        this.pin = pin;
    }
}
