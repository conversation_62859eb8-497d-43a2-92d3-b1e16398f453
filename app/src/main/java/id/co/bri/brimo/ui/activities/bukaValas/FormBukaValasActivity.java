package id.co.bri.brimo.ui.activities.bukaValas;

import android.app.Activity;
import android.content.Intent;
import android.os.Bundle;

import androidx.annotation.Nullable;
import androidx.fragment.app.FragmentTransaction;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.ethanhua.skeleton.Skeleton;
import com.ethanhua.skeleton.SkeletonScreen;

import java.util.ArrayList;
import java.util.List;

import javax.inject.Inject;

import id.co.bri.brimo.R;
import id.co.bri.brimo.adapters.BukaValasAdapter;
import id.co.bri.brimo.contract.IPresenter.bukaValas.IPilihRekeningBukaValasPresenter;
import id.co.bri.brimo.contract.IView.bukaValas.IPilihRekeningValasView;
import id.co.bri.brimo.databinding.ActivityPilihRekeningValasBinding;
import id.co.bri.brimo.domain.config.Constant;
import id.co.bri.brimo.domain.helpers.GeneralHelper;
import id.co.bri.brimo.models.VallasModel;
import id.co.bri.brimo.models.apimodel.response.onExceptionWH;
import id.co.bri.brimo.models.apimodel.response.KursList;
import id.co.bri.brimo.models.apimodel.response.ListKantorResponse;
import id.co.bri.brimo.models.apimodel.response.MessageResponse;
import id.co.bri.brimo.models.apimodel.response.PilihRekeningValasResponse;
import id.co.bri.brimo.ui.activities.DashboardIBActivity;
import id.co.bri.brimo.ui.activities.base.BaseActivity;
import id.co.bri.brimo.ui.customviews.dialog.DialogValas;

public class FormBukaValasActivity extends BaseActivity implements BukaValasAdapter.OnClickListener,DialogValas.DialogDefaultButtonListener,IPilihRekeningValasView {

    private ActivityPilihRekeningValasBinding binding;

    private BukaValasAdapter adapter;
    private List<String> list;
    private List<VallasModel> vallasModelList = new ArrayList<>();
    private List<KursList> kursLists = new ArrayList<>();
    SkeletonScreen skeletonScreen;
    private KursList mKursList;
    private PilihRekeningValasResponse mPilihRekeningValas;
    protected static  ListKantorResponse.Office  officeList1;

    protected static String errorMessage = null;
    private MessageResponse messageResponse;

    @Inject
    IPilihRekeningBukaValasPresenter<IPilihRekeningValasView> presenter;

    public static void launchIntent(Activity caller, ListKantorResponse.Office office) {
        Intent intent = new Intent(caller, FormBukaValasActivity.class);
        officeList1 = office;
        caller.startActivityForResult(intent, Constant.REQ_PAYMENT);
    }

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        binding = ActivityPilihRekeningValasBinding.inflate(getLayoutInflater());
        setContentView(binding.getRoot());

        injectDependency();

        if (binding.toolbarPilihValas.toolbar != null) {
            GeneralHelper.setToolbar(this, binding.toolbarPilihValas.toolbar, "Britama Valas");
        }
        skeletonView();
    }

    private void injectDependency() {
        getActivityComponent().inject(this);

        if (presenter != null) {
            presenter.start();
            presenter.setView(this);
            presenter.setFormUrl(GeneralHelper.getString(R.string.url_form_buka_valas));
            presenter.setKonfirmasiUrl(GeneralHelper.getString(R.string.url_konfirmasi_buka_valas));
            presenter.setPaymentUrl(GeneralHelper.getString(R.string.url_cek_status_buka_valas));
            presenter.getData();
        }
    }

    public void skeletonView(){
        binding.rvListVallas.setLayoutManager(new LinearLayoutManager(this, RecyclerView.VERTICAL, false));
        adapter = new BukaValasAdapter(this,kursLists, this,0);
        skeletonScreen = Skeleton.bind(binding.rvListVallas)
                .adapter(adapter)
                .shimmer(true)
                .angle(20)
                .frozen(false)
                .duration(1200)
                .count(10)
                .load(R.layout.item_skeleton_daftar_transfer)
                .show();
    }


    @Override
    public void onSuccesGetData(PilihRekeningValasResponse pilihRekeningValasResponse) {
        kursLists = pilihRekeningValasResponse.getKursList();
        mPilihRekeningValas = pilihRekeningValasResponse;
        LinearLayoutManager linearLayoutManager = new LinearLayoutManager(this);
        binding.rvListVallas.setLayoutManager(linearLayoutManager);
        binding.rvListVallas.setHasFixedSize(true);
        adapter = new BukaValasAdapter(this,kursLists,this,0);
        adapter.notifyDataSetChanged();
        binding.rvListVallas.setAdapter(adapter);
    }

    @Override
    public void onSuccesGetInquiry(PilihRekeningValasResponse pilihRekeningValasResponse) {
        kursLists = pilihRekeningValasResponse.getKursList();
    }

    @Override
    public void onClickItem(int position, KursList vallasModel) {
        mKursList = vallasModel;
        InquiryBukaValasActivity.launchIntent(this,mPilihRekeningValas,mKursList,GeneralHelper.getString(R.string.url_konfirmasi_buka_valas),GeneralHelper.getString(R.string.url_payment_buka_valas),officeList1);
    }

    @Override
    public void onException60(onExceptionWH onExceptionWH) {
        messageResponse = onExceptionWH.getMessageResponse();
        DialogValas dialogValas = new DialogValas(this,messageResponse.getTitle(),messageResponse.getDesc(), onExceptionWH.getOffice_hour(),false);
        FragmentTransaction ft = this.getSupportFragmentManager().beginTransaction();
        ft.add(dialogValas, null);
        ft.commitAllowingStateLoss();
    }

    @Override
    public void onException03(onExceptionWH exceptionCase03) {
        messageResponse = exceptionCase03.getMessageResponse();
        DialogValas dialogValas = new DialogValas(()->
                DashboardIBActivity.launchIntent(this),messageResponse.getTitle(),messageResponse.getDesc(),"",true);
        FragmentTransaction ft = this.getSupportFragmentManager().beginTransaction();
        ft.add(dialogValas, null);
        ft.commitAllowingStateLoss();
    }

    @Override
    public void onException12Form(String message) {
        Intent returnIntent = new Intent();
        returnIntent.putExtra(Constant.TAG_ERROR_MESSAGE, message);
        this.setResult(RESULT_CANCELED, returnIntent);
        this.finish();
    }

    @Override
    public void onException93(String message) {
        Intent returnIntent = new Intent();

        returnIntent.putExtra(Constant.TAG_ERROR_MESSAGE, message);

        this.setResult(RESULT_CANCELED, returnIntent);
        this.finish();
    }

    @Override
    protected void onActivityResult(int requestCode, int resultCode, @Nullable Intent data) {
        super.onActivityResult(requestCode, resultCode, data);
        if (requestCode == Constant.REQ_PAYMENT) {
            if (resultCode == RESULT_OK) {
                this.setResult(RESULT_OK);
                this.finish();
            } else {
                this.setResult(RESULT_CANCELED, data);
                this.finish();
                if (data != null) {
                    if (!data.getStringExtra(Constant.TAG_ERROR_MESSAGE).isEmpty())
                        this.finish();
                }
            }
        }
    }

    @Override
    public void onClickYesBtn() {
        super.onBackPressed();
        this.finish();
    }

    @Override
    protected void onDestroy() {
        super.onDestroy();
        binding = null;
    }
}