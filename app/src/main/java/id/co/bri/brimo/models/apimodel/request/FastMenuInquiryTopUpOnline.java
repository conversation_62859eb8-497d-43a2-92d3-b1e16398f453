package id.co.bri.brimo.models.apimodel.request;

import com.google.gson.annotations.SerializedName;

public class FastMenuInquiryTopUpOnline extends FastMenuRequest{
    @SerializedName("card_number")
    private String cardNumber;



    public FastMenuInquiryTopUpOnline(FastMenuRequest request, String cardNumber) {
        super(request.getUsername(), request.getTokenKey());
        this.cardNumber = cardNumber;

    }

    public String getCardNumber() {
        return cardNumber;
    }

    public void setCardNumber(String cardNumber) {
        this.cardNumber = cardNumber;
    }


}
