package id.co.bri.brimo.ui.activities.dplk;

import android.app.Activity;
import android.content.Intent;
import android.os.Bundle;
import android.view.View;
import android.widget.LinearLayout;
import androidx.annotation.Nullable;
import androidx.fragment.app.Fragment;
import androidx.viewpager.widget.ViewPager;
import java.util.ArrayList;
import java.util.List;
import javax.inject.Inject;
import id.co.bri.brimo.R;
import id.co.bri.brimo.adapters.CatatanKeuanganAdapter;
import id.co.bri.brimo.contract.IPresenter.dplk.IDescriptionProductDplkPresenter;
import id.co.bri.brimo.contract.IView.dplk.IDescriptonProductDplkView;
import id.co.bri.brimo.databinding.ActivityDescriptionDplkProductBinding;
import id.co.bri.brimo.domain.config.Constant;
import id.co.bri.brimo.domain.helpers.GeneralHelper;
import id.co.bri.brimo.models.apimodel.response.dplk.DplkBoardingResponse;
import id.co.bri.brimo.models.apimodel.response.dplk.DplkFormResponse;
import id.co.bri.brimo.ui.activities.base.BaseActivity;
import id.co.bri.brimo.ui.fragments.DetailTabTabunganFragment;

public class DescriptionDplkProductActivity extends BaseActivity implements View.OnClickListener, ViewPager.OnPageChangeListener, IDescriptonProductDplkView {

    private ActivityDescriptionDplkProductBinding binding;

    LinearLayout linearLayout;

    private static final String TAG_ONBOARDING = "onboarding";

    private static DplkBoardingResponse response;
    private static List<String> titleTab = new ArrayList<>();
    private static List<String> webTab = new ArrayList<>();

    @Inject
    IDescriptionProductDplkPresenter<IDescriptonProductDplkView> presenter;

    public static void launchIntent(Activity caller, DplkBoardingResponse responseDplk){
        Intent i = new Intent(caller, DescriptionDplkProductActivity.class);
        response = responseDplk;
        caller.startActivityForResult(i, Constant.REQ_PAYMENT);
    }

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        binding = ActivityDescriptionDplkProductBinding.inflate(getLayoutInflater());
        setContentView(binding.getRoot());

        injectDependency();
        setupView();

        binding.btnSubmit.setOnClickListener(this);
    }

    private void injectDependency() {
        getActivityComponent().inject(this);
        if (presenter != null) {
            presenter.setView(this);
            presenter.setUrlForm(GeneralHelper.getString(R.string.url_dplk_form));
            presenter.start();
        }
    }

    private void setupView() {
        GeneralHelper.setToolbar(this, binding.toolbar.toolbar, "BRIFINE");

        GeneralHelper.loadImageUrl(getApplicationContext(), "", binding.imageRekening, R.drawable.logo_brifine_biru, 0);

        if(response != null) {
            binding.tvDescription.setText(response.getProduct().getDescription());
            titleTab.clear();
            webTab.clear();

            for (int i = 0; i < response.getProduct().getTab().size(); i++) {
                titleTab.add(response.getProduct().getTab().get(i).getTitle());
                webTab.add(response.getProduct().getTab().get(i).getContent());
            }
        }

        List<Fragment> fragmentList = new ArrayList<>();
        for (int i = 0; i < webTab.size(); i++) {
            fragmentList.add(new DetailTabTabunganFragment(webTab.get(i)));
        }

        CatatanKeuanganAdapter viewAdapter = new CatatanKeuanganAdapter(getSupportFragmentManager(), this, fragmentList, titleTab);
        binding.viewpager.setAdapter(viewAdapter);
        binding.tabDetail.setViewPager(binding.viewpager);
        binding.tabDetail.setOnPageChangeListener(this);
        linearLayout = (LinearLayout) binding.tabDetail.getChildAt(0);

        GeneralHelper.changeTabsFontSimple(this, linearLayout, 0);
    }

    @Override
    public void onClick(View v) {
        switch (v.getId()){
            case R.id.btnSubmit:
                presenter.getDataForm();
                break;
        }
    }

    @Override
    public void onPageScrolled(int position, float positionOffset, int positionOffsetPixels) {

    }

    @Override
    public void onPageSelected(int position) {
        GeneralHelper.changeTabsFontSimple(this, linearLayout, position);
    }

    @Override
    public void onPageScrollStateChanged(int state) {

    }

    @Override
    public void onSuccessGetDataForm(DplkFormResponse response) {
        FormOpenDplkActivity.launchIntent(this, response);
    }

    @Override
    protected void onActivityResult(int requestCode, int resultCode, @Nullable Intent data) {
        super.onActivityResult(requestCode, resultCode, data);
        if (requestCode == Constant.REQ_PAYMENT && data != null) {
            if (resultCode == RESULT_OK) {
                this.setResult(RESULT_OK, data);
                this.finish();
            }
            else if (resultCode == Activity.RESULT_CANCELED){
                if (data.hasExtra(Constant.TAG_ERROR_MESSAGE)){
                    this.setResult(RESULT_CANCELED, data);
                    finish();
                }
                this.finish();
            }
        }
    }

    @Override
    protected void onDestroy() {
        super.onDestroy();
        binding = null;
    }
}