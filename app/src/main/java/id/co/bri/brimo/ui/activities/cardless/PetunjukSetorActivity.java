package id.co.bri.brimo.ui.activities.cardless;

import android.app.Activity;
import android.content.Intent;
import android.os.Bundle;
import android.view.View;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.fragment.app.Fragment;
import androidx.fragment.app.FragmentManager;
import androidx.fragment.app.FragmentStatePagerAdapter;
import androidx.viewpager.widget.ViewPager;
import id.co.bri.brimo.R;
import id.co.bri.brimo.databinding.ActivityPetunjukSetorBinding;
import id.co.bri.brimo.domain.config.Constant;
import id.co.bri.brimo.domain.helpers.GeneralHelper;
import id.co.bri.brimo.ui.activities.base.BaseActivity;

public class PetunjukSetorActivity extends BaseActivity implements View.OnClickListener {

    private ActivityPetunjukSetorBinding binding;

    FragmentStatePagerAdapter adapterViewPager;

    public static void launchIntent(Activity caller) {
        Intent intent = new Intent(caller, PetunjukSetorActivity.class);
        caller.startActivityForResult(intent, Constant.REQ_PAYMENT);
    }


    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        binding = ActivityPetunjukSetorBinding.inflate(getLayoutInflater());
        setContentView(binding.getRoot());

        setSupportActionBar(binding.tbLewati);

        binding.textlewat.setOnClickListener(this);
        binding.btnpentunjuk.setOnClickListener(this);
        getSupportActionBar().setTitle(null);
        adapterViewPager = new MyPagerAdapter(getSupportFragmentManager());
        binding.viewpager.setAdapter(adapterViewPager);
        binding.viewpager.setOnPageChangeListener(new ViewPager.OnPageChangeListener() {
            @Override
            public void onPageScrolled(int position, float positionOffset, int positionOffsetPixels) {

            }

            @Override
            public void onPageSelected(int position) {
                if(position == 7){
                    binding.pageIndicatorView.setVisibility(View.GONE);
                    binding.btnpentunjuk.setVisibility(View.VISIBLE);
                    binding.textlewat.setVisibility(View.GONE);
                }
                if(position == 6){
                    binding.pageIndicatorView.setVisibility(View.VISIBLE);
                    binding.btnpentunjuk.setVisibility(View.GONE);
                    binding.textlewat.setVisibility(View.VISIBLE);
                }
            }

            @Override
            public void onPageScrollStateChanged(int state) {

            }
        });

    }

    @Override
    public void onClick(View view) {
        int id = view.getId();
        switch (id) {

            case R.id.btnpentunjuk:
                finish();
                break;
            case R.id.textlewat:
                finish();
                break;
            default:
                break;
        }
    }

    @Override
    protected void onActivityResult(int requestCode, int resultCode, @Nullable Intent data) {
        super.onActivityResult(requestCode, resultCode, data);

        if (requestCode == Constant.REQ_PAYMENT) {
            if (resultCode == RESULT_OK) {
                this.setResult(RESULT_OK,data);
                this.finish();
            } else {
                this.setResult(RESULT_CANCELED);
                this.setResult(RESULT_CANCELED,data);
            }
        }
    }

    public static class MyPagerAdapter extends FragmentStatePagerAdapter {
        private static int NUM_ITEMS = 8;
        boolean button =true;


        public MyPagerAdapter(FragmentManager fragmentManager) {
            super(fragmentManager);


        }


        // Returns total number of pages
        @Override
        public int getCount() {
            return NUM_ITEMS;
        }

        // Returns the fragment to display for that page
        @Override
        public Fragment getItem(int position) {
            switch (position) {
                case 0: // Fragment # 0 - This will show FirstFragment
                    return PetunjukSetorFragment.newInstance(position, GeneralHelper.getString(R.string.cash_deposit_tutorial_page_1),false);
                case 1: // Fragment # 1 - This will show SecondFragment
                    return PetunjukSetorFragment.newInstance(position, GeneralHelper.getString(R.string.cash_deposit_tutorial_page_2), false);
                case 2: // Fragment # 2 - This will show ThirdFragment
                    return PetunjukSetorFragment.newInstance(position, GeneralHelper.getString(R.string.cash_deposit_tutorial_page_3),false);
                case 3: // Fragment # 3 - This will show FourthFragment
                    return PetunjukSetorFragment.newInstance(position,GeneralHelper.getString(R.string.cash_deposit_tutorial_page_4), false);
                case 4: // Fragment # 4 - This will show FifthFragment
                    return PetunjukSetorFragment.newInstance(position, GeneralHelper.getString(R.string.cash_deposit_tutorial_page_5),true);
                case 5: // Fragment # 5 - This will show SixthFragment
                    return PetunjukSetorFragment.newInstance(position, GeneralHelper.getString(R.string.cash_deposit_tutorial_page_6),true);
                case 6: // Fragment # 5 - This will show SeventhFragment
                    return PetunjukSetorFragment.newInstance(position, GeneralHelper.getString(R.string.cash_deposit_tutorial_page_7), true);
                case 7: // Fragment # 5 - This will show EighthFragment
                    return PetunjukSetorFragment.newInstance(position, GeneralHelper.getString(R.string.cash_deposit_tutorial_page_8),true);
                default:
                    return null;
            }
        }

        @Override
        public int getItemPosition(@NonNull Object object) {
            return POSITION_NONE;
        }
    }

    @Override
    protected void onDestroy() {
        super.onDestroy();
        binding = null;
    }
}
