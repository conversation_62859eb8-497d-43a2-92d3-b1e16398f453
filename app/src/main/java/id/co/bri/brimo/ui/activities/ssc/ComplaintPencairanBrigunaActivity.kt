package id.co.bri.brimo.ui.activities.ssc

import android.app.Activity
import android.content.Intent
import android.os.Bundle
import android.text.Editable
import android.text.Html
import android.view.View
import com.google.gson.Gson
import com.google.gson.reflect.TypeToken
import id.co.bri.brimo.R
import id.co.bri.brimo.adapters.HeaderTransaksiAdapter
import id.co.bri.brimo.databinding.ActivityComplaintPencairanBrigunaBinding
import id.co.bri.brimo.domain.config.Constant
import id.co.bri.brimo.domain.helpers.GeneralHelper
import id.co.bri.brimo.models.AccountModel
import id.co.bri.brimo.models.apimodel.request.ssc.CreateTicketCairBrigunaReq
import id.co.bri.brimo.models.apimodel.response.*
import id.co.bri.brimo.models.apimodel.response.ssc.ComplaintInformasiResponse
import id.co.bri.brimo.models.apimodel.response.ssc.ComplaintTicketCreateRes
import id.co.bri.brimo.ui.activities.GeneralSyaratActivity
import java.util.*

class ComplaintPencairanBrigunaActivity : BaseComplaintActivity(),
    View.OnClickListener {

    private lateinit var binding: ActivityComplaintPencairanBrigunaBinding

    private var isTerm = false
    private var isClickBtmSheet = ""
    private var sAccountPayroll = ""
    private var sAccountBriguna = ""

    companion object {
        private const val TAG_TOOLBAR = "toolbar"
        private const val TAG_INFORESPONSE = "informasi_response"
        private const val TAG_BRIGUNA = "briguna"
        private const val TAG_FEATURE_ID = "feature_id"
        private const val TAG_ACCOUNTS = "account"

        @JvmStatic
        fun launchIntentCairBriguna(
            caller: Activity, sToolbar: String,
            accounts: List<ListRekeningResponse.Account>,
            brigunaList: List<ListRekeningResponse.Account>,
            informasiResponse: ComplaintInformasiResponse,
            sFeatureId: String
        ) {
            val intent = Intent(caller, ComplaintPencairanBrigunaActivity::class.java)
            intent.putExtra(TAG_TOOLBAR, sToolbar)
            intent.putExtra(TAG_ACCOUNTS, Gson().toJson(accounts))
            intent.putExtra(TAG_BRIGUNA, Gson().toJson(brigunaList))
            intent.putExtra(TAG_INFORESPONSE, Gson().toJson(informasiResponse))
            intent.putExtra(TAG_FEATURE_ID, sFeatureId)
            caller.startActivityForResult(intent, Constant.REQ_NON_PAYMENT)
        }
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        binding = ActivityComplaintPencairanBrigunaBinding.inflate(layoutInflater)
        setContentView(binding.root)

        intentExtra()
        setToolbar()
        setupDataRes()
        setupViews()
    }

    override fun intentExtra() {
        if (intent.extras != null) {
            if (intent.hasExtra(TAG_TOOLBAR))
                sToolbar = intent.getStringExtra(TAG_TOOLBAR).toString()

            if (intent.hasExtra(TAG_INFORESPONSE))
                informationRes = Gson().fromJson(
                    intent.getStringExtra(TAG_INFORESPONSE),
                    ComplaintInformasiResponse::class.java
                )

            if (intent.hasExtra(TAG_BRIGUNA)) {
                val collectionType =
                    object : TypeToken<Collection<ListRekeningResponse.Account>>() {}.type
                val gson = Gson()
                val jsonString = intent.extras?.getString(TAG_BRIGUNA)
                val enums = gson.fromJson<Collection<ListRekeningResponse.Account>>(
                    jsonString,
                    collectionType
                )
                brigunaList!!.addAll(enums)
            }

            if (intent.hasExtra(TAG_FEATURE_ID))
                sFeatureId = intent.getStringExtra(TAG_FEATURE_ID).toString()

            if (intent.hasExtra(TAG_ACCOUNTS)) {
                val collectionType =
                    object : TypeToken<Collection<ListRekeningResponse.Account>>() {}.type
                val gson = Gson()
                val jsonString = intent.extras?.getString(TAG_ACCOUNTS)
                val enums = gson.fromJson<Collection<ListRekeningResponse.Account>>(
                    jsonString,
                    collectionType
                )
                accountList!!.addAll(enums)
            }
        }
    }

    override fun setToolbar() {
        GeneralHelper.setToolbar(this, binding.toolbar.toolbar, sToolbar)
    }

    override fun setupDataRes() {
        if (transactionRes != null) sTrxDetail = transactionRes!!.detail

        if (informationRes != null) {
            if (informationRes!!.information.isNotEmpty())
                dataInformationList = informationRes!!.information as ArrayList<DataView>?

            if (informationRes!!.term.isNotEmpty())
                sTerm = informationRes!!.term
        }
    }

    @Suppress("DEPRECATION")
    override fun setupViews() {
        presenter.setUrlCreateTicket(GeneralHelper.getString(R.string.url_ss_complaint_ticket_create_non_mutation))

        showViews()

        binding.layoutPermasalahanTerm.tvDescSyarat.text =
            Html.fromHtml(GeneralHelper.getString(R.string.syarat_ssc))

        binding.etNorekPayroll.setOnClickListener(this)
        binding.etNorekBriguna.setOnClickListener(this)
        binding.layoutPermasalahanTerm.etPermasalahan.addTextChangedListener(activityTextListener)
        binding.layoutPermasalahanTerm.layoutSyarat.setOnClickListener(this)
        binding.btnSubmit.setOnClickListener(this)
    }

    override fun changeText(charSequence: CharSequence?, i: Int, i1: Int, i2: Int) {

    }

    override fun recyclerTrxInfo(headerTransaksiAdapter: HeaderTransaksiAdapter) {
        // do nothing
    }

    override fun notFoundTrxInfo() {
        // do nothing
    }

    override fun afterText(editable: Editable?) {
        if (binding.layoutPermasalahanTerm.etPermasalahan.toString()
                .isNotEmpty() && binding.layoutPermasalahanTerm.etPermasalahan.isFocused
        )
            binding.layoutPermasalahanTerm.tvLength.text = editable!!.length.toString() + "/200"

        validationButton()
    }

    override fun showViews() {
        binding.layoutNorekBriguna.alpha = 0.7f
        accountModelList!!.addAll(fetchAccountModel())
        brigunaModelList!!.addAll(fetchBrigunaModel())
    }

    override fun enableButton(isEnable: Boolean) {
        if (isEnable) {
            binding.btnSubmit.isEnabled = true
            binding.btnSubmit.alpha = 1f
        } else {
            binding.btnSubmit.isEnabled = false
            binding.btnSubmit.alpha = 0.3f
        }
    }

    override fun validationButton() {
        enableButton(false)
        if (binding.etNorekPayroll.text.toString().isEmpty())
            return
        if (binding.etNorekBriguna.text.toString().isEmpty())
            return
        if (binding.layoutPermasalahanTerm.etPermasalahan.text.toString().isEmpty())
            return
        if (!isTerm)
            return
        enableButton(true)
    }

    override fun onClick(v: View) {
        when (v.id) {
            binding.etNorekPayroll.id -> {
                isClickBtmSheet = Constant.ComplaintMenu.PAYROLL
                listAccount(findViewById(R.id.content))
            }
            binding.etNorekBriguna.id -> {
                if (binding.etNorekPayroll.text!!.isNotEmpty()) {
                    isClickBtmSheet = Constant.ComplaintMenu.BRIGUNA
                    listBriguna(findViewById(R.id.content))
                }
            }
            binding.layoutPermasalahanTerm.layoutSyarat.id -> launchTerm()
            binding.btnSubmit.id -> buttonClickSendReq()
        }
    }

    override fun buttonClickSendReq() {
        val request = CreateTicketCairBrigunaReq(
            sFeatureId,
            sAccountPayroll,
            sAccountBriguna,
            binding.layoutPermasalahanTerm.etPermasalahan.text.toString()
        )
        presenter.sendCreateTicket(request)
    }

    override fun onSuccessCreateTicket(response: ComplaintTicketCreateRes) {
        LaporanTransaksiActivity.launchIntent(this, sToolbar, response, Constant.CIAType.TYPE_COMPLAINT_IN_APPS_GENERAL)
    }

    override fun onSelectSumberDana(bankModel: AccountModel?) {
        if (isClickBtmSheet == Constant.ComplaintMenu.PAYROLL) {
            binding.layoutNorekBriguna.alpha = 1f
            accountModel = bankModel
            account = accountModel!!.acoount
            if (sAccountBriguna == accountModel!!.acoount) {
                GeneralHelper.showSnackBar(
                    Objects.requireNonNull(this).findViewById(R.id.content),
                    GeneralHelper.getString(R.string.alert_norek_payroll)
                )
                return
            }
            sAccountPayroll = accountModel!!.acoount
            binding.etNorekPayroll.setText(accountModel!!.acoountString)
        }

        if (isClickBtmSheet == Constant.ComplaintMenu.BRIGUNA) {
            accountModel = bankModel
            account = accountModel!!.acoount
            if (sAccountPayroll == accountModel!!.acoount) {
                GeneralHelper.showSnackBar(
                    Objects.requireNonNull(this).findViewById(R.id.content),
                    GeneralHelper.getString(R.string.alert_norek_briguna)
                )
                return
            }

            sAccountBriguna = accountModel!!.acoount
            binding.etNorekBriguna.setText(accountModel!!.acoountString)
        }

        validationButton()
    }

    private fun launchTerm() {
        GeneralSyaratActivity.launchIntentNoArrow(
            this,
            sTerm,
            true
        )
    }

    @Suppress("DEPRECATION")
    @Deprecated("Deprecated in Java")
    override fun onActivityResult(requestCode: Int, resultCode: Int, data: Intent?) {
        super.onActivityResult(requestCode, resultCode, data)

        if (requestCode == Constant.REQ_PETTUNJUK1 && resultCode == RESULT_OK && data != null) {
            isTerm = java.lang.Boolean.parseBoolean(data.getStringExtra("checkbox"))
            if (isTerm) {
                binding.layoutPermasalahanTerm.cbSyarat.setBackgroundResource(R.drawable.checkbox_on)
            } else {
                binding.layoutPermasalahanTerm.cbSyarat.setBackgroundResource(R.drawable.checkbox_off)
            }
            validationButton()
        }

        if (resultCode == RESULT_CANCELED && requestCode == Constant.REQ_NON_PAYMENT && data != null) {
            setResult(RESULT_CANCELED, data)
            finish()
        }
    }
}