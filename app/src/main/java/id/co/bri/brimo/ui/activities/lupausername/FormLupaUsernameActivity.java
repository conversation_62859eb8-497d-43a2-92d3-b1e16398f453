package id.co.bri.brimo.ui.activities.lupausername;

import android.app.Activity;
import android.content.Intent;
import android.os.Build;
import android.os.Bundle;
import android.view.Window;
import android.view.WindowManager;
import androidx.annotation.Nullable;
import androidx.fragment.app.FragmentTransaction;
import java.util.Objects;
import javax.inject.Inject;
import id.co.bri.brimo.R;
import id.co.bri.brimo.contract.IPresenter.lupausername.IFormLupaUsernamePresenter;
import id.co.bri.brimo.contract.IView.lupausername.IFormLupaUsernameView;
import id.co.bri.brimo.databinding.ActivityFormLupaUsernameBinding;
import id.co.bri.brimo.domain.config.Constant;
import id.co.bri.brimo.domain.helpers.GeneralHelper;
import id.co.bri.brimo.models.apimodel.request.ForgetUsernameInquReq;
import id.co.bri.brimo.models.apimodel.response.DetailAkunResponse;
import id.co.bri.brimo.ui.activities.FastMenuActivity;
import id.co.bri.brimo.ui.activities.InfoForgotUserPassActivity;
import id.co.bri.brimo.ui.activities.LoginActivity;
import id.co.bri.brimo.ui.activities.base.BaseActivity;
import id.co.bri.brimo.ui.activities.lupapassword.KonfirmasiLupaUserPassActivity;
import id.co.bri.brimo.ui.customviews.dialog.DialogInformation;
import id.co.bri.brimo.ui.fragments.PinFragment;

import android.app.Activity;
import android.content.Intent;
import android.os.Bundle;

import javax.inject.Inject;

public class FormLupaUsernameActivity extends BaseActivity implements
        IFormLupaUsernameView,
        PinFragment.SendPin,
        DialogInformation.OnActionClick {

    private ActivityFormLupaUsernameBinding binding;

    @Inject
    IFormLupaUsernamePresenter<IFormLupaUsernameView> presenter;

    private static boolean isOpenApp = false;
    private static String sMessage;

    private boolean isNotLogin = false;

    public static void launchIntent(Activity caller, boolean isOpenAp) {
        Intent intent = new Intent(caller, FormLupaUsernameActivity.class);
        isOpenApp = isOpenAp;
        sMessage = null;
        caller.startActivityForResult(intent, Constant.REQ_FORGET);
    }

    public static void launchIntentCloseApp(Activity caller, boolean isOpenAp, String message) {
        Intent intent = new Intent(caller, FormLupaUsernameActivity.class);
        isOpenApp = isOpenAp;
        sMessage = message;
        caller.startActivityForResult(intent, Constant.REQ_FORGET);
    }

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        binding = ActivityFormLupaUsernameBinding.inflate(getLayoutInflater());
        setContentView(binding.getRoot());

        //set blue bar
        if (Build.VERSION.SDK_INT >= 21) {
            Window window = getWindow();
            window.addFlags(WindowManager.LayoutParams.FLAG_DRAWS_SYSTEM_BAR_BACKGROUNDS);
            window.clearFlags(WindowManager.LayoutParams.FLAG_TRANSLUCENT_STATUS);
            window.setStatusBarColor(getResources().getColor(R.color.toolbar_blue));
        }

        GeneralHelper.setToolbar(this, binding.toolbar.toolbar, GeneralHelper.getString(R.string.toolbar_lupa_username));

        if (!isOpenApp && sMessage != null) {
            GeneralHelper.showSnackBar(this.findViewById(R.id.content), sMessage);
            sMessage = null;
        }

        injectDependency();
        setupViews();
    }

    private void injectDependency() {
        getActivityComponent().inject(this);
        if (presenter != null) {
            presenter.setView(this);
            presenter.start();
            presenter.setUrl(GeneralHelper.getString(R.string.url_forget_username_inquiry_v2));
            presenter.getIsNotLogin();
        }
    }

    private void setupViews() {
        binding.etNomorKtp.addTextChangedListener(activityTextListener);
        binding.etNamaIbu.addTextChangedListener(activityTextListener);
        binding.etNomorRekening.addTextChangedListener(activityTextListener);

        binding.btnLanjut.setOnClickListener(v -> {
            PinFragment pinFragment = new PinFragment(this, this);
            pinFragment.show();
        });
    }

    @Override
    protected void changeText(CharSequence charSequence, int i, int i1, int i2) {
        if (getNomorKtp().length() == 16 && !getNamaIbu().isEmpty() && getNomorRek().length() == 15) {
            binding.btnLanjut.setAlpha(1);
            binding.btnLanjut.setEnabled(true);
        } else {
            binding.btnLanjut.setAlpha(0.3f);
            binding.btnLanjut.setEnabled(false);
        }
    }

    protected String getNomorKtp() {
        return binding.etNomorKtp.getText().toString();
    }

    protected String getNamaIbu() {
        return binding.etNamaIbu.getText().toString();
    }

    protected String getNomorRek() {
        return binding.etNomorRekening.getText().toString();
    }

    @Override
    public void onDataSuccess(DetailAkunResponse response) {
        KonfirmasiLupaUserPassActivity.launchIntentForgetUsername(this, response, getNomorKtp(), getNamaIbu(), getNomorRek(), true);
    }

    @Override
    public void isNotLogin(Boolean isNotLogin) {
        this.isNotLogin = isNotLogin;
    }

    @Override
    protected void onActivityResult(int requestCode, int resultCode, @Nullable Intent data) {
        super.onActivityResult(requestCode, resultCode, data);

        if (requestCode == Constant.REQ_FORGET && resultCode == RESULT_OK && data != null) {
            this.setResult(RESULT_OK, data);
            this.finish();
        } else if (resultCode == RESULT_CANCELED && data != null) {
            if (data.hasExtra(Constant.TAG_ERROR_MESSAGE)) {
                GeneralHelper.showSnackBar(this.findViewById(R.id.content), data.getStringExtra(Constant.TAG_ERROR_MESSAGE));
            }
        } else {
            this.setResult(RESULT_CANCELED);
            this.finish();
        }
    }

    @Override
    public void onBackPressed() {
        if (isOpenApp)
            super.onBackPressed();
        else {
            InfoForgotUserPassActivity.launchIntent(this, false);
            finish();
        }
    }

    @Override
    public void onSendPinComplete(String pin) {
        ForgetUsernameInquReq request = new ForgetUsernameInquReq(getNomorKtp(), getNamaIbu(), getNomorRek(), pin);
        presenter.sendDataForm(request);
    }

    @Override
    public void onLupaPin() {
        DialogInformation dialog = new DialogInformation(this, "ic_forbidden_lupa_pin",
                GeneralHelper.getString(R.string.title_maaf_lupa_pin),
                GeneralHelper.getString(R.string.desc_maaf_lupa_pin_username),
                GeneralHelper.getString(R.string.ok),
                this, true, false);
        FragmentTransaction ft = getSupportFragmentManager().beginTransaction();
        ft.add(dialog, null);
        ft.commitAllowingStateLoss();
    }

    @Override
    public void onClickAction() {
        Intent resultIntent = new Intent();
        setResult(RESULT_CANCELED, resultIntent);
        this.finish();
    }

    @Override
    public void onSessionEnd(String message) {
        hideProgress();
        if (isNotLogin)
            LoginActivity.launchIntentSessionEnd(this, message);
        else FastMenuActivity.launchIntentSessionEnd(this, message);
    }

    @Override
    protected void onDestroy() {
        super.onDestroy();
        binding = null;
    }
}