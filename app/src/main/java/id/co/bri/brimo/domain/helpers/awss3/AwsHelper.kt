package id.co.bri.brimo.domain.helpers.awss3

import android.content.Context
import android.util.Base64
import android.util.Log
import id.co.bri.brimo.domain.config.Constant
import id.co.bri.brimo.domain.helpers.GeneralHelper
import java.io.File
import java.io.FileOutputStream
import java.io.IOException

class AwsHelper(private val onUploadListener: OnUploadListener) :
    AwsUpload.OnAwsImageUploadListener {

    private lateinit var savedFile: File

    object FileName {
        const val IMAGE = "image"
        const val IMAGE1 = "image1.png"
        const val IMAGE2 = "image2.png"
        const val VIDEO = "video.mp4"
    }

    object ContentType {
        const val IMAGE = "image/png"
        const val VIDEO = "movie/mp4"
    }


    interface OnUploadListener {
        fun onSuccessUpload(imgUrl: String)
        fun onErrorUpload(errorMsg: String)
    }

    fun uploadToAwsPrivy(
        context: Context,
        fileName: String,
        base64: String,
        bucket: String,
        folderPath: String,
        contentType: String
    ) {
        savedFileName(context, fileName)
        saveDataDecodeBase64(base64)

        AwsUpload(
            context,
            bucket,
            savedFile.path,
            this,
            folderPath,
            contentType
        ).beginUpload()
    }

    fun uploadToAwsVida(
        context: Context,
        fileName: String,
        byteArray: ByteArray,
        bucket: String,
        folderPath: String,
        contentType: String
    ) {
        savedFileName(context, fileName)
        saveDataByte(byteArray)

        AwsUpload(
            context,
            bucket,
            savedFile.path,
            this,
            folderPath,
            contentType
        ).beginUpload()
    }

    private fun savedFileName(context: Context, fileName: String) {
        val tempDir = File(context.cacheDir, Constant.TAG_START_NAME)
        if (!tempDir.exists()) {
            tempDir.mkdirs()
        }

        savedFile = File(tempDir, fileName)
    }

    private fun saveDataByte(byteArray: ByteArray) {
        var outputStream: FileOutputStream? = null
        try {
            outputStream = FileOutputStream(savedFile.path)
            outputStream.write(byteArray)
            outputStream.flush()
            outputStream.close()
        } catch (e: IOException) {
            if (!GeneralHelper.isProd()) {
                Log.d("TestDecode", "decodeBit64: $e")
            }
        } finally {
            try {
                assert(outputStream != null)
                outputStream!!.close()
            } catch (e: IOException) {
                if (!GeneralHelper.isProd()) {
                    Log.d("TestDecode", "decodeBit64: $e")
                }
            }
        }
    }

    private fun saveDataDecodeBase64(base64Data: String) {
        val decodedBytes = Base64.decode(base64Data, Base64.DEFAULT)

        var outputStream: FileOutputStream? = null
        try {
            outputStream = FileOutputStream(savedFile.path)
            outputStream.write(decodedBytes)
            outputStream.flush()
            outputStream.close()
        } catch (e: IOException) {
            if (!GeneralHelper.isProd()) {
                Log.d("TestDecode", "decodeBit64: $e")
            }
        } finally {
            try {
                assert(outputStream != null)
                outputStream!!.close()
            } catch (e: IOException) {
                if (!GeneralHelper.isProd()) {
                    Log.d("TestDecode", "decodeBit64: $e")
                }
            }
        }
    }

    override fun onSuccess(imgUrl: String) {
        onUploadListener.onSuccessUpload(imgUrl)
        deleteTempFile()
    }

    override fun onError(errorMsg: String) {
        onUploadListener.onErrorUpload(errorMsg)
        deleteTempFile()
    }

    private fun deleteTempFile() {
        if (::savedFile.isInitialized && savedFile.exists()) {
            val isDeleted = savedFile.delete()
            if (!isDeleted && !GeneralHelper.isProd()) {
                Log.d("TestDecode", "deleteTempFile: Gagal menghapus file")
            }
        }
    }
}