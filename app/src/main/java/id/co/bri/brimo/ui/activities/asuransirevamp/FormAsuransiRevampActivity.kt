package id.co.bri.brimo.ui.activities.asuransirevamp

import android.app.Activity
import android.content.Intent
import android.os.Bundle
import android.text.Editable
import android.text.InputFilter
import android.text.TextWatcher
import android.view.View
import id.co.bri.brimo.R
import id.co.bri.brimo.contract.IPresenter.asuransiRevamp.IFormAsuransiRevampPresenter
import id.co.bri.brimo.contract.IView.asuransiRevamp.IFormAsuransiRevampView
import id.co.bri.brimo.databinding.ActivityFormAsuransiBinding
import id.co.bri.brimo.domain.config.Constant
import id.co.bri.brimo.domain.helpers.GeneralHelper
import id.co.bri.brimo.models.ParameterModel
import id.co.bri.brimo.models.apimodel.request.asuransirevamp.KonfirmasiAsuransiRequest
import id.co.bri.brimo.models.apimodel.response.InquiryBrivaRevampResponse
import id.co.bri.brimo.models.apimodel.response.asuransi.FormAsuransiResponse
import id.co.bri.brimo.models.optionmodel.OptionGeneralModel
import id.co.bri.brimo.ui.activities.InquiryKonfirmasiBrivaRevampCloseActivity
import id.co.bri.brimo.ui.activities.base.BaseActivity
import id.co.bri.brimo.ui.fragments.KodePosRevampFragment
import id.co.bri.brimo.ui.fragments.PilihGeneralNoLogoFragment
import javax.inject.Inject

class FormAsuransiRevampActivity : BaseActivity(), IFormAsuransiRevampView, View.OnClickListener, KodePosRevampFragment.onSetDataPos {

    private lateinit var binding : ActivityFormAsuransiBinding
    private var  kodePosBottomFragment: KodePosRevampFragment? = null
    private var mKotaKode : String? = null
    private var hubunganFragment: PilihGeneralNoLogoFragment? = null
    private var sHubungan: String? = null
    private var iHubungan: String? = null

    @Inject
    lateinit var presenter : IFormAsuransiRevampPresenter<IFormAsuransiRevampView>
    companion object {

        var mResponse : FormAsuransiResponse? = null
        var mProductCode : String? = null

        @JvmStatic
        fun launchIntent(caller: Activity, fromFastMenu: Boolean, response: FormAsuransiResponse, productCode : String?) {
            val intent = Intent(caller, FormAsuransiRevampActivity::class.java)
            isFromFastMenu = fromFastMenu
            mResponse = response
            mProductCode = productCode
            caller.startActivityForResult(intent, Constant.REQ_PAYMENT)
        }
    }


    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        binding = ActivityFormAsuransiBinding.inflate(layoutInflater)
        setContentView(binding.root)

        injectDependency()
        setupView()
    }

    private fun setupView() {
        GeneralHelper.setToolbarRevamp(this, binding.toolbar.toolbar, GeneralHelper.getString(R.string.tb_beli_asuransi))
        binding.btnSubmit.setOnClickListener(this)
        binding.imgHubunganAhliWaris.setOnClickListener(this)
        binding.imgKodePos.setOnClickListener(this)
        binding.etHubunganAhliWaris.setOnClickListener(this)
        binding.etKodePos.setOnClickListener(this)
        binding.etNamaAhliWaris.addTextChangedListener(object : TextWatcher{
            override fun beforeTextChanged(p0: CharSequence?, p1: Int, p2: Int, p3: Int) {
            }

            override fun onTextChanged(p0: CharSequence?, p1: Int, p2: Int, p3: Int) {
            }

            override fun afterTextChanged(p0: Editable?) {
                if (binding.etNamaAhliWaris.length() < 3) {
                    binding.tvErrorAhliwaris.visibility = View.VISIBLE
                    binding.tvErrorAhliwaris.text = "Nama terlalu pendek, minimal 3 karakter"
                }
                else{
                    binding.tvErrorAhliwaris.visibility = View.GONE
                }

                val inputFilter = InputFilter { source, start, end, dest, dstart, dend ->
                    if (dstart == 0 && source.isNotEmpty() && source[0] == ' ') {
                        // Don't allow space as the first character
                        ""
                    } else {
                        null // Accept the input as it is
                    }
                }
                binding.etNamaAhliWaris.filters = arrayOf(inputFilter)
                validation()

            }

        })
        binding.etIsiNoHp.addTextChangedListener(object : TextWatcher{
            override fun beforeTextChanged(p0: CharSequence?, p1: Int, p2: Int, p3: Int) {
            }

            override fun onTextChanged(p0: CharSequence?, p1: Int, p2: Int, p3: Int) {
                    if (p0.toString().trim().startsWith("0")) {
                        // If the input starts with "0", handle the case
                        binding.etIsiNoHp.setText("")
                        binding.tvErrorNohp.visibility = View.GONE
                    }
                }

            override fun afterTextChanged(p0: Editable?) {
                if (binding.etIsiNoHp.equals("")){
                    binding.tvErrorNohp.visibility = View.GONE
                }else{
                    if (binding.etIsiNoHp.length() in 1..9){
                        binding.tvErrorNohp.visibility = View.VISIBLE
                        binding.tvErrorNohp.text = "Nomor terlalu pendek, minimal 10 karakter"
                    }
                    else{
                        binding.tvErrorNohp.visibility = View.GONE
                    }
                }

                validation()
            }

        })

        binding.etKodePos.addTextChangedListener(activityTextListener)
        binding.etHubunganAhliWaris.addTextChangedListener(activityTextListener)
        binding.etAlamat.addTextChangedListener(activityTextListener)

        validation()

    }


    override fun afterText(editable: Editable?) {
        validation()
    }

    private fun injectDependency() {
        activityComponent.inject(this)
        presenter.view = this
        presenter.start()
        presenter.setUrlKonfirmasi(GeneralHelper.getString(R.string.url_konfirmasi_asuransi_revamp))
    }

    private fun fetchOptionListHubungan(hubunganList: List<FormAsuransiResponse.Heir>?): List<OptionGeneralModel>? {
        val list: MutableList<OptionGeneralModel> = ArrayList()
        if (hubunganList != null) {
            for (pointHubungan in hubunganList) {
                list.add(OptionGeneralModel(0, pointHubungan.name, "", ""))
            }
        }
        return list
    }

    override fun onClick(p0: View?) {
        when(p0!!.id) {
            R.id.btnSubmit -> {presenter.getDataKonfirmasi(KonfirmasiAsuransiRequest(mProductCode,binding.etNamaAhliWaris.text.toString(),"0"+binding.etIsiNoHp.text.toString(),
                    iHubungan,binding.etKodePos.text.toString(),mKotaKode, binding.etAlamat.text.toString()))
            }
            R.id.img_kode_pos ->{
                kodePosBottomFragment = KodePosRevampFragment.newInstance(this, this)
                kodePosBottomFragment!!.show(supportFragmentManager, "")
            }
            R.id.et_kode_pos->{
                kodePosBottomFragment = KodePosRevampFragment.newInstance(this, this)
                kodePosBottomFragment!!.show(supportFragmentManager, "")
            }
            R.id.img_hubungan_ahli_waris ->{
                hubunganFragment = PilihGeneralNoLogoFragment(fetchOptionListHubungan(mResponse!!.heirList)) { position: Int, optionModel: OptionGeneralModel? ->
                    sHubungan = mResponse!!.heirList!![position].name
                    iHubungan = mResponse!!.heirList!![position].code
                    binding.etHubunganAhliWaris.setText(mResponse!!.heirList!![position].name)
                }
                hubunganFragment!!.show(supportFragmentManager, "")
                hubunganFragment!!.isCancelable = true
            }
            R.id.et_hubungan_ahli_waris ->{
                hubunganFragment = PilihGeneralNoLogoFragment(fetchOptionListHubungan(mResponse!!.heirList)) { position: Int, optionModel: OptionGeneralModel? ->
                    sHubungan = mResponse!!.heirList!![position].name
                    iHubungan = mResponse!!.heirList!![position].code
                    binding.etHubunganAhliWaris.setText(mResponse!!.heirList!![position].name)
                }
                hubunganFragment!!.show(supportFragmentManager, "")
                hubunganFragment!!.isCancelable = true
            }

        }
    }

    fun setParameter(): ParameterModel {
        val parameterModel = ParameterModel()
        parameterModel.stringLabelTujuan = "Nomor Tujuan"
        parameterModel.stringLabelNominal = "Nominal Pembayaran"
        parameterModel.stringButtonSubmit = "BRIVA"
        parameterModel.stringLabelMinimum = "BRIVA"
        parameterModel.defaultIcon = getDefaultIconResource()
        return parameterModel
    }
    fun getDefaultIconResource(): Int {
        return R.drawable.bri
    }

    override fun onSuccessKonfirmasiAsuransi(response: InquiryBrivaRevampResponse) {
       InquiryKonfirmasiBrivaRevampCloseActivity.launchIntent(this, response,
               GeneralHelper.getString(R.string.url_konfirmasi_asuransi_revamp), GeneralHelper.getString(R.string.url_payment_asuransi_revamp), isFromFastMenu, setParameter(),
       Constant.TRX_TYPE_ASURANSI)
    }


    override fun setKodePos(id: String?, kodePos: String?, kelurahan: String?, kecamatan: String?, kota: String?, provinsi: String?) {
        binding.etKodePos.setText(kodePos)
        mKotaKode = kota
        validation()
    }

    fun validation(){
        if (!binding.etHubunganAhliWaris.text.isNullOrEmpty()&& binding.etNamaAhliWaris.length() >= 3 && !binding.etAlamat.text.isNullOrEmpty()
                &&!binding.etKodePos.text.isNullOrEmpty()&&binding.etIsiNoHp.length() > 9){
            binding.btnSubmit.isEnabled = true
            binding.btnSubmit.setTextColor(GeneralHelper.getColor(R.color.whiteColor))
        }else{
            binding.btnSubmit.isEnabled = false
            binding.btnSubmit.setTextColor(GeneralHelper.getColor(R.color.neutral_light60))
        }
    }

    override fun onActivityResult(requestCode: Int, resultCode: Int, data: Intent?) {
        super.onActivityResult(requestCode, resultCode, data)

        if (requestCode == Constant.REQ_PAYMENT) {
            if (resultCode == RESULT_OK) {
                this.setResult(RESULT_OK)
                finish()
            }else if (resultCode == RESULT_CANCELED && data != null) {
                this.setResult(RESULT_CANCELED, data)
                finish()
            }
        }
    }
}