package id.co.bri.brimo.ui.activities.ibbiz;

import android.app.Activity;
import android.content.Intent;
import android.os.Bundle;
import androidx.annotation.Nullable;
import androidx.fragment.app.Fragment;
import androidx.fragment.app.FragmentTransaction;
import java.util.Objects;
import javax.inject.Inject;
import id.co.bri.brimo.R;
import id.co.bri.brimo.adapters.baseadapter.ViewPagerAdapter;
import id.co.bri.brimo.contract.IPresenter.ibbiz.IOnboardingIbbizPresenter;
import id.co.bri.brimo.contract.IView.ibbiz.IOnboardingIbbizView;
import id.co.bri.brimo.databinding.ActivityOnboardingIbbizBinding;
import id.co.bri.brimo.domain.config.Constant;
import id.co.bri.brimo.domain.helpers.GeneralHelper;
import id.co.bri.brimo.models.apimodel.response.AccountIbbizResponse;
import id.co.bri.brimo.models.apimodel.response.ProductIbbizResponse;
import id.co.bri.brimo.ui.activities.base.BaseActivity;
import id.co.bri.brimo.ui.activities.bukarekening.TabunganActivity;
import id.co.bri.brimo.ui.customviews.dialog.DialogExitCustom;
import id.co.bri.brimo.ui.fragments.ItemViewPagerFragment;

public class OnboardingIbbizActivity extends BaseActivity implements
        IOnboardingIbbizView,
        DialogExitCustom.DialogDefaultListener {

    private ActivityOnboardingIbbizBinding binding;

    @Inject
    IOnboardingIbbizPresenter<IOnboardingIbbizView> presenter;

    private static final int AUTO_SCROLL_THRESHOLD_IN_MILLI = 3500;

    private static ProductIbbizResponse productIbbizRes;

    private static final String TAG_TITLE = "title";
    private static final String TAG_DESC = "desc";
    private static final String TAG_IMAGE = "image";

    public static void launchIntent(Activity caller, ProductIbbizResponse productResponse) {
        Intent intent = new Intent(caller, OnboardingIbbizActivity.class);
        productIbbizRes = productResponse;
        caller.startActivityForResult(intent, Constant.REQ_REGIS);
    }

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        binding = ActivityOnboardingIbbizBinding.inflate(getLayoutInflater());
        setContentView(binding.getRoot());

        GeneralHelper.setToolbar(this, binding.toolbar.toolbar, GeneralHelper.getString(R.string.toolbar_qlola));

        injectDependency();
        setupViews();
    }

    private void injectDependency() {
        getActivityComponent().inject(this);
        if (presenter != null) {
            presenter.setView(this);
            presenter.start();
            presenter.setUrl(GeneralHelper.getString(R.string.url_ibbiz_account_list));
        }
    }

    private void setupViews() {
        ViewPagerAdapter vpIbbizAdapter = new ViewPagerAdapter(getSupportFragmentManager());
        ProductIbbizResponse.ProductBrief productBrief;

        for (int i = 0; i < productIbbizRes.getProductBriefs().size(); i++) {
            productBrief = productIbbizRes.getProductBriefs().get(i);
            vpIbbizAdapter.addFragment(addFragment(productBrief.getTitle(), productBrief.getDescription(), productBrief.getImageUrl()), "");
        }

        binding.vpIbbiz.setAdapter(vpIbbizAdapter);
        binding.vpIbbiz.startAutoScroll();
        binding.vpIbbiz.setInterval(AUTO_SCROLL_THRESHOLD_IN_MILLI);
        binding.vpIbbiz.setAutoScrollDurationFactor(6);
        binding.vpIbbiz.setCycle(true);
        binding.vpIbbiz.setStopScrollWhenTouch(true);

        binding.dotsIndicator.setViewPager(binding.vpIbbiz);

        binding.btnDaftar.setOnClickListener(v -> presenter.onGetDataAccount());
    }

    /**
     * Use this method if you want to send data from activity to specific fragment
     */
    private Fragment addFragment(String title, String desc, String image) {
        Bundle bundle = new Bundle();
        bundle.putString(TAG_TITLE, title);
        bundle.putString(TAG_DESC, desc);
        bundle.putString(TAG_IMAGE, image);
        ItemViewPagerFragment fragment = new ItemViewPagerFragment();
        fragment.setArguments(bundle);
        return fragment;
    }

    /**
     * method punya rekening bisnis
     *
     * @param accountResponse
     */
    @Override
    public void onSuccessGetAccount(AccountIbbizResponse accountResponse) {
        RekeningIbbizActivity.launchIntent(this, accountResponse);
    }

    /**
     * method tidak punya rekening bisnis
     */
    @Override
    public void onSuccessGoRek() {
        DialogExitCustom dialogExitCustom = new DialogExitCustom(this, GeneralHelper.getString(R.string.title_dialog_open_rekening),
                GeneralHelper.getString(R.string.desc_dialog_open_rekening),
                GeneralHelper.getString(R.string.batal2), GeneralHelper.getString(R.string.buka_rek_bisnis));
        FragmentTransaction ft = getSupportFragmentManager().beginTransaction();
        ft.add(dialogExitCustom, null);
        ft.commitAllowingStateLoss();
    }

    @Override
    public void onClickYes() {
        TabunganActivity.Companion.launchIntent(this);
    }

    @Override
    protected void onActivityResult(int requestCode, int resultCode, @Nullable Intent data) {
        super.onActivityResult(requestCode, resultCode, data);

        if (requestCode == Constant.REQ_REGIS && resultCode == RESULT_OK && data != null) {
            this.setResult(RESULT_OK, data);
            this.finish();
        } else if (requestCode == Constant.REQ_REGIS && resultCode == RESULT_CANCELED && data != null) {
            GeneralHelper.showSnackBar(Objects.requireNonNull(this).findViewById(R.id.content), data.getStringExtra(Constant.TAG_ERROR_MESSAGE));
        }
    }

    @Override
    protected void onDestroy() {
        super.onDestroy();
        binding = null;
    }
}