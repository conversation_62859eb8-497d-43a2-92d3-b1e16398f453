package id.co.bri.brimo.models.apimodel.request;

import com.google.gson.annotations.Expose;
import com.google.gson.annotations.SerializedName;

public class KonfirmasiTopupImpianRequest {
    @SerializedName("parent_number")
    @Expose
    private String parentNumber;
    @SerializedName("child_number")
    @Expose
    private String childNumber;
    @SerializedName("amount")
    @Expose
    private Long amount;

    public KonfirmasiTopupImpianRequest(String parentNumber, String childNumber, Long amount) {
        this.parentNumber = parentNumber;
        this.childNumber = childNumber;
        this.amount = amount;
    }

    public String getParentNumber() {
        return parentNumber;
    }

    public void setParentNumber(String parentNumber) {
        this.parentNumber = parentNumber;
    }

    public String getChildNumber() {
        return childNumber;
    }

    public void setChildNumber(String childNumber) {
        this.childNumber = childNumber;
    }

    public Long getAmount() {
        return amount;
    }

    public void setAmount(Long amount) {
        this.amount = amount;
    }
}