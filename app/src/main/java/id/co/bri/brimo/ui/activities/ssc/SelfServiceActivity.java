package id.co.bri.brimo.ui.activities.ssc;

import android.Manifest;
import android.app.Activity;
import android.content.Intent;
import android.content.pm.PackageManager;
import android.net.Uri;
import android.os.Build;
import android.os.Bundle;
import android.util.Log;
import android.util.TypedValue;
import android.view.View;
import android.widget.EditText;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.annotation.RequiresApi;
import androidx.core.app.ActivityCompat;
import androidx.core.content.res.ResourcesCompat;

import id.co.bri.brimo.R;
import id.co.bri.brimo.contract.IPresenter.ssc.ISelfServicePresenter;
import id.co.bri.brimo.contract.IView.ssc.ISelfServiceView;
import id.co.bri.brimo.databinding.ActivitySelfServiceBinding;
import id.co.bri.brimo.domain.config.Constant;
import id.co.bri.brimo.domain.helpers.GeneralHelper;
import id.co.bri.brimo.models.apimodel.request.RevokeSessionRequest;
import id.co.bri.brimo.models.apimodel.response.TokenVoipResponse;
import id.co.bri.brimo.models.apimodel.response.voip.CategoryVoipRes;
import id.co.bri.brimo.ui.activities.KontakKamiActivity;
import id.co.bri.brimo.ui.activities.PusatBantuanActivity;
import id.co.bri.brimo.ui.activities.voip.CategoryVoipActivity;
import id.co.bri.brimo.ui.activities.voip.VoipActivity;
import id.co.bri.brimo.ui.activities.base.BaseActivity;
import id.co.bri.brimo.ui.customviews.dialog.DialogExitCustom;

import android.Manifest;
import android.app.Activity;
import android.content.Intent;
import android.content.pm.PackageManager;
import android.net.Uri;
import android.os.Build;
import android.os.Bundle;
import android.util.Log;
import android.util.TypedValue;
import android.view.View;
import android.widget.EditText;

import javax.inject.Inject;

public class SelfServiceActivity extends BaseActivity implements
        View.OnClickListener,
        ISelfServiceView {

    private ActivitySelfServiceBinding binding;

    @Inject
    ISelfServicePresenter<ISelfServiceView> presenter;

    protected static final String[] PermissionSelfSevice = {
            Manifest.permission.WRITE_EXTERNAL_STORAGE,
            Manifest.permission.READ_EXTERNAL_STORAGE,
            Manifest.permission.RECORD_AUDIO,
            Manifest.permission.CALL_PHONE
    };

    @RequiresApi(api = Build.VERSION_CODES.S)
    protected static final String[] PermissionSelfSevice32 = {
            Manifest.permission.WRITE_EXTERNAL_STORAGE,
            Manifest.permission.READ_EXTERNAL_STORAGE,
            Manifest.permission.RECORD_AUDIO,
            Manifest.permission.CALL_PHONE,
            Manifest.permission.READ_PHONE_STATE,
            Manifest.permission.BLUETOOTH_CONNECT,
            Manifest.permission.BLUETOOTH_SCAN
    };

    @RequiresApi(api = Build.VERSION_CODES.TIRAMISU)
    protected static final String[] PermissionSelfService33 = {
            Manifest.permission.READ_MEDIA_AUDIO,
            Manifest.permission.RECORD_AUDIO,
            Manifest.permission.CALL_PHONE,
            Manifest.permission.READ_PHONE_STATE,
            Manifest.permission.BLUETOOTH_CONNECT,
            Manifest.permission.BLUETOOTH_SCAN
    };

    public static String[] permissions() {
        String[] permission;
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU)
            permission = PermissionSelfService33;
        else if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.S)
            permission = PermissionSelfSevice32;
        else permission = PermissionSelfSevice;

        return permission;
    }

    protected String[] permissionsService = permissions();

    protected static final int PERMISSIONS_ALL = 1;

    private String userName;
    private String tokenKey;

    private String session;

    public static void launchIntent(Activity caller) {
        Intent intent = new Intent(caller, SelfServiceActivity.class);
        caller.startActivityForResult(intent, Constant.REQ_NON_PAYMENT);
    }

    public static void launchIntentClearTop(Activity caller) {
        Intent intent = new Intent(caller, SelfServiceActivity.class);
        intent.setFlags(Intent.FLAG_ACTIVITY_CLEAR_TOP | Intent.FLAG_ACTIVITY_SINGLE_TOP);
        caller.startActivityForResult(intent, Constant.REQ_NON_PAYMENT);
    }

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        binding = ActivitySelfServiceBinding.inflate(getLayoutInflater());
        setContentView(binding.getRoot());

        GeneralHelper.setToolbar(this, binding.toolbar.toolbar, GeneralHelper.getString(R.string.help_center));

        injectDependency();
        setupView();

        binding.cvPengajuanTerakhir.setOnClickListener(this);
        binding.cvKeluhanTrx.setOnClickListener(this);
        binding.cvTentangBrimo.setOnClickListener(this);
        binding.cvProdukLainnya.setOnClickListener(this);
        binding.layoutKontakLainnya.setOnClickListener(this);
        binding.layoutInternetCall.setOnClickListener(this);
        binding.layoutPhoneCall.setOnClickListener(this);
        binding.layoutSabrina.setOnClickListener(this);

        checkPermission();
    }

    private void checkPermission() {
        if (!hasPermissions(this, permissionsService)) {
            ActivityCompat.requestPermissions(this, permissionsService, PERMISSIONS_ALL);
        }
    }

    private void injectDependency() {
        getActivityComponent().inject(this);
        if (presenter != null) {
            presenter.setView(this);
            presenter.setUrlListVoip(GeneralHelper.getString(R.string.url_get_list_voip));
            presenter.start();
        }
    }

    private void setupView() {
        EditText editText = binding.searchview.findViewById(R.id.search_src_text);
        editText.setHintTextColor(GeneralHelper.getColor(R.color.black3));
        editText.setTypeface(ResourcesCompat.getFont(this, R.font.avenir_next_medium));
        editText.setTextSize(TypedValue.COMPLEX_UNIT_DIP, 16);
        editText.setHintTextColor(GeneralHelper.getColor(R.color.colorStatusGagal));
    }

    @Override
    public void onClick(View v) {
        switch (v.getId()) {
            case R.id.cv_pengajuan_terakhir:
                ListComplainActivity.launchIntent(this, Constant.CIAType.TYPE_COMPLAINT_IN_APPS_GENERAL);
                break;
            case R.id.cv_keluhan_trx:
                PengaduanTransaksiActivity.launchIntent(this, GeneralHelper.getString(R.string.toolbar_pengaduang_trx), false);
                break;
            case R.id.cv_tentang_brimo:
                PusatBantuanActivity.launchIntent(this);
                break;
            case R.id.cv_produk_lainnya:
                PengaduanTransaksiActivity.launchIntent(this, GeneralHelper.getString(R.string.title_lainnya), true);
                break;
            case R.id.layout_kontak_lainnya:
                KontakKamiActivity.launchIntent(this);
                break;
            case R.id.layout_internet_call:
                presenter.getListVoip();
                break;
            case R.id.layout_phone_call:
                String phoneNumber = "1500017";
                Intent phone = new Intent(Intent.ACTION_CALL, Uri.parse("tel:" + phoneNumber));
                if (ActivityCompat.checkSelfPermission(this, Manifest.permission.CALL_PHONE) != PackageManager.PERMISSION_GRANTED) {
                    return;
                }
                startActivity(phone);
                break;
            case R.id.layout_sabrina:
                String playstoreWa = "https://play.google.com/store/apps/details?id=com.whatsapp&hl=in";
                if (isAppInstalled("com.whatsapp")) {
                    try {
                        String text = "";
                        String toNumber = "628121214017"; // prod
//                String toNumber = "628111440177"; // dev

                        Intent intent = new Intent(Intent.ACTION_VIEW);
                        intent.setData(Uri.parse("http://api.whatsapp.com/send?phone=" + toNumber + "&text=" + text));
                        startActivity(intent);
                    } catch (Exception e) {
                        if (!GeneralHelper.isProd())
                            Log.d("TestWA", "onClick: " + e.getMessage());
                    }
                } else {
                    startActivity(new Intent(Intent.ACTION_VIEW, Uri.parse(playstoreWa)));
                }
                break;
            default:
                break;
        }
    }

    private boolean isAppInstalled(String packageName) {
        boolean isInstall;
        try {
            getPackageManager().getPackageInfo(packageName, PackageManager.GET_ACTIVITIES);
            isInstall = true;
        } catch (PackageManager.NameNotFoundException ignored) {
            isInstall = false;
        }
        return isInstall;
    }

    @Override
    public void onRequestPermissionsResult(int requestCode, @NonNull String[] permissions, @NonNull int[] grantResults) {
        super.onRequestPermissionsResult(requestCode, permissions, grantResults);

        boolean grantAll = true;
        if (grantResults.length > 0) {
            for (int i = 0; i < grantResults.length; i++) {
                if (grantResults[i] != PackageManager.PERMISSION_GRANTED) {
                    grantAll = false;
                    break;
                }
            }
        }

        if (!grantAll) {
            showAlertFinish(getString(R.string.notes_need_permission));
        } else {
            checkPermission();
        }
    }

    @Override
    protected void onActivityResult(int requestCode, int resultCode, @Nullable Intent data) {
        super.onActivityResult(requestCode, resultCode, data);

        if (requestCode == Constant.REQ_NON_PAYMENT) {
            if (resultCode == RESULT_OK) {
                this.setResult(RESULT_OK);
                this.finish();
            } else {
                if (data != null && data.getStringExtra(Constant.TAG_VALUE) == null)
                    showSnackbarErrorMessage(data.getStringExtra(Constant.TAG_ERROR_MESSAGE), ALERT_ERROR, this, false);
            }
        }
    }

    @Override
    public void onSuccessGetListVoip(CategoryVoipRes response) {
        CategoryVoipActivity.launchIntent(this, response);
    }

    @Override
    protected void onDestroy() {
        binding = null;
        super.onDestroy();
    }
}