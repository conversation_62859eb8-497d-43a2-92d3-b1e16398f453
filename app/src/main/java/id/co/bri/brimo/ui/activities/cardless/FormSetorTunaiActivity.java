package id.co.bri.brimo.ui.activities.cardless;

import android.app.Activity;
import android.content.Intent;
import android.os.Build;
import android.os.Bundle;
import android.os.SystemClock;
import android.view.View;
import android.view.Window;
import android.view.WindowManager;
import androidx.annotation.Nullable;
import com.ethanhua.skeleton.Skeleton;
import com.ethanhua.skeleton.SkeletonScreen;
import java.util.List;
import javax.inject.Inject;
import id.co.bri.brimo.R;
import id.co.bri.brimo.contract.IPresenter.cardless.IFormSetorTunaiPresenter;
import id.co.bri.brimo.contract.IView.cardless.IFormSetorTunaiView;
import id.co.bri.brimo.databinding.ActivityFormSetorTunaiBinding;
import id.co.bri.brimo.domain.config.Constant;
import id.co.bri.brimo.domain.helpers.GeneralHelper;
import id.co.bri.brimo.models.AccountModel;
import id.co.bri.brimo.models.ParameterKonfirmasiModel;
import id.co.bri.brimo.models.apimodel.response.PaymentSetorResponse;
import id.co.bri.brimo.models.apimodel.response.SetorTunaiResponse;
import id.co.bri.brimo.ui.activities.LupaPinActivity;
import id.co.bri.brimo.ui.activities.LupaPinFastActivity;
import id.co.bri.brimo.ui.activities.base.BaseActivity;
import id.co.bri.brimo.ui.fragments.ListRekeningFragment;
import id.co.bri.brimo.ui.fragments.PinFragment;
import id.co.bri.brimo.ui.fragments.SumberDanaFragment;

public class FormSetorTunaiActivity extends BaseActivity implements View.OnClickListener, IFormSetorTunaiView, SumberDanaFragment.SelectSumberDanaInterface, PinFragment.SendPin {

    private ActivityFormSetorTunaiBinding binding;

    @Inject
    IFormSetorTunaiPresenter<IFormSetorTunaiView> setorPresenter;


    protected static ParameterKonfirmasiModel mParameterKonfirmasiModel;
    protected static String errorMessage = null;

    AccountModel accountDefaultModel;
    int counter = 0;

    private List<AccountModel> mListAccountModel;
    private List<Integer> mListFailed;
    private SetorTunaiResponse mSetorTunai;
    protected SkeletonScreen skeletonScreen1;


    public static void launchIntent(Activity caller, boolean fromFastMenu) {
        Intent intent = new Intent(caller, FormSetorTunaiActivity.class);
        caller.startActivityForResult(intent, Constant.REQ_PAYMENT);
        isFromFastMenu = fromFastMenu;
    }
    public static void launchIntentReciept(Activity caller, boolean fromFastMenu) {
        isFromFastMenu = fromFastMenu;
        Intent intent = new Intent(caller, FormSetorTunaiActivity.class);
        caller.startActivityForResult(intent, Constant.REQ_PAYMENT);
        caller.finish();
    }

    public static void launchIntentReciept(Activity caller, String errorMessageL, boolean fromFastMenu) {
        isFromFastMenu = fromFastMenu;
        errorMessage = errorMessageL;
        Intent intent = new Intent(caller, FormSetorTunaiActivity.class);
        caller.startActivityForResult(intent, Constant.REQ_PAYMENT);
        caller.finish();
    }


    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        binding = ActivityFormSetorTunaiBinding.inflate(getLayoutInflater());
        setContentView(binding.getRoot());
        GeneralHelper.setToolbar(this, binding.toolbar.toolbar, GeneralHelper.getString(R.string.txt_setor_tunai));

        injectDependency();

        binding.btnSubmit.setOnClickListener(this);
        binding.itemLayoutBackground.setOnClickListener(this);
        binding.tvInfoLimit.setOnClickListener(null);
        binding.tvInfoLimit.setTextColor(GeneralHelper.getColor(R.color.colorOppacityText));

        validasiButton();
        cekErrorMessage();

        if (Build.VERSION.SDK_INT >= 21) {
            Window window = getWindow();
            window.addFlags(WindowManager.LayoutParams.FLAG_DRAWS_SYSTEM_BAR_BACKGROUNDS);
            window.clearFlags(WindowManager.LayoutParams.FLAG_TRANSLUCENT_STATUS);
            window.setStatusBarColor(getResources().getColor(R.color.toolbar_blue));
        }
        skeletonScreen1 = Skeleton.bind(binding.itemLayoutBackground)
                .shimmer(true)
                .angle(20)
                .duration(1200)
                .load(R.layout.skeleton_pilih_sumber_dana)
                .show();
    }

    private void injectDependency() {
        getActivityComponent().inject(this);
        if (setorPresenter != null) {
            setorPresenter.setView(this);
            if (isFromFastMenu){
                setorPresenter.setFormUrl(GeneralHelper.getString(R.string.url_fm_cash_deposit_form));
                setorPresenter.setPaymentUrl(GeneralHelper.getString(R.string.url_fm_cash_deposit_pay));
                setorPresenter.start();
                setorPresenter.getFormSetorFm(isFromFastMenu);
            }else{
                setorPresenter.setFormUrl(GeneralHelper.getString(R.string.url_form_setor_tunai));
                setorPresenter.setPaymentUrl(GeneralHelper.getString(R.string.url_cash_deposit_pay));
                setorPresenter.start();
                setorPresenter.getFormSetor();
            }

        }
    }

    @Override
    public void onException93(String message) {
        showSnackbarErrorMessage(message, ALERT_ERROR, this, false);
    }


    @Override
    public void onClick(View v) {
        if (SystemClock.elapsedRealtime() - mLastClickTime < 1000) {
            return;
        }
        mLastClickTime = SystemClock.elapsedRealtime();
        int id = v.getId();
        switch (id) {
            case R.id.tv_info_limit:
                if (mSetorTunai.getInfo_limit() != null){
                    InformasiLimitSetorTunaiActivity.launchIntent(this, mSetorTunai.getInfo_limit());
                }
                break;
            case R.id.btnSubmit:
                PinFragment pinFragment = new PinFragment(this, this);
                pinFragment.show();
                break;
            case R.id.item_layout_background:
                counter++;
                if (mListAccountModel == null) {
                    GeneralHelper.showToast(this, GeneralHelper.getString(R.string.you_dont_have_any_accounts_yet));
                } else {
                    ListRekeningFragment fragmentSumberDana = new ListRekeningFragment(mListAccountModel, this, counter, mListFailed);
                    fragmentSumberDana.show(getSupportFragmentManager(), Constant.TAG_PICK_ACCOUNT);
                }
                break;
            default:
                break;

        }
    }

    public void validasiButton() {
        if (binding.tvNorek.getText().toString().equalsIgnoreCase("-")){
            binding.btnSubmit.setEnabled(false);
            binding.btnSubmit.setAlpha(0.3f);
        }else{
            binding.btnSubmit.setEnabled(true);
            binding.btnSubmit.setAlpha(1);
        }
    }

    @Override
    protected void onActivityResult(int requestCode, int resultCode, @Nullable Intent data) {
        super.onActivityResult(requestCode, resultCode, data);
        if (requestCode == Constant.REQ_PAYMENT) {
            if (resultCode == RESULT_OK) {
                setResult(RESULT_OK, data);
                this.finish();
            } else {
                if (data != null) {
                    errorMessage = data.getStringExtra(Constant.TAG_ERROR_MESSAGE);
                    cekErrorMessage();
                }
            }
        } else if (requestCode == Constant.REQ_FORGET_PIN) {
            if (resultCode == RESULT_OK) {
                this.setResult(RESULT_OK);
                this.finish();
            }
        }
    }


    @Override
    public int getDefaultIconResource() {
        return R.drawable.bri;
    }

    @Override
    public long getAmount() {
        return 0;
    }

    @Override
    public void onException12(String message) {
        // do nothing
    }

    @Override
    public void showInputError(String message) {
        // do nothing
    }

    @Override
    public void setDefaultSaldo(double defaultSaldo, String stringSaldo, String akunDefault) {
        // do nothing
    }

    @Override
    public int getLayoutResource() {
        return 0;
    }

    @Override
    public void setTextForm() {
        // do nothing
    }

    @Override
    public String getTitleBar() {
        return null;
    }



    protected void setupAccount() {
        //List Account
        accountDefaultModel = new AccountModel();
        if (mSetorTunai.getAccountList().size() > 0) {
            mListAccountModel = mSetorTunai.getAccountList();
        }

        for (AccountModel accountModel : mListAccountModel) {
            if (accountModel.getIsDefault() == 1) {
                this.accountDefaultModel = accountModel;
                break;
            } else {
                this.accountDefaultModel = mListAccountModel.get(0);
            }
        }
        if (accountDefaultModel != null) {

            if (accountDefaultModel.getAcoountString() != null) {
                binding.tvNorek.setText(accountDefaultModel.getAcoountString());
            }

            if (accountDefaultModel.getName() != null) {
                binding.tvInisial.setText(GeneralHelper.formatInitialName(accountDefaultModel.getName()));
            }

            validasiButton();
        }

    }

    @Override
    public void onSuccessGetAccount(SetorTunaiResponse setorTunaiResponse) {
        if (setorTunaiResponse.getInfo_limit() != null){
            binding.tvInfoLimit.setOnClickListener(this);
            binding.tvInfoLimit.setTextColor(GeneralHelper.getColor(R.color.colorTextBlueBri));
        }else{
            binding.tvInfoLimit.setOnClickListener(null);
            binding.tvInfoLimit.setTextColor(GeneralHelper.getColor(R.color.colorOppacityText));
        }
        mSetorTunai = setorTunaiResponse;
        setupAccount();
    }

    @Override
    public void onSuccessGetSetorTunai(PaymentSetorResponse paymentSetorResponse) {
        SetorTunaiActivity.launchIntent(this, paymentSetorResponse, isFromFastMenu);
    }

    @Override
    public void onTokenActive(PaymentSetorResponse statusSetorResponse) {
        SetorTunaiActivity.launchIntent(this, statusSetorResponse, isFromFastMenu);
    }


    @Override
    public void onException(String message) {
        if (GeneralHelper.isContains(Constant.LIST_TYPE_GAGAL, message))
            GeneralHelper.showDialogGagalBack(this, message);
        else
            showSnackbarErrorMessage(message, -2, this, false);

    }

    /*
     *  method untuk menampilkan error snackbar dari Intent aktivity inquiry dan konfirmasi
     */
    protected void cekErrorMessage() {
        if (errorMessage != null) {
            //menampilkan snacknar error
            showSnackbarErrorMessage(errorMessage, ALERT_ERROR, this, false);

            //clear error message
            errorMessage = null;
        }
    }

    @Override
    protected void onDestroy() {
        setorPresenter.stop();
        binding = null;
        super.onDestroy();
    }
    @Override
    public void onBackPressed() {
        setorPresenter.stop();
        super.onBackPressed();
    }

    @Override
    public void hideProgress() {
        super.hideProgress();
        skeletonScreen1.hide();
    }
    @Override
    public void showProgress() {
        super.showProgress();
        skeletonScreen1.show();
    }


    @Override
    public void onSelectSumberDana(AccountModel bankModel) {
        accountDefaultModel = bankModel;
        validasiButton();

        binding.tvNorek.setText(accountDefaultModel.getAcoountString());
        binding.tvInisial.setText(GeneralHelper.formatInitialName(bankModel.getName()));
    }
    @Override
    public void onSendFailedList(List<Integer> list) {
        this.mListFailed = list;
    }

    @Override
    public void onSendPinComplete(String pin) {
        if (setorPresenter != null) {
            String pfmCat = "106";
            setorPresenter.getPayment(
                    accountDefaultModel.getAcoount(),
                    pin,
                    pfmCat,"", isFromFastMenu
            );
        }

    }

    @Override
    public void onLupaPin() {
        if(isFromFastMenu) LupaPinFastActivity.launchIntent(this);
        else LupaPinActivity.launchIntent(this);
    }

}