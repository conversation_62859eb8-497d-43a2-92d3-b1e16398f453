package id.co.bri.brimo.ui.activities.simpedes;

import android.Manifest;
import android.app.Activity;
import android.content.Intent;
import android.content.pm.PackageManager;
import android.graphics.Bitmap;
import android.os.Build;
import android.os.Bundle;
import android.util.Log;
import android.util.Size;
import android.view.Surface;

import androidx.annotation.NonNull;
import androidx.annotation.RequiresApi;
import androidx.camera.core.CameraSelector;
import androidx.camera.core.ImageCapture;
import androidx.camera.core.ImageCaptureException;
import androidx.camera.core.Preview;
import androidx.camera.lifecycle.ProcessCameraProvider;
import androidx.core.app.ActivityCompat;
import androidx.core.content.ContextCompat;

import com.google.common.util.concurrent.ListenableFuture;

import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.concurrent.ExecutionException;

import id.co.bri.brimo.R;
import id.co.bri.brimo.databinding.ActivityKameraBrifineBinding;
import id.co.bri.brimo.domain.config.Constant;
import id.co.bri.brimo.domain.helpers.GeneralHelper;
import id.co.bri.brimo.domain.helpers.image.ImageHelper;
import id.co.bri.brimo.ui.activities.base.BaseActivity;

public class KameraBrifineActivity extends BaseActivity {

    private ActivityKameraBrifineBinding binding;

    private static final String TAG = "KameraBrifineActivity";

    Bitmap resizedbitmap;
    ListenableFuture<ProcessCameraProvider> cameraProviderFuture;

    private File tempDir = null; //directori image
    private File savedPhoto = null;
    private ImageCapture imageCapture = null;
    private ImageHelper imageHelper = new ImageHelper(this);

    final String[] permissions_camera = new String[]{
            Manifest.permission.CAMERA,
            Manifest.permission.WRITE_EXTERNAL_STORAGE,
            Manifest.permission.READ_EXTERNAL_STORAGE
    };

    @RequiresApi(api = Build.VERSION_CODES.TIRAMISU)
    static String[] permissions_camera_33 = {
            Manifest.permission.READ_MEDIA_IMAGES,
            Manifest.permission.CAMERA,
    };

    private String[] getPermissionCamera() {
        String[] permission;
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU) {
            permission = permissions_camera_33;
        } else {
            permission = permissions_camera;
        }
        return permission;
    }

    protected static final int PERMISSIONS_ALL_BRIFINE = 1;

    public static void launchIntent(Activity caller) {
        Intent intent = new Intent(caller, KameraBrifineActivity.class);
        caller.startActivityForResult(intent, Constant.REQ_IMAGE);
    }

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        binding = ActivityKameraBrifineBinding.inflate(getLayoutInflater());
        setContentView(binding.getRoot());

        startCamera();

        binding.capture.setOnClickListener(v -> {
            getPermission();
        });

    }

    public static Bitmap resizeImage(Bitmap realImage, float maxImageSize,
                                     boolean filter) {
        float ratio = Math.min(
                maxImageSize / realImage.getWidth(),
                maxImageSize / realImage.getHeight());
        int width = Math.round(ratio * realImage.getWidth());
        int height = Math.round(ratio * realImage.getHeight());

        Bitmap newBitmap = Bitmap.createScaledBitmap(realImage, width,
                height, filter);
        return newBitmap;
    }

    private void generateSavedImage() {
        String timeNow = new SimpleDateFormat("yyyyMMddHHmmss").format(new Date());
        String fileName = timeNow + "_ktp.png";

        prepareImageDirectory();
        if (tempDir != null) {
            savedPhoto = new File(tempDir, fileName);
        }
    }


    private void prepareImageDirectory() {
        tempDir = new File(getCacheDir(), Constant.TAG_START_NAME);
        if (!tempDir.exists()) {
            tempDir.mkdirs(); // membuat direktori jika belum ada
        }
    }

    private void startCamera() {
        cameraProviderFuture = ProcessCameraProvider.getInstance(this);
        cameraProviderFuture.addListener(() -> {
            // Used to bind the lifecycle of cameras to the lifecycle owner
            ProcessCameraProvider cameraProvider;
            try {
                cameraProvider = cameraProviderFuture.get();

                // Select back camera as a default
                CameraSelector cameraSelector = CameraSelector.DEFAULT_BACK_CAMERA;

                imageCapture = new ImageCapture.Builder()
                        .setTargetRotation(Surface.ROTATION_0)
                        .setJpegQuality(100)
                        .setTargetResolution(new Size(1080, 1920))
                        .build();

                // Preview
                Preview preview = new Preview.Builder().build();
                preview.setSurfaceProvider(binding.camera.getSurfaceProvider());

                try {
                    // Unbind use cases before rebinding
                    cameraProvider.unbindAll();
                    // Bind use cases to camera
                    cameraProvider.bindToLifecycle(this, cameraSelector, preview, imageCapture);
                } catch (Exception exc) {
                    if (!GeneralHelper.isProd())
                        Log.e("TestCamera", "Use case binding failed", exc);
                }
            } catch (ExecutionException | InterruptedException e) {
                if (!GeneralHelper.isProd()) {
                    Log.e(TAG, "startCamera: ", e);
                }
            }
        }, ContextCompat.getMainExecutor(this));
    }

    private void getPermission() {
        if (hasPermissions(this, getPermissionCamera())) {
            takePhoto();
        } else {
            ActivityCompat.requestPermissions(this, getPermissionCamera(), 1);
        }
    }

    private void takePhoto() {
        // Get a stable reference of the modifiable image capture use case
        if (imageCapture == null) {
            return;
        }

        //initialize image
        generateSavedImage();

        ImageCapture.OutputFileOptions outputFileOptions = new ImageCapture.OutputFileOptions.Builder(savedPhoto).build();

        // Set up image capture listener, which is triggered after photo has been taken
        imageCapture.takePicture(
                outputFileOptions,
                ContextCompat.getMainExecutor(this),
                new ImageCapture.OnImageSavedCallback() {
                    @Override
                    public void onImageSaved(ImageCapture.OutputFileResults outputFileResults) {
                        Bitmap bitmapKtp = imageHelper.outputFileResultsToBitmap(outputFileResults);

                        if (bitmapKtp != null) {
                            FileOutputStream outputStream = null;

                            try {
                                resizedbitmap = Bitmap.createBitmap(bitmapKtp, 0, Math.round((bitmapKtp.getHeight() / 3)), bitmapKtp.getWidth() - 20, 600);

                                outputStream = new FileOutputStream(savedPhoto.getPath());
                                outputStream.write(imageHelper.bitmapToByteArray(resizedbitmap));
                                outputStream.close();

                                Bitmap newResize = resizeImage(resizedbitmap, 1000, true);

                                File deletePicture = new File(savedPhoto.getPath());

                                if (deletePicture.delete()) {
                                    Intent resultIntent = new Intent();
                                    resultIntent.putExtra(Constant.IMAGEKTP, imageHelper.encodeImage(newResize));
                                    setResult(Activity.RESULT_OK, resultIntent);
                                    finish();
                                }
                            } catch (IOException io) {
                                if (!GeneralHelper.isProd()) {
                                    Log.e(TAG, "onImageSaved: ", io);
                                }
                            } finally {
                                if (outputStream != null) {
                                    try {
                                        outputStream.close();
                                    } catch (IOException io) {
                                        if (!GeneralHelper.isProd()) {
                                            Log.e(TAG, "onImageSaved: ", io);
                                        }
                                    }
                                }
                            }
                        } else {
                            // Handle the case where bitmapKtp is null
                            if (!GeneralHelper.isProd()) {
                                Log.e(TAG, "bitmapKtp is null");
                            }
                        }
                    }

                    @Override
                    public void onError(@NonNull ImageCaptureException exception) {
                        if (!GeneralHelper.isProd()) {
                            Log.d("TestCamera", "onError: " + exception);
                        }
                    }
                }
        );
    }

    @Override
    public void onRequestPermissionsResult(int requestCode, @NonNull String[] permissions, @NonNull int[] grantResults) {
        super.onRequestPermissionsResult(requestCode, permissions, grantResults);
        boolean grantAll = true;
        if (grantResults.length > 0) {
            for (int i = 0; i < grantResults.length; i++) {
                if (grantResults[i] != PackageManager.PERMISSION_GRANTED) {
                    grantAll = false;
                    break;
                }
            }
        }

        if (!grantAll) {
            showAlertFinish(getString(R.string.notes_need_permission));
        } else {
            takePhoto();
        }
    }

    @Override
    protected void onResume() {
        super.onResume();
        try {
            Thread.sleep(100);
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
        }
    }

    @Override
    protected void onDestroy() {
        super.onDestroy();
        binding = null;
    }
}