package id.co.bri.brimo.ui.activities.dplkrevamp

import android.app.Activity
import android.content.Intent
import android.os.Bundle
import android.util.Log
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView.LayoutManager
import com.ethanhua.skeleton.Skeleton
import com.ethanhua.skeleton.SkeletonScreen
import id.co.bri.brimo.R
import id.co.bri.brimo.adapters.dplkrevamp.OnboardReksadanaAdapter
import id.co.bri.brimo.contract.IPresenter.dplkrevamp.IOnboardingAutoPaymentRevampPresenter
import id.co.bri.brimo.contract.IView.dplkrevamp.IOnboardingAutoPaymentRevampView
import id.co.bri.brimo.databinding.ActivityOnboardingAutopaymentBinding
import id.co.bri.brimo.domain.config.Constant
import id.co.bri.brimo.domain.helpers.GeneralHelper
import id.co.bri.brimo.models.apimodel.request.dplkrevamp.InquiryAutoPaymentRequest
import id.co.bri.brimo.models.apimodel.response.dplkrevamp.BodyItem
import id.co.bri.brimo.models.apimodel.response.dplkrevamp.InquiryAutoPaymentResponse
import id.co.bri.brimo.models.apimodel.response.emas.InquiryOpenEmasResponse
import id.co.bri.brimo.ui.activities.base.BaseActivity
import id.co.bri.brimo.ui.activities.emas.InquiryRegisEmasActivity
import javax.inject.Inject

class OnboardingAutopaymentActivity :
    BaseActivity(),
    IOnboardingAutoPaymentRevampView{

    lateinit var binding : ActivityOnboardingAutopaymentBinding

    private var mAdapter : OnboardReksadanaAdapter? = null
    private var onBoardBody = listOf<BodyItem>()
    private lateinit var mResponse : InquiryAutoPaymentResponse

    @Inject
    lateinit var presenter: IOnboardingAutoPaymentRevampPresenter<IOnboardingAutoPaymentRevampView>
    private lateinit var skeletonScreen: SkeletonScreen
    private lateinit var skeletonDesc : SkeletonScreen
    private lateinit var skeletonImage : SkeletonScreen

    companion object{
        lateinit var dplkAccountNumber : String
        @JvmStatic
        fun launchIntent(caller: Activity, dplkAccount: String) {
            val intent = Intent(caller, OnboardingAutopaymentActivity::class.java)
            dplkAccountNumber = dplkAccount
            caller.startActivityForResult(intent, Constant.REQ_AUTO_PAYMENT)
        }
    }


    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        binding = ActivityOnboardingAutopaymentBinding.inflate(layoutInflater)
        setContentView(binding.root)
        setupView()
        injectDependency()
        initListener()
    }

    private fun initListener() {
        with(binding){
            btnSubmit.setOnClickListener {
                InquiryAutoPaymentActivity.launchIntent(this@OnboardingAutopaymentActivity,mResponse)
            }
        }
    }

    private fun injectDependency() {
        activityComponent.inject(this)
        presenter.view = this
        presenter.setUrlInquiryAddAutoPayment(getString(R.string.url_inquiry_add_auto_payment_dplk))
        presenter.getInquiryAddPayment(InquiryAutoPaymentRequest(dplkAccountNumber))
        presenter.start()
    }

    private fun setupView() {
        with(binding){
            GeneralHelper.setToolbarRevamp(this@OnboardingAutopaymentActivity,tbAutoPayment.toolbar, getString(R.string.tb_autopayment))

            mAdapter = OnboardReksadanaAdapter(this@OnboardingAutopaymentActivity,onBoardBody)
            binding.rvContent.layoutManager = LinearLayoutManager(this@OnboardingAutopaymentActivity,LinearLayoutManager.VERTICAL,false)
            binding.rvContent.adapter = mAdapter

            skeletonScreen = Skeleton.bind(rvContent)
                .adapter(mAdapter)
                .shimmer(true)
                .angle(3)
                .count(4)
                .frozen(false)
                .duration(1200)
                .load(R.layout.item_skeleton_onboard_autopayment_dplk)
                .show()

            skeletonDesc = Skeleton.bind(tvDesc)
                .shimmer(true)
                .angle(3)
                .duration(1200)
                .load(R.layout.item_skeleton_textview)
                .show()

            skeletonImage = Skeleton.bind(ivOnboard)
                .shimmer(true)
                .angle(3)
                .duration(1200)
                .load(R.layout.item_skeleton_iv_onboard)
                .show()


        }
    }

    override fun onSuccessInquiryAddAutoPayment(response: InquiryAutoPaymentResponse) {
        skeletonScreen.hide()
        skeletonDesc.hide()
        skeletonImage.hide()
        mResponse = response
        onBoardBody = mResponse.addAutopaymentCover!!.body
        binding.tvDesc.text = response.addAutopaymentCover!!.header!!.description
        GeneralHelper.loadImageUrl(
            this,
            response.addAutopaymentCover!!.header!!.iconPath,
            binding.ivOnboard,
            R.drawable.bri,
            0
        )

        mAdapter = OnboardReksadanaAdapter(this@OnboardingAutopaymentActivity,onBoardBody)
        binding.rvContent.layoutManager = LinearLayoutManager(this@OnboardingAutopaymentActivity,LinearLayoutManager.VERTICAL,false)
        binding.rvContent.adapter = mAdapter
    }

    override fun onException12(desc: String) {
        skeletonScreen.hide()
        skeletonDesc.hide()
        skeletonImage.hide()
        val intent = Intent()
        intent.putExtra(Constant.TAG_ERROR_MESSAGE,desc)
        setResult(RESULT_CANCELED,intent)
        finish()
    }

    override fun onActivityResult(requestCode: Int, resultCode: Int, data: Intent?) {
        super.onActivityResult(requestCode, resultCode, data)

        if (requestCode == Constant.REQ_AUTO_PAYMENT && data != null) {
            if (resultCode == RESULT_OK) {
                setResult(RESULT_OK, data)
                finish()
            } else if(resultCode == RESULT_CANCELED){
                setResult(RESULT_CANCELED, data)
                finish()
            } else {
                setResult(RESULT_CANCELED, data)
                finish()
            }
        }
    }

}