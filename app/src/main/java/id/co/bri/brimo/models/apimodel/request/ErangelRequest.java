package id.co.bri.brimo.models.apimodel.request;

import id.co.bri.brimo.security.AES;

public class ErangelRequest {

    public final static String SEQ_NUMB_KEY = "YnJpbW9fc2VxdW5jZV9udW1i";

    private String xRandomKey;
    private String xDeviceId;
    private String xDevice;
    private String xId;
    private String request;
    private String xSequenceKey;

    public boolean isDklC2() {
        return isDklC2;
    }

    public void setDklC2(boolean dklC2) {
        isDklC2 = dklC2;
    }

    private boolean isDklC2;

    public String getxRandomKey() {
        return xRandomKey;
    }

    public void setxRandomKey(String xRandomKey) {
        this.xRandomKey = xRandomKey;
    }

    public String getxDeviceId() {
        return xDeviceId;
    }

    public void setxDeviceId(String xDeviceId) {
        this.xDeviceId = xDeviceId;
    }

    public String getxDevice() {
        return xDevice;
    }

    public void setxDevice(String xDevice) {
        this.xDevice = xDevice;
    }

    public String getRequest() {
        return request;
    }

    public void setRequest(String request) {
        this.request = request;
    }

    public String getxId() {
        return xId;
    }

    public void setxId(String xId) {
        this.xId = xId;
    }

    public String getXSequenceKey() {
        return xSequenceKey;
    }

    public void setXSequenceKey(String xSequenceKey) {
        this.xSequenceKey = AES.encryptGcm(xSequenceKey, SEQ_NUMB_KEY);
    }
}