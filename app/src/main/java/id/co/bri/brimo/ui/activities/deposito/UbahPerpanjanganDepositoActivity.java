package id.co.bri.brimo.ui.activities.deposito;

import android.app.Activity;
import android.content.Intent;
import android.os.Build;
import android.os.Bundle;
import android.view.View;
import android.view.Window;
import android.view.WindowManager;
import com.google.gson.Gson;
import javax.inject.Inject;
import id.co.bri.brimo.R;
import id.co.bri.brimo.adapters.ListItemDepositoOptionAdapter;
import id.co.bri.brimo.contract.IPresenter.deposito.IUbahPerpanjanganDepositoPresenter;
import id.co.bri.brimo.contract.IView.deposito.IUbahPerpanjanganDepositoView;
import id.co.bri.brimo.databinding.ActivityUbahPerpanjanganDepositoBinding;
import id.co.bri.brimo.domain.config.Constant;
import id.co.bri.brimo.domain.helpers.GeneralHelper;
import id.co.bri.brimo.models.apimodel.request.UpdatePerpanjanganDepositoRequest;
import id.co.bri.brimo.models.apimodel.response.RenewalDepositoResponse;
import id.co.bri.brimo.ui.activities.LupaPinActivity;
import id.co.bri.brimo.ui.activities.LupaPinFastActivity;
import id.co.bri.brimo.ui.activities.base.BaseActivity;
import id.co.bri.brimo.ui.customviews.datepicker.CustomLinearLayoutManager;
import id.co.bri.brimo.ui.fragments.PinFragment;

public class UbahPerpanjanganDepositoActivity extends BaseActivity implements ListItemDepositoOptionAdapter.onClickItem, View.OnClickListener,PinFragment.SendPin, IUbahPerpanjanganDepositoView {

    private ActivityUbahPerpanjanganDepositoBinding binding;

    @Inject
    IUbahPerpanjanganDepositoPresenter<IUbahPerpanjanganDepositoView> presenter;

    private static String TAG_RESPONSE = "response";
    private static String TAG_ACCOUNT = "account";

    ListItemDepositoOptionAdapter adapter;

    RenewalDepositoResponse response;

    String currentRenewal;

    protected static void launchIntent(Activity caller, RenewalDepositoResponse response, String account){
        Intent intent = new Intent(caller,UbahPerpanjanganDepositoActivity.class);
        intent.putExtra(TAG_RESPONSE,new Gson().toJson(response));
        intent.putExtra(TAG_ACCOUNT,account);
        caller.startActivityForResult(intent, Constant.REQ_PAYMENT);
    }

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        binding = ActivityUbahPerpanjanganDepositoBinding.inflate(getLayoutInflater());
        setContentView(binding.getRoot());

        injectDependency();

        setupView();

        binding.btnSubmit.setOnClickListener(this);
    }

    private void injectDependency() {
        getActivityComponent().inject(this);
        if (presenter != null) {
            presenter.setView(this);
            presenter.setUrl(GeneralHelper.getString(R.string.url_deposito_renewal_update));
            presenter.start();
        }
    }

    private void setupView() {
        GeneralHelper.setToolbar(this, binding.toolbar.toolbar, GeneralHelper.getString(R.string.ubah_perpanjang_text_toolbar));

        //set blue bar
        if (Build.VERSION.SDK_INT >= 21) {
            Window window = getWindow();
            window.addFlags(WindowManager.LayoutParams.FLAG_DRAWS_SYSTEM_BAR_BACKGROUNDS);
            window.clearFlags(WindowManager.LayoutParams.FLAG_TRANSLUCENT_STATUS);
            window.setStatusBarColor(getResources().getColor(R.color.toolbar_blue));
        }

        if (getIntent().getStringExtra(TAG_RESPONSE)!=null)
            response = new Gson().fromJson(getIntent().getStringExtra(TAG_RESPONSE),RenewalDepositoResponse.class);

        CustomLinearLayoutManager linearLayoutManager = new CustomLinearLayoutManager(this);
        binding.rvJenisPerpanjangan.setLayoutManager(linearLayoutManager);
        binding.rvJenisPerpanjangan.setHasFixedSize(true);
        adapter = new ListItemDepositoOptionAdapter(this,response.getTypeCurrent(),this,response.getCurrentRenewal());

        binding.rvJenisPerpanjangan.setAdapter(adapter);
        adapter.notifyDataSetChanged();
    }

    @Override
    public void onClickDetail(String currentRenewal) {
        this.currentRenewal = currentRenewal;
        adapter.setSelector(currentRenewal);
        adapter.notifyDataSetChanged();
        updateButton();
    }

    private void updateButton() {
        if (currentRenewal.equals(response.getCurrentRenewal())){
            binding.btnSubmit.setEnabled(false);
            binding.btnSubmit.setAlpha(0.3f);
        }
        else{
            binding.btnSubmit.setEnabled(true);
            binding.btnSubmit.setAlpha(1);
        }
    }

    @Override
    public void onClick(View v) {
        switch (v.getId()){
            case R.id.btnSubmit:
            PinFragment pinFragment = new PinFragment(this, this);
            pinFragment.show();
            break;
        }
    }

    @Override
    public void onSuccessUpdateDeposito(String desc) {
        Intent i = new Intent();
        i.putExtra(Constant.TAG_MESSAGE,desc);
        setResult(RESULT_OK,i);
        finish();
    }

    @Override
    public void onSendPinComplete(String pin) {
        UpdatePerpanjanganDepositoRequest request = new UpdatePerpanjanganDepositoRequest(pin,getIntent().getStringExtra(TAG_ACCOUNT),currentRenewal);
        presenter.sendUpdatePerpanjangan(request);
    }

    @Override
    public void onLupaPin() {
        if (isFromFastMenu) LupaPinFastActivity.launchIntent(this);
        else LupaPinActivity.launchIntent(this);
    }

    @Override
    protected void onDestroy() {
        super.onDestroy();
        binding = null;
    }
}