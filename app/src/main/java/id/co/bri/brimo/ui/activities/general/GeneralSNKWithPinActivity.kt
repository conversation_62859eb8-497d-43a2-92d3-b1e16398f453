package id.co.bri.brimo.ui.activities.general

import android.app.Activity
import android.content.Intent
import android.os.Build
import androidx.appcompat.app.AppCompatActivity
import android.os.Bundle
import android.os.SystemClock
import android.util.Log
import android.view.View
import android.view.ViewTreeObserver
import android.view.WindowManager
import androidx.recyclerview.widget.LinearLayoutManager
import id.co.bri.brimo.R
import id.co.bri.brimo.contract.IPresenter.general.IGeneralSnkWithPinPresenter
import id.co.bri.brimo.contract.IView.general.IGeneralSnkWithPinView
import id.co.bri.brimo.databinding.ActivityGeneralSnkwithPinBinding
import id.co.bri.brimo.domain.config.Constant
import id.co.bri.brimo.domain.helpers.GeneralHelper
import id.co.bri.brimo.models.apimodel.request.general.RequestGeneralKonfirmasiInquiry
import id.co.bri.brimo.models.apimodel.response.GeneralConfirmationResponse
import id.co.bri.brimo.models.apimodel.response.PendingResponse
import id.co.bri.brimo.models.apimodel.response.ReceiptRevampResponse
import id.co.bri.brimo.models.apimodel.response.emas.GeneralConfirmationChecklistResponse
import id.co.bri.brimo.ui.activities.GeneralSyaratRevampActivity
import id.co.bri.brimo.ui.activities.KonfirmasiGeneralInvestasiActivity
import id.co.bri.brimo.ui.activities.KonfirmasiGeneralInvestasiActivity.Companion
import id.co.bri.brimo.ui.activities.LupaPinActivity
import id.co.bri.brimo.ui.activities.LupaPinFastActivity
import id.co.bri.brimo.ui.activities.base.BaseActivity
import id.co.bri.brimo.ui.activities.bukarekening.KonfirmasiTabunganRevActivity
import id.co.bri.brimo.ui.activities.bukarekening.PendingTabunganActivity
import id.co.bri.brimo.ui.activities.bukarekening.ReceiptTabunganActivity
import id.co.bri.brimo.ui.activities.receipt.ReceiptAbnormalRevampActivity
import id.co.bri.brimo.ui.activities.receipt.ReceiptRevampActivity
import id.co.bri.brimo.ui.fragments.PinFragment
import javax.inject.Inject

class GeneralSNKWithPinActivity : BaseActivity(),IGeneralSnkWithPinView,View.OnClickListener,PinFragment.SendPin {
    lateinit var binding : ActivityGeneralSnkwithPinBinding
    var isArrow = false

    @Inject
    lateinit var presenter : IGeneralSnkWithPinPresenter<IGeneralSnkWithPinView>
    companion object{
        private lateinit var mResponse : GeneralConfirmationResponse
        private var mUrlPayment : String? = null
        private var mTrxType : String? = ""
        var mIsScrollable = false
        var mIsFromTransaksi = false
        private var mAccount: String? = null
        private var mUrlPending: String = ""

        @JvmStatic
        fun launchIntent(caller: Activity, response : GeneralConfirmationResponse, urlPayment : String,isScrollable: Boolean,trxType :String = "", urlPending : String) {
            val intent = Intent(caller, GeneralSNKWithPinActivity::class.java)
            mResponse = response
            mUrlPayment = urlPayment
            mUrlPending  = urlPending
            mIsScrollable = isScrollable
            mIsFromTransaksi = false
            caller.startActivityForResult(intent, Constant.REQ_BUKA_REKENING)
        }

        @JvmStatic
        fun launchIntentTransaksi(caller: Activity, response : GeneralConfirmationResponse, urlPayment : String,isScrollable: Boolean,trxType :String, account: String? = null) {
            val intent = Intent(caller, GeneralSNKWithPinActivity::class.java)
            mResponse = response
            mUrlPayment =  urlPayment
            mIsScrollable = isScrollable
            mTrxType = trxType
            mAccount = account
            mIsFromTransaksi = true

            caller.startActivityForResult(intent, Constant.REQ_PAYMENT)
        }
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        binding = ActivityGeneralSnkwithPinBinding.inflate(layoutInflater)
        setContentView(binding.root)
        injectDependency()
        setupView()
    }

    private fun setupView() {
        //set blue bar
        setStatusColor(R.color.highlightColor)
        with(binding) {
            if (mIsScrollable) {
                scrollview.getViewTreeObserver()
                    .addOnScrollChangedListener(ViewTreeObserver.OnScrollChangedListener {
                        val view: View = scrollview.getChildAt(scrollview.getChildCount() - 1)
                        val diff: Int = view.bottom - (scrollview.getHeight() + scrollview
                            .getScrollY())
                        if (scrollview.getHeight() == scrollview.getChildAt(0)
                                .getHeight()
                        ) imgBawah.setVisibility(View.GONE) else if (diff == 0) {
                            imgBawah.setVisibility(View.GONE)
                        } else {
                            imgBawah.setVisibility(View.VISIBLE)
                        }
                    })
            }


            btSetuju.setOnClickListener(this@GeneralSNKWithPinActivity)
            btBatal.setOnClickListener(this@GeneralSNKWithPinActivity)
            imgBawah.setOnClickListener(this@GeneralSNKWithPinActivity)
            GeneralHelper.setToolbar(
                this@GeneralSNKWithPinActivity,
                binding.toolbar.toolbar,
                GeneralHelper.getString(R.string.tnc)
            )
            GeneralHelper.setWebView(binding.wvSyarat, "", mResponse.snk)
        }
    }

    fun injectDependency() {
        activityComponent.inject(this)
        presenter.view = this
        presenter.setUrlPayment(mUrlPayment!!)
        presenter.start()
    }

    override fun onClick(p0: View?) {
        when (p0!!.id) {
            R.id.bt_batal -> {
                onBackPressed()
            }
            R.id.img_bawah -> {
                binding.scrollview.scrollTo(0, binding.btBatal.bottom)
                binding.imgBawah.visibility = View.GONE
            }
            R.id.bt_setuju -> {
                openPin()
            }
        }
    }

    override fun onSuccessGetPayment(paymentResponse: ReceiptRevampResponse) {
        if (mIsFromTransaksi){
            if (mTrxType != ""){
                if (mTrxType.equals(Constant.TRX_TYPE_KLAIM_DPLK_RECEIPT) == true){
                    ReceiptStatusActivity.launchIntent(this,paymentResponse.referenceNumber, paymentResponse,false,Constant.TRX_TYPE_KLAIM_DPLK_RECEIPT)
                }
            }else{
                if (paymentResponse.isOnProcess == true) {
                    ReceiptAbnormalRevampActivity.launchIntentInvestasi(this, paymentResponse,
                        isFromFastMenu,true,false)
                }
                else {
                    ReceiptRevampActivity.launchIntentInvestasi(false, false,true,this,paymentResponse)
                }
            }

        }else{
            if (paymentResponse.immediatelyFlag == true){
                ReceiptTabunganActivity.launchIntent(
                    this,
                    paymentResponse,
                    false,GeneralHelper.getString(R.string.btn_receipt_tabungan))
            }
            else{
                PendingTabunganActivity.launchIntent(
                    this,
                    paymentResponse,
                    mUrlPending
                )
            }
        }

    }

    override fun onExceptionTrxExpired(message: String?) {
        val i = Intent()
        i.putExtra(Constant.TAG_ERROR_MESSAGE, message)
        setResult(RESULT_CANCELED, i)
        finish()
    }

    override fun onException01(message: String?) {
        GeneralHelper.showDialogGagalBackDescBerubahRevamp(this, Constant.TRANSAKSI_GAGAL, message,true,false)

    }

    override fun onSendPinComplete(pin: String?) {
        if (mAccount != null){
            presenter.getDataPaymentWithRequest(
                mResponse,
                RequestGeneralKonfirmasiInquiry(pin.orEmpty(), mResponse.referenceNumber, mAccount.orEmpty())
            )
        } else {
            presenter.getDataPayment(
                pin,
                "",
                mResponse,
                isFromFastMenu
            )
        }
    }

    override fun onLupaPin() {
        //TO DO routing
        if (isFromFastMenu) LupaPinFastActivity.launchIntent(this) else LupaPinActivity.launchIntent(
                this
        )
    }


    private fun openPin() {
        val pinFragment = PinFragment(this, this)
        pinFragment.show()
    }

    @Suppress("DEPRECATION")
    @Deprecated("Deprecated in Java")
    override fun onActivityResult(requestCode: Int, resultCode: Int, data: Intent?) {
        super.onActivityResult(requestCode, resultCode, data)

        if (requestCode == Constant.REQ_BUKA_REKENING) {
            if (resultCode == RESULT_OK) {
                this.setResult(RESULT_OK, data)
                finish()
            } else if (resultCode == RESULT_CANCELED && data != null){
                this.setResult(RESULT_CANCELED, data)
                finish()
            }else if(resultCode == RESULT_FIRST_USER && data != null){
                this.setResult(RESULT_FIRST_USER, data)
                finish()
            }
        } else if (requestCode == Constant.REQ_FORGET_PIN) {
            if (resultCode == RESULT_OK) {
                this.setResult(RESULT_OK)
                finish()
            }
        }else  if (requestCode == Constant.REQ_PAYMENT) {
            if (resultCode == RESULT_OK) {
                this.setResult(RESULT_OK, data)
                finish()
            } else {
                this.setResult(RESULT_CANCELED, data)
                finish()
            }
        }
    }
}