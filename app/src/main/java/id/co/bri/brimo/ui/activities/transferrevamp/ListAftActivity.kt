package id.co.bri.brimo.ui.activities.transferrevamp

import android.annotation.SuppressLint
import android.app.Activity
import android.content.Intent
import android.os.Bundle
import android.view.View
import android.view.ViewGroup
import android.view.WindowManager
import androidx.appcompat.widget.SearchView
import androidx.fragment.app.FragmentTransaction
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import androidx.swiperefreshlayout.widget.SwipeRefreshLayout.OnRefreshListener
import com.ethanhua.skeleton.Skeleton
import com.ethanhua.skeleton.SkeletonScreen
import id.co.bri.brimo.R
import id.co.bri.brimo.adapters.autograbfund.ListAftAdapter
import id.co.bri.brimo.contract.IPresenter.transferrevamp.IListAftPresenter
import id.co.bri.brimo.contract.IView.transferrevamp.IListAftView
import id.co.bri.brimo.databinding.ActivityListAftBinding
import id.co.bri.brimo.domain.config.Constant
import id.co.bri.brimo.domain.helpers.GeneralHelper
import id.co.bri.brimo.domain.helpers.SearchViewHelper
import id.co.bri.brimo.models.apimodel.response.transferrevamp.DetailAftResponse
import id.co.bri.brimo.models.apimodel.response.transferrevamp.ListAftResponse
import id.co.bri.brimo.ui.activities.LupaPinActivity
import id.co.bri.brimo.ui.activities.base.BaseActivity
import id.co.bri.brimo.ui.customviews.dialog.DialogSetDefaultRevamp
import id.co.bri.brimo.ui.fragments.BaseFragment
import id.co.bri.brimo.ui.fragments.PinFragment
import javax.inject.Inject

class ListAftActivity : BaseActivity(), OnRefreshListener, IListAftView, PinFragment.SendPin {

    private var _binding: ActivityListAftBinding? = null
    private val binding get() = _binding!!

    private var skeletonScreenInbox: SkeletonScreen? = null
    private var listAftAdapter: ListAftAdapter? = null
    private var layoutManager: LinearLayoutManager? = null
    private var mListAftResponse: ListAftResponse? = null
    private var messageSnackbar: String? = null
    private var mListAft = ArrayList<DetailAftResponse>()
    private var mPin: String = ""
    private var TAG_IS_FROM_BANNER_AFT = "TAG_IS_FROM_BANNER_AFT"

    object mActionAft {
        const val remove = "ActionAftRemove"
        const val updateActive = "ActionAftUpdateActive"
        const val updateNonActive = "ActionAftUpdateNonActive"
    }

    private var mSelectedActionAgf = ""
    private var mSelectedIdAgf = ""

    @Inject
    lateinit var listAftPresenter: IListAftPresenter<IListAftView>

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)

        _binding = ActivityListAftBinding.inflate(layoutInflater)
        setContentView(binding.root)
        injectDependency()

        setupView()

        initiateAdapter()
        binding.swipeRefresh.setOnRefreshListener(this)
    }

    private fun setupView() {
        setStatusColor(R.color.primary_blue80)
        if (GeneralHelper.isProd()) window.setFlags(
            WindowManager.LayoutParams.FLAG_SECURE,
            WindowManager.LayoutParams.FLAG_SECURE
        )
        GeneralHelper.setToolbarRevamp(
            this,
            binding.toolbar.toolbar,
            GeneralHelper.getString(R.string.toolbar_list_aft)
        )
        SearchViewHelper.setupSearchView(binding.searchview, this)
        binding.searchview.setOnQueryTextListener(object : SearchView.OnQueryTextListener {
            override fun onQueryTextSubmit(query: String): Boolean {
                listAftAdapter?.filter?.filter(query)
                return false
            }

            override fun onQueryTextChange(newText: String): Boolean {
                listAftAdapter?.filter?.filter(newText)
                if (newText.isEmpty()) {
                    if (mListAft.isNotEmpty()) {
                        binding.llNoDataSavedFound.visibility = View.GONE
                        binding.rvItem.visibility = View.VISIBLE
                        binding.llNoDataSaved.visibility = View.GONE
                    } else {
                        binding.llNoDataSavedFound.visibility = View.GONE
                        binding.llNoDataSaved.visibility = View.VISIBLE
                        binding.rvItem.visibility = View.GONE
                    }
                }

                return false
            }
        })
        binding.btnCreateNew.setOnClickListener {
            FormTransferAliasRevampActivity.launchIntent(
                this,
                isFromFastMenu,
                FormTransferAliasRevampActivity.mJourneyTypeList.ListAft,
                mIsCreateFromBanner
            )
        }
    }

    private fun setupCheckSafetyMode() {
        if (mListAftResponse?.isSafetyMode == true && mListAftResponse?.safetyModeAlert?.isNotEmpty() == true) {
            binding.mcvInfo.visibility = View.VISIBLE
            binding.tvAlertSafetyMode.text = mListAftResponse?.safetyModeAlert
            binding.btnCreateNew.isEnabled = false
            binding.btnCreateNew.setTextColor(GeneralHelper.getColor(R.color.neutral_light80))
        } else {
            binding.mcvInfo.visibility = View.GONE
        }
    }


    private fun injectDependency() {
        activityComponent.inject(this)
        listAftPresenter.view = this
        listAftPresenter.start()
        callService()
    }

    /**
     * panggil service
     */
    private fun callService() {
        runOnUiThread {
            if (skeletonScreenInbox != null) {
                skeletonScreenInbox?.show()
            }
        }
        listAftPresenter.setUrlListAft(GeneralHelper.getString(R.string.url_aft_list))
        listAftPresenter.setUrlUpdateStatus(GeneralHelper.getString(R.string.url_aft_update_status))
        listAftPresenter.setUrlDetailAft(GeneralHelper.getString(R.string.url_aft_detail))
        listAftPresenter.getDataListAft()

    }

    /**
     * inisialisasi adapter
     */
    private fun initiateAdapter() {
        layoutManager = LinearLayoutManager(this, RecyclerView.VERTICAL, false)
        binding.rvItem.setHasFixedSize(true)
        binding.rvItem.setLayoutManager(
            LinearLayoutManager(
                applicationContext,
                RecyclerView.VERTICAL,
                false
            )
        )
        listAftAdapter = ListAftAdapter(mListAft)
        listAftAdapter?.onShowDialog { showDialogRevamp(it) }
        listAftAdapter?.onNoItemSearch {
            if (it && mListAft.isNotEmpty()) {
                binding.llNoDataSavedFound.visibility = View.VISIBLE
            } else {
                binding.llNoDataSavedFound.visibility = View.GONE
            }
        }
        listAftAdapter?.onItemClick {
            listAftPresenter.getDetailAft(it.aftId)
        }

        binding.rvItem.setAdapter(listAftAdapter)

        skeletonScreenInbox = Skeleton.bind(binding.rvItem)
            .adapter(listAftAdapter)
            .shimmer(true)
            .angle(20)
            .frozen(false)
            .duration(1200)
            .count(7)
            .load(R.layout.item_skeleton_inbox_revamp)
            .show()
    }

    fun showDialogRevamp(dialogSetDefaultRevamp: DialogSetDefaultRevamp) {
        val ft: FragmentTransaction = this.supportFragmentManager.beginTransaction()
        ft.add(dialogSetDefaultRevamp, null)
        ft.commitAllowingStateLoss()
    }


    override fun onSendPinComplete(pin: String) {
        mPin = pin
        when (mSelectedActionAgf) {
            mActionAft.updateActive -> {
                listAftPresenter.updateStatusAft(mSelectedIdAgf, true, mPin)
            }

            mActionAft.updateNonActive -> {
                listAftPresenter.updateStatusAft(mSelectedIdAgf, false, mPin)
            }

        }
        mPin = ""
    }

    override fun onLupaPin() {
        LupaPinActivity.launchIntent(this@ListAftActivity)
    }


    override fun hideProgress() {
        super.hideProgress()
        binding.swipeRefresh.isRefreshing = false
    }

    override fun showProgress() {
        super.showProgress()
    }

    @SuppressLint("NotifyDataSetChanged")
    override fun onSuccessGetListAft(listAftResponse: ListAftResponse) {
        mListAftResponse = listAftResponse
        mListAftResponse?.isSafetyMode.let {
            if (it != null) {
                listAftAdapter?.setSafetyMode(it)
            }
        }
        this.mListAft.clear()
        this.mListAft.addAll(listAftResponse.listAft)
        listAftAdapter?.notifyDataSetChanged()
        listAftAdapter?.onSetAction { action, idAgf ->
            mSelectedActionAgf = action
            mSelectedIdAgf = idAgf
        }
        if (this.mListAft.size > 0) {
            enableSearchView(binding.searchview, true)
            binding.llNoDataSaved.visibility = View.GONE
        } else {
            binding.llNoDataSaved.visibility = View.VISIBLE
            binding.rvItem.visibility = View.GONE
        }

        listAftAdapter?.onShowPin { showPin() }
        binding.btnCreateNew.isEnabled = true
        if (mListAft.isNotEmpty()) {
            binding.llNoDataSavedFound.visibility = View.GONE
            binding.rvItem.visibility = View.VISIBLE
        } else {
            binding.llNoDataSavedFound.visibility = View.GONE
            binding.llNoDataSaved.visibility = View.VISIBLE
            binding.rvItem.visibility = View.GONE
        }
        setupCheckSafetyMode()
    }

    private fun showPin() {
        val pinFragment = PinFragment(this, this)
        pinFragment.setOnDismissListener {
            if (mPin.isEmpty()) {
                listAftPresenter.getDataListAft()
            }
        }
        pinFragment.show()
    }

    override fun showSkeleton(isShow: Boolean) {
        runOnUiThread {
            if (isShow) {
                skeletonScreenInbox?.show()
            } else {
                skeletonScreenInbox?.hide()
            }
            binding.swipeRefresh.isRefreshing = false
        }
    }

    override fun onSuccessGetDetailAft(detailAftResponse: DetailAftResponse) {
        DetailAftActivity.launchIntent(this, detailAftResponse)
    }

    override fun onSuccessUpdateAft(message: String) {
        showSnackbarErrorMessage(message, BaseFragment.ALERT_CONFIRM, this, false)
        listAftPresenter.getDataListAft()
    }

    override fun onException(message: String) {
        binding.swipeRefresh.isRefreshing = false
        if (GeneralHelper.isContains(
                Constant.LIST_TYPE_GAGAL,
                message
            )
        ) GeneralHelper.showDialogGagalBack(this, message) else GeneralHelper.showSnackBar(
            binding.content, message
        )
    }


    /**
     * Merefresh Data inbox
     */
    override fun onRefresh() {
        enableSearchView(binding.searchview, false)
        if (skeletonScreenInbox != null) {
            binding.rvItem.visibility = View.VISIBLE
            binding.llDesc12.visibility = View.GONE
            binding.llNoDataSavedFound.visibility = View.GONE
            binding.llNoDataSaved.visibility = View.GONE
        }
        listAftPresenter.getDataListAft()
    }


    @Deprecated("Deprecated in Java")
    @Suppress("DEPRECATION")
    override fun onActivityResult(requestCode: Int, resultCode: Int, data: Intent?) {
        super.onActivityResult(requestCode, resultCode, data)
        if (requestCode == Constant.REQ_NON_PAYMENT || requestCode == Constant.REQ_PAYMENT) {
            if (resultCode == RESULT_OK) {
                this.setResult(RESULT_OK, data)
            }
            messageSnackbar = data?.getStringExtra(Constant.TAG_MESSAGE)
            cekMessage()
            if (data != null && (data.getStringExtra(Constant.ActivityJourneyTypeAftString) == Constant.ActivityJourneyTypeAft.delete
                        || data.getStringExtra(Constant.ActivityJourneyTypeAftString) == Constant.ActivityJourneyTypeAft.update || !messageSnackbar.equals(
                    ""
                )
                        )
            ) {
                listAftPresenter.getDataListAft()
            }

        }
    }

    private fun cekMessage() {
        if (messageSnackbar != null) {
            showSnackbarErrorMessageRevamp(messageSnackbar, ALERT_CONFIRM, this, false)
            messageSnackbar = null
        }
    }

    override fun onDestroy() {
        listAftPresenter.stop()
        super.onDestroy()
    }

    override fun onBackPressed() {
        listAftPresenter.stop()
        _binding = null
        if (mIsCreateFromBanner) {
            cancelCreateAftFromBanner()
        } else {
            super.onBackPressed()
        }
    }

    companion object {
        private var mIsCreateFromBanner: Boolean = false

        @JvmStatic
        fun launchIntent(caller: Activity, isCreateFromBanner: Boolean?) {
            val intent = Intent(caller, ListAftActivity::class.java)
            mIsCreateFromBanner = isCreateFromBanner ?: false
            caller.startActivityForResult(intent, Constant.REQ_NON_PAYMENT)
        }

        @JvmStatic
        fun launchIntentFinish(caller: Activity) {
            val intent = Intent(caller, ListAftActivity::class.java)
            caller.startActivityForResult(intent, Constant.REQ_NON_PAYMENT)
            caller.finish()
        }
    }

    //method untuk enable disable searchview
    fun enableSearchView(view: View, enabled: Boolean) {
        view.isEnabled = enabled
        if (view is ViewGroup) {
            for (i in 0 until view.childCount) {
                val child = view.getChildAt(i)
                enableSearchView(child, enabled)
            }
        }
    }

    private fun cancelCreateAftFromBanner() {
        val returnIntent = Intent()
        returnIntent.putExtra(TAG_IS_FROM_BANNER_AFT, false)

        this.setResult(RESULT_CANCELED, returnIntent)
        this.finish()
    }
}