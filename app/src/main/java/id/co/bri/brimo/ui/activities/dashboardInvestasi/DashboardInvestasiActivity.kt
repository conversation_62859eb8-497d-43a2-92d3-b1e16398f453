package id.co.bri.brimo.ui.activities.dashboardInvestasi

import android.app.Activity
import android.content.Context
import android.content.Intent
import android.content.pm.PackageManager
import android.graphics.Bitmap
import android.graphics.Color
import android.graphics.drawable.Drawable
import android.os.Bundle
import android.os.Handler
import android.os.SystemClock
import android.view.MotionEvent
import android.view.View
import android.view.animation.AnimationUtils
import androidx.core.content.FileProvider
import androidx.core.view.isVisible
import androidx.lifecycle.lifecycleScope
import androidx.recyclerview.widget.GridLayoutManager
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.swiperefreshlayout.widget.SwipeRefreshLayout.OnRefreshListener
import com.airbnb.lottie.LottieComposition
import com.airbnb.lottie.LottieCompositionFactory
import com.airbnb.lottie.LottieTask
import com.bumptech.glide.Glide
import com.bumptech.glide.request.target.CustomTarget
import com.bumptech.glide.request.transition.Transition
import com.ethanhua.skeleton.Skeleton
import com.ethanhua.skeleton.SkeletonScreen
import id.co.bri.brimo.BuildConfig
import id.co.bri.brimo.R
import id.co.bri.brimo.adapters.dashboardInvestasi.AsetInvestasiAdapter
import id.co.bri.brimo.adapters.dashboardInvestasi.FaqInvestasiAdapter
import id.co.bri.brimo.adapters.dashboardInvestasi.MenuInvestasiAdapter
import id.co.bri.brimo.adapters.dashboardInvestasi.PromoInvestasiAdapter
import id.co.bri.brimo.adapters.dashboardInvestasi.RecomendationInvestasiAdapter
import id.co.bri.brimo.adapters.dashboardInvestasi.StoryInvestasiAdapter
import id.co.bri.brimo.adapters.dashboardInvestasi.ValasInvestasiAdapter
import id.co.bri.brimo.contract.IPresenter.dashboard.IDashboardInvestasiPresenter
import id.co.bri.brimo.contract.IView.dashboard.IDashboardInvestasiView
import id.co.bri.brimo.data.preference.BRImoPrefRepository
import id.co.bri.brimo.databinding.ActivityDashboardInvestasiBinding
import id.co.bri.brimo.domain.config.Constant
import id.co.bri.brimo.domain.config.MenuConfig
import id.co.bri.brimo.domain.helpers.GeneralHelper
import id.co.bri.brimo.domain.helpers.bubbleShowCaseView.BubbleShowCase
import id.co.bri.brimo.domain.helpers.bubbleShowCaseView.BubbleShowCaseBuilder
import id.co.bri.brimo.domain.helpers.bubbleShowCaseView.BubbleShowCaseSequence
import id.co.bri.brimo.domain.helpers.image.ImageHelper
import id.co.bri.brimo.models.apimodel.response.TermConditionTabRes
import id.co.bri.brimo.models.apimodel.response.dashboardInvestasi.BannerResponse
import id.co.bri.brimo.models.apimodel.response.dashboardInvestasi.ContentResponse
import id.co.bri.brimo.models.apimodel.response.dashboardInvestasi.DashboardInvestasiResponse
import id.co.bri.brimo.models.apimodel.response.dashboardInvestasi.InvenstasiFaqResponse
import id.co.bri.brimo.models.apimodel.response.dashboardInvestasi.InvestmentRecapResponse
import id.co.bri.brimo.models.apimodel.response.dashboardInvestasi.KeyDataItem
import id.co.bri.brimo.models.apimodel.response.dashboardInvestasi.MenuInvestasiResponse
import id.co.bri.brimo.models.apimodel.response.dashboardInvestasi.RecomendationResponse
import id.co.bri.brimo.models.apimodel.response.dplk.DplkBoardingResponse
import id.co.bri.brimo.models.apimodel.response.rdn.InquiryRdnResponse
import id.co.bri.brimo.models.apimodel.response.rdn.RdnAccountResponse
import id.co.bri.brimo.models.apimodel.response.rdn.RdnAccountResponse.AccountRdn
import id.co.bri.brimo.models.apimodel.response.rdn.RdnOnBoardingResponse
import id.co.bri.brimo.models.apimodel.response.rdn.RdnOnCheckpointResponse
import id.co.bri.brimo.models.apimodel.response.sbnrevamp.SbnBeliResponse
import id.co.bri.brimo.models.apimodel.response.sbnrevamp.SbnDashboardHeaderResponse
import id.co.bri.brimo.models.daomodel.DashboardMenu.LanguageModel
import id.co.bri.brimo.models.daomodel.DashboardMenu.MenuDashboard
import id.co.bri.brimo.ui.activities.base.BaseActivity
import id.co.bri.brimo.ui.activities.britamarencanarevamp.DashboardRencanaActivity
import id.co.bri.brimo.ui.activities.bukarekening.TabunganActivity
import id.co.bri.brimo.ui.activities.deposito.InfoBukaDepositoActivity
import id.co.bri.brimo.ui.activities.depositorevamp.DashboardDepositoActivity
import id.co.bri.brimo.ui.activities.dplk.OnboardingDplkActivity
import id.co.bri.brimo.ui.activities.dplkrevamp.DashboardDplkRevampActivity
import id.co.bri.brimo.ui.activities.emas.DashboardEmasActivity
import id.co.bri.brimo.ui.activities.launcher.BrowserIntentActivity
import id.co.bri.brimo.ui.activities.portofolioksei.DashboardKseiActivity
import id.co.bri.brimo.ui.activities.rdn.CekStatusRdnActivity
import id.co.bri.brimo.ui.activities.rdn.InquiryRdnRevampActivity.Companion.launchIntent
import id.co.bri.brimo.ui.activities.rdn.OnBoardingRdn1Activity
import id.co.bri.brimo.ui.activities.rdnrevamp.dashboard.DashboardRdnRevampActivity
import id.co.bri.brimo.ui.activities.sbnrevamp.DashboardSbnRevampActivity
import id.co.bri.brimo.ui.activities.sbnrevamp.ListBeliSbnRevampActivity
import id.co.bri.brimo.ui.customviews.dialog.DialogInformation
import id.co.bri.brimo.ui.customviews.dialog.DialogInformation.OnActionClick
import id.co.bri.brimo.ui.fragments.dashboard.BottomFragmentInvestmentRecap
import id.co.bri.brimo.ui.fragments.dashboard.BottomFragmentKomposisi
import id.co.bri.brimo.ui.fragments.rdn.CustomBottomDialogFragment
import id.co.bri.brimo.util.json.JsonManager
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import java.io.BufferedInputStream
import java.io.File
import java.io.FileInputStream
import java.io.FileOutputStream
import javax.inject.Inject


class DashboardInvestasiActivity : BaseActivity(), IDashboardInvestasiView,
        View.OnClickListener, ValasInvestasiAdapter.onClickListenerValas,
        MenuInvestasiAdapter.onClickMenuListener, OnRefreshListener,
        RecomendationInvestasiAdapter.onClickListenerRecommended,AsetInvestasiAdapter.onClickListenerAsset,
        OnActionClick,PromoInvestasiAdapter.OnClickListenerPromo,CustomBottomDialogFragment.DialogDefaultListener, StoryInvestasiAdapter.OnClickListener {

    private lateinit var binding : ActivityDashboardInvestasiBinding
    private var valas: BubbleShowCaseBuilder? = null
    private var komposisi:BubbleShowCaseBuilder? = null
    private var aset:BubbleShowCaseBuilder? = null
    private var mAssetResponse : DashboardInvestasiResponse? = null
    private var handlerPromo: Handler? = null
    private var runnablePromo: Runnable? = null
    private val speedScroll = 3500
    private var skeletonScreenSaldo: SkeletonScreen? = null
    private var skeletonBtnKomposisi: SkeletonScreen? = null
    private var skeletonScreenSaldoRdn: SkeletonScreen? = null
    private var skeletonScreenRecommended: SkeletonScreen? = null
    private var skeletonScreenMenu: SkeletonScreen? = null
    private var skeletonScreenPromo: SkeletonScreen? = null
    private var skeletonScreenContent: SkeletonScreen? = null
    private var skeletonScreenFaq: SkeletonScreen? = null
    private var skeletonScreenAsset: SkeletonScreen? = null
    private var skeletonScreenShowHideAsset: SkeletonScreen? = null
    private var skeletonInvesmentRecap: SkeletonScreen? = null
    private var menuDashboardListInvestasi: MutableList<MenuDashboard> = mutableListOf()
    private var mPosition : Int? = 0
    private var currentPage = 1
    private var isLoading = false
    private var isShowAsset = false
    private val maxPages = 2
    private var recomendationInvestasiAdapter : RecomendationInvestasiAdapter? = null
    private var faqInvestasiAdapter : FaqInvestasiAdapter? = null
    private var menuInvestasiAdapter : MenuInvestasiAdapter? = null
    private var storyInvestasiAdapter : StoryInvestasiAdapter? = null
    private var promoInvestasiAdapter : PromoInvestasiAdapter? = null
    private var asetInvestasiAdapter : AsetInvestasiAdapter? = null
    private var mPromoResponse : MutableList<BannerResponse.Banner>? = mutableListOf()
    private var mRecommendeResponse : MutableList<RecomendationResponse.Recommend>? = mutableListOf()
    private var mStoryResponse : MutableList<ContentResponse.Content>? = mutableListOf()
    private var mMenuResponse : MenuInvestasiResponse? = null
    private var mFaqResponse : MutableList<InvenstasiFaqResponse.Faq>? = mutableListOf()
    private var mListAssetResponse : MutableList<DashboardInvestasiResponse.Asset> = mutableListOf()
    private var brImoPrefRepository: BRImoPrefRepository? = BRImoPrefRepository(this)
    private val bubleInvestasi = BubbleShowCaseSequence()
    private val bubleInvestasiNoCurrency = BubbleShowCaseSequence()
    private var listRdn: MutableList<AccountRdn>? = null
    private var customBottomDialogFragment: CustomBottomDialogFragment? = null
    private var mSortedMenuAcak : MutableList<MenuDashboard> = mutableListOf()
    private var mMappingList : MutableList<String> = mutableListOf()
    private var mIdMenu : MutableList<String> = mutableListOf()
    private var mAccount : String= ""
    private var mSid : String = ""
    private var mSre : String = ""
    private var imageHelper: ImageHelper? = null
    private var mResponseInvesmentRecap = InvestmentRecapResponse()
    private var listLottieTaskRecap = mutableListOf<LottieTask<LottieComposition>>()
    private var mapOfDynamicLists: List<List<Map<String, String>>>? = null


    @Inject
    lateinit var presenter : IDashboardInvestasiPresenter<IDashboardInvestasiView>

    companion object{
        @JvmStatic
        fun launchIntent(caller : Activity){
            val intent = Intent(caller, DashboardInvestasiActivity::class.java)
            caller.startActivityForResult(intent, Constant.REQ_PAYMENT)
        }
    }
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        binding = ActivityDashboardInvestasiBinding.inflate(layoutInflater)
        setContentView(binding.root)
        showSekelton()
        injectDepedency()
        setupView()
    }

    private fun setupView() {
        GeneralHelper.setToolbarRevamp(this, binding.toolbar.toolbar,GeneralHelper.getString(R.string.txt_investasi))
        binding.swipeRefresh.setOnRefreshListener(this)
        imageHelper = ImageHelper(this)

        setOnListener()

        binding.scrollView.viewTreeObserver.addOnScrollChangedListener {
            val scrollY = binding.scrollView.scrollY
            val scrollViewHeight = binding.scrollView.height
            val contentHeight = binding.scrollView.getChildAt(0).height

            if (scrollY + scrollViewHeight >= contentHeight * 0.93) {
                binding.imgBawah.visibility = View.VISIBLE
            } else {
                binding.imgBawah.visibility = View.GONE
            }
        }

        binding.rlTotal.visibility = View.VISIBLE
        binding.llDotLoadingSaldo.visibility = View.VISIBLE

        binding.llKeListRekening.setOnClickListener {
            toggleSaldoDot()
        }

        binding.tbLihatAsset.setOnClickListener {
            toggleSaldoDot()
        }
    }

    private fun toggleSaldoDot() {
        if (!isShowAsset) {
            isShowAsset = true
            showAsset(isShowAsset)
        } else {
            isShowAsset = false
            hideAsset(isShowAsset)
        }
    }

    private fun showAsset(isShowAsset: Boolean) {
        binding.ivSaldoEye.setImageResource(R.drawable.ic_eye_close)
        val animation = AnimationUtils.loadAnimation(this@DashboardInvestasiActivity, R.anim.fade_in_trasition)
        binding.tvTotalSaldo.startAnimation(animation);
        binding.tvTotalSaldo.visibility = View.VISIBLE
        binding.ivDotSaldo.visibility = View.GONE

        binding.tvNominalSaldoRdn.startAnimation(animation)
        binding.tvNominalSaldoRdn.visibility = View.VISIBLE
        binding.ivDotSaldoRdn.visibility = View.GONE

        binding.ivLihatAsset.setImageResource(R.drawable.ic_eye_close)
        binding.tvLihatAsset.text = getString(R.string.sembunyikan)
        asetInvestasiAdapter?.showAsset(isShowAsset)
    }

    private fun hideAsset(isShowAsset: Boolean) {
        binding.ivSaldoEye.setImageResource(R.drawable.ic_eye_open)
        val animation = AnimationUtils.loadAnimation(this@DashboardInvestasiActivity, R.anim.fade_in_trasition)
        binding.ivDotSaldo.startAnimation(animation);
        binding.ivDotSaldo.visibility = View.VISIBLE
        binding.tvTotalSaldo.visibility = View.GONE

        binding.ivDotSaldoRdn.startAnimation(animation)
        binding.ivDotSaldoRdn.visibility = View.VISIBLE
        binding.tvNominalSaldoRdn.visibility = View.GONE

        binding.ivLihatAsset.setImageResource(R.drawable.ic_eye_open)
        binding.tvLihatAsset.text = getString(R.string.tampilkan)
        asetInvestasiAdapter?.showAsset(isShowAsset)
    }

    private fun setOnListener() {
        binding.btnKomposisi.setOnClickListener(this)
        binding.imgBawah.setOnClickListener(this)
        binding.btnBuy.setOnClickListener(this)
        binding.llMenuBrights.setOnClickListener(this)
        binding.llMenuKsei.setOnClickListener(this)
        binding.llSaldoRdn.setOnClickListener(this)
        binding.rlRefreshSaldoRdn.setOnClickListener(this)
        binding.animationView.setOnClickListener(this)
    }

    private fun loadMoreData() {
        if (!isLoading) {
            isLoading = true
            currentPage++
            presenter.getDataInvestasiContent()
            presenter.getDataInvestasiFaq()

            isLoading = false
        }
    }
    private fun injectDepedency() {
        activityComponent.inject(this)
        presenter.view = this
        presenter.setUrlInvestasi(GeneralHelper.getString(R.string.url_dashboard_investasi))
        presenter.setUrlInvestasiFaq(GeneralHelper.getString(R.string.url_dashboard_investasi_faq))
        presenter.setUrlInvestasiPromo(GeneralHelper.getString(R.string.url_dashboard_investasi_promo))
        presenter.setUrlInvestasiContent(GeneralHelper.getString(R.string.url_dashboard_investasi_story))
        presenter.setUrlInvestasiRecomendation(GeneralHelper.getString(R.string.url_dashboard_investasi_recommend))
        presenter.setUrlInvestasiMenu(GeneralHelper.getString(R.string.url_dashboard_investasi_menu))
        presenter.setUrlDepositoRegis(GeneralHelper.getString(R.string.url_deposito_open_term_condition))
        presenter.seturlDplkRegis(GeneralHelper.getString(R.string.url_dplk_onboarding))
        presenter.setUrlGetListAccountRdn(GeneralHelper.getString(R.string.url_rdn_account_list))
        presenter.setUrlInquiryRdn(GeneralHelper.getString(R.string.url_inquiry_rdn))
        presenter.setUrlSbnDashboardHeader(GeneralHelper.getString(R.string.url_sbn_dashboard_header))
        presenter.setUrlBeliSbn(GeneralHelper.getString(R.string.url_beli_sbn))
        presenter.setUrlInvesmentRecap(GeneralHelper.getString(R.string.url_invesment_recap))
        presenter.getDataInvestasiAsset()
        presenter.getDataInvestasiPromo()
        presenter.getDataInvestasiRecommedantion()
        presenter.getDataInvestasiContent()
        presenter.getDataInvestasiFaq()
        presenter.getInvesmentRecap()
        presenter.start()
    }

    override fun onSuccessInvestasi(response: DashboardInvestasiResponse) {
        mAssetResponse = response
        binding.swipeRefresh.setRefreshing(false)
        skeletonScreenSaldo!!.hide()
        skeletonBtnKomposisi!!.hide()
        skeletonScreenSaldoRdn!!.hide()
        skeletonScreenAsset!!.hide()
        skeletonScreenShowHideAsset!!.hide()

        binding.rvListAset.visibility = View.VISIBLE

        val adapter = ValasInvestasiAdapter(this, response.investment!!,this)
        binding.rvValas.adapter = adapter
        binding.rvValas.layoutManager = LinearLayoutManager(this, LinearLayoutManager.HORIZONTAL, false)

        binding.tvTerakhirPerbarui.text = response.lastUpdate
        binding.tvTotalSaldo.text = response.investment[mPosition!!].totalString
        if(response.investment.size == 1){
            binding.rvValas.visibility = View.GONE
        }else{
            binding.rvValas.visibility = View.VISIBLE
        }

        if (response.investment[mPosition!!].totalStatus == "12"){
            binding.rlNoAset.visibility = View.GONE
            binding.rlTotal.visibility = View.GONE
            binding.btnKomposisi.visibility = View.GONE
            binding.rlLayoutSaldo.visibility =View.VISIBLE
            binding.rlRefreshSaldo.visibility = View.VISIBLE
            binding.rvListAset.visibility = View.VISIBLE
            binding.tvNominalSaldoRdn.text = response.rdn!!.totalString
            binding.rlRefreshAsset.setOnClickListener(View.OnClickListener {
                presenter.setUrlInvestasiForced(GeneralHelper.getString(R.string.url_dashboard_investasi_refresh))
                presenter.getDataInvestasiAssetForce()
                binding.rlRefreshAsset.visibility = View.GONE
                binding.rlRefreshSaldo.visibility = View.GONE
                binding.rlRefreshSaldoRdn.visibility = View.GONE
                onShowSkeletonAsset()
            })
            binding.rlRefreshSaldo.setOnClickListener(View.OnClickListener {
                presenter.setUrlInvestasiForced(GeneralHelper.getString(R.string.url_dashboard_investasi_refresh))
                presenter.getDataInvestasiAssetForce()
                binding.rlRefreshAsset.visibility = View.GONE
                binding.rlRefreshSaldo.visibility = View.GONE
                binding.rlRefreshAsset.visibility = View.GONE
                onShowSkeletonAsset()
            })
            binding.rlRefreshSaldoRdn.setOnClickListener(View.OnClickListener {
                presenter.setUrlInvestasiForced(GeneralHelper.getString(R.string.url_dashboard_investasi_refresh))
                presenter.getDataInvestasiAssetForce()
                binding.rlRefreshAsset.visibility = View.GONE
                binding.rlRefreshSaldo.visibility = View.GONE
                binding.rlRefreshSaldoRdn.visibility = View.GONE
                onShowSkeletonAsset()
            })
        }else{
            if (response.investment[mPosition!!].status == 0){
                binding.rlNoAset.visibility = View.VISIBLE
                binding.rlLayoutSaldo.visibility = View.GONE
                if(response.investment.size == 1){
                    binding.rvValas.visibility = View.GONE
                }else{
                    binding.rvValas.visibility = View.VISIBLE
                }

            }else{
                binding.rlNoAset.visibility = View.GONE
                binding.rlLayoutSaldo.visibility = View.VISIBLE
                binding.llKeListRekening.visibility = View.VISIBLE
                binding.tvTerakhirPerbarui.visibility = View.VISIBLE
                binding.llDotLoadingSaldo.visibility = View.GONE
                if(response.investment.size == 1){
                    binding.rvValas.visibility = View.GONE
                    if (java.lang.Boolean.FALSE == brImoPrefRepository!!.investasiNoCurrency){
                        addBubbleShowCaseNoCurrency()
                        brImoPrefRepository!!.saveFirstInvestasiNoCurency(true)
                    }
                }else{
                    binding.rvValas.visibility = View.VISIBLE
                    if (java.lang.Boolean.FALSE == brImoPrefRepository!!.investasi){
                        addBubbleShowCase()
                        brImoPrefRepository!!.saveFirstInvestasi(true)
                    }
                }

            }
        }
        assetRdnShow(response)
        skeletonScreenAsset!!.hide()
        mListAssetResponse.clear()
        mListAssetResponse.addAll(response.investment[mPosition!!].assets!!)
        asetInvestasiAdapter?.setItems(mListAssetResponse)


        response.investment.forEach { investmentEntry ->
            investmentEntry.assets?.forEach { asset ->
                mMappingList.add(asset.mappingMenu ?: "")
                mIdMenu.add(asset.assetId ?: "")
            }
        }

        presenter.getDataInvestasiMenu()
    }

    override fun onSuccessInvestasiForced(response: DashboardInvestasiResponse) {
        mAssetResponse = response
        binding.swipeRefresh.setRefreshing(false)
        skeletonScreenSaldo!!.hide()
        skeletonBtnKomposisi!!.hide()
        skeletonScreenSaldoRdn!!.hide()
        skeletonScreenShowHideAsset!!.hide()

        binding.rvListAset.visibility = View.VISIBLE

        binding.tvTerakhirPerbarui.text = response.lastUpdate
        binding.tvTotalSaldo.text = response.investment?.get(mPosition!!)!!.totalString

        if (response.investment[mPosition!!].totalStatus == "12"){
            if(response.investment.size == 1){
                binding.rvValas.visibility = View.GONE
            }else{
                binding.rvValas.visibility = View.VISIBLE
            }

            binding.rlNoAset.visibility = View.GONE
            binding.rlTotal.visibility = View.GONE
            binding.btnKomposisi.visibility = View.GONE
            binding.rlLayoutSaldo.visibility =View.VISIBLE
            binding.rlRefreshSaldo.visibility = View.VISIBLE
            binding.rvListAset.visibility = View.VISIBLE
            binding.tvNominalSaldoRdn.text = response.rdn!!.totalString
            binding.rlRefreshAsset.setOnClickListener(View.OnClickListener {
                presenter.setUrlInvestasiForced(GeneralHelper.getString(R.string.url_dashboard_investasi_refresh))
                presenter.getDataInvestasiAssetForce()
                binding.rlRefreshAsset.visibility = View.GONE
                binding.rlRefreshSaldo.visibility = View.GONE
                binding.rlRefreshSaldoRdn.visibility = View.GONE
                onShowSkeletonAsset()
            })
            binding.rlRefreshSaldo.setOnClickListener(View.OnClickListener {
                presenter.setUrlInvestasiForced(GeneralHelper.getString(R.string.url_dashboard_investasi_refresh))
                presenter.getDataInvestasiAssetForce()
                binding.rlRefreshAsset.visibility = View.GONE
                binding.rlRefreshSaldo.visibility = View.GONE
                binding.rlRefreshAsset.visibility = View.GONE
                onShowSkeletonAsset()
            })
            binding.rlRefreshSaldoRdn.setOnClickListener(View.OnClickListener {
                presenter.setUrlInvestasiForced(GeneralHelper.getString(R.string.url_dashboard_investasi_refresh))
                presenter.getDataInvestasiAssetForce()
                binding.rlRefreshAsset.visibility = View.GONE
                binding.rlRefreshSaldo.visibility = View.GONE
                binding.rlRefreshSaldoRdn.visibility = View.GONE
                onShowSkeletonAsset()
            })
        }else{
            if (response.investment[mPosition!!].status == 0){
                binding.rlNoAset.visibility = View.VISIBLE
                binding.rlLayoutSaldo.visibility = View.GONE
                if(response.investment.size == 1){
                    binding.rvValas.visibility = View.GONE
                }else{
                    binding.rvValas.visibility = View.VISIBLE
                }

            }else {
                binding.rlNoAset.visibility = View.GONE
                binding.rlLayoutSaldo.visibility = View.VISIBLE
                binding.llKeListRekening.visibility = View.VISIBLE
                binding.tvTerakhirPerbarui.visibility = View.VISIBLE
                binding.llDotLoadingSaldo.visibility = View.GONE
                if (response.investment.size == 1) {
                    binding.rvValas.visibility = View.GONE

                    if (java.lang.Boolean.FALSE == brImoPrefRepository!!.investasiNoCurrency) {
                        addBubbleShowCaseNoCurrency()
                        brImoPrefRepository!!.saveFirstInvestasiNoCurency(true)
                    }
                } else {
                    binding.rvValas.visibility = View.VISIBLE

                    if (java.lang.Boolean.FALSE == brImoPrefRepository!!.investasi) {
                        addBubbleShowCase()
                        brImoPrefRepository!!.saveFirstInvestasi(true)
                    }
                }
            }
        }
        assetRdnShow(response)
        skeletonScreenAsset!!.hide()
        mListAssetResponse.clear()
        mListAssetResponse.addAll(response.investment[mPosition!!].assets!!)
        asetInvestasiAdapter?.setItems(mListAssetResponse)
        asetInvestasiAdapter?.showAsset(isShowAsset)

        response.investment.forEach { investmentEntry ->
            // Iterasi melalui asset di setiap investmentEntry
            investmentEntry.assets?.forEach { asset ->
                // Tambahkan setiap nilai (termasuk string kosong) ke dalam mMappingList
                mMappingList.add(asset.mappingMenu ?: "")
                mIdMenu.add(asset.assetId ?: "")
            }
        }

        presenter.getDataInvestasiMenu()

    }

    fun assetRdnShow(response: DashboardInvestasiResponse){
        if (response.rdn!!.assetStatus.equals("01")){
            binding.tvTitleBukaRdn.visibility = View.VISIBLE
            binding.llSaldo.visibility = View.GONE
            binding.tvSubtitleBukaRdn.visibility = View.VISIBLE
            binding.tvTitleSaldoRdn.visibility = View.GONE
            binding.tvNominalSaldoRdn.visibility = View.GONE
            binding.ivArrow.visibility = View.GONE
            binding.tvTitleBukaRdn.text = response.rdn.title
            binding.tvSubtitleBukaRdn.text = response.rdn.subtitle
            binding.btnBuy.text = response.rdn.buttonString

        }else if (response.rdn.assetStatus.equals("12")){
            binding.rlRefreshSaldoRdn.visibility = View.VISIBLE
            binding.llSaldo.visibility = View.GONE
            binding.rlSaldoRdn.visibility = View.GONE

            binding.rlRefreshSaldoRdn.setOnClickListener(View.OnClickListener {
                presenter.setUrlInvestasiForced(GeneralHelper.getString(R.string.url_dashboard_investasi_refresh))
                presenter.getDataInvestasiAssetForce()
                binding.rlRefreshAsset.visibility = View.GONE
                binding.rlRefreshSaldo.visibility = View.GONE
                binding.rlRefreshSaldoRdn.visibility = View.GONE
                onShowSkeletonAsset()
            })
        }
        else{
            binding.tvTitleBukaRdn.visibility = View.GONE
            binding.tvSubtitleBukaRdn.visibility = View.GONE
            binding.llSaldo.visibility = View.VISIBLE
            binding.ivArrow.visibility = View.VISIBLE
            binding.tvNominalSaldoRdn.text = response.rdn.totalString
            binding.btnBuy.text = response.rdn.buttonString
            binding.rlSaldoRdn.visibility = View.VISIBLE
        }
    }

    override fun onSuccessInvestasiFaq(response: InvenstasiFaqResponse) {
        binding.swipeRefresh.setRefreshing(false)
        skeletonScreenFaq!!.hide()
        mFaqResponse!!.clear()
        mFaqResponse!!.addAll(response.faq!!)
        faqInvestasiAdapter!!.setItems(mFaqResponse!!)

    }

    override fun onSuccessInvestasiPromo(response: BannerResponse) {
        if (response.banner!!.isEmpty()){
            binding.rlPromoInvestasi.visibility= View.GONE
        }else{
            if (handlerPromo != null) {
                handlerPromo!!.removeCallbacks(runnablePromo!!) //or this handler.removeCallbacksAndMessages(null);
            }
            binding.rlPromoInvestasi.visibility= View.VISIBLE
            binding.swipeRefresh.setRefreshing(false)
            skeletonScreenPromo?.hide()
            mPromoResponse?.clear()
            mPromoResponse?.addAll(response.banner)
            promoInvestasiAdapter?.setItems(mPromoResponse!!)
            autoScroll()
        }
    }

    override fun onSuccessInvestasiContent(response: ContentResponse) {
        binding.swipeRefresh.setRefreshing(false)
        skeletonScreenContent!!.hide()
        mStoryResponse!!.clear()
        mStoryResponse!!.addAll(response.content!!)
        storyInvestasiAdapter!!.setItems(mStoryResponse!!)
    }

    override fun onSuccessInvestasiRecommendation(response: RecomendationResponse) {
        if (response.recommend!!.isEmpty()){
            binding.rlRecomendation.visibility= View.GONE
        }else {
            binding.swipeRefresh.setRefreshing(false)
            skeletonScreenRecommended!!.hide()
            mRecommendeResponse!!.clear()
            mRecommendeResponse!!.addAll(response.recommend!!)
            recomendationInvestasiAdapter!!.setItems(mRecommendeResponse!!)
        }

    }

    override fun onSuccessInvestasiMenu(response: MenuInvestasiResponse) {
        skeletonScreenMenu!!.hide()
        binding.swipeRefresh.isRefreshing = false
        menuDashboardListInvestasi.clear()
        mMenuResponse = response

        val eventList = response.event
        if (mAssetResponse != null){
            if (mAssetResponse?.investment!![mPosition!!].status == 0){
                if (java.lang.Boolean.FALSE == brImoPrefRepository!!.investasiNoAset){
                    addBubbleShowCaseNoAset()
                    brImoPrefRepository!!.saveFirstInvestasiNoAset(true)
                }
            }
        }


        if (eventList!!.isEmpty()){
            menuDashboardListInvestasi = MenuConfig.fetchMenuDefaultInvestasi()
            binding.rvMenu.layoutManager =
                GridLayoutManager(this, 4, GridLayoutManager.VERTICAL, false)
            menuInvestasiAdapter = MenuInvestasiAdapter(this, menuDashboardListInvestasi, this)

            binding.rvMenu.adapter = menuInvestasiAdapter
        }else{
            menuDashboardListInvestasi = MenuConfig.fetchMenuInvestasi()

            val resultMenuDashboard: List<MenuDashboard> = menuDashboardListInvestasi.filter {
                it.id in eventList.map { it.menuId!! }
            }

            val idMappingMap = mIdMenu.zip(mMappingList).toMap()

            resultMenuDashboard.forEach { menuDashboard ->
                val correspondingEvent = eventList.find { it.menuId == menuDashboard.id }
                menuDashboard.eventName = correspondingEvent?.actions ?: ""

                val menuId = menuDashboard.id
                if (mIdMenu.contains(menuId.toString())) {
                    val mappingRouteValue = idMappingMap[menuId.toString()] ?: ""
                    menuDashboard.mappingRoute = mappingRouteValue
                }
            }


            val sortedMenuAcak = resultMenuDashboard.sortedByDescending {
                if (it.eventName == "warn_sale") 1 else 0
            }

            mSortedMenuAcak.clear()
            mSortedMenuAcak.addAll(sortedMenuAcak)
            menuInvestasiAdapter!!.setItems(sortedMenuAcak, response)
        }
    }

    override fun onSuccessOnBoarding(response: RdnOnBoardingResponse) {
        OnBoardingRdn1Activity.launchIntent(this, response)
    }

    override fun onSuccessCheckPoint(response: RdnOnCheckpointResponse) {
        CekStatusRdnActivity.launchIntentInvestasi(this, response,true)
    }

    override fun onGetDataTerm(termConditionTabRes: TermConditionTabRes) {
        InfoBukaDepositoActivity.launchIntent(this, termConditionTabRes)
    }

    override fun onSuccessDplkRegis(response: DplkBoardingResponse) {
        OnboardingDplkActivity.launchIntent(this, response)

    }

    override fun onSuccessGetAccountRdn(response: RdnAccountResponse) {
        listRdn = response.accountRdn
        DashboardRdnRevampActivity.launchIntent(this,false,true, listRdn as ArrayList<AccountRdn>)

    }

    override fun onSuccessInquiry(response: InquiryRdnResponse) {
        launchIntent(this, response)
    }

    override fun onGetDataProses() {
        val dialog = DialogInformation(this, GeneralHelper.getString(R.string.mohon_tunggu), GeneralHelper.getString(R.string.desc_tunggu), this, "maaf_maaf")
        val ft = supportFragmentManager.beginTransaction()
        ft.add(dialog, null)
        ft.commitAllowingStateLoss()
    }

    override fun onException21Content() {
        binding.swipeRefresh.setRefreshing(false)
        skeletonScreenContent!!.hide()
        binding.rlContent.visibility = View.GONE
        binding.rlRefreshContent.visibility = View.VISIBLE
        binding.rlRefreshContent.setOnClickListener(View.OnClickListener {
            presenter.getDataInvestasiContent()
            binding.rlRefreshContent.visibility = View.GONE
            onShowSkeletonContent()
        })
    }

    override fun onException21Asset() {
        binding.swipeRefresh.setRefreshing(false)
        skeletonScreenAsset!!.hide()
        skeletonScreenSaldo!!.hide()
        skeletonBtnKomposisi!!.hide()
        skeletonScreenSaldoRdn!!.hide()
        binding.rlNoAset.visibility = View.GONE
        binding.rvListAset.visibility = View.GONE
        binding.rlSaldoRdn.visibility = View.GONE
        binding.rlTotal.visibility = View.GONE
        binding.rvValas.visibility = View.GONE
        binding.btnKomposisi.visibility = View.GONE
        binding.rlLayoutSaldo.visibility =View.VISIBLE
        binding.rlRefreshAsset.visibility = View.VISIBLE
        binding.rlRefreshSaldo.visibility = View.VISIBLE
        binding.rlRefreshSaldoRdn.visibility = View.VISIBLE
        binding.rlRefreshAsset.setOnClickListener(View.OnClickListener {
            presenter.setUrlInvestasiForced(GeneralHelper.getString(R.string.url_dashboard_investasi_refresh))
            presenter.getDataInvestasiAssetForce()
            binding.rlRefreshAsset.visibility = View.GONE
            binding.rlRefreshSaldo.visibility = View.GONE
            binding.rlRefreshSaldoRdn.visibility = View.GONE
            onShowSkeletonAsset()
        })
        binding.rlRefreshSaldo.setOnClickListener(View.OnClickListener {
            presenter.setUrlInvestasiForced(GeneralHelper.getString(R.string.url_dashboard_investasi_refresh))
            presenter.getDataInvestasiAssetForce()
            binding.rlRefreshAsset.visibility = View.GONE
            binding.rlRefreshSaldo.visibility = View.GONE
            binding.rlRefreshAsset.visibility = View.GONE
            onShowSkeletonAsset()
        })
        binding.rlRefreshSaldoRdn.setOnClickListener(View.OnClickListener {
            presenter.setUrlInvestasiForced(GeneralHelper.getString(R.string.url_dashboard_investasi_refresh))
            presenter.getDataInvestasiAssetForce()
            binding.rlRefreshAsset.visibility = View.GONE
            binding.rlRefreshSaldo.visibility = View.GONE
            binding.rlRefreshSaldoRdn.visibility = View.GONE
            onShowSkeletonAsset()
        })
    }

    override fun onException21Promo() {
        binding.swipeRefresh.setRefreshing(false)
        skeletonScreenPromo!!.hide()
        binding.rvPromo.visibility = View.GONE
        binding.rlRefreshPromo.visibility = View.VISIBLE
        binding.rlRefreshPromo.setOnClickListener(View.OnClickListener {
            presenter.getDataInvestasiPromo()
            binding.rlRefreshPromo.visibility = View.GONE
            onShowSkeletonPromo()
        })
    }

    override fun onException21Faq() {
        binding.swipeRefresh.setRefreshing(false)
        skeletonScreenFaq!!.hide()
        binding.rvFaq.visibility = View.GONE
        binding.rlRefreshFaq.visibility = View.VISIBLE
        binding.rlRefreshFaq.setOnClickListener(View.OnClickListener {
            presenter.getDataInvestasiFaq()
            binding.rlFaq.visibility = View.GONE
            onShowSkeletonFaq()
        })
    }

    override fun onException21Recommedetion() {
        binding.swipeRefresh.setRefreshing(false)
        skeletonScreenRecommended!!.hide()
        binding.rvRecomendation.visibility = View.GONE
        binding.rlRefreshRecommended.visibility = View.VISIBLE
        binding.rlRefreshRecommended.setOnClickListener(View.OnClickListener {
            presenter.getDataInvestasiRecommedantion()
            binding.rlRefreshRecommended.visibility = View.GONE
            onShowSkeletonRecommended()
        })
    }

    override fun onException12(message: String) {
        showSnackbarErrorMessageRevamp(message, ALERT_ERROR, this, false)
    }

    override fun onException02Rdn(title: String, msg: String) {
        customBottomDialogFragment = CustomBottomDialogFragment(this, title, msg, GeneralHelper.getString(R.string.refresh), GeneralHelper.getString(R.string.drawable_time_out), false, this)
        customBottomDialogFragment!!.setCancelable(false)
        customBottomDialogFragment!!.show(supportFragmentManager, customBottomDialogFragment!!.getTag())
    }

    override fun onException01Rdn(title: String, msg: String) {
        customBottomDialogFragment = CustomBottomDialogFragment(this, GeneralHelper.getString(R.string.txt_data_tidak_sesuai), msg, GeneralHelper.getString(R.string.hint_ok), GeneralHelper.getString(R.string.drawable_transaksi_gagal), true, this)
        customBottomDialogFragment!!.show(supportFragmentManager, "")
    }

    override fun onSuccessSbnDashboardHeader(response: SbnDashboardHeaderResponse) {
        mAccount = response.sbnUserProfile.accountSbn
        mSid = response.sbnUserProfile.sid
        mSre = response.sbnUserProfile.sre
        presenter.getSbnBeliSbn()

    }

    override fun onExceptionTrxExpired(message: String) {
        val i = Intent()
        i.putExtra(Constant.TAG_ERROR_MESSAGE, message)
        setResult(RESULT_CANCELED, i)
        finish()
    }

    override fun onSuccessBeliSbn(response: SbnBeliResponse) {
        ListBeliSbnRevampActivity.launchIntent(this, response,mAccount,mSid,mSre)
    }

    private suspend fun saveContentLocally(urls: List<String>): List<String> = withContext(Dispatchers.IO) {
        urls.mapNotNull { url ->
            // Generate a custom file name (e.g., "recap-blablabla.json")
            val fileName = "recap-${url.substringAfterLast('/')}.json"

            // Use the same directory as in saveJsonLocally
            val localPath = File(getDir("jsonDir", Context.MODE_PRIVATE), fileName).absolutePath

            // Check if the file already exists locally
            val file = File(localPath)
            if (file.exists()) {
                localPath
            } else {
                // Save the JSON file locally if it doesn't exist, using the custom file name
                val savedPath = JsonManager.saveJsonLocally(this@DashboardInvestasiActivity, url, fileName) // Pass custom filename
                if (savedPath != null) {
                    val savedFile = File(savedPath)
                    if (savedFile.exists()) {
                        savedPath
                    } else {
                        null
                    }
                } else {
                    null
                }
            }
        }
    }


    fun transformKeyDataToMapOfDynamicList(keyData: List<KeyDataItem>): List<Map<String, String>> {
        return keyData.map { item ->
            val formattedKey = "{${item.key.replace(" ", "_").lowercase()}}"
            mapOf(formattedKey to item.value)
        }
    }

    override fun onSuccessInvesmentRecap(response: InvestmentRecapResponse) {
        setupDataInvestmentRecap(response)
    }

    override fun onExceptionInvesmentRecap(message: String) {
        skeletonInvesmentRecap?.hide()
        binding.lyLottieAnimation.isVisible = false
        showSnackbarErrorMessageRevamp(message, ALERT_ERROR, this, false)
    }


    fun showSekelton() {
        try{

            onShowSkeletonPromo()

            onShowSkeletonContent()

            onShowSkeletonFaq()

            onShowSkeletonRecommended()

            onShowSkeletonAsset()

            onShowSkeletonMenu()

            onShowSkeletonInvesmentRecap()

        }catch(e : Exception){
        }
    }

    private fun onShowSkeletonAsset() {
        binding.rlRefreshSaldoRdn.visibility = View.GONE
        binding.rlTotal.visibility = View.VISIBLE
        binding.llKeListRekening.visibility = View.GONE
        binding.llDotLoadingSaldo.visibility = View.VISIBLE
        binding.tvTerakhirPerbarui.visibility = View.GONE
        skeletonScreenSaldo = Skeleton.bind(binding.tvTotalInvestasi)
            .shimmer(true)
            .angle(20)
            .duration(1200)
            .load(R.layout.skeleton_long_line_investasi)
            .show()
        skeletonBtnKomposisi = Skeleton.bind(binding.btnKomposisi)
            .shimmer(true)
            .angle(20)
            .duration(1200)
            .load(R.layout.skeleton_short_line_investasi)
            .show()
        skeletonScreenSaldoRdn = Skeleton.bind(binding.rlSaldoRdn)
            .shimmer(true)
            .angle(20)
            .duration(1200)
            .load(R.layout.skeleton_saldo_rdn_investasi)
            .show()
        skeletonScreenShowHideAsset = Skeleton.bind(binding.tbLihatAsset)
            .shimmer(true)
            .angle(20)
            .duration(1200)
            .load(R.layout.skeleton_short_line_investasi)
            .show()
        binding.rvListAset.layoutManager = LinearLayoutManager(this, LinearLayoutManager.VERTICAL, false)
        asetInvestasiAdapter = AsetInvestasiAdapter(this, mListAssetResponse,this)
        skeletonScreenAsset = Skeleton.bind(binding.rvListAset)
            .adapter(asetInvestasiAdapter)
            .shimmer(true)
            .angle(20)
            .frozen(false)
            .duration(1200)
            .load(R.layout.skeleton_aset_investasi)
            .count(4)
            .show()
    }

    fun onShowSkeletonMenu(){
        binding.rvMenu.layoutManager =
            GridLayoutManager(this, 4, GridLayoutManager.VERTICAL, false)
        menuInvestasiAdapter = MenuInvestasiAdapter(this, mSortedMenuAcak, this)
        skeletonScreenMenu = Skeleton.bind(binding.rvMenu)
            .adapter(menuInvestasiAdapter)
            .shimmer(true)
            .angle(20)
            .frozen(false)
            .duration(1200)
            .load(R.layout.skeleton_menu_investasi)
            .count(4)
            .show()
    }

    fun onShowSkeletonPromo(){
        binding.rvPromo.layoutManager = LinearLayoutManager(this, LinearLayoutManager.HORIZONTAL, false)
        promoInvestasiAdapter = PromoInvestasiAdapter(this,mPromoResponse!!,this)
        skeletonScreenPromo = Skeleton.bind(binding.rvPromo)
            .adapter(promoInvestasiAdapter)
            .shimmer(true)
            .angle(20)
            .frozen(false)
            .duration(1200)
            .load(R.layout.skeleton_promo_investasi)
            .count(5)
            .show()
    }

    fun onShowSkeletonRecommended(){
        binding.rvRecomendation.layoutManager = LinearLayoutManager(this, LinearLayoutManager.HORIZONTAL, false)
        recomendationInvestasiAdapter = RecomendationInvestasiAdapter(this,mRecommendeResponse!!,this)
        skeletonScreenRecommended = Skeleton.bind(binding.rvRecomendation)
            .adapter(recomendationInvestasiAdapter)
            .shimmer(true)
            .angle(20)
            .frozen(false)
            .duration(1200)
            .count(5)
            .load(R.layout.skeleton_recommended_investasi)
            .show()
    }
    fun onShowSkeletonContent(){
        binding.rvListContent.layoutManager = LinearLayoutManager(this, LinearLayoutManager.HORIZONTAL, false)
        storyInvestasiAdapter = StoryInvestasiAdapter(this,mStoryResponse!!, this)
        skeletonScreenContent = Skeleton.bind(binding.rvListContent)
            .adapter(storyInvestasiAdapter)
            .shimmer(true)
            .angle(20)
            .frozen(false)
            .duration(1200)
            .count(5)
            .load(R.layout.skeleton_content_investasi)
            .show()
    }

    fun onShowSkeletonInvesmentRecap(){
        skeletonInvesmentRecap = Skeleton.bind(binding.animationView)
            .shimmer(true)
            .angle(20)
            .duration(1200)
            .load(R.layout.skeleton_invesment_recap)
            .show()
    }

    fun onShowSkeletonFaq(){
        binding.rvFaq.layoutManager = LinearLayoutManager(this, LinearLayoutManager.VERTICAL, false)
        faqInvestasiAdapter = FaqInvestasiAdapter(this, mFaqResponse!!)
        skeletonScreenFaq = Skeleton.bind(binding.rvFaq)
            .adapter(faqInvestasiAdapter)
            .shimmer(true)
            .angle(20)
            .frozen(false)
            .duration(1200)
            .count(4)
            .load(R.layout.skeleton_faq_investasi)
            .show()
    }

    override fun onRefresh() {
        showSekelton()
        binding.rlTotal.visibility = View.VISIBLE
        binding.llDotLoadingSaldo.visibility = View.VISIBLE
        binding.llDotLoadingSaldo.visibility = View.VISIBLE
        binding.llKeListRekening.visibility = View.GONE
        binding.tvTerakhirPerbarui.visibility = View.GONE
        binding.rlRefreshSaldo.visibility = View.GONE
        binding.ivDotSaldoRdn.visibility = View.VISIBLE
        binding.ivDotSaldoRdn.visibility = View.VISIBLE
        binding.tvNominalSaldoRdn.visibility = View.GONE
        if (!isShowAsset) {
            hideAsset(isShowAsset)
        } else {
            showAsset(isShowAsset)
        }
        presenter.setUrlInvestasiForced(GeneralHelper.getString(R.string.url_dashboard_investasi_refresh))
        presenter.getDataInvestasiAssetForce()
        presenter.getDataInvestasiPromo()
        presenter.getDataInvestasiContent()
        presenter.getDataInvestasiRecommedantion()
        presenter.getDataInvestasiFaq()
//        presenter.getInvesmentRecap()
    }


    private fun addBubbleShowCase() {
        try {
            valas = BubbleShowCaseBuilder(this) //Activity instance
                .title(GeneralHelper.getString(R.string.txt_title_aset)) //Any title for the bubble view
                .description(GeneralHelper.getString(R.string.txt_desc_aset))
                .backgroundColor(Color.WHITE)
                .textColor(Color.BLACK)
                .buttonTitle(GeneralHelper.getString(R.string.berikutnya_txt))
                .targetView(binding.rvValas)
                .enableLewati(true)
                .enableViewClose(true)
                .textViewLewati(GeneralHelper.getString(R.string.txt_lewati))
                .textColorDesc(R.color.neutral_light60)
                .arrowPosition(BubbleShowCase.ArrowPosition.TOP)
            komposisi = BubbleShowCaseBuilder(this) //Activity instance
                .title(GeneralHelper.getString(R.string.txt_title_komposisi)) //Any title for the bubble view
                .description(GeneralHelper.getString(R.string.txt_desc_komposisi))
                .backgroundColor(Color.WHITE)
                .textColor(Color.BLACK)
                .buttonTitle(GeneralHelper.getString(R.string.berikutnya_txt))
                .targetView(binding.btnKomposisi)
                .enableLewati(true)
                .textViewLewati(GeneralHelper.getString(R.string.txt_lewati))
                .textColorDesc(R.color.neutral_light60)
                .arrowPosition(BubbleShowCase.ArrowPosition.TOP)
            aset = BubbleShowCaseBuilder(this) //Activity instance
                .title(GeneralHelper.getString(R.string.txt_title_no_aset))//Any title for the bubble view
                .description(GeneralHelper.getString(R.string.txt_desc_no_aset))
                .backgroundColor(Color.WHITE)
                .textColor(Color.BLACK)
                .targetView(binding.rlMenuInvestasi)
                .buttonTitle(GeneralHelper.getString(R.string.close))
                .textViewLewati("")
                .enableLewati(true)
                .textColorDesc(R.color.neutral_light60)
                .arrowPosition(BubbleShowCase.ArrowPosition.BOTTOM)
            val list: MutableList<BubbleShowCaseBuilder> = java.util.ArrayList()
            list.add(valas!!)
            list.add(komposisi!!)
            list.add(aset!!)
            bubleInvestasi.addShowCases(list)
            bubleInvestasi.show()
        } catch (e: Exception) {
        }
    }

    private fun addBubbleShowCaseNoCurrency() {
        try {
            komposisi = BubbleShowCaseBuilder(this) //Activity instance
                .title(GeneralHelper.getString(R.string.txt_title_komposisi)) //Any title for the bubble view
                .description(GeneralHelper.getString(R.string.txt_desc_komposisi))
                .backgroundColor(Color.WHITE)
                .textColor(Color.BLACK)
                .buttonTitle(GeneralHelper.getString(R.string.berikutnya_txt))
                .targetView(binding.btnKomposisi)
                .enableLewati(true)
                .textViewLewati(GeneralHelper.getString(R.string.txt_lewati))
                .textColorDesc(R.color.neutral_light60)
                .arrowPosition(BubbleShowCase.ArrowPosition.TOP)
            aset = BubbleShowCaseBuilder(this) //Activity instance
                .title(GeneralHelper.getString(R.string.txt_title_no_aset))//Any title for the bubble view
                .description(GeneralHelper.getString(R.string.txt_desc_no_aset))
                .backgroundColor(Color.WHITE)
                .textColor(Color.BLACK)
                .targetView(binding.rlMenuInvestasi)
                .buttonTitle(GeneralHelper.getString(R.string.close))
                .textViewLewati("")
                .enableLewati(true)
                .textColorDesc(R.color.neutral_light60)
                .arrowPosition(BubbleShowCase.ArrowPosition.BOTTOM)
            val list: MutableList<BubbleShowCaseBuilder> = java.util.ArrayList()
            list.add(komposisi!!)
            list.add(aset!!)
            bubleInvestasiNoCurrency.addShowCases(list)
            bubleInvestasiNoCurrency.show()
        } catch (e: Exception) {
        }
    }

    private fun addBubbleShowCaseNoAset() {
        try {
            aset = BubbleShowCaseBuilder(this) //Activity instance
                .title(GeneralHelper.getString(R.string.txt_title_no_aset)) //Any title for the bubble view
                .description(GeneralHelper.getString(R.string.txt_desc_no_aset))
                .backgroundColor(Color.WHITE)
                .textColor(Color.BLACK)
                .targetView(binding.rlMenuInvestasi)
                .buttonTitle(GeneralHelper.getString(R.string.close))
                .textViewLewati("")
                .enableLewati(true)
                .textColorDesc(R.color.neutral_light60)
                .arrowPosition(BubbleShowCase.ArrowPosition.BOTTOM)

            aset!!.show()
        } catch (e: Exception) {
        }
    }


    override fun onClick(p0: View?) {
        when(p0!!.id){
            R.id.btn_komposisi -> {
                val bottomFragmentKomposisi = BottomFragmentKomposisi(this,mAssetResponse!!,mPosition!!)
                bottomFragmentKomposisi.show(supportFragmentManager, "")
            }
            R.id.img_bawah ->{
                binding.scrollView.smoothScrollTo(0,0)
            }
            R.id.btn_buy ->{
                if (mAssetResponse!!.rdn!!.mapping == "rdn_open"){
                    presenter.setUrlBukaRdn(GeneralHelper.getString(R.string.url_rdn_onboarding_new))
                    presenter.onGetOnBoarding()
                }else if (mAssetResponse!!.rdn!!.mapping == "rdn_topup"){
                    presenter.getListAccountRdn()
                }else{
                    DashboardRdnRevampActivity.launchIntent(this)
                }

            }
            R.id.ll_menu_brights -> {
                BrowserIntentActivity.launchIntent(this, GeneralHelper.getString(R.string.url_get_launcher), Constant.MENU_BRIGHTS)

            }
            R.id.ll_menu_ksei -> {
                DashboardKseiActivity.launchIntent(this,false)
            }
            R.id.ll_saldo_rdn ->{
                DashboardRdnRevampActivity.launchIntent(this)
            }
            R.id.animationView -> {
                if (SystemClock.elapsedRealtime() - mLastClickTime < 1000) {
                    return
                }

                mapOfDynamicLists?.let { mapOfDynamicListsNonNull ->
                    mLastClickTime = SystemClock.elapsedRealtime()

                    val bottomSheetFragment = BottomFragmentInvestmentRecap(
                        this@DashboardInvestasiActivity,
                        listLottieTaskRecap,
                        "invement_recap",
                        mapOfDynamicListsNonNull,
                        onClickShare = {
                            requestPermission(mResponseInvesmentRecap.shareImage)
                        },
                    )
                    bottomSheetFragment.show(supportFragmentManager, bottomSheetFragment.tag)
                    GeneralHelper.dismissDialog()
                }
            }
        }
    }

    private fun setupDataInvestmentRecap(response: InvestmentRecapResponse) {
        binding.lyLottieAnimation.isVisible = response.recapAvailable

        if (response.recapAvailable){
            mResponseInvesmentRecap = response

            val urls = mResponseInvesmentRecap.dataRecap.map { it.jsonPath }
            val urlsCoverRecap = mutableListOf<String>()
            mapOfDynamicLists = mResponseInvesmentRecap.dataRecap.map { recapPage ->
                transformKeyDataToMapOfDynamicList(recapPage.keyData)
            }

            lifecycleScope.launch {
                //For preparing lottie recap investment
                val localListRecapPaths = saveContentLocally(urls)
                val tempListLottieTask = mutableListOf<LottieTask<LottieComposition>>()

                mapOfDynamicLists?.forEachIndexed { index, _ ->
                    val animationPath = localListRecapPaths.getOrNull(index)
                    val inputStream = BufferedInputStream(FileInputStream(animationPath), 1024)
                    val task = LottieCompositionFactory.fromJsonInputStream(inputStream, null)
                    tempListLottieTask.add(task)
                }

                listLottieTaskRecap.clear()
                listLottieTaskRecap.addAll(tempListLottieTask)

                //For handle animation cover recap
                urlsCoverRecap.add(mResponseInvesmentRecap.recapCover)
                val animationCoverRecapPath = saveContentLocally(urlsCoverRecap)
                val inputStreamCoverRecap = BufferedInputStream(FileInputStream(animationCoverRecapPath[0]), 1024)
                val lottieTaskCoverRecap = LottieCompositionFactory.fromJsonInputStream(inputStreamCoverRecap, null)
                lottieTaskCoverRecap.addListener { composition ->
                    composition?.let {
                        skeletonInvesmentRecap?.hide()
                        binding.animationView.setComposition(it)
                        binding.animationView.playAnimation()
                    }
                }.addFailureListener {
                    // Handle failure
                }
            }
        }
    }

    override fun ClickValasInvestasi(position: Int, listResponse: List<DashboardInvestasiResponse.Investment>) {
        mPosition = position
        if (!isShowAsset) {
            hideAsset(isShowAsset)
        } else {
            showAsset(isShowAsset)
        }
        if (listResponse[mPosition!!].totalStatus == "12"){
            binding.rlNoAset.visibility = View.GONE
            binding.rlTotal.visibility = View.GONE
            binding.btnKomposisi.visibility = View.GONE
            binding.rlLayoutSaldo.visibility =View.VISIBLE
            binding.rlRefreshSaldo.visibility = View.VISIBLE
            binding.rvListAset.visibility = View.VISIBLE
            binding.tvNominalSaldoRdn.text = mAssetResponse!!.rdn!!.totalString
            binding.rlRefreshAsset.setOnClickListener(View.OnClickListener {
                presenter.setUrlInvestasiForced(GeneralHelper.getString(R.string.url_dashboard_investasi_refresh))
                presenter.getDataInvestasiAssetForce()
                binding.rlRefreshAsset.visibility = View.GONE
                binding.rlRefreshSaldo.visibility = View.GONE
                binding.rlRefreshSaldoRdn.visibility = View.GONE
                onShowSkeletonAsset()
            })
            binding.rlRefreshSaldo.setOnClickListener(View.OnClickListener {
                presenter.setUrlInvestasiForced(GeneralHelper.getString(R.string.url_dashboard_investasi_refresh))
                presenter.getDataInvestasiAssetForce()
                binding.rlRefreshAsset.visibility = View.GONE
                binding.rlRefreshSaldo.visibility = View.GONE
                binding.rlRefreshAsset.visibility = View.GONE
                onShowSkeletonAsset()
            })
            binding.rlRefreshSaldoRdn.setOnClickListener(View.OnClickListener {
                presenter.setUrlInvestasiForced(GeneralHelper.getString(R.string.url_dashboard_investasi_refresh))
                presenter.getDataInvestasiAssetForce()
                binding.rlRefreshAsset.visibility = View.GONE
                binding.rlRefreshSaldo.visibility = View.GONE
                binding.rlRefreshSaldoRdn.visibility = View.GONE
                onShowSkeletonAsset()
            })
        }else{
            if (listResponse[mPosition!!].status == 0){
                binding.rlNoAset.visibility = View.VISIBLE
                binding.rlLayoutSaldo.visibility = View.GONE

            }else {
                binding.rlNoAset.visibility = View.GONE
                binding.rlLayoutSaldo.visibility = View.VISIBLE
                binding.llKeListRekening.visibility = View.VISIBLE
                binding.tvTerakhirPerbarui.visibility = View.VISIBLE
                binding.llDotLoadingSaldo.visibility = View.GONE
            }
        }

        if (mAssetResponse!!.rdn!!.assetStatus.equals("01")){
            binding.tvTitleBukaRdn.visibility = View.VISIBLE
            binding.ivDotSaldoRdn.visibility = View.GONE
            binding.tvSubtitleBukaRdn.visibility = View.VISIBLE
            binding.tvTitleSaldoRdn.visibility = View.GONE
            binding.tvNominalSaldoRdn.visibility = View.GONE
            binding.ivArrow.visibility = View.GONE
            binding.tvTitleBukaRdn.text = mAssetResponse!!.rdn!!.title
            binding.tvSubtitleBukaRdn.text = mAssetResponse!!.rdn!!.subtitle
            binding.btnBuy.text = mAssetResponse!!.rdn!!.buttonString

        }else if (mAssetResponse!!.rdn!!.assetStatus.equals("12")){
            binding.rlRefreshSaldoRdn.visibility = View.VISIBLE
            binding.rlSaldoRdn.visibility = View.GONE
            binding.rlRefreshSaldoRdn.setOnClickListener(View.OnClickListener {
                presenter.setUrlInvestasiForced(GeneralHelper.getString(R.string.url_dashboard_investasi_refresh))
                presenter.getDataInvestasiAssetForce()
                binding.rlRefreshAsset.visibility = View.GONE
                binding.rlRefreshSaldo.visibility = View.GONE
                binding.rlRefreshSaldoRdn.visibility = View.GONE
                onShowSkeletonAsset()
            })
        }
        else{
            binding.tvTitleBukaRdn.visibility = View.GONE
            binding.tvSubtitleBukaRdn.visibility = View.GONE
            binding.ivArrow.visibility = View.VISIBLE
            binding.tvNominalSaldoRdn.text = mAssetResponse!!.rdn!!.totalString
            binding.btnBuy.text = mAssetResponse!!.rdn!!.buttonString
            binding.rlSaldoRdn.visibility = View.VISIBLE
        }

        binding.tvTotalSaldo.text = listResponse[position].totalString
        asetInvestasiAdapter = AsetInvestasiAdapter(this, listResponse[position].assets!!,this)
        binding.rvListAset.adapter = asetInvestasiAdapter
        binding.rvListAset.layoutManager = LinearLayoutManager(this, LinearLayoutManager.VERTICAL, false)
        asetInvestasiAdapter?.showAsset(isShowAsset)
    }


    fun autoScroll() {
        handlerPromo = Handler()
        runnablePromo = object : Runnable {
            var count = 0
            var flag = true
            override fun run() {
                if (promoInvestasiAdapter?.itemCount ?: 0 > 1) { // Check if there is more than one item
                    if (count < promoInvestasiAdapter!!.itemCount) {
                        if (count == promoInvestasiAdapter!!.itemCount - 1) {
                            flag = false
                        } else if (count == 0) {
                            flag = true
                        }
                        if (flag) count++ else count--
                        binding.rvPromo.smoothScrollToPosition(count)
                        handlerPromo!!.postDelayed(this, speedScroll.toLong())
                    }
                }
            }
        }
        handlerPromo?.postDelayed(runnablePromo!!, speedScroll.toLong())

        // Stop auto scrolling when user interacts with the RecyclerView
        binding.rvPromo.setOnTouchListener { _, motionEvent ->
            when (motionEvent.action) {
                MotionEvent.ACTION_DOWN, MotionEvent.ACTION_MOVE -> {
                    handlerPromo?.removeCallbacks(runnablePromo!!)
                }
                MotionEvent.ACTION_UP, MotionEvent.ACTION_CANCEL -> {
                    handlerPromo?.postDelayed(runnablePromo!!, speedScroll.toLong())
                }
            }
            false
        }
    }

    override fun onClickRecommended(route: String) {
        routerMenu(route)
    }

    override fun onClickAsset(route : String, isRefresh : Boolean) {
        if (isRefresh){
            presenter.setUrlInvestasiForced(GeneralHelper.getString(R.string.url_dashboard_investasi_refresh))
            presenter.getDataInvestasiAssetForce()
            binding.rlRefreshAsset.visibility = View.GONE
            binding.rlRefreshSaldo.visibility = View.GONE
            binding.rlRefreshAsset.visibility = View.GONE
            onShowSkeletonAsset()
        }else {
            routerMenu(route)
        }
    }

    override fun onClickAction() {
    }

    override fun onClickPromo(route: String) {
        routerMenu(route)
    }

    override fun onClickDialog() {
        onRefresh()
    }

    override fun onActivityResult(requestCode: Int, resultCode: Int, data: Intent?) {
        super.onActivityResult(requestCode, resultCode, data)
        if (requestCode == Constant.REQ_PAYMENT) {
            if (resultCode == RESULT_OK && data != null) {
                binding.scrollView.post {
                    binding.scrollView.smoothScrollTo(binding.scrollView.bottom, 0)
                }
                onRefresh()
            } else if(resultCode== RESULT_OK){
                binding.scrollView.post {
                    binding.scrollView.smoothScrollTo(binding.scrollView.bottom, 0)
                }
                onRefresh()
            }
            else if (resultCode == RESULT_CANCELED && data != null) {
                val errorMesage = data.getStringExtra(Constant.TAG_ERROR_MESSAGE)
                showSnackbarErrorMessageRevamp(errorMesage, ALERT_ERROR, this, false)
            }
        }
        if (requestCode == Constant.REQ_BUKA_REKENING) {
            if (resultCode == RESULT_OK && data != null) {
                onRefresh()
            } else if(resultCode== RESULT_OK){
                onRefresh()
            }
            else if(resultCode== RESULT_FIRST_USER && data != null){
                onRefresh()
            }
            else if (resultCode == RESULT_CANCELED && data != null) {
                onRefresh()
            }
        }
    }

    override fun onShowProgress() {
        GeneralHelper.showDialog(this)
    }

    override fun onDismissProgress() {
        GeneralHelper.dismissDialog()
    }


    private fun requestPermission(imageUrl: String) {
        downloadImageToTempFile(imageUrl) { file ->
            file?.let { shareTempImage(it) }
        }
    }

    private fun downloadImageToTempFile(url: String, callback: (File?) -> Unit) {
        // Validate URL
        if (url.isEmpty()) {
            onException(GeneralHelper.getString(R.string.txt_transaksi_gagal))
            callback(null)
            return
        }

        // Create a temporary file in the files directory
        val tempFile = File(filesDir, "recap_share_${System.currentTimeMillis()}.png")

        // Use Glide to download the image
        Glide.with(this)
            .asBitmap()
            .load(url)
            .into(object : CustomTarget<Bitmap>() {
                override fun onLoadCleared(placeholder: Drawable?) {
                    // Handle placeholder if needed
                }

                override fun onLoadFailed(errorDrawable: Drawable?) {
                    onException(GeneralHelper.getString(R.string.txt_transaksi_gagal))
                    callback(null) // Handle error case
                }

                override fun onResourceReady(resource: Bitmap, transition: Transition<in Bitmap>?) {
                    try {
                        FileOutputStream(tempFile).use { fOut ->
                            resource.compress(Bitmap.CompressFormat.PNG, 90, fOut)
                            fOut.flush()
                        }
                        callback(tempFile) // Return the temporary file
                    } catch (e: Exception) {
                        callback(null)
                    }
                }
            })
    }


    private fun shareTempImage(tempFile: File) {

        if (!tempFile.exists()) {
            return
        }

        try {
            val fileUri = FileProvider.getUriForFile(
                this,
                "${BuildConfig.APPLICATION_ID}.fileprovider",
                tempFile
            )

            val shareIntent = Intent(Intent.ACTION_SEND).apply {
                type = "image/*"
                putExtra(Intent.EXTRA_STREAM, fileUri)
                addFlags(Intent.FLAG_GRANT_READ_URI_PERMISSION) // Grant read permission
            }

            val resInfoList = packageManager.queryIntentActivities(shareIntent, PackageManager.MATCH_DEFAULT_ONLY)
            for (resolveInfo in resInfoList) {
                val packageName = resolveInfo.activityInfo.packageName
                grantUriPermission(packageName, fileUri, Intent.FLAG_GRANT_READ_URI_PERMISSION)
            }

            startActivity(Intent.createChooser(shareIntent, "Share image via"))
        } catch (e: IllegalArgumentException) {
            //do nothing
        } catch (e: SecurityException) {
            //do nothing
        }
    }




    override fun onClickMenuInvestasi(idMenu: Int, mampingRoute: String) {
        if (SystemClock.elapsedRealtime() - mLastClickTime < 1000) {
            return
        }

        if (idMenu == Constant.MENU_EMAS){
            DashboardEmasActivity.launchIntent(this,false)
        }
        else if (idMenu == Constant.MENU_ESBN){
            DashboardSbnRevampActivity.launchIntent(this, false, false)
        }
        else if(idMenu == Constant.MENU_DPLK){
            DashboardDplkRevampActivity.launchIntent(this@DashboardInvestasiActivity)
        }
        else if (idMenu == Constant.MENU_DEPOSITO){
            DashboardDepositoActivity.launchIntent(this,false)
        }
        else if(idMenu == Constant.MENU_RENCANA && mampingRoute == "" || idMenu == Constant.MENU_RENCANA && mampingRoute == "rencana_dashboard"){
            DashboardRencanaActivity.launchIntent(this, false)
        }
        else if (idMenu == Constant.MENU_RDN){
            DashboardRdnRevampActivity.launchIntent(this@DashboardInvestasiActivity)
        }
    }

    fun routerMenu(route: String){
        if (SystemClock.elapsedRealtime() - mLastClickTime < 1000) {
            return
        }
        if (route == "deposito_dashboard"){
            DashboardDepositoActivity.launchIntent(this,false, true)
        }else if (route == "emas_dashboard"){
            DashboardEmasActivity.launchIntent(this,false)
        }
        else if (route == "emas_open") {
            DashboardEmasActivity.launchIntentRegis(this,false)
        }
        else if (route == "sbn_dashboard" || route == "sbn_open"){
            DashboardSbnRevampActivity.launchIntent(this, false,false)
        }
        else if (route == "dplk_dashboard"){
            DashboardDplkRevampActivity.launchIntent(this@DashboardInvestasiActivity)
        }
        else if(route == "dplk_open"){
            DashboardDplkRevampActivity.launchIntent(this@DashboardInvestasiActivity)
        }
        else if (route == "deposito_open"){
            presenter.getDepositoRegis()
        }
        else if (route == "ksei_dashboard"){
            DashboardKseiActivity.launchIntent(this,false)
        }
        else if (route == "rencana_dashboard"){
            DashboardRencanaActivity.launchIntent(this, false)
        }
        else if (route == "sbn_buy"){
            presenter.getSbnDashboardHeader()
        }
        else if(route == "emas_buy"){
            DashboardEmasActivity.launchIntent(this,true)
        }
        else if(route == "rencana_open"){
            TabunganActivity.launchIntentTabungan(this@DashboardInvestasiActivity, Constant.OPEN_ACCOUNT_RENCANA)
        }
    }

}