package id.co.bri.brimo.models.apimodel.request;

import com.google.gson.annotations.SerializedName;

public class InquiryPbbRequest {
    @SerializedName("code_wilayah")
    private String codeWilayah;

    @SerializedName("no_pembayaran")
    private String noPembayaran;

    @SerializedName("tahun")
    private String tahun;

    public InquiryPbbRequest(String codeWilayah, String noPembayaran, String tahun) {
        this.codeWilayah = codeWilayah;
        this.noPembayaran = noPembayaran;
        this.tahun = tahun;
    }

    public String getCodeWilayah() {
        return codeWilayah;
    }

    public void setCodeWilayah(String codeWilayah) {
        this.codeWilayah = codeWilayah;
    }

    public String getNoPembayaran() {
        return noPembayaran;
    }

    public void setNoPembayaran(String noPembayaran) {
        this.noPembayaran = noPembayaran;
    }

    public String getTahun() {
        return tahun;
    }

    public void setTahun(String tahun) {
        this.tahun = tahun;
    }
}
