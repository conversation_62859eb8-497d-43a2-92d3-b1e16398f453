package id.co.bri.brimo.ui.activities.cc_sof;

import android.app.Activity;
import android.app.DatePickerDialog;
import android.content.Intent;
import android.graphics.Canvas;
import android.graphics.Paint;
import android.os.Bundle;
import android.text.Editable;
import android.text.Spannable;
import android.text.TextWatcher;
import android.text.style.ReplacementSpan;
import android.view.View;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;

import com.google.gson.Gson;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.Calendar;
import java.util.Date;
import java.util.Locale;

import javax.inject.Inject;

import id.co.bri.brimo.R;
import id.co.bri.brimo.contract.IPresenter.cc_sof.IFormAktivasiCcSofPresenter;
import id.co.bri.brimo.contract.IView.cc_sof.IFormAktivasiCcSofView;
import id.co.bri.brimo.databinding.ActivityFormAktivasiCcSofBinding;
import id.co.bri.brimo.domain.config.Constant;
import id.co.bri.brimo.domain.helpers.GeneralHelper;
import id.co.bri.brimo.domain.helpers.calendar.CalendarHelper;
import id.co.bri.brimo.models.apimodel.request.ActivateCcSofRequest;
import id.co.bri.brimo.models.apimodel.response.cc.DetailCcSofResponse;
import id.co.bri.brimo.models.apimodel.response.GeneralOtpResponse;
import id.co.bri.brimo.ui.activities.GeneralOtpActivity;
import id.co.bri.brimo.ui.activities.base.BaseActivity;

public class FormAktivasiCcSofActivity extends BaseActivity implements
        View.OnClickListener,
        IFormAktivasiCcSofView {

    private ActivityFormAktivasiCcSofBinding binding;

    @Inject
    IFormAktivasiCcSofPresenter<IFormAktivasiCcSofView> presenter;

    protected static final String TAG_RESPONSE = "response";
    private static final String FROMQR = "fromqr";

    protected int maxLength = 4;
    protected boolean internalStopFormatFlag;
    protected boolean isValidExp = false;

    protected DetailCcSofResponse detailResponse;

    protected final Calendar myCalendar = Calendar.getInstance();
    protected final Calendar calendarSetting = Calendar.getInstance();

    protected String tglLahirKode;
    protected String tglLahir;

    public static void launchIntent(Activity caller, DetailCcSofResponse detailCcSofResponse) {
        Intent intent = new Intent(caller, FormAktivasiCcSofActivity.class);
        intent.putExtra(TAG_RESPONSE, new Gson().toJson(detailCcSofResponse));
        caller.startActivityForResult(intent, Constant.REQ_CC_SOF);
    }

    public static void launchIntentFromQr(Activity caller, DetailCcSofResponse detailCcSofResponse) {
        Intent intent = new Intent(caller, FormAktivasiCcSofActivity.class);
        intent.putExtra(TAG_RESPONSE, new Gson().toJson(detailCcSofResponse));
        intent.putExtra(FROMQR, true);
        caller.startActivityForResult(intent, Constant.REQ_CC_SOF);
    }

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        binding = ActivityFormAktivasiCcSofBinding.inflate(getLayoutInflater());
        setContentView(binding.getRoot());

        GeneralHelper.setToolbar(this, binding.toolbar.toolbar, GeneralHelper.getString(R.string.aktivasi));

        if (getIntent().getExtras().getString(TAG_RESPONSE) != null)
            detailResponse = new Gson().fromJson(getIntent().getExtras().getString(TAG_RESPONSE), DetailCcSofResponse.class);

        injectDependency();
        setupView();
    }

    private void injectDependency() {
        getActivityComponent().inject(this);
        if (presenter != null) {
            presenter.setView(this);
            presenter.start();
            presenter.setUrl(GeneralHelper.getString(R.string.url_cc_sof_validate));
        }
    }

    private void setupView() {
        binding.etTglLahir.setOnClickListener(this);
        binding.btAktivasi.setOnClickListener(this);

        binding.etNoKredit.setText(detailResponse.getCardNumber());

        binding.etExpDate.addTextChangedListener(new TextWatcher() {
            @Override
            public void beforeTextChanged(CharSequence s, int start, int count, int after) {
                // Do nothing
            }

            @Override
            public void onTextChanged(CharSequence s, int start, int before, int count) {
                // Do nothing
            }

            @Override
            public void afterTextChanged(Editable editable) {
                if (internalStopFormatFlag) {
                    return;
                }
                internalStopFormatFlag = true;
                formatExpiryDate(editable, maxLength);
                internalStopFormatFlag = false;

                String expDate = editable.toString();
                String month;
                if (expDate.isEmpty()) isValidExp = false;
                else if (expDate.length() < 4) isValidExp = false;
                else {
                    month = expDate.substring(0, 2);
                    if (Integer.parseInt(month) > 12 || Integer.parseInt(month) <= 0)
                        isValidExp = false;
                    else isValidExp = true;
                }
                checkValidate();
            }
        });

        binding.etTglLahir.addTextChangedListener(new TextWatcher() {
            @Override
            public void beforeTextChanged(CharSequence s, int start, int count, int after) {
                // Do nothing
            }

            @Override
            public void onTextChanged(CharSequence s, int start, int before, int count) {
                checkValidate();
            }

            @Override
            public void afterTextChanged(Editable s) {
                // Do nothing
            }
        });
    }

    protected void checkValidate() {
        if (isValidExp && binding.etTglLahir.length() != 0) {
            binding.btAktivasi.setEnabled(true);
            binding.btAktivasi.setAlpha(1);
        } else {
            binding.btAktivasi.setEnabled(false);
            binding.btAktivasi.setAlpha(0.3f);
        }
    }

    @Override
    public void onClick(View v) {
        switch (v.getId()) {
            case R.id.et_tgl_lahir:
                pickTglLahir();
                break;
            case R.id.bt_aktivasi:
                sendData();
                break;
            default:
                break;
        }
    }

    private void sendData() {
        String strExpDate = binding.etExpDate.getText().toString().replace("/", "");

        ActivateCcSofRequest request = new ActivateCcSofRequest(detailResponse.getCardToken(), strExpDate, parseDateToyyyyMMdd(tglLahirKode), "");
        presenter.getDataAktivasi(request);
    }

    public String parseDateToyyyyMMdd(String dateInput) {
        String inputDate = "dd MM yyyy";
        String outputDate = "yyyy-MM-dd";
        SimpleDateFormat inputFormat = new SimpleDateFormat(inputDate);
        SimpleDateFormat outputFormat = new SimpleDateFormat(outputDate);

        Date date;
        String str = null;

        try {
            date = inputFormat.parse(dateInput);
            if (date != null) {
                str = outputFormat.format(date);
            }

        } catch (ParseException e) {
            // do nothing
        }
        return str;
    }

    @Override
    public void onSuccessAktivasi(GeneralOtpResponse generalOtpResponse) {
        if (getIntent().hasExtra(FROMQR)) {
            GeneralOtpActivity.launchIntentFromQr(this,
                    generalOtpResponse,
                    GeneralHelper.getString(R.string.aktivasi),
                    GeneralHelper.getString(R.string.verifikasi_data),
                    GeneralHelper.getString(R.string.url_cc_sof_validation_otp),
                    GeneralHelper.getString(R.string.url_cc_sof_resend_otp),
                    true);
        } else {
            GeneralOtpActivity.launchIntent(this,
                    generalOtpResponse,
                    GeneralHelper.getString(R.string.aktivasi),
                    GeneralHelper.getString(R.string.verifikasi_data),
                    GeneralHelper.getString(R.string.url_cc_sof_validation_otp),
                    GeneralHelper.getString(R.string.url_cc_sof_resend_otp),
                    true);
        }
    }

    @Override
    public void onException93(String desc) {
        Intent resultIntent = new Intent();
        resultIntent.putExtra(Constant.TAG_ERROR_MESSAGE, desc);

        setResult(Activity.RESULT_CANCELED, resultIntent);
        finish();
    }

    public static class SlashSpan extends ReplacementSpan {

        public SlashSpan() {
            // do nothing
        }

        @Override
        public int getSize(@NonNull Paint paint, CharSequence text, int start, int end, Paint.FontMetricsInt fm) {
            float[] widths = new float[end - start];
            float[] slashWidth = new float[1];
            paint.getTextWidths(text, start, end, widths);
            paint.getTextWidths("/", slashWidth);
            int sum = (int) slashWidth[0];
            for (int i = 0; i < widths.length; ++i) {
                sum += widths[i];
            }
            return sum;
        }

        @Override
        public void draw(@NonNull Canvas canvas, CharSequence text, int start, int end, float x, int top, int y, int bottom, @NonNull Paint paint) {
            String xtext = "/" + text.subSequence(start, end);
            canvas.drawText(xtext, 0, xtext.length(), x, y, paint);
        }
    }

    /**
     * method untuk format Xpired Date
     *
     * @param expiryDate
     * @param maxLength
     */
    public static void formatExpiryDate(@NonNull Editable expiryDate, int maxLength) {
        int textLength = expiryDate.length();
        // first remove any previous span
        SlashSpan[] spans = expiryDate.getSpans(0, expiryDate.length(), SlashSpan.class);
        for (int i = 0; i < spans.length; ++i) {
            expiryDate.removeSpan(spans[i]);
        }
        // then truncate to max length
        if (maxLength > 0 && textLength > maxLength - 1) {
            expiryDate.replace(maxLength, textLength, "");
            --textLength;
        }
        // finally add margin spans
        for (int i = 1; i <= ((textLength - 1) / 2); ++i) {
            int end = i * 2 + 1;
            int start = end - 1;
            SlashSpan marginSPan = new SlashSpan();
            expiryDate.setSpan(marginSPan, start, end, Spannable.SPAN_EXCLUSIVE_EXCLUSIVE);
        }
    }

    /**
     * method untuk setCalendar
     */
    private void pickTglLahir() {
        DatePickerDialog.OnDateSetListener date = (view, year, monthOfYear, dayOfMonth) -> {
            myCalendar.set(Calendar.YEAR, year);
            myCalendar.set(Calendar.MONTH, monthOfYear);
            myCalendar.set(Calendar.DAY_OF_MONTH, dayOfMonth);
            updateLabel();
        };
        DatePickerDialog datePickerDialog = new DatePickerDialog(this, date, myCalendar
                .get(Calendar.YEAR), myCalendar.get(Calendar.MONTH),
                myCalendar.get(Calendar.DAY_OF_MONTH));

        if (myCalendar.get(Calendar.YEAR) == calendarSetting.get(Calendar.YEAR)) {
            myCalendar.add(Calendar.YEAR, -17);
        } else {
            myCalendar.clear(Calendar.YEAR);
            myCalendar.add(Calendar.YEAR, +((calendarSetting.get(Calendar.YEAR) - myCalendar.get(Calendar.YEAR)) - 17));
        }
        datePickerDialog.getDatePicker().setMaxDate(myCalendar.getTimeInMillis());
        datePickerDialog.show();
    }

    private void updateLabel() {
        String myFormat = "dd MM yyyy";
        SimpleDateFormat sdf = new SimpleDateFormat(myFormat, Locale.getDefault());

        tglLahirKode = sdf.format(myCalendar.getTime());
        tglLahir = tglLahirKode.substring(0, 3) + CalendarHelper.convertMont(tglLahirKode.substring(3, 5)) + tglLahirKode.substring(5, 10);
        binding.etTglLahir.setText(tglLahir);
    }

    @Override
    protected void onActivityResult(int requestCode, int resultCode, @Nullable Intent data) {
        super.onActivityResult(requestCode, resultCode, data);
        if (requestCode == Constant.REQ_CC_SOF) {
            if (resultCode == Activity.RESULT_OK && data != null) {
                setResult(Activity.RESULT_OK, data);
                finish();
            }
            if (resultCode == Activity.RESULT_CANCELED && data != null) {
                if (data.hasExtra(Constant.TAG_ERROR_MESSAGE)) {
                    onException(data.getStringExtra(Constant.TAG_ERROR_MESSAGE));
                }
                if (data.hasExtra(Constant.TAG_CONFIRM_DIALOG)) {
                    setResult(Activity.RESULT_CANCELED, data);
                    finish();
                }
            }
        }
    }

    @Override
    protected void onDestroy() {
        if (presenter != null)
            presenter.stop();
        binding = null;
        super.onDestroy();
    }
}