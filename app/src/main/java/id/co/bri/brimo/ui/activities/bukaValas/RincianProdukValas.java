package id.co.bri.brimo.ui.activities.bukaValas;


import android.app.Activity;
import android.content.Intent;
import android.os.Bundle;
import android.view.View;
import android.widget.LinearLayout;
import androidx.fragment.app.Fragment;
import androidx.viewpager.widget.ViewPager;
import java.util.ArrayList;
import java.util.List;
import id.co.bri.brimo.R;
import id.co.bri.brimo.adapters.CatatanKeuanganAdapter;
import id.co.bri.brimo.databinding.ActivityRincianProdukValasBinding;
import id.co.bri.brimo.domain.helpers.GeneralHelper;
import id.co.bri.brimo.models.apimodel.response.bukarekening.JenisTabunganResponse;
import id.co.bri.brimo.ui.activities.base.BaseActivity;
import id.co.bri.brimo.ui.fragments.DetailTabTabunganFragment;

public class RincianProdukValas extends BaseActivity implements ViewPager.OnPageChangeListener, View.OnClickListener {

    private ActivityRincianProdukValasBinding binding;

    private static String tipeProduk, descTabungan, imageTabungan;
    private static List<String> titleTab = new ArrayList<>();
    private static List<String> webTab = new ArrayList<>();

    LinearLayout linearLayout;

    public static void launchIntent(Activity caller, List<JenisTabunganResponse.Product> productList, int position) {
        Intent intent = new Intent(caller, RincianProdukValas.class);


        tipeProduk = productList.get(position).getProductId();
        imageTabungan = productList.get(position).getProductUrlPath();
        descTabungan = productList.get(position).getFullDescription();

        titleTab.clear();
        webTab.clear();
        for (int i = 0; i < productList.get(position).getTab().size(); i++) {
            titleTab.add(productList.get(position).getTab().get(i).getTitle());
            webTab.add(productList.get(position).getTab().get(i).getContent());
        }

        caller.startActivity(intent);
    }

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        binding = ActivityRincianProdukValasBinding.inflate(getLayoutInflater());
        setContentView(binding.getRoot());

//        injectDependency();
        GeneralHelper.setToolbar(this, binding.toolbar.toolbar, "Britama Valas");
        binding.btnSubmit.setOnClickListener(this);
        setupView();
    }

    private void setupView() {
        GeneralHelper.loadImageUrl(getApplicationContext(), imageTabungan, binding.imageRekening, R.drawable.tabungan_bri_brit_ama_valas, 0);
        binding.textRekening.setText(descTabungan);

        List<Fragment> fragmentList = new ArrayList<>();
        for (int i = 0; i < webTab.size(); i++) {
            fragmentList.add(new DetailTabTabunganFragment(webTab.get(i)));
        }

        CatatanKeuanganAdapter viewAdapter = new CatatanKeuanganAdapter(getSupportFragmentManager(), this, fragmentList, titleTab);
        binding.viewpager.setAdapter(viewAdapter);
        binding.tabDetailRekening.setViewPager(binding.viewpager);

        binding.tabDetailRekening.setOnPageChangeListener(this);
        linearLayout = (LinearLayout) binding.tabDetailRekening.getChildAt(0);

        GeneralHelper.changeTabsFontSimple(this, linearLayout, 0);
    }

    @Override
    public void onClick(View view) {
        PilihKantorValas.launchIntent(this);
    }

    @Override
    public void onPageScrolled(int position, float positionOffset, int positionOffsetPixels) {

    }

    @Override
    public void onPageSelected(int position) {
        GeneralHelper.changeTabsFontSimple(this, linearLayout, position);
    }

    @Override
    public void onPageScrollStateChanged(int state) {

    }

    @Override
    protected void onDestroy() {
        super.onDestroy();
        binding = null;
    }

//    @Override
//    protected void onActivityResult(int requestCode, int resultCode, @Nullable Intent data) {
//        super.onActivityResult(requestCode, resultCode, data);
//        if (requestCode == Constant.REQ_PAYMENT) {
//            if (resultCode == RESULT_OK) {
//                this.setResult(RESULT_OK);
//                this.finish();
//            } else {
//                this.setResult(RESULT_CANCELED, data);
//                if (data != null) {
//                    if (!data.getStringExtra(Constant.TAG_ERROR_MESSAGE).isEmpty())
//                        this.finish();
//                }
//
//            }
//        }
//    }
}