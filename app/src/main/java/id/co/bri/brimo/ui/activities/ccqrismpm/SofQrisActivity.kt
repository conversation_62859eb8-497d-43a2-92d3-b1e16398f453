package id.co.bri.brimo.ui.activities.ccqrismpm

import android.app.Activity
import android.content.Intent
import android.os.Bundle
import android.view.View
import androidx.core.text.HtmlCompat
import androidx.swiperefreshlayout.widget.SwipeRefreshLayout
import com.ethanhua.skeleton.Skeleton
import com.ethanhua.skeleton.SkeletonScreen
import id.co.bri.brimo.R
import id.co.bri.brimo.adapters.ccqrismpm.CcQrisMpmAdapter
import id.co.bri.brimo.adapters.ccqrismpm.SavingsQrisMpmAdapter
import id.co.bri.brimo.contract.IPresenter.ccqrismpm.ICcQrisMPMPresenter
import id.co.bri.brimo.contract.IView.ICcQrisMpmView
import id.co.bri.brimo.databinding.ActivitySofQrisBinding
import id.co.bri.brimo.databinding.FragmentBottomSheetGeneralBinding
import id.co.bri.brimo.domain.config.Constant
import id.co.bri.brimo.domain.helpers.GeneralHelper
import id.co.bri.brimo.models.apimodel.request.ccqrismpm.SetDefaultQrPaymentRequest
import id.co.bri.brimo.models.apimodel.response.SyaratKetentuanResponse
import id.co.bri.brimo.models.apimodel.response.cc.DetailCcSofResponse
import id.co.bri.brimo.models.apimodel.response.ccqrismpm.CcQrisMpmResponse
import id.co.bri.brimo.ui.activities.GeneralSyaratActivity
import id.co.bri.brimo.ui.activities.base.BaseActivity
import id.co.bri.brimo.ui.customviews.dialog.DialogExitCustom
import id.co.bri.brimo.ui.fragments.bottomsheet.BottomSheetCustomViewGeneralFragment
import javax.inject.Inject


class SofQrisActivity : BaseActivity(), View.OnClickListener,
    DialogExitCustom.DialogClickYesNoListener, ICcQrisMpmView,
    SwipeRefreshLayout.OnRefreshListener {

    companion object {
        @JvmStatic
        fun launchIntent(caller: Activity) {
            val intent = Intent(caller, SofQrisActivity::class.java)
            caller.startActivityForResult(intent, Constant.REQ_CARD)
        }
    }

    @Inject
    lateinit var presenter: ICcQrisMPMPresenter<ICcQrisMpmView>
    private lateinit var binding: ActivitySofQrisBinding
    private var ccAccount: CcQrisMpmResponse.CcList? = null
    private var savingAccount: CcQrisMpmResponse.AccountList? = null
    private var dataResponse: CcQrisMpmResponse? = null
    private lateinit var savingAdapter: SavingsQrisMpmAdapter
    private lateinit var ccAdapter: CcQrisMpmAdapter
    private var skelotonCC: SkeletonScreen? = null
    private var skelotonSavings: SkeletonScreen? = null
    private var ccNeedActivate: CcQrisMpmResponse.CcList? = null
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        binding = ActivitySofQrisBinding.inflate(layoutInflater)
        setContentView(binding.root)
        injectDependency()
        showSkeleton()
        GeneralHelper.setToolbarRevamp(
            this,
            binding.tbReceipt.toolbar,
            GeneralHelper.getString(R.string.toolbar_title_cc_sof)
        )
        initOnAction()
    }

    private fun showSkeleton() {
        setEnableButtonNext(false)
        binding.cardInfo.gone()
        ccAdapter = CcQrisMpmAdapter(arrayListOf())
        binding.rvCc.adapter = ccAdapter
        skelotonCC = Skeleton.bind(binding.rvCc)
            .adapter(ccAdapter)
            .shimmer(true)
            .count(1)
            .angle(5)
            .frozen(false)
            .shimmer(true)
            .color(R.color.white)
            .duration(1200)
            .load(R.layout.item_skeleton_cc_qris_mpm)
            .show()

        savingAdapter = SavingsQrisMpmAdapter(arrayListOf())
        binding.rvSavings.adapter = savingAdapter
        skelotonSavings = Skeleton.bind(binding.rvSavings)
            .adapter(savingAdapter)
            .shimmer(true)
            .count(3)
            .angle(5)
            .frozen(false)
            .shimmer(true)
            .color(R.color.white)
            .duration(1200)
            .load(R.layout.item_skeleton_cc_qris_mpm)
            .show()

    }

    private fun injectDependency() {
        activityComponent.inject(this)
        presenter.view = this
        presenter.setUrlListSof(GeneralHelper.getString(R.string.url_list_cc_sof))
        presenter.setUrlChangeSof(GeneralHelper.getString(R.string.url_change_cc_sof))
        presenter.setUrlGetBalanceCc(GeneralHelper.getString(R.string.url_saldo_cc_sof))
        presenter.setUrlTerm(GeneralHelper.getString(R.string.url_cc_sof_tnc))
        presenter.start()

        presenter.getDataListSof()
    }

    private fun initOnAction() {
        binding.apply {
            btnConfirm.setOnClickListener(this@SofQrisActivity)
            swipeRefresh.setOnRefreshListener(this@SofQrisActivity)
        }

    }

    override fun onClick(p0: View?) {
        when (p0?.id) {
            R.id.btn_confirm -> {
                val rekName = getTitleForDialog()
                initDialogConfirmationPrimarySof(dataResponse?.dialogBox, rekName)
            }
        }
    }

    private fun getTitleForDialog(): String? {
        return if (isCcSelected()) ccAccount?.name
        else {
            var name =
                if (savingAccount?.productType.isNullOrEmpty()) savingAccount?.name else savingAccount?.productType
            name =
                name + ": ****" + savingAccount?.accountString?.replace(" ", "").addStartLastIfCc()
            name
        }
    }

    override fun onClickBtnYes() {
        showSkeleton()
        presenter.setDefaultSof(createDataRequest())
    }


    override fun onClickBtnNo() {
        //do nothing
    }

    private fun createDataRequest(): SetDefaultQrPaymentRequest {
        val sourceType = if (isCcSelected()) Constant.CcRequest else Constant.SavingsRequst
        val source = if (isCcSelected()) ccAccount?.cardToken else savingAccount?.account
        return SetDefaultQrPaymentRequest(sourceType, source)
    }

    private fun isCcSelected(): Boolean {
        return ccAccount != null
    }

    private fun showBottomSheetInfo(
        imageName: String = GeneralHelper.getString(R.string.drawable_src_maaf),
        title: String = GeneralHelper.getString(R.string.state_finance_not_yet_active_title),
        desc: String = GeneralHelper.getString(R.string.state_finance_not_yet_active_description),
        textFirstButton: String = GeneralHelper.getString(R.string.activated_state_financial),
        textSecondButton: String = GeneralHelper.getString(R.string.btn_tutup),
        isShowSecondButton: Boolean = true
    ) {
        val viewBind = FragmentBottomSheetGeneralBinding.inflate(layoutInflater)
        val bottomSheet = BottomSheetCustomViewGeneralFragment(viewBind.root, true, true) {}
        viewBind.apply {
            ivCenter.setImageResource(
                GeneralHelper.getImageId(
                    binding.root.context,
                    imageName
                )
            )
            tvTopTxt.gone()
            tvTitle.text = title
            tvDesc.text = desc

            firstBtn.text = textFirstButton
            secondBtn.text = textSecondButton
            firstBtn.setOnClickListener {
                bottomSheet.dismissNow()
                if (isShowSecondButton) {
                    presenter.getDataTerm()
                }
            }
            if (!isShowSecondButton) {
                firstBtn.setText(GeneralHelper.getString(R.string.close))
                secondBtn.gone()
            }
            secondBtn.setBackgroundResource(R.drawable.background_cardview)
            secondBtn.setOnClickListener { bottomSheet.dismissNow() }
        }
        if (!supportFragmentManager.isStateSaved) {
            bottomSheet.show(supportFragmentManager, "")
        }
    }

    override fun onSuccess(data: CcQrisMpmResponse?) {
        skelotonCC?.hide()
        skelotonSavings?.hide()
        dataResponse = data
        dataResponse?.apply {
            data?.let {
                presenter.getListBalanceSavings(it.accountList)
                presenter.getListBalanceCc(it.ccList)
            }
            initContentSof()
            initCcAdapter()
            initSavingsAdapter()
        }
    }

    private fun initContentSof() {
        binding.let {
            it.swipeRefresh.isRefreshing = false
            it.cardInfo.visible()
            it.textViewAlert.text = dataResponse?.infoCard
            if (dataResponse?.accountList?.isEmpty() == true) {
                it.savingsTitle.gone()
                it.rvSavings.gone()
            } else {
                it.savingsTitle.visible()
                it.rvSavings.visible()
            }

            if (dataResponse?.ccList?.isEmpty() == true) {
                it.ccTitle.gone()
                it.rvCc.gone()
            } else {
                it.ccTitle.visible()
                it.rvCc.visible()
            }
        }
    }

    private fun initSavingsAdapter() {
        val accountList = dataResponse?.accountList
        savingAdapter = SavingsQrisMpmAdapter(accountList ?: arrayListOf())
        binding.rvSavings.adapter = savingAdapter
        savingAdapter.onItemClick {
            if (accountList?.get(it)?.saldoResponse?.isOnHold == false) {
                ccAccount = null
                ccAdapter.resetSelectedList()
                setEnableButtonNext(true)
                savingAccount = accountList[it]
                accountList.forEach { it.isSelected = false }
                accountList[it].isSelected = true
                savingAdapter.notifyDataSetChanged()
            }
        }
    }

    private fun initCcAdapter() {
        val ccList = dataResponse?.ccList ?: arrayListOf()
        ccAdapter = CcQrisMpmAdapter(ccList)
        binding.rvCc.adapter = ccAdapter
        ccAdapter.onItemClick {
            if (ccList[it].isDisable == true) {
                val title = ccList[it].disableDialog?.title ?: ""
                val desc = ccList[it].disableDialog?.description ?: ""
                showBottomSheetInfo(title = title, desc = desc, isShowSecondButton = false)
            } else {
                if (ccList[it].isActive == 0) {
                    ccNeedActivate = ccList[it]
                    showBottomSheetInfo()
                } else {
                    savingAccount = null
                    savingAdapter.resetSelectedList()
                    setEnableButtonNext(true)
                    ccAccount = ccList[it]
                    ccList.forEach { it.isSelected = false }
                    ccList[it].isSelected = true
                    ccAdapter.notifyDataSetChanged()
                }
            }

        }
        ccAdapter.onActivatedClick {
            ccNeedActivate = it
            presenter.getDataTerm()
        }

    }

    private fun setEnableButtonNext(value: Boolean) {
        binding.btnConfirm.isEnabled = value
        binding.btnConfirm.setTextColor(GeneralHelper.getColor(R.color.white))
    }

    override fun onSuccessChangeSof(message: String) {
        GeneralHelper.showSnackBarGreenRevamp(
            binding.content, message
        )
        presenter.getDataListSof()
    }

    override fun onGetSaldoComplete() {
        //do nothing
    }

    override fun onResultBalanceSavings(accountList: ArrayList<CcQrisMpmResponse.AccountList>) {
        savingAdapter.setBalance(accountList)
    }

    override fun onResultBalanceCc(accountList: ArrayList<CcQrisMpmResponse.CcList>) {
        ccAdapter.setBalance(accountList)
    }

    override fun onSuccessTerm(syaratKetentuanResponse: SyaratKetentuanResponse) {
        val detailCcSofResponse = DetailCcSofResponse()
        detailCcSofResponse.cardNumber = ccNeedActivate?.cardNumberString
        detailCcSofResponse.cardToken = ccNeedActivate?.cardToken
        GeneralSyaratActivity.launchIntentFromQr(
            this,
            syaratKetentuanResponse.term,
            detailCcSofResponse,
            true
        )

    }

    private fun initDialogConfirmationPrimarySof(
        dialogBox: CcQrisMpmResponse.DialogBox?,
        rekName: String?
    ) {
        val dialogExitCustom = DialogExitCustom(
            this,
            dialogBox?.title,
            HtmlCompat.fromHtml(
                String.format(
                    "<b>${rekName}</b> ${dialogBox?.message}"
                ), HtmlCompat.FROM_HTML_MODE_LEGACY
            ),
            dialogBox?.buttonCancel,
            dialogBox?.buttonOk,
            true,
            true
        )
        val ft = supportFragmentManager.beginTransaction()
        if (ft != null) {
            ft.add(dialogExitCustom, null)
            ft.commitAllowingStateLoss()
        }
    }

    override fun onActivityResult(requestCode: Int, resultCode: Int, data: Intent?) {
        super.onActivityResult(requestCode, resultCode, data)
        if (requestCode == Constant.REQ_CC_SOF) {
            if (resultCode == RESULT_OK) {
                if (data?.hasExtra("checkbox") == false) {
                    presenter.getDataListSof()
                }
            }
            if (resultCode == RESULT_CANCELED && data != null) {
                if (data.hasExtra(Constant.TAG_CONFIRM_DIALOG)) {
                    GeneralHelper.showSnackBar(
                        findViewById(R.id.content),
                        data.getStringExtra(Constant.TAG_CONFIRM_DIALOG)
                    )
                }

                if (data.hasExtra(Constant.TAG_ERROR_MESSAGE)) {
                    onException(data.getStringExtra(Constant.TAG_ERROR_MESSAGE))
                }
            }
        }


    }

    override fun onRefresh() {
        binding.cardInfo.gone()
        showSkeleton()
        presenter.getDataListSof()
    }

    override fun onDestroy() {
        super.onDestroy()
        presenter.stop()
    }

    private fun String?.addStartLastIfCc(): String? {
        return if (isCcSelected()) this
        else {
            val lenght = this?.trim()?.length ?: 0
            if (lenght > 0) {
                val digit = 4
                if (lenght >= digit) {
                    this?.substring(lenght - digit)
                } else this

            } else this
        }
    }

    private fun View.visible() {
        this.visibility = View.VISIBLE
    }


    private fun View.gone() {
        this.visibility = View.GONE
    }
}