package id.co.bri.brimo.ui.customviews.dialog;



import android.annotation.SuppressLint;
import android.app.Dialog;
import android.graphics.drawable.ColorDrawable;
import android.os.Bundle;
import android.view.View;
import android.view.Window;
import android.widget.Button;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.fragment.app.DialogFragment;
import id.co.bri.brimo.R;


public class DialogCatatan extends DialogFragment {
    private Dialog alertDialog;
    private Button btnYes, btnNo;
    private TextView tvTitle, tvSubtitle;
    private String mTitle, mSubtitle;
    private DialogSetDefault.DialogDefaultListener dialogDefaulListener;

    public DialogCatatan(String title, String subTitle) {
        this.mTitle = title;
        this.mSubtitle = subTitle;
    }
    @SuppressLint("ValidFragment")
    public DialogCatatan() {
    }

    @NonNull
    @Override
    public Dialog onCreateDialog(@Nullable Bundle savedInstanceState) {
        alertDialog = new Dialog(getActivity());
        alertDialog.setCanceledOnTouchOutside(false);
        alertDialog.getWindow().requestFeature(Window.FEATURE_NO_TITLE);
        alertDialog.getWindow().setBackgroundDrawable(new ColorDrawable(0));
        alertDialog.setContentView(R.layout.fragment_dialog_catatan);
        alertDialog.setOnKeyListener((dialogInterface, i, keyEvent) -> false);
        alertDialog.show();

        initView();

        btnNo.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View view) {
                dismiss();
            }
        });

//        btnYes.setOnClickListener(new View.OnClickListener() {
//            @Override
//            public void onClick(View view) {
//                if (dialogDefaulListener != null) {
//                    dialogDefaulListener.onClickYes();
//                }
//                dismiss();
//            }
//        });

        return alertDialog;
    }

    private void initView() {

//        btnYes = alertDialog.findViewById(R.id.btn_yes);
        btnNo = alertDialog.findViewById(R.id.btn_no);
        tvTitle = alertDialog.findViewById(R.id.tv_title);
        tvSubtitle = alertDialog.findViewById(R.id.tv_subtitle);

        tvTitle.setText(mTitle);
        tvSubtitle.setText(mSubtitle);


    }

//    public interface DialogDefaultListener {
//        void onClickYes();
//    }

}