package id.co.bri.brimo.ui.activities.britamarencanarevamp

import android.app.Activity
import android.content.ClipData
import android.content.ClipboardManager
import android.content.Intent
import android.graphics.Color
import android.os.Bundle
import android.os.Handler
import android.os.Looper
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.LinearLayout
import androidx.core.content.ContextCompat
import androidx.fragment.app.Fragment
import androidx.viewpager.widget.ViewPager
import id.co.bri.brimo.R
import id.co.bri.brimo.adapters.CatatanKeuanganAdapter
import id.co.bri.brimo.contract.IPresenter.ubahrencana.IChangeDetailPlanPresenter
import id.co.bri.brimo.contract.IView.ubahdetailrencana.IChangeDetailPlanView
import id.co.bri.brimo.data.preference.BRImoPrefRepository
import id.co.bri.brimo.databinding.ActivityDetailRencanaRevampBinding
import id.co.bri.brimo.databinding.FragmentBottomSheetDetailPencairanRevampBinding
import id.co.bri.brimo.domain.config.Constant
import id.co.bri.brimo.domain.helpers.GeneralHelper
import id.co.bri.brimo.domain.helpers.bubbleShowCaseView.BubbleShowCase
import id.co.bri.brimo.domain.helpers.bubbleShowCaseView.BubbleShowCaseBuilder
import id.co.bri.brimo.models.ChangeDetailPlan
import id.co.bri.brimo.models.apimodel.request.dashboardrencanarevamp.InquiryPencairanRencanaRequest
import id.co.bri.brimo.models.apimodel.request.ubahdetailrencana.ChangeDetailPlanRequest
import id.co.bri.brimo.models.apimodel.response.dashboardrecana.DashboardRencanaResponse
import id.co.bri.brimo.models.apimodel.response.dashboardrecana.InquiryPencairanResponse
import id.co.bri.brimo.models.apimodel.response.topuprencana.TopupPlanDataResponse
import id.co.bri.brimo.models.apimodel.response.ubahdetailrencana.ChangeDetailPlanDataResponse
import id.co.bri.brimo.ui.activities.base.BaseActivity
import id.co.bri.brimo.ui.activities.topuprencana.TopUpPlanActivity
import id.co.bri.brimo.ui.activities.ubahdetailrencana.ChangeDetailPlanActivity
import id.co.bri.brimo.ui.fragments.bottomsheet.BottomSheetCustomViewGeneralFragment
import id.co.bri.brimo.ui.fragments.britamarencanarevamp.DetailRencanaRevampFragment
import id.co.bri.brimo.ui.fragments.britamarencanarevamp.MutasiRencanaFragment
import javax.inject.Inject


class DetailRencanaRevampActivity : BaseActivity(), IChangeDetailPlanView,
    ViewPager.OnPageChangeListener {

    private val binding by lazy { ActivityDetailRencanaRevampBinding.inflate(layoutInflater) }
    private var titleList: MutableList<String>? = mutableListOf()
    private var fragmentList: MutableList<Fragment>? = mutableListOf()
    private var mutasiFragment: MutasiRencanaFragment = MutasiRencanaFragment()
    private var detailFragment: DetailRencanaRevampFragment = DetailRencanaRevampFragment()
    private var fragmentAdapter: CatatanKeuanganAdapter? = null
    private var linearLayout: LinearLayout? = null
    private var myClipboard: ClipboardManager? = null
    private var myClip: ClipData? = null
    private var errorMessage: String? = ""
    var brImoPrefRepository = BRImoPrefRepository(this)

    private var inquiryResponse: InquiryPencairanResponse? = null

    private val handler = Handler(Looper.getMainLooper())
    var detailTutorial: BubbleShowCaseBuilder? = null

    @Inject
    lateinit var presenter: IChangeDetailPlanPresenter<IChangeDetailPlanView>

    companion object {
        const val EXTRA_DETAIL_PLAN = "DETAIL_RENCANA_INTENT"
        const val EXTRA_POSITION_DETAIL_PLAN = "DETAIL_POSITION_RENCANA_INTENT"
        var mResponse: List<DashboardRencanaResponse.DashboardData.Account>? = null
        var mPosition: Int? = null

        @JvmStatic
        fun launchIntent(
            caller: Activity,
            response: List<DashboardRencanaResponse.DashboardData.Account>,
            position: Int
        ) {
            val responseArrayList = ArrayList(response)
            val intent = Intent(caller, DetailRencanaRevampActivity::class.java).apply {
                putParcelableArrayListExtra(EXTRA_DETAIL_PLAN, responseArrayList)
                putExtra(EXTRA_POSITION_DETAIL_PLAN, position)
            }
            caller.startActivityForResult(intent, Constant.REQ_PAYMENT)
        }
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContentView(binding.root)
        getData()
        injectDependency()
        setupView()
    }

    private fun injectDependency() {
        activityComponent.inject(this)
        presenter.view = this
        presenter.apply {
            setUrlChangeDetailPlan(getString(R.string.url_change_detail_plan))
            setUrlPencairanRencana(getString(R.string.url_inquiry_pencairan_rencana))
            setUrlTopUpPlan(getString(R.string.url_topup_rencana))
            start()
        }
    }

    private fun setupView() {
        if (!brImoPrefRepository.rencanaDetailBubble) {
            brImoPrefRepository.saveRencanaDetailBubble(true)
            addBubbleShowCaseDetailRencana()
        }

        binding.apply {
            GeneralHelper.setToolbarRevamp(
                this@DetailRencanaRevampActivity,
                tbDetailRencana.toolbar,
                getString(R.string.txt_detail_rencana)
            )

            val responsePosition = mResponse?.get(mPosition ?: 0)
            GeneralHelper.loadImageUrl(this@DetailRencanaRevampActivity,responsePosition?.purposeImagePath,imgDashboardRencana,0,0)
            tvRencana.text = responsePosition?.purpose
            tvTotalSaldoRencana.text = responsePosition?.balanceNowString
            tvRekeningRencana.text = responsePosition?.bancassAccountString
            tvPercentage.text = responsePosition?.percentageString
            tvKekurangan.text = responsePosition?.balanceGoalString
            sbRencana.progress = responsePosition?.percentageFloat ?: 0
            sbRencana.setOnTouchListener { _, _ -> true }
            if (responsePosition?.depositDetails?.buttonEnabled == 1) {
                imgToRencana.setOnClickListener {
                    presenter.postChangeDetailPlanRequest(
                        ChangeDetailPlanRequest(
                            responsePosition?.bancassAccount.orEmpty()
                        )
                    )
                }
                rlInformasi.visibility = View.GONE
                imgToRencana.visibility = View.VISIBLE
                gotoPencairan()
            }else{
                imgToRencana.visibility = View.GONE
                imgPencairan.alpha = 0.3f
                tvPencairan.alpha = 0.3f
                rlInformasi.visibility = View.VISIBLE
                GeneralHelper.setWebViewStandart(wvInfo, "", responsePosition?.depositDetails?.infoBranch)
            }

            if (responsePosition?.topupFlag == 1) {
                imgTopup.setOnClickListener {
                    presenter.postTopUpPlanRequest(
                            ChangeDetailPlanRequest(
                                    responsePosition?.bancassAccount.orEmpty()
                            )
                    )
                }

                imgTopup.visibility = View.VISIBLE
                tvTopup.visibility = View.VISIBLE

                imgPencairan.visibility = View.VISIBLE
                tvPencairan.visibility = View.VISIBLE
            } else {
                imgTopup.alpha = 0.3f
                tvTopup.alpha = 0.3f
                imgTopup.visibility = View.GONE
                tvTopup.visibility = View.GONE

                imgPencairan.visibility = View.GONE
                tvPencairan.visibility = View.GONE
            }

            detailFragment.setResponse(mResponse)
            detailFragment.setPosition(mPosition ?: 0)
            mutasiFragment.setAccount(mResponse)
            mutasiFragment.setPosition(mPosition ?: 0)

            fragmentList = mutableListOf(detailFragment, mutasiFragment)
            titleList = mutableListOf(getString(R.string.detail), getString(R.string.mutasi_title_bar))

            fragmentAdapter = CatatanKeuanganAdapter(
                supportFragmentManager,
                this@DetailRencanaRevampActivity,
                fragmentList,
                titleList
            )
            vpReport.adapter = fragmentAdapter
            stReport.setViewPager(vpReport)
            stReport.setOnPageChangeListener(this@DetailRencanaRevampActivity)
            linearLayout = stReport.getChildAt(0) as LinearLayout

            GeneralHelper.changeTabsFontBoldForRencana(
                this@DetailRencanaRevampActivity,
                linearLayout,
                0
            )

            if ((responsePosition?.percentageFloat ?: 0) <= 33) {
                tvPercentage.setTextColor(
                    ContextCompat.getColor(
                        this@DetailRencanaRevampActivity,
                        R.color.semanticRed80
                    )
                )
                rlBgPercentage.setBackgroundResource(R.drawable.background_red10)
            } else if ((responsePosition?.percentageFloat ?: 0) <= 66) {
                tvPercentage.setTextColor(
                    ContextCompat.getColor(
                        this@DetailRencanaRevampActivity,
                        R.color.secondaryColor
                    )
                )
                rlBgPercentage.setBackgroundResource(R.drawable.background_orange10)

            } else {
                tvPercentage.setTextColor(
                    ContextCompat.getColor(
                        this@DetailRencanaRevampActivity,
                        R.color.success80
                    )
                )
                rlBgPercentage.setBackgroundResource(R.drawable.background_green10)
            }

            rlRekeningRencana.setOnClickListener {
                myClipboard = getSystemService(CLIPBOARD_SERVICE) as ClipboardManager
                myClip = ClipData.newPlainText("text", responsePosition?.bancassAccount)
                myClipboard?.setPrimaryClip(myClip!!)
                showSnackbarErrorMessage(GeneralHelper.getString(R.string.txt_copy_detail_rencana), ALERT_CONFIRM, this@DetailRencanaRevampActivity, false)
            }

        }
    }

    private fun gotoPencairan() {
        val responsePosition = mResponse?.get(mPosition ?: 0)
        binding.imgPencairan.setOnClickListener{
            presenter.getInquiryPencairanRencana(
                InquiryPencairanRencanaRequest(
                    mResponse?.get(
                        mPosition ?: 0
                    )?.bancassAccount.orEmpty()
                )
            )
        }
    }

    private fun showBottoSheetPencairan() {
        val viewBind = FragmentBottomSheetDetailPencairanRevampBinding.inflate(LayoutInflater.from(this))
        val bottomSheet = BottomSheetCustomViewGeneralFragment(viewBind.root, true, true) {}

        viewBind.apply {
            layoutImage.visibility = View.VISIBLE
            tvTitle.text = inquiryResponse?.popupWithdraw?.title
            tvDesc.text = inquiryResponse?.popupWithdraw?.description

            cardViewPinalty.setOnClickListener {
                bottomSheet.dismissNow()
                handler.postDelayed({
                    showBottoSheetTnC()
                }, 500)
            }
            cancelBtn.setOnClickListener{
                bottomSheet.dismissNow()
            }
            confirmBtn.setOnClickListener {
                bottomSheet.dismissNow()
                InquiryPencairanRencanaActivity.launcIntent(
                    caller = this@DetailRencanaRevampActivity,
                    inquiryResponse = inquiryResponse!!
                )
            }
        }

        if (!supportFragmentManager.isStateSaved){
            bottomSheet.show(supportFragmentManager,"")
        }
    }

    private fun showBottoSheetTnC() {
        val viewBind = FragmentBottomSheetDetailPencairanRevampBinding.inflate(LayoutInflater.from(this))
        val bottomSheet = BottomSheetCustomViewGeneralFragment(viewBind.root, true, true) {}

        inquiryResponse?.tncWithdraw?.let {
            viewBind.apply {
                tvTitle.text = it.title
                tvDesc.visibility = View.GONE
                wvDesc.visibility = View.VISIBLE
                wvDesc.loadDataWithBaseURL(null, it.description.orEmpty(), "text/html", "utf-8", null)

                var params = confirmBtn.layoutParams
                params.width = ViewGroup.LayoutParams.MATCH_PARENT
                confirmBtn.layoutParams = params

                var paramsMargin = buttonContainer.layoutParams as (ViewGroup.MarginLayoutParams)
                paramsMargin.topMargin = 1
                buttonContainer.layoutParams = paramsMargin

                confirmBtn.text = getString(R.string.kembali)
                confirmBtn.setOnClickListener{
                    bottomSheet.dismissNow()
                    handler.postDelayed({
                        showBottoSheetPencairan()
                    }, 500)
                }

                cardViewPinalty.visibility = View.GONE
                cancelBtn.visibility = View.GONE
            }
        }

        if (!supportFragmentManager.isStateSaved){
            bottomSheet.show(supportFragmentManager,"")
        }
    }

    private fun showBottomSheetCannotDisburseFunds() {
        val viewBind = FragmentBottomSheetDetailPencairanRevampBinding.inflate(LayoutInflater.from(this))
        val bottomSheet = BottomSheetCustomViewGeneralFragment(viewBind.root, true, true) {}

        inquiryResponse?.popupFailed.let {
            viewBind.apply {
                tvTitle.text = it?.title
                tvDesc.text = it?.description

                var params = confirmBtn.layoutParams
                params.width = ViewGroup.LayoutParams.MATCH_PARENT
                confirmBtn.layoutParams = params

                var paramsMargin = buttonContainer.layoutParams as (ViewGroup.MarginLayoutParams)
                paramsMargin.topMargin = 1
                buttonContainer.layoutParams = paramsMargin

                confirmBtn.text = getString(R.string.ok)
                confirmBtn.setOnClickListener{
                    bottomSheet.dismissNow()
                }

                cardViewPinalty.visibility = View.GONE
                cancelBtn.visibility = View.GONE
            }
        }

        if (!supportFragmentManager.isStateSaved){
            bottomSheet.show(supportFragmentManager,"")
        }
    }

    private fun getData() {
        mResponse = intent.getParcelableArrayListExtra(EXTRA_DETAIL_PLAN)
        mPosition = intent.getIntExtra(EXTRA_POSITION_DETAIL_PLAN, 0)
    }

    override fun onSuccessResponse(changeDetailPlanData: ChangeDetailPlanDataResponse) {
        ChangeDetailPlanActivity.launchIntent(
            caller = this@DetailRencanaRevampActivity,
            keyChangeDetailPlan = ChangeDetailPlanActivity.EXTRA_CHANGE_DETAIL_PLAN,
            mChangeDetailPlanDataResponse = changeDetailPlanData,
            keyBancassAccount = ChangeDetailPlanActivity.EXTRA_POST_CHANGE_DETAIL_PLAN,
            bancassAccount = (mResponse?.get(mPosition ?: 0)?.bancassAccount.orEmpty())
        )
    }

    override fun onSuccessResponse(topupPlanDataResponse: TopupPlanDataResponse) {
        TopUpPlanActivity.launchIntent(
            caller = this@DetailRencanaRevampActivity,
            keyTopUpPlan = TopUpPlanActivity.EXTRA_TOP_UP_PLAN,
            mTopUpPlanDataResponse = topupPlanDataResponse,
            keyBancassAccount = TopUpPlanActivity.EXTRA_POST_TOP_UP_PLAN,
            bancassAccount = (mResponse?.get(mPosition ?: 0)?.bancassAccount.orEmpty())
        )
    }

    override fun onSuccessInquiry(inquiryPencairanResponse: InquiryPencairanResponse) {
        inquiryResponse = inquiryPencairanResponse

        val detailRencanaResponse = mResponse?.get(mPosition ?: 0)
        if (inquiryPencairanResponse.canWithdraw == true) {
            showBottoSheetPencairan()
        } else {
            showBottomSheetCannotDisburseFunds()
        }
    }

    override fun onExceptionCode12(message: String) {
        showSnackbarErrorMessage(message, ALERT_ERROR, this, false)
    }

    override fun onPageScrolled(position: Int, positionOffset: Float, positionOffsetPixels: Int) {
        // no action required
    }

    override fun onPageSelected(position: Int) {
        GeneralHelper.changeTabsFontBoldForRencana(this, linearLayout, position)
    }

    override fun onPageScrollStateChanged(state: Int) {
        // no action required
    }
    private fun addBubbleShowCaseDetailRencana() {
        try {
            detailTutorial = BubbleShowCaseBuilder(this) //Activity instance
                    .title(GeneralHelper.getString(R.string.txt_title_detail_tutorial)) //Any title for the bubble view
                    .description(GeneralHelper.getString(R.string.txt_desc_detail_tutorial))
                    .backgroundColor(Color.WHITE)
                    .textColor(Color.BLACK)
                    .buttonTitle(GeneralHelper.getString(R.string.btn_tutup))
                    .targetView(binding.rlNamaRencana)
                    .enableLewati(false)
                    .arrowPosition(BubbleShowCase.ArrowPosition.TOP)
            detailTutorial?.show()
        } catch (e: Exception) {
        }
    }
    override fun onActivityResult(requestCode: Int, resultCode: Int, data: Intent?) {
        super.onActivityResult(requestCode, resultCode, data)
        //melanjutkan activity result ke fragment onresultActivity
        for (fragment in supportFragmentManager.fragments) {
            fragment.onActivityResult(requestCode, resultCode, data)
        }

        when (requestCode) {
            Constant.REQ_PAYMENT -> {
                when (resultCode) {
                    RESULT_OK -> {
                        this.setResult(RESULT_OK, data)
                        finish()
                    }
                    RESULT_CANCELED -> {
                        if (data != null) {
                            errorMessage = data.getStringExtra(Constant.TAG_ERROR_MESSAGE).orEmpty()
                            checkErrorMessage()
                        }
                    }
                    else -> {
                        this.setResult(RESULT_CANCELED)
                    }
                }
            }
            Constant.REQ_EDIT_SAVED -> {
                if (resultCode == RESULT_OK) {
                    binding.apply {
                        val changeDetailPlan = data?.getParcelableExtra<ChangeDetailPlan>(Constant.TAG_EDIT_RENCANA)
                        val descriptionChangePlan = changeDetailPlan?.description.orEmpty()
                        val iconUrl = changeDetailPlan?.iconUrl.orEmpty().trim()
                        val messageChangePlan = changeDetailPlan?.message.orEmpty()

                        GeneralHelper.showSnackBarGreenRevamp(content, descriptionChangePlan)
                        GeneralHelper.loadImageUrl(
                            this@DetailRencanaRevampActivity,
                            iconUrl,
                            imgDashboardRencana,
                            R.drawable.bri,
                            0
                        )
                        tvRencana.text = messageChangePlan
                    }
                    this.setResult(RESULT_OK, data)
                }
            }
        }
    }

    private fun checkErrorMessage() {
        if (errorMessage != null) {
            showSnackbarErrorMessage(errorMessage, ALERT_ERROR, this, false)
            errorMessage = null
        }
    }

    override fun onResume() {
        super.onResume()
        injectDependency()
    }

    override fun onDestroy() {
        super.onDestroy()
        presenter.stop()
    }
}