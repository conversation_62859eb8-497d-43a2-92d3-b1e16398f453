package id.co.bri.brimo.ui.activities.pengelolaankartu;

import android.app.Activity;
import android.content.Intent;
import android.os.Bundle;
import androidx.annotation.Nullable;
import androidx.fragment.app.FragmentTransaction;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;
import com.ethanhua.skeleton.Skeleton;
import com.ethanhua.skeleton.SkeletonScreen;
import javax.inject.Inject;
import id.co.bri.brimo.R;
import id.co.bri.brimo.adapters.ListCardTypesAdapter;
import id.co.bri.brimo.contract.IPresenter.pengelolaankartu.IListCardTypePresenter;
import id.co.bri.brimo.contract.IView.pengelolaankartu.IListCardTypeView;
import id.co.bri.brimo.databinding.ActivityListCardTypesBinding;
import id.co.bri.brimo.domain.config.Constant;
import id.co.bri.brimo.domain.helpers.GeneralHelper;
import id.co.bri.brimo.models.ProductLimitDataModel;
import id.co.bri.brimo.models.apimodel.response.LimitCardTypeResponse;
import id.co.bri.brimo.ui.activities.base.BaseActivity;
import id.co.bri.brimo.ui.customviews.dialog.DialogInformation;

public class ListCardTypesActivity extends BaseActivity implements IListCardTypeView, ListCardTypesAdapter.ClickItemCard, DialogInformation.OnActionClick {

    private ActivityListCardTypesBinding binding;

    protected ListCardTypesAdapter adapter;
    private SkeletonScreen skeletonScreen;
    protected LimitCardTypeResponse limitCardTypeResponse;
    private static String accountNumber;
    private int type;
    private boolean safetyMode;

    @Inject
    IListCardTypePresenter<IListCardTypeView> presenter;

    public static void launchIntent(Activity caller, String accountNumbers) {
        Intent intent = new Intent(caller, ListCardTypesActivity.class);
        accountNumber = accountNumbers;
        caller.startActivityForResult(intent, Constant.REQ_CARD);
    }

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        binding = ActivityListCardTypesBinding.inflate(getLayoutInflater());
        setContentView(binding.getRoot());

        GeneralHelper.setToolbar(this, binding.toolbar.toolbar, GeneralHelper.getString(R.string.set_limits));

        initiateAdapter();

        injectDependency();
    }

    private void injectDependency() {
        getActivityComponent().inject(this);
        if (presenter != null) {
            presenter.setView(this);
            presenter.start();
            presenter.setUrl(GeneralHelper.getString(R.string.url_get_list_limit_card));
            presenter.getListCard(accountNumber, "");
        }
    }

    public void initiateAdapter() {
        LinearLayoutManager linearLayoutManager = new LinearLayoutManager(this, RecyclerView.VERTICAL, false);
        binding.rvListCard.setLayoutManager(linearLayoutManager);
        binding.rvListCard.setHasFixedSize(true);

        skeletonScreen = Skeleton.bind(binding.rvListCard)
                .adapter(adapter)
                .shimmer(true)
                .angle(20)
                .frozen(false)
                .duration(1200)
                .count(1)
                .load(R.layout.item_skeleton_mutasi)
                .show();
    }

    @Override
    public void showSkeleton(boolean isShow) {
        if (isShow) {
            skeletonScreen.show();
        } else {
            skeletonScreen.hide();
        }
    }

    @Override
    public void successGetListLimitCard(LimitCardTypeResponse response, String responseSnack) {
        limitCardTypeResponse = response;
        if (limitCardTypeResponse.getCardDataModel().getCardCategory().
                equalsIgnoreCase(Constant.REGULAR_CARD)) {
            type = 1;
        } else {
            type = 2;
        }
        safetyMode = limitCardTypeResponse.getSafetyModeDataModel().isStatus();
        adapter = new ListCardTypesAdapter(limitCardTypeResponse.getListProductLimitData(), this, type);
        binding.rvListCard.setAdapter(adapter);
        adapter.setResponse(limitCardTypeResponse.getListProductLimitData());
        adapter.notifyDataSetChanged();
    }

    @Override
    public void onException12(String message) {
        GeneralHelper.showSnackBar(this.findViewById(R.id.content), message);
    }

    @Override
    public void onExceptionTrxExpired(String message) {
        GeneralHelper.showSnackBar(this.findViewById(R.id.content), message);
    }

    @Override
    protected void onDestroy() {
        presenter.stop();
        binding = null;
        super.onDestroy();
    }

    @Override
    public void onActionClick(ProductLimitDataModel data, int type) {
        if (safetyMode) {
            DialogInformation dialog = new DialogInformation(this, limitCardTypeResponse.getSafetyModeDataModel().getImage(),
                    limitCardTypeResponse.getSafetyModeDataModel().getTitle(),
                    limitCardTypeResponse.getSafetyModeDataModel().getDesc(), GeneralHelper.getString(R.string.oke), this, true, false);
            FragmentTransaction ft = getSupportFragmentManager().beginTransaction();
            ft.add(dialog, null);
            ft.commitAllowingStateLoss();
        } else {
            DetailLimitCardActivity.launchIntent(this, data, limitCardTypeResponse.getCardDataModel().getAccountNumber());
        }
    }

    @Override
    protected void onActivityResult(int requestCode, int resultCode, @Nullable Intent data) {
        super.onActivityResult(requestCode, resultCode, data);
        if (requestCode == Constant.REQ_CARD) {
            if (resultCode == RESULT_OK && data != null) {
                if (data.hasExtra(Constant.ACCOUNT_NUM)) {
                    accountNumber = data.getStringExtra(Constant.ACCOUNT_NUM);
                }
                presenter.getListCard(accountNumber, "");
            } else {
                this.setResult(RESULT_CANCELED, data);
            }
        }
    }

    @Override
    public void onClickAction() {
        // do nothing
    }
}