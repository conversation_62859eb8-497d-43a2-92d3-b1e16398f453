package id.co.bri.brimo.ui.activities.bukaValas;

import android.app.Activity;
import android.app.SearchManager;
import android.content.Context;
import android.content.Intent;
import android.net.Uri;
import android.os.Bundle;
import android.provider.Settings;
import android.util.TypedValue;
import android.view.View;
import android.widget.EditText;
import androidx.annotation.Nullable;
import androidx.appcompat.widget.SearchView;
import androidx.core.content.res.ResourcesCompat;
import androidx.recyclerview.widget.LinearLayoutManager;
import com.ethanhua.skeleton.Skeleton;
import com.ethanhua.skeleton.SkeletonScreen;
import java.util.ArrayList;
import java.util.List;
import javax.inject.Inject;
import id.co.bri.brimo.R;
import id.co.bri.brimo.adapters.ListLokasiKantorAdapter;
import id.co.bri.brimo.contract.IPresenter.bukaValas.IPilihKantorBukaValasPresenter;
import id.co.bri.brimo.contract.IView.bukaValas.IPilihKantorValasView;
import id.co.bri.brimo.databinding.ActivityPilihKantorValasBinding;
import id.co.bri.brimo.domain.config.Constant;
import id.co.bri.brimo.domain.helpers.GeneralHelper;
import id.co.bri.brimo.domain.helpers.GpsTracker;
import id.co.bri.brimo.models.apimodel.request.LocationRequest;
import id.co.bri.brimo.models.apimodel.request.SearchLocationRequest;
import id.co.bri.brimo.models.apimodel.response.ListKantorResponse;
import id.co.bri.brimo.ui.activities.base.BaseActivity;

public class PilihKantorValas extends BaseActivity implements IPilihKantorValasView, ListLokasiKantorAdapter.onClickItem, View.OnClickListener {

    private ActivityPilihKantorValasBinding binding;

    @Inject
    IPilihKantorBukaValasPresenter<IPilihKantorValasView> presenter;

    private SkeletonScreen skeletonScreen;
    private ListLokasiKantorAdapter lokasiKantorAdapter;
    private List<ListKantorResponse.Office> officeList = new ArrayList<>();

    private String inLatitude;
    private String inLongitude;
    private String address;
    protected static String errorMessage = null;


    public static void launchIntent(Activity caller) {
        Intent intent = new Intent(caller, PilihKantorValas.class);
        caller.startActivity(intent);
    }
    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        binding = ActivityPilihKantorValasBinding.inflate(getLayoutInflater());
        setContentView(binding.getRoot());

        injectDependency();

        if (binding.toolbar.toolbar != null) {
            GeneralHelper.setToolbar(this, binding.toolbar.toolbar, "Britama Valas");
        }

        getLocation();
        searchLocation();


        EditText editText = binding.searchviewDigitalsaving.findViewById(androidx.appcompat.R.id.search_src_text);
        editText.setHintTextColor(getResources().getColor(R.color.black3));
        editText.setTypeface(ResourcesCompat.getFont(this, R.font.avenir_next_medium));
        editText.setTextSize(TypedValue.COMPLEX_UNIT_SP, 14);

        lokasiKantorAdapter = new ListLokasiKantorAdapter(officeList, getApplicationContext(), this);
        LinearLayoutManager layoutManager = new LinearLayoutManager(this);
        binding.recyclerView.setHasFixedSize(true);
        binding.recyclerView.setLayoutManager(layoutManager);
        binding.recyclerView.setAdapter(lokasiKantorAdapter);

        skeletonScreen = Skeleton.bind(binding.recyclerView)
                .adapter(lokasiKantorAdapter)
                .shimmer(true)
                .angle(10)
                .frozen(false)
                .duration(1200)
                .load(R.layout.item_skeleton_pilih_kantor)
                .show(); //default count is 10
    }

    private void getLocation() {
        GpsTracker gpsTracker = new GpsTracker(PilihKantorValas.this);
        if (gpsTracker.canGetLocation()) {
            inLatitude = String.valueOf(gpsTracker.getLatitude());
            inLongitude = String.valueOf(gpsTracker.getLongitude());

            presenter.sendLokasiSendiri(createLocationRequest());
        } else {
            showDialogEnableLocation();
        }
    }

    private void searchLocation() {
        SearchManager searchManager = (SearchManager) this.getSystemService(Context.SEARCH_SERVICE);
        if (searchManager != null) {
            binding.searchviewDigitalsaving.setSearchableInfo(searchManager.getSearchableInfo(this.getComponentName()));
        }
        binding.searchviewDigitalsaving.setMaxWidth(Integer.MAX_VALUE);
        binding.searchviewDigitalsaving.setOnQueryTextListener(new SearchView.OnQueryTextListener() {
            @Override
            public boolean onQueryTextSubmit(String query) {
                skeletonScreen.show();
                address = query;
                presenter.sendSearchLocation(createSearchLocationRequest());
                return false;
            }

            @Override
            public boolean onQueryTextChange(String newText) {
                return false;
            }
        });
    }


    protected void injectDependency() {
        getActivityComponent().inject(this);
        if (presenter != null) {
            presenter.setView(this);
            presenter.setUrl(GeneralHelper.getString(R.string.url_lokasi_sendiri_valas));
            presenter.setSearchUrl(GeneralHelper.getString(R.string.url_search_lokasi_valas));
            presenter.start();
        }
    }

    @Override
    protected void onResume() {
        super.onResume();
        if (presenter != null) {
            presenter.setView(this);
            presenter.setUrl(GeneralHelper.getString(R.string.url_lokasi_sendiri_valas));
            presenter.setSearchUrl(GeneralHelper.getString(R.string.url_search_lokasi_valas));
            presenter.start();
        }
    }

    protected void cekErrorMessage() {
        if (errorMessage != null) {
            //menampilkan snacknar error
            showSnackbarErrorMessage(errorMessage, ALERT_ERROR, this, false);

            //clear error message
            errorMessage = null;
        }
    }

    @Override
    public void onClickMaps(Class clickMap, ListKantorResponse.Office office) {
        String uri = "https://www.google.com/maps/place/" + office.getLatitude() + "," + office.getLongitude();
        Intent intent = new Intent(Intent.ACTION_VIEW, Uri.parse(uri));
        intent.setClassName("com.google.android.apps.maps", "com.google.android.maps.MapsActivity");
        startActivity(intent);
    }

    @Override
    public void onClickKantor(Class clickKantor, ListKantorResponse.Office office) {
        FormBukaValasActivity.launchIntent(this,office);
    }


    @Override
    public void onSuccessLocationDefault(ListKantorResponse kantorResponse) {
        skeletonScreen.hide();
        lokasiKantorAdapter.setItems(kantorResponse.getOfficeList());
        lokasiKantorAdapter.notifyDataSetChanged();
    }

    @Override
    public void onFailedLocation() {
        dataTidakDitemukan();
    }

    private void dataTidakDitemukan() {
        binding.tvDataTidakDitemukan.setVisibility(View.VISIBLE);
        binding.recyclerView.setVisibility(View.GONE);
    }

    @Override
    public LocationRequest createLocationRequest() {
        return new LocationRequest(inLatitude, inLongitude);
    }

    @Override
    public SearchLocationRequest createSearchLocationRequest() {
        SearchLocationRequest searchLocationRequest = new SearchLocationRequest(inLatitude, inLongitude, address);
        binding.recyclerView.setVisibility(View.VISIBLE);
        binding.tvDataTidakDitemukan.setVisibility(View.GONE);
        return searchLocationRequest;
    }

    @Override
    public void onClick(View v) {
//        PilihRekeningValas.launchIntent(this);
    }

    @Override
    protected void onDestroy() {
        presenter.stop();
        binding = null;
        super.onDestroy();
    }

    @Override
    protected void onActivityResult(int requestCode, int resultCode, @Nullable Intent data) {
        super.onActivityResult(requestCode, resultCode, data);
        if (requestCode == Constant.REQ_PAYMENT) {
            if (resultCode == RESULT_OK) {
                //membalikan data agar otomatis cek saldo
                setResult(RESULT_OK, data);
                this.finish();
            } else {
                if (data != null) {
                    //membalikan data error message agar muncul snackbar di dashboard
                    errorMessage = data.getStringExtra(Constant.TAG_ERROR_MESSAGE);
                    cekErrorMessage();
                }
            }
        }
    }
}