package id.co.bri.brimo.models.apimodel.request;

import com.google.gson.annotations.Expose;
import com.google.gson.annotations.SerializedName;

public class DetailCcSofRequest {
    @SerializedName("card_token")
    @Expose
    String cardToken;
    @SerializedName("year")
    @Expose
    String year;
    @SerializedName("month")
    @Expose
    String month;


    public String getCardToken() {
        return cardToken;
    }

    public void setCardToken(String cardToken) {
        this.cardToken = cardToken;
    }

    public DetailCcSofRequest(String cardToken) {
        this.cardToken = cardToken;
    }

    public DetailCcSofRequest(String cardToken, String year, String month) {
        this.cardToken = cardToken;
        this.year = year;
        this.month = month;
    }
}
