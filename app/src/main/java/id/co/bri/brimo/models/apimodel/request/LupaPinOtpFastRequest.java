package id.co.bri.brimo.models.apimodel.request;

import com.google.gson.annotations.Expose;
import com.google.gson.annotations.SerializedName;

public class LupaPinOtpFastRequest {
    @SerializedName("reference_number")
    @Expose
    private String referenceNumber;
    @SerializedName("username")
    @Expose
    private String username;
    @SerializedName("otp")
    @Expose
    private String otp;
    @SerializedName("token_key")
    @Expose
    private String tokenKey;

    public LupaPinOtpFastRequest(String referenceNumber, String username, String otp, String tokenKey) {
        this.referenceNumber = referenceNumber;
        this.username = username;
        this.otp = otp;
        this.tokenKey = tokenKey;
    }

    public String getReferenceNumber() {
        return referenceNumber;
    }

    public void setReferenceNumber(String referenceNumber) {
        this.referenceNumber = referenceNumber;
    }

    public String getUsername() {
        return username;
    }

    public void setUsername(String username) {
        this.username = username;
    }

    public String getOtp() {
        return otp;
    }

    public void setOtp(String otp) {
        this.otp = otp;
    }

    public String getTokenKey() {
        return tokenKey;
    }

    public void setTokenKey(String tokenKey) {
        this.tokenKey = tokenKey;
    }
}
