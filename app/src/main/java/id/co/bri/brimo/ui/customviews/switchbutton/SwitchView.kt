package id.co.bri.brimo.ui.customviews.switchbutton

import android.content.Context
import android.util.AttributeSet
import android.view.LayoutInflater
import android.widget.LinearLayout
import id.co.bri.brimo.databinding.ComponentLayoutSwitchViewBinding
import id.co.bri.brimo.domain.extension.gone
import id.co.bri.brimo.util.extension.visibleView

class SwitchView(context: Context?, attributeSet: AttributeSet?) :
    LinearLayout(context, attributeSet) {

    private var binding: ComponentLayoutSwitchViewBinding? = null
    private var isSwitchSave: Boolean = false

    init {
        initView()
    }

    private fun initView() {
        binding = ComponentLayoutSwitchViewBinding.inflate(
            LayoutInflater.from(context), this, true
        )
    }

    fun setSwitchListener(switchListener: ISwitchListener) {
        binding?.switchSave?.setOnCheckedChangeListener { _, isChecked ->
            isSwitchSave = isChecked
            switchListener.onSwitchChange(isChecked)
        }
    }

    fun setVisibilitySavedListSwitch(
        isFromFastMenu: <PERSON>olean,
        saved: String,
    ) {
        if (isFromFastMenu) {
            binding?.rlDaftarTersimpan?.gone()
        } else {
            binding?.rlDaftarTersimpan?.visibleView(saved.isEmpty())
        }
    }

    fun isSwitchChecked() = isSwitchSave

    fun showDetailTotalPay(showDetailPay: Boolean) {
        binding?.switchSave?.isEnabled = showDetailPay
    }

    fun interface ISwitchListener {

        fun onSwitchChange(isChecked: Boolean)
    }
}