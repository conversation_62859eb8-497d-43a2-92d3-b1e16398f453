package id.co.bri.brimo.data.repository.rate;

import java.util.Date;

import id.co.bri.brimo.models.daomodel.RateUs;
import io.reactivex.Single;

public interface RateSource {
    Single<Long> saveRateUs(RateUs rate);

    Single<Integer> updateRateUs(long id, long rateStat, Date rateDate, long rateCounter);

    Single<Integer> updateRateCounter(long rateCounter, long id);

    Single<Integer> updateDateRate(Date date, long rateCounter, long id);

    Single<RateUs> getRateAll();
}
