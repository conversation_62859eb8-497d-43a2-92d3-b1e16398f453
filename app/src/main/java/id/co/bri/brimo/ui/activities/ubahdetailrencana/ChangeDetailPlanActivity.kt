package id.co.bri.brimo.ui.activities.ubahdetailrencana

import android.app.Activity
import android.content.Intent
import android.os.Bundle
import androidx.core.content.ContextCompat
import androidx.core.widget.addTextChangedListener
import androidx.recyclerview.widget.LinearLayoutManager
import id.co.bri.brimo.R
import id.co.bri.brimo.adapters.ubahdetailrencana.ChangeDetailPlanAdapter
import id.co.bri.brimo.contract.IPresenter.ubahrencana.IPostChangeDetailPlanPresenter
import id.co.bri.brimo.contract.IView.ubahdetailrencana.IPostChangeDetailPlanView
import id.co.bri.brimo.databinding.ActivityChangeDetailPlanBinding
import id.co.bri.brimo.domain.config.Constant
import id.co.bri.brimo.domain.helpers.GeneralHelper
import id.co.bri.brimo.models.ChangeDetailPlan
import id.co.bri.brimo.models.apimodel.request.ubahdetailrencana.PostChangeDetailPlanRequest
import id.co.bri.brimo.models.apimodel.response.ubahdetailrencana.ChangeDetailPlanDataResponse
import id.co.bri.brimo.models.apimodel.response.ubahdetailrencana.ChangeDetailPlanPurpose
import id.co.bri.brimo.ui.activities.base.BaseActivity
import javax.inject.Inject
import kotlin.properties.Delegates

class ChangeDetailPlanActivity : BaseActivity(), IPostChangeDetailPlanView {

    private val binding by lazy { ActivityChangeDetailPlanBinding.inflate(layoutInflater) }
    private var mChangeDetailPlanDataResponse: ChangeDetailPlanDataResponse? = null
    private var bancassAccount = ""
    private var urlIcon = ""
    private var imageUrl = ""
    private var selectedRencanaIndex = 0
    private var selectedIndex = 0
    private var purposeId = 0
    private var planName = ""
    private var purposeList: List<ChangeDetailPlanPurpose>? = listOf()
    private var onButtonEnabled by Delegates.observable(false) { _, _, newValue ->
        binding.btnSaveChangeDetailPlan.apply {
            isEnabled = newValue
            if (context == null) return@apply
            val buttonDrawable = if (this.isEnabled) R.drawable.rounded_button_blue else R.drawable.bg_neutral_light_20_revamp
            background = ContextCompat.getDrawable(this@ChangeDetailPlanActivity, buttonDrawable)
        }
    }

    private val planAdapter by lazy { ChangeDetailPlanAdapter { setPlanPosition(it) } }

    @Inject
    lateinit var presenter: IPostChangeDetailPlanPresenter<IPostChangeDetailPlanView>

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContentView(binding.root)

        getData()
        injectDependency()
        setToolbarDetailPlan()
        initPlanAdapter()
        setPlanData(mChangeDetailPlanDataResponse)
    }

    private fun setToolbarDetailPlan() {
        GeneralHelper.setToolbar(
            this@ChangeDetailPlanActivity,
            binding.toolbarChangeDetailPlan.toolbar,
            GeneralHelper.getString(R.string.toolbar_change_detail_plan)
        )
    }

    private fun getData() {
        mChangeDetailPlanDataResponse = intent.getParcelableExtra(EXTRA_CHANGE_DETAIL_PLAN)
        bancassAccount = intent.getStringExtra(EXTRA_POST_CHANGE_DETAIL_PLAN).orEmpty()
    }

    private fun injectDependency() {
        activityComponent.inject(this)
        presenter.view = this
        presenter.apply {
            start()
            setUrlPostChangeDetailPlan(getString(R.string.url_post_change_detail_plan))
        }
    }

    private fun initPlanAdapter() {
        binding.rvSavingPlan.apply {
            layoutManager = LinearLayoutManager(this@ChangeDetailPlanActivity, LinearLayoutManager.HORIZONTAL, false)
            setHasFixedSize(true)
            adapter = planAdapter
        }
    }

    private fun setPlanData(changeDetailPlanDataResponse: ChangeDetailPlanDataResponse?) {
        binding.apply {
            changeDetailPlanDataResponse?.let { response ->
                planName = response.planName
                val selectedPurposeId = mChangeDetailPlanDataResponse?.selectedPurposeId ?: 0
                imageUrl = ""
                purposeList = mChangeDetailPlanDataResponse?.purposeList
                selectedIndex = purposeList?.indexOfFirst { it.id == selectedPurposeId } ?: 0

                purposeList?.forEach {
                    if (it.id == selectedPurposeId) {
                        purposeId = it.id
                        imageUrl = it.iconUrl
                    }
                }

                handleNamaRencanaForm()

                tvCharsCount.text = if (planName.isNotEmpty())
                    getString(R.string.txt_chars_count, planName.length.toString())
                else
                    getString(R.string.txt_chars_count_zero)

                planAdapter.submitList(purposeList)

                if (selectedIndex != -1) {
                    selectedRencanaIndex = selectedIndex
                    binding.rvSavingPlan.smoothScrollToPosition(selectedRencanaIndex)
                }

                planAdapter.setDefaultSelectedItemId(selectedPurposeId)
                sendChangeDetailPlanData(selectedPurposeId)
            }
        }
    }

    private fun handleNamaRencanaForm() {
        binding.apply {
            etPlanName.setText(planName)
            etPlanName.addTextChangedListener(onTextChanged = { _, _, _, _ ->
                tvCharsCount.text = getString(R.string.txt_chars_count, etPlanName.text?.length.toString())
                onButtonEnabled = etPlanName.text.toString() != planName && etPlanName.text?.isNotEmpty() == true
            })
            etPlanName.setOnFocusChangeListener { _, hasFocus ->
                etPlanName.background = ContextCompat.getDrawable(
                    this@ChangeDetailPlanActivity,
                    if (hasFocus) R.drawable.bg_blue_border else R.drawable.bg_white_border_neutral_light30
                )

                tvCharsCount.setTextColor(ContextCompat.getColor(
                    this@ChangeDetailPlanActivity,
                    if (hasFocus) R.color.primary_blue80 else R.color.neutralLight60
                ))
            }
        }
    }

    private fun setPlanPosition(selectedPurposeId: Int) {
        onButtonEnabled = (selectedPurposeId != purposeId) || (binding.etPlanName.text.toString() != planName && binding.etPlanName.text?.isNotEmpty() == true)
        planAdapter.setDefaultSelectedItemId(selectedPurposeId)
        sendChangeDetailPlanData(selectedPurposeId)
        purposeList?.forEach {
            if (it.id == selectedPurposeId) {
                urlIcon = it.iconUrl
            }
        }
    }

    private fun sendChangeDetailPlanData(selectedPurposeId: Int) {
        binding.apply {
            if (purposeId == selectedPurposeId) {
                urlIcon = imageUrl
            }

            btnSaveChangeDetailPlan.setOnClickListener {
                presenter.postChangeDetailPlanRequest(
                    PostChangeDetailPlanRequest(
                        rencanaAccount = bancassAccount,
                        planName = etPlanName.text.toString().trim(),
                        selectedPurposeId = selectedPurposeId
                    )
                )
            }
        }
    }

    override fun onSuccessResponse(description: String) {
        val changeDetailPlan = ChangeDetailPlan(
            description,
            urlIcon,
            binding.etPlanName.text.toString().trim()
        )

        val intent = Intent().apply {
            putExtra(Constant.TAG_EDIT_RENCANA, changeDetailPlan)
        }
        setResult(RESULT_OK, intent)
        finish()
    }

    override fun onDestroy() {
        presenter.stop()
        super.onDestroy()
    }

    companion object {
        const val EXTRA_CHANGE_DETAIL_PLAN = "detail_plan"
        const val EXTRA_POST_CHANGE_DETAIL_PLAN = "post_detail_plan"

        @JvmStatic
        fun launchIntent(
            caller: Activity,
            keyChangeDetailPlan: String,
            mChangeDetailPlanDataResponse: ChangeDetailPlanDataResponse,
            keyBancassAccount: String,
            bancassAccount: String
        ) {
            val intent = Intent(caller, ChangeDetailPlanActivity::class.java).apply {
                putExtra(keyChangeDetailPlan, mChangeDetailPlanDataResponse)
                putExtra(keyBancassAccount, bancassAccount)
            }
            caller.startActivityForResult(intent, Constant.REQ_EDIT_SAVED)
        }
    }
}