package id.co.bri.brimo.ui.customviews.cardview

import android.content.Context
import android.content.res.ColorStateList
import android.util.AttributeSet
import android.view.LayoutInflater
import android.view.View
import android.widget.ImageView
import android.widget.LinearLayout
import android.widget.RelativeLayout
import android.widget.TextView
import androidx.cardview.widget.CardView
import id.co.bri.brimo.R
import id.co.bri.brimo.domain.config.LifestyleConfig
import id.co.bri.brimo.domain.helpers.GeneralHelper
import id.co.bri.brimo.util.extension.gone
import id.co.bri.brimo.util.extension.visible

class LifestyleCardView : CardView {

    //general routing id layout
    private lateinit var rootCardViewLayout: CardView
    private lateinit var tvDate: TextView
    private lateinit var tvRoundtrip: TextView
    private lateinit var tvAccomodationName: TextView
    private lateinit var tvDot: TextView
    private lateinit var tvTransportClass: TextView

    //routing id travel
    private lateinit var rlCardTravel: RelativeLayout
    private lateinit var tvDepartHourTravel: TextView
    private lateinit var tvDepartDateTravel: TextView
    private lateinit var ivDepartTravel: ImageView
    private lateinit var separatorTravel: View
    private lateinit var llDurationTravel: LinearLayout
    private lateinit var tvDepartCityTravel: TextView
    private lateinit var tvDepartCodeTravel: TextView
    private lateinit var tvDurationTravel: TextView
    private lateinit var tvArrivalHourTravel: TextView
    private lateinit var tvArrivalDateTravel: TextView
    private lateinit var ivArrivalTravel: ImageView
    private lateinit var tvArrivalCityTravel: TextView
    private lateinit var tvArrivalCodeTravel: TextView

    //routing id expedition
    private lateinit var rlCardExpedition: RelativeLayout
    private lateinit var ivSenderExpedition: ImageView
    private lateinit var tvTitleSenderExpedition: TextView
    private lateinit var tvSenderNameExpedition: TextView
    private lateinit var tvSenderPhoneExpedition: TextView
    private lateinit var tvSenderAddressExpedition: TextView
    private lateinit var separatorExpedition: View
    private lateinit var llSenderExpedition: LinearLayout
    private lateinit var ivRecipientExpedition: ImageView
    private lateinit var tvTitleRecipientExpedition: TextView
    private lateinit var tvRecipientNameExpedition: TextView
    private lateinit var tvRecipientPhoneExpedition: TextView
    private lateinit var tvRecipientAddressExpedition: TextView

    //routing id ship
    private lateinit var rlInfoShip: RelativeLayout
    private lateinit var separatorShip: View
    private lateinit var tvTitleInfoCheckinShip : TextView
    private lateinit var tvTimeInfoCheckinShip : TextView

    //style cardview
    private var styleCard = ""

    //variable general
    private var bgCardView = 0
    private var heightSeparator = 0
    private var textDate = ""
    private var textRoundtrip = ""
    private var textAccomodationName = ""
    private var textTransportClass = ""

    //travel content
    private var textDepartHourTravel = ""
    private var textDepartDateTravel = ""
    private var imageDepartTravel = ""
    private var textDepartCityTravel = ""
    private var textDepartCodeTravel = ""
    private var textDurationTravel = ""
    private var textArrivalHourTravel = ""
    private var textArrivalDateTravel = ""
    private var imageArrivalTravel = ""
    private var textArrivalCityTravel = ""
    private var textArrivalCodeTravel = ""

    //expedition
    private var imageSenderExpedition = ""
    private var textTitleSenderExpedition = ""
    private var textSenderNameExpedition = ""
    private var textSenderPhoneExpedition = ""
    private var textSenderAddressExpedition = ""
    private var imageRecipientExpedition = ""
    private var textTitleRecipientExpedition = ""
    private var textRecipientNameExpedition = ""
    private var textRecipientPhoneExpedition = ""
    private var textRecipientAddressExpedition = ""

    //ship
    private var textTitleInfoCheckinShip = ""
    private var textTimeCheckinShip = ""

    private lateinit var sContext: Context

    constructor(mContext: Context, attri: AttributeSet?) : super(mContext, attri) {
        loadView(attri, mContext)
        initCardViewLifestyle(mContext)
    }

    constructor(mContext: Context, attri: AttributeSet?, defStyleAttri: Int) :
            super(mContext, attri, defStyleAttri) {
        initCardViewLifestyle(mContext)
    }

    private fun loadView(mAttri: AttributeSet?, mContext: Context) {
        val dataView = mContext.obtainStyledAttributes(mAttri, R.styleable.CardViewLifestyle)

        when (dataView.getInt(R.styleable.CardViewLifestyle_style, 0)) {
            0 -> styleCard = LifestyleConfig.CardViewStyle.DEFAULT.toString()
            1 -> styleCard = LifestyleConfig.CardViewStyle.EXPEDITION.toString()
        }

        dataView.recycle()
    }

    private fun initCardViewLifestyle(mContext: Context) {
        sContext = mContext
        layoutParams = LayoutParams(LayoutParams.MATCH_PARENT, LayoutParams.WRAP_CONTENT)
        LayoutInflater.from(mContext).inflate(R.layout.layout_cardview_lifestyle, this, true)

        //initiate view
        rootCardViewLayout = findViewById(R.id.cv_lifestyle)
        tvDate = findViewById(R.id.tv_date_lifestyle)
        tvRoundtrip = findViewById(R.id.tv_roundtrip)
        tvAccomodationName = findViewById(R.id.tv_accomodation_name)
        tvDot = findViewById(R.id.tv_dot)
        tvTransportClass = findViewById(R.id.tv_transport_class)

        //initiate id travel
        rlInfoShip = findViewById(R.id.rl_info_ship)
        separatorShip = findViewById(R.id.separator_ship)
        tvTitleInfoCheckinShip = findViewById(R.id.tv_title_info_checkin)
        tvTimeInfoCheckinShip = findViewById(R.id.tv_time_info_checkin)
        rlCardTravel = findViewById(R.id.rl_card_travel)
        tvDepartHourTravel = findViewById(R.id.tv_departure_hour_travel)
        tvDepartDateTravel = findViewById(R.id.tv_departure_date_travel)
        ivDepartTravel = findViewById(R.id.iv_depart_travel)
        separatorTravel  = findViewById(R.id.separator_travel)
        llDurationTravel = findViewById(R.id.ll_depart_duration_travel)
        tvDepartCityTravel  = findViewById(R.id.tv_departure_city_travel)
        tvDepartCodeTravel  = findViewById(R.id.tv_departure_code_travel)
        tvDurationTravel  = findViewById(R.id.tv_duration_travel)
        tvArrivalHourTravel = findViewById(R.id.tv_arrival_hour_travel)
        tvArrivalDateTravel  = findViewById(R.id.tv_arrival_date_travel)
        ivArrivalTravel  = findViewById(R.id.iv_arrival_travel)
        tvArrivalCityTravel  = findViewById(R.id.tv_arrival_city_travel)
        tvArrivalCodeTravel = findViewById(R.id.tv_arrival_code_travel)

        //initiate id expedition
        rlCardExpedition = findViewById(R.id.rl_card_expedition)
        ivSenderExpedition = findViewById(R.id.iv_sender_expedition)
        tvTitleSenderExpedition = findViewById(R.id.tv_title_sender)
        tvSenderNameExpedition = findViewById(R.id.tv_sender_name)
        tvSenderPhoneExpedition = findViewById(R.id.tv_sender_phone)
        tvSenderAddressExpedition = findViewById(R.id.tv_sender_address)
        separatorExpedition = findViewById(R.id.separator_expedition)
        llSenderExpedition = findViewById(R.id.ll_sender)
        ivRecipientExpedition = findViewById(R.id.iv_recipient_expedition)
        tvTitleRecipientExpedition = findViewById(R.id.tv_title_recipient)
        tvRecipientNameExpedition = findViewById(R.id.tv_recipient_name)
        tvRecipientPhoneExpedition = findViewById(R.id.tv_recipient_phone)
        tvRecipientAddressExpedition = findViewById(R.id.tv_recipient_address)

        /**
         * To set text for TextView and image from string for ImageView from xml
         */
        //GENERAL
        if (styleCard.isNotEmpty()) {
            cardviewStyle(styleCard)
        }

        if (bgCardView != 0) {
            rootCardViewLayout.setCardBackgroundColor(GeneralHelper.getColor(bgCardView))
        }

        if (textDate.isNotEmpty()) {
            tvDate.text = textDate
        }

        if (textRoundtrip.isNotEmpty()) {
            tvRoundtrip.text = textRoundtrip
        }

        if (textAccomodationName.isNotEmpty()) {
            tvAccomodationName.text = textAccomodationName
        }

        if (textTransportClass.isNotEmpty()) {
            tvTransportClass.text = textTransportClass
        }

        //TRAVEL
        if (textDepartHourTravel.isNotEmpty()) {
            tvDepartHourTravel.text = textDepartHourTravel
        } else {
            tvDepartHourTravel.gone()
        }

        if (textDepartDateTravel.isNotEmpty()) {
            tvDepartDateTravel.text = textDepartDateTravel
        } else {
            tvDepartDateTravel.gone()
        }

        if (imageDepartTravel.isNotEmpty()) {
            ivDepartTravel.setImageResource(GeneralHelper.getImageId(mContext, imageDepartTravel))
        }

        if (textDepartCityTravel.isNotEmpty()) {
            tvDepartCityTravel.text = textDepartCityTravel
        }

        if (textDepartCodeTravel.isNotEmpty()) {
            tvDepartCodeTravel.visible()
            tvDepartCodeTravel.text = textDepartCodeTravel
        } else {
            tvDepartHourTravel.gone()
        }

        if (textDurationTravel.isNotEmpty()) {
            tvDurationTravel.visible()
            tvDurationTravel.text = textDurationTravel
        } else {
            tvDurationTravel.gone()
        }

        if (textArrivalHourTravel.isNotEmpty()) {
            tvArrivalHourTravel.text = textArrivalHourTravel
        } else {
            tvArrivalHourTravel.gone()
        }

        if (textArrivalDateTravel.isNotEmpty()) {
            tvArrivalDateTravel.text = textArrivalDateTravel
        } else {
            tvArrivalDateTravel.gone()
        }

        if (imageArrivalTravel.isNotEmpty()) {
            ivArrivalTravel.setImageResource(GeneralHelper.getImageId(mContext, imageArrivalTravel))
        }

        if (textArrivalCityTravel.isNotEmpty()) {
            tvArrivalCityTravel.text = textArrivalCityTravel
        }

        if (textArrivalCodeTravel.isNotEmpty()) {
            tvArrivalCodeTravel.visible()
            tvArrivalCodeTravel.text = textArrivalCodeTravel
        } else {
            tvArrivalCodeTravel.gone()
        }

        //check if text date and time depart arrival empty set size separator fixed
        if (textDepartDateTravel.isEmpty() && textArrivalDateTravel.isEmpty() &&
            textDepartHourTravel.isEmpty() && textArrivalHourTravel.isEmpty()) {
            val params = RelativeLayout.LayoutParams(
                RelativeLayout.LayoutParams.WRAP_CONTENT,
                RelativeLayout.LayoutParams.WRAP_CONTENT
            )
            params.setMargins(GeneralHelper.dpToPx(context, 10), 0, 0, 0)
            if (textDepartCodeTravel.isNotEmpty() && textArrivalCodeTravel.isNotEmpty()) {
                heightSeparator = llDurationTravel.measuredHeight

                if (heightSeparator != 0) {
                    separatorTravel.layoutParams = LayoutParams(
                        1,
                        heightSeparator
                    )
                }
            } else {
                params.height = 100
                params.width = 3
            }
            separatorTravel.layoutParams = params
        }

        //EXPEDITION
        if (imageSenderExpedition.isNotEmpty()) {
            ivSenderExpedition.setImageResource(GeneralHelper.getImageId(mContext, imageSenderExpedition))
        }

        if (textTitleSenderExpedition.isNotEmpty()) {
            tvTitleSenderExpedition.text = textTitleSenderExpedition
        }

        if (textSenderNameExpedition.isNotEmpty()) {
            tvSenderNameExpedition.text = textSenderNameExpedition
        }

        if (textSenderPhoneExpedition.isNotEmpty()) {
            tvSenderPhoneExpedition.text = textSenderPhoneExpedition
        }

        if (textSenderAddressExpedition.isNotEmpty()) {
            tvSenderAddressExpedition.text = textSenderAddressExpedition
        }

        if (imageRecipientExpedition.isNotEmpty()) {
            ivRecipientExpedition.setImageResource(GeneralHelper.getImageId(mContext, imageRecipientExpedition))
        }

        if (textTitleRecipientExpedition.isNotEmpty()) {
            tvTitleSenderExpedition.text = textTitleRecipientExpedition
        }

        if (textRecipientNameExpedition.isNotEmpty()) {
            tvRecipientNameExpedition.text = textRecipientNameExpedition
        }

        if (textRecipientPhoneExpedition.isNotEmpty()) {
            tvRecipientPhoneExpedition.text = textRecipientPhoneExpedition
        }

        if (textRecipientAddressExpedition.isNotEmpty()) {
            tvRecipientAddressExpedition.text = textRecipientAddressExpedition
        }

        //SHIP
        if (textTitleInfoCheckinShip.isNotEmpty()) {
            rlInfoShip.visible()
            separatorShip.visible()
            tvTitleInfoCheckinShip.text = textTitleInfoCheckinShip
        } else {
            rlInfoShip.gone()
            separatorShip.gone()
        }

        if (textTimeCheckinShip.isNotEmpty()) {
            rlInfoShip.visible()
            separatorShip.visible()
            tvTimeInfoCheckinShip.text = textTimeCheckinShip
        } else {
            rlInfoShip.gone()
            separatorShip.gone()
        }

        refreshDrawableState()
    }

    //set style cardview by type
    private fun cardviewStyle(cardStyle: String) {
        when (cardStyle) {
            LifestyleConfig.CardViewStyle.DEFAULT.toString() -> {
                rlCardExpedition.gone()
                //set margin top to text duration
                val params: MarginLayoutParams = tvDurationTravel.layoutParams as MarginLayoutParams
                params.topMargin = GeneralHelper.dpToPx(context, 16)
                tvDurationTravel.layoutParams = params

                heightSeparator = llDurationTravel.measuredHeight

                if (heightSeparator != 0) {
                    separatorTravel.layoutParams = LayoutParams(
                        1,
                        heightSeparator
                    )
                }
                else {
                    separatorTravel.layoutParams = LayoutParams(
                        1,
                        100
                    )
                }
            }

            LifestyleConfig.CardViewStyle.EXPEDITION.toString() -> {
                rlCardTravel.gone()
                rlCardExpedition.visible()
                tvAccomodationName.gone()
                tvDot.gone()
                ivSenderExpedition.setImageResource(R.drawable.ic_expedition_send)
                ivRecipientExpedition.setImageResource(R.drawable.ic_expedition_receive)

                heightSeparator = llSenderExpedition.measuredHeight

                if (heightSeparator != 0) {
                    separatorExpedition.layoutParams = LayoutParams(
                        1,
                        heightSeparator
                    )
                } else {
                    separatorExpedition.minimumHeight = GeneralHelper.dpToPx(context, 44)
                }
            }

            else -> {}
        }
    }

    //set style cardview programmatically
    fun setStyleCard(cardStyle: String) {
        styleCard = cardStyle
        if (styleCard.isNotEmpty()) {
            cardviewStyle(styleCard)
        }
    }

    //set background color cardview
    fun setBackgroundColorCard(colorBg: Int) {
        bgCardView = colorBg
        if (bgCardView != 0) {
            rootCardViewLayout.setCardBackgroundColor(GeneralHelper.getColor(bgCardView))
        }
    }

    /**
     *  To set text for TextView and image for ImageView programmatically
     */
    fun setTextDate(txtDate: String) {
        textDate = txtDate
        if (textDate.isNotEmpty()) {
            tvDate.text = textDate
        }
    }

    fun setTextRoundtrip(txtRoundtrip: String) {
        textRoundtrip = txtRoundtrip
        if (textRoundtrip.isNotEmpty()) {
            if (textRoundtrip == GeneralHelper.getString(R.string.txt_pulang)) {
                tvRoundtrip.backgroundTintList =
                    ColorStateList.valueOf(GeneralHelper.getColor(R.color.primary_orange80))
            } else {
                tvRoundtrip.backgroundTintList =
                    ColorStateList.valueOf(GeneralHelper.getColor(R.color.primary_blue60))
            }
            tvRoundtrip.text = textRoundtrip
        }
    }

    fun setTextAccomodationName(txtTransportName: String) {
        textAccomodationName = txtTransportName
        if (textAccomodationName.isNotEmpty()) {
            tvAccomodationName.text = textAccomodationName
        }
    }

    fun setTextTransportClass(txtTransportClass: String) {
        textTransportClass = txtTransportClass
        if (textTransportClass.isNotEmpty()) {
            tvTransportClass.text = textTransportClass
        }
    }

    fun setTextDepartHourTravel(txtDepartHour: String) {
        textDepartHourTravel = txtDepartHour
        if (textDepartHourTravel.isNotEmpty()) {
            tvDepartHourTravel.visible()
            tvDepartHourTravel.text = textDepartHourTravel
        } else {
            tvDepartHourTravel.gone()
        }
    }

    fun setTextDepartDateTravel(txtDepartDate: String) {
        textDepartDateTravel = txtDepartDate
        if (textDepartDateTravel.isNotEmpty()) {
            tvDepartDateTravel.text = textDepartDateTravel
        } else {
            tvDepartDateTravel.gone()
        }
    }

    fun setImageDepartTravel(imgDepart: String) {
        imageDepartTravel = imgDepart
        if (imageDepartTravel.isNotEmpty()) {
            ivDepartTravel.setImageResource(GeneralHelper.getImageId(sContext, imageDepartTravel))
        }
    }

    fun setTextDepartCityTravel(txtDepartCity: String) {
        textDepartCityTravel = txtDepartCity
        if (textDepartCityTravel.isNotEmpty()) {
            tvDepartCityTravel.text = textDepartCityTravel
        }
    }

    fun setTextDepartCodeTravel(txtDepartCode: String) {
        textDepartCodeTravel = txtDepartCode
        if (textDepartCodeTravel.isNotEmpty()) {
            tvDepartCodeTravel.visible()
            tvDepartCodeTravel.text = textDepartCodeTravel
        } else {
            tvDepartCodeTravel.gone()
        }
    }

    fun setTextDurationTravel(txtDuration: String) {
        textDurationTravel = txtDuration
        if (textDurationTravel.isNotEmpty()) {
            tvDurationTravel.visible()
            tvDurationTravel.text = textDurationTravel
        } else {
            tvDurationTravel.gone()
        }
    }

    fun setTextArrivalHourTravel(txtArrivalHour: String) {
        textArrivalHourTravel = txtArrivalHour
        if (textArrivalHourTravel.isNotEmpty()) {
            tvArrivalHourTravel.visible()
            tvArrivalHourTravel.text = textArrivalHourTravel
        } else {
            tvArrivalHourTravel.gone()
        }
    }

    fun setTextArrivalDateTravel(txtArrivaltDate: String) {
        textArrivalDateTravel = txtArrivaltDate
        if (textArrivalDateTravel.isNotEmpty()) {
            tvArrivalDateTravel.text = textArrivalDateTravel
        } else {
            tvArrivalDateTravel.gone()
        }
    }

    fun setImageArrivalTravel(imgArrival: String) {
        imageArrivalTravel = imgArrival
        if (imageArrivalTravel.isNotEmpty()) {
            ivArrivalTravel.setImageResource(GeneralHelper.getImageId(sContext, imageArrivalTravel))
        }
    }

    fun setTextArrivalCityTravel(txtArrivalCity: String) {
        textArrivalCityTravel = txtArrivalCity
        if (textArrivalCityTravel.isNotEmpty()) {
            tvArrivalCityTravel.text = textArrivalCityTravel
        }
    }

    fun setTextArrivalCodeTravel(txtArrivalCode: String) {
        textArrivalCodeTravel = txtArrivalCode
        if (textArrivalCodeTravel.isNotEmpty()) {
            tvArrivalCodeTravel.visible()
            tvArrivalCodeTravel.text = textArrivalCodeTravel
        } else {
            tvArrivalCodeTravel.gone()
        }
    }

    //SHIP
    fun setTextTitleInfoCheckinShip(txtTitleInfoCheckin: String) {
        textTitleInfoCheckinShip = txtTitleInfoCheckin
        if (textTitleInfoCheckinShip.isNotEmpty()) {
            rlInfoShip.visible()
            tvTitleInfoCheckinShip.text = textTitleInfoCheckinShip
        } else {
            tvTitleInfoCheckinShip.gone()
        }
    }

    fun setTextTimeInfoCheckinShip(txtTimeInfoCheckin: String) {
        textTimeCheckinShip = txtTimeInfoCheckin
        if (textTimeCheckinShip.isNotEmpty()) {
            tvTimeInfoCheckinShip.text = textTimeCheckinShip
        }
    }

    //EXPEDITION
    fun setImageSenderExpedition(imgSender: String) {
        imageSenderExpedition = imgSender
        if (imageSenderExpedition.isNotEmpty()) {
            ivSenderExpedition.setImageResource(GeneralHelper.getImageId(sContext, imageSenderExpedition))
        }
    }

    fun settextTitleSenderExpedition(txtTitleSender: String) {
        textTitleSenderExpedition = txtTitleSender
        if (textTitleSenderExpedition.isNotEmpty()) {
            tvTitleSenderExpedition.text = textTitleSenderExpedition
        }
    }

    fun settextSenderNameExpedition(txtSenderName: String) {
        textSenderNameExpedition = txtSenderName
        if (textSenderNameExpedition.isNotEmpty()) {
            tvSenderNameExpedition.text = textSenderNameExpedition
        }
    }

    fun settextSenderPhoneExpedition(txtSenderPhone: String) {
        textSenderPhoneExpedition = txtSenderPhone
        if (textSenderPhoneExpedition.isNotEmpty()) {
            tvSenderPhoneExpedition.text = textSenderPhoneExpedition
        }
    }

    fun settextSenderAddressExpedition(txtSenderAddress: String) {
        textSenderAddressExpedition = txtSenderAddress
        if (textSenderAddressExpedition.isNotEmpty()) {
            tvSenderAddressExpedition.text = textSenderAddressExpedition
        }
    }

    fun setImageRecipientExpedition(imgRecipient: String) {
        imageRecipientExpedition = imgRecipient
        if (imageRecipientExpedition.isNotEmpty()) {
            ivRecipientExpedition.setImageResource(GeneralHelper.getImageId(sContext, imageRecipientExpedition))
        }
    }

    fun settextTitleRecipientExpedition(txtTitleRecipient: String) {
        textTitleRecipientExpedition = txtTitleRecipient
        if (textTitleRecipientExpedition.isNotEmpty()) {
            tvTitleRecipientExpedition.text = textTitleRecipientExpedition
        }
    }

    fun settextRecipientNameExpedition(txtRecipientName: String) {
        textRecipientNameExpedition = txtRecipientName
        if (textRecipientNameExpedition.isNotEmpty()) {
            tvRecipientNameExpedition.text = textRecipientNameExpedition
        }
    }

    fun settextRecipientPhoneExpedition(txtRecipientPhone: String) {
        textRecipientPhoneExpedition = txtRecipientPhone
        if (textRecipientPhoneExpedition.isNotEmpty()) {
            tvRecipientPhoneExpedition.text = textRecipientPhoneExpedition
        }
    }

    fun settextRecipientAddressExpedition(txtRecipientAddress: String) {
        textRecipientAddressExpedition = txtRecipientAddress
        if (textRecipientAddressExpedition.isNotEmpty()) {
            tvRecipientAddressExpedition.text = textRecipientAddressExpedition
        }
    }

}