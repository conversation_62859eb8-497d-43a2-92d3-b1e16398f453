package id.co.bri.brimo.ui.activities.applyvcc

import android.content.Intent
import android.os.Bundle
import androidx.appcompat.app.AppCompatActivity
import androidx.core.widget.addTextChangedListener
import id.co.bri.brimo.R
import id.co.bri.brimo.contract.IPresenter.applyvcc.IApplyVccLocalStoragePresenter
import id.co.bri.brimo.contract.IView.applyvcc.IApplyVccLocalStorageView
import id.co.bri.brimo.databinding.ActivityApplyVccInformationJobBinding
import id.co.bri.brimo.domain.config.Constant
import id.co.bri.brimo.models.apimodel.request.applyccrevamp.ApplyCcSubmitDataLocalRequest
import id.co.bri.brimo.ui.activities.base.BaseActivity
import id.co.bri.brimo.ui.fragments.DatePickerSpinnerGeneralFragment
import id.co.bri.brimo.ui.fragments.applyccrevamp.ApplyVccBottomFragment
import id.co.bri.brimo.util.extension.toArrayList
import id.co.bri.brimo.util.extension.toNewFormat
import id.co.bri.brimo.util.extension.visibleView
import javax.inject.Inject

class ApplyVccInformationJobActivity : BaseActivity(), IApplyVccLocalStorageView {

    private var hasInitial: Boolean = false
    private var isEdit: Boolean = false
    private var applyVccRequestModel = ApplyCcSubmitDataLocalRequest()

    private lateinit var binding: ActivityApplyVccInformationJobBinding

    @Inject
    lateinit var iApplyVccLocalStoragePresenter: IApplyVccLocalStoragePresenter<IApplyVccLocalStorageView>

    companion object {
        private const val IS_EDIT = "IS_EDIT"

        fun launchIntent(caller: AppCompatActivity, isEdit: Boolean = false) {
            val intent = Intent(caller, ApplyVccInformationJobActivity::class.java).apply {
                putExtra(IS_EDIT, isEdit)
            }
            caller.startActivity(intent)
        }
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        binding = ActivityApplyVccInformationJobBinding.inflate(layoutInflater)
        setContentView(binding.root)
        injectDependency()
        getData()
        setContent()
    }

    override fun onRestart() {
        super.onRestart()
        getData()
        updateUiValidationAndSetLocal()
    }

    override fun onPause() {
        super.onPause()
        iApplyVccLocalStoragePresenter.setApplyVccRequestLocal(applyVccRequestModel)
    }

    override fun onResume() {
        super.onResume()
        getData()
        updateUiValidationAndSetLocal()
    }

    private fun setContent() = with(binding) {
        main
        toolbar.setSupportActionBar(this@ApplyVccInformationJobActivity)
        content
        layoutStep.root.visibleView(!isEdit)
        layoutStep.setStep(2)
        tvStepInfo.visibleView(!isEdit)

        etTempatKerja.addTextChangedListener {
            if (it.toString().length in 1..2) {
                if (hasInitial) {
                    tilTempatKerja.error = getString(R.string.txt_panjang_karakter_tidak_mencukupi)
                    tilTempatKerja.isErrorEnabled = true
                }
                applyVccRequestModel.company = ""
            } else {
                tilTempatKerja.error = ""
                tilTempatKerja.isErrorEnabled = false
                applyVccRequestModel.company = it.toString()
            }
            updateUiValidationAndSetLocal(false)
        }
        etJobSector.setOnClickListener { showBusinessTypeListDialog() }
        etJabatan.setOnClickListener { showJobCategoryListDialog() }
        etWorkSince.setOnClickListener { showWorkSinceDateDialog() }

        layoutButton
        btnNext.setOnClickListener {
            iApplyVccLocalStoragePresenter.setApplyVccRequestLocal(applyVccRequestModel)
            gotoNextPageOrFinish()
        }
        if (isEdit) btnNext.text = getString(R.string.simpan)

        updateUiValidationAndSetLocal()
        hasInitial = true
    }

    private fun injectDependency() {
        activityComponent.inject(this)
        iApplyVccLocalStoragePresenter.view = this
        iApplyVccLocalStoragePresenter.start()
    }

    private fun getData() {
        applyVccRequestModel = iApplyVccLocalStoragePresenter.getApplyVccRequestLocal()
        intent.apply {
            isEdit = getBooleanExtra(IS_EDIT, isEdit)
        }
    }

    private fun validationButtonNext() = with(binding) {
        val isValid = when {
            applyVccRequestModel.company.isEmpty() -> false
            applyVccRequestModel.jobBidangUsahaId == 0 -> false
            applyVccRequestModel.jobCategoryId == 0 -> false
            applyVccRequestModel.workSince.isEmpty() -> false
            else -> true
        }

        btnNext.isEnabled = isValid
    }

    private fun setUiFormApplyVccRequestModel() = with(binding) {
        etTempatKerja.setText(applyVccRequestModel.company)
        etJobSector.setText(applyVccRequestModel.jobBidangUsahaText)
        etJabatan.setText(applyVccRequestModel.jobCategoryText)
        etWorkSince.setText(applyVccRequestModel.workSinceText)
    }

    private fun showBusinessTypeListDialog() {
        ApplyVccBottomFragment(
            getString(R.string.jenis_usaha),
            iApplyVccLocalStoragePresenter.getApplyVccDataFormLocal().businessFieldList.toArrayList()
        ) {
            applyVccRequestModel.jobBidangUsahaText = it.value
            applyVccRequestModel.jobBidangUsahaId = it.codeInt
            updateUiValidationAndSetLocal()
        }.show(this)
    }

    private fun showJobCategoryListDialog() {
        ApplyVccBottomFragment(
            getString(R.string.kategori_pekerjaan),
            iApplyVccLocalStoragePresenter.getApplyVccDataFormLocal().occupationList.toArrayList()
        ) {
            applyVccRequestModel.jobCategoryText = it.value
            applyVccRequestModel.jobCategoryId = it.codeInt
            updateUiValidationAndSetLocal()
        }.show(this)
    }

    private fun showWorkSinceDateDialog() {
        val datePickerSpinnerFragment = DatePickerSpinnerGeneralFragment(
            this@ApplyVccInformationJobActivity,
            getString(R.string.bekerja_sejak),
                applyVccRequestModel.workSinceText
        ) {
            applyVccRequestModel.workSince = it.toNewFormat(Constant.DATE_FORMAT, Constant.DB_DATE_FORMAT)
            applyVccRequestModel.workSinceText = it.toNewFormat(Constant.DATE_FORMAT, Constant.DATE_FORMAT_SLASH)
            updateUiValidationAndSetLocal()
        }
        datePickerSpinnerFragment.isCancelable = true
        return datePickerSpinnerFragment.show(supportFragmentManager, "")
    }

    private fun gotoNextPageOrFinish() {
        if (isEdit) finish()
        else ApplyVccPhotoKtpActivity.launchIntent(this@ApplyVccInformationJobActivity)
    }

    private fun updateUiValidationAndSetLocal(isWithSetUiForm: Boolean = true, isWithButtonValidation: Boolean = true) {
        if (isWithSetUiForm) setUiFormApplyVccRequestModel()
        if (isWithButtonValidation) validationButtonNext()
    }
}