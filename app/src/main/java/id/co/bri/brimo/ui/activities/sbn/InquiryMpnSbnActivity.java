package id.co.bri.brimo.ui.activities.sbn;

import androidx.annotation.Nullable;
import androidx.appcompat.widget.Toolbar;
import androidx.fragment.app.FragmentTransaction;
import androidx.recyclerview.widget.GridLayoutManager;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;
import androidx.viewbinding.ViewBinding;

import butterknife.Bind;
import butterknife.ButterKnife;
import id.co.bri.brimo.R;
import id.co.bri.brimo.adapters.BillingAmountAdapter;
import id.co.bri.brimo.adapters.BillingDetailAdapter;
import id.co.bri.brimo.adapters.BillingInfoAdapter;
import id.co.bri.brimo.contract.IPresenter.base.IBaseInquiryPresenter;
import id.co.bri.brimo.contract.IView.base.IBaseInquiryView;
import id.co.bri.brimo.domain.config.Constant;
import id.co.bri.brimo.domain.helpers.GeneralHelper;
import id.co.bri.brimo.domain.helpers.textwatcher.AmountFormatWatcher;
import id.co.bri.brimo.models.AccountModel;
import id.co.bri.brimo.models.BillingDetail;
import id.co.bri.brimo.models.ParameterKonfirmasiModel;
import id.co.bri.brimo.models.ParameterModel;
import id.co.bri.brimo.models.apimodel.response.GeneralInquiryResponse;
import id.co.bri.brimo.ui.activities.InquiryGeneralCloseActivity;
import id.co.bri.brimo.ui.activities.base.BaseInquiryActivity;
import id.co.bri.brimo.ui.customviews.dialog.DialogExitCustom;
import info.hoang8f.android.segmented.SegmentedGroup;

import android.app.Activity;
import android.content.Context;
import android.content.Intent;
import android.os.Bundle;
import android.util.Log;
import android.view.View;
import android.view.inputmethod.InputMethodManager;
import android.widget.Button;
import android.widget.LinearLayout;
import android.widget.TextView;

import com.google.gson.Gson;

import java.util.ArrayList;
import java.util.List;

import javax.inject.Inject;

public class InquiryMpnSbnActivity extends BaseInquiryActivity
        implements
        AmountFormatWatcher.onAmountChange,
        IBaseInquiryView, DialogExitCustom.DialogDefaultListener{
    private static final String TAG = "InquiryGeneralCloseActi";

    @Bind(R.id.rv_billing_amount)
    RecyclerView rvAmount;
    @Bind(R.id.rv_billing_detail)
    RecyclerView rvDetail;
    @Bind(R.id.rv_billing_info)
    RecyclerView rvInfo;
    @Bind(R.id.rv_billing_info_minimum)
    RecyclerView rvInfoMinimum;
    @Bind(R.id.lihat_sedikit)
    LinearLayout lihatSedikit;
    @Bind(R.id.lihat_lebih)
    LinearLayout lihatLebih;
    @Bind(R.id.tb_inquiry_toolbar)
    Toolbar tbClose;

    ///Input Bayar CC
    @Bind(R.id.ll_input_bayar)
    LinearLayout layoutInputNominal;
    @Bind(R.id.ll_edit_text)
    LinearLayout layoutInputManual;
    @Bind(R.id.btn_bayar_penuh)
    Button btnBayarPenuh;
    @Bind(R.id.btn_bayar_minimal)
    Button btnBayarMinimal;
    @Bind(R.id.btn_input_manual)
    Button btnInputManual;
    @Bind(R.id.segmented2)
    SegmentedGroup btnOpsiNominal;
    @Bind(R.id.ll_button_segment)
    LinearLayout ll_button_segement;

    @Bind(R.id.tv_minimal)
    TextView tvMinimal;
    @Bind(R.id.layout_minimum)
    LinearLayout layoutMinimum;
    @Bind(R.id.lblNominal)
    TextView lblNominal;

    boolean isCardShow, isLihatLebih = false;

    BillingDetailAdapter detailAdapter;
    BillingInfoAdapter infoAdapter;
    BillingInfoAdapter infoMinimumAdapter;
    BillingAmountAdapter amountAdapter;

    List<BillingDetail> listAllData = new ArrayList<>();
    List<BillingDetail> listMinimumData = new ArrayList<>();
    int rowMinimumData, rowAllData;

    @Inject
    IBaseInquiryPresenter<IBaseInquiryView> brivaPresenter;

    public static void launchIntent(Activity caller, GeneralInquiryResponse inquiryResponse, String urlKonfirmasi, String urlPayment, String title, ParameterModel parameterModel, boolean fromFastMenu) {
        Intent intent = new Intent(caller, InquiryMpnSbnActivity.class);

        isFromFastMenu = fromFastMenu;

        //put data to Intent
        if (inquiryResponse != null) {
            try {
                intent.putExtra(TAG_INQUIRY_DATA, new Gson().toJson(inquiryResponse));
            } catch (Exception e) {
                if (!GeneralHelper.isProd())
                    Log.e(TAG_INQUIRY_DATA, "launchIntent: ", e);
            }
        }

        if (parameterModel != null) {
            try {
                intent.putExtra(TAG_PARAMS_DATA, new Gson().toJson(parameterModel));
            } catch (Exception e) {
                if (!GeneralHelper.isProd())
                    Log.e(TAG_PARAMS_DATA, "launchIntent: ", e);
            }
        }

        //set URL service
        try {
            intent.putExtra(TAG_URL_KONFIRM, urlKonfirmasi);
            intent.putExtra(TAG_URL_PAYMENT, urlPayment);
            intent.putExtra(TAG_TITLE, title);
        } catch (Exception e) {
            if (!GeneralHelper.isProd())
                Log.e(TAG, "launchIntent: ", e);
        }

        caller.startActivityForResult(intent, Constant.REQ_PAYMENT);
    }

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);

        ButterKnife.bind(this);
        //inject presenter dari ActivityModule
        injectDependency();

        setLihatLebih();

        selectInputAmount();

        setupTextWatcher();

        layoutInputManual.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View view) {
                edNominal.requestFocus();
                edNominal.setFocusableInTouchMode(true);
                InputMethodManager imm = (InputMethodManager) getSystemService(Context.INPUT_METHOD_SERVICE);
                imm.showSoftInput(edNominal, InputMethodManager.SHOW_FORCED);
            }
        });

    }

    @Override
    protected int getLayoutResource() {
        return R.layout.activity_inquiry_mpn_sbn;
    }



    @Override
    protected String getTitleBar() {
        return "Beli SBN";
    }


    public void setLihatLebih() {
        if(mInquiryResponse.getBillingDetail() == null)
            return;

        listAllData = mInquiryResponse.getBillingDetail();
        rowAllData = mInquiryResponse.getBillingDetail().size() / 2;
        rowMinimumData = mInquiryResponse.getRowDataShow();

        if(rowAllData <= rowMinimumData || rowMinimumData == 0){
            //jika jumlah row sama
            if(rowAllData == rowMinimumData){
                //apakah ada sisa total item modulus 2?
                int rowSisa = mInquiryResponse.getBillingDetail().size() % 2;
                //jika ada sisa tampilkan lebih sedikit
                if(rowSisa > 0){
                    lihatSedikit();
                    rvInfoMinimum.setVisibility(View.VISIBLE);
                    rvInfo.setVisibility(View.GONE);
                    lihatLebih.setVisibility(View.VISIBLE);
                    lihatSedikit.setVisibility(View.GONE);
                } else {
                    lihatSemua();
                }
            } else {
                lihatSemua();
            }
        } else {
            lihatSedikit();
            rvInfoMinimum.setVisibility(View.VISIBLE);
            rvInfo.setVisibility(View.GONE);
            lihatLebih.setVisibility(View.VISIBLE);
            lihatSedikit.setVisibility(View.GONE);
        }

        lihatSedikit.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View view) {
                toggleLihat();
            }
        });

        lihatLebih.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View view) {
                toggleLihat();
            }
        });

    }

    protected void toggleLihat() {
        if (!isLihatLebih) {
            lihatLebih();
        } else {
            lihatSedikit();
        }
    }

    private void lihatSemua() {
        lihatLebih();

        lihatLebih.setVisibility(View.GONE);
        lihatSedikit.setVisibility(View.GONE);
    }

    private void lihatLebih() {
        // Info Transaksi
        rvInfo.setHasFixedSize(true);
        rvInfo.setLayoutManager(new GridLayoutManager(this, 2));
        infoAdapter = new BillingInfoAdapter(this, listAllData);
        rvInfo.setAdapter(infoAdapter);

        onAnimatorFade(rvInfoMinimum, false, Constant.REQUEST_CLOSE_INQUIRY);
        onAnimatorShow(rvInfo, true, Constant.REQUEST_CLOSE_INQUIRY);

        lihatLebih.setVisibility(View.GONE);
        lihatSedikit.setVisibility(View.VISIBLE);

        isLihatLebih = true;
    }

    private void lihatSedikit(){
        listMinimumData = mInquiryResponse.getBillingDetailMinimum();
        rvInfoMinimum.setHasFixedSize(true);
        rvInfoMinimum.setLayoutManager(new GridLayoutManager(this, 2));
        infoMinimumAdapter = new BillingInfoAdapter(this, listMinimumData);
        rvInfoMinimum.setAdapter(infoMinimumAdapter);

        onAnimatorFade(rvInfo, true, Constant.REQUEST_CLOSE_INQUIRY);
        onAnimatorShow(rvInfoMinimum, false, Constant.REQUEST_CLOSE_INQUIRY);

        lihatLebih.setVisibility(View.VISIBLE);
        lihatSedikit.setVisibility(View.GONE);

        isLihatLebih = false;
    }

    // select input amount untuk CC
    protected void selectInputAmount() {
        btnBayarPenuh.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View view) {
                amountPay.setText(mInquiryResponse.getAmount().toString());
                showNominalField(false);
            }
        });
        btnBayarMinimal.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View view) {
                amountPay.setText(mInquiryResponse.getMinimumAmount().toString());
                showNominalField(false);
            }
        });
        btnInputManual.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View view) {
                edNominal.getText().clear();
                showNominalField(true);
            }
        });
    }


    // set default amount
    protected void showNominalField(boolean isEnable){
        //menampilkan field
        showCardNominal();
        edNominal.setText(amountPay.getText());
        edNominal.setEnabled(isEnable);
        setupInputError(edNominal);
    }


    private void setupTextWatcher() {
        edNominal.addTextChangedListener(new AmountFormatWatcher(edNominal, this, false));
    }


    @Override
    protected void onStop() {
        super.onStop();

    }

    @Override
    protected void injectDependency() {
        getActivityComponent().inject(this);

        if (brivaPresenter != null) {
            brivaPresenter.setView(this);
            brivaPresenter.setUrlConfirmation(mUrlKonfirmasi);
            brivaPresenter.start();
        }
    }

    @Override
    protected void setupView() {
        if (mInquiryResponse == null)
            return;

        //set view untuk CC
        tvMinimal.setText(mInquiryResponse.getMinimumAmountString());
        if (mInquiryResponse.getOpenPayment().equals(true) && mInquiryResponse.getIsBilling().equals(true) && mInquiryResponse.getMinimumPayment().equals(true)) {
            layoutInputNominal.setVisibility(View.VISIBLE);
            layoutMinimum.setVisibility(View.VISIBLE);
            btnInputManual.setSelected(true);
            btnOpsiNominal.check(R.id.btn_input_manual);
            showNominalField(true);
        }else if (mInquiryResponse.getOpenPayment().equals(true) && mInquiryResponse.getIsBilling().equals(true) && mInquiryResponse.getMinimumPayment().equals(false)){
            layoutInputNominal.setVisibility(View.VISIBLE);
            layoutMinimum.setVisibility(View.VISIBLE);
            ll_button_segement.setVisibility(View.GONE);
            btnOpsiNominal.setVisibility(View.GONE);
            showNominalField(true);
        }
        //amount untuk inquiry close
        else {
            amountPay.setText(mInquiryResponse.getPayAmount().toString());
            edNominal.setText(amountPay.getText());
            setupInputError(edNominal);
        }

        // Detail Amount
        rvAmount.setHasFixedSize(true);
        rvAmount.setLayoutManager(new LinearLayoutManager(this, RecyclerView.VERTICAL, false));
        amountAdapter = new BillingAmountAdapter(this, mInquiryResponse.getBillingAmount());
        rvAmount.setAdapter(amountAdapter);

        // Detail Transaksi
        rvDetail.setHasFixedSize(true);
        rvDetail.setLayoutManager(new LinearLayoutManager(this, RecyclerView.VERTICAL, false));
        detailAdapter = new BillingDetailAdapter(this, mInquiryResponse.getBillingAmountDetail());
        rvDetail.setAdapter(detailAdapter);

        //Set View from Parameter
        btnSubmit.setText(mParameterModel.getStringButtonSubmit());
        lblNominal.setText(mParameterModel.getStringLabelNominal());

        //set minimum
        if( mInquiryResponse.getMinimumTransaction() != null &&  mInquiryResponse.getMinimumTransactionString() != null) {
            minTrx = mInquiryResponse.getMinimumTransaction();
            minTrxString = mInquiryResponse.getMinimumTransactionString();
        }

        tvError.setText(String.format("Minimal %s %s", mParameterModel.getStringLabelMinimum(), minTrxString));
    }

    @Override
    public int getAmount() {
        return amountPay.getValue();
    }


    @Override
    public void onSubmit() {
        if (!isLoading) {
            brivaPresenter.getDataConfirmation(mInquiryResponse.getReferenceNumber(), model.getAcoount(), amountPay.getText(), etSaved.getText().toString(), isFromFastMenu);
            isLoading = true;
        }
    }


    @Override
    public ParameterKonfirmasiModel setParameterKonfirmasi() {
        ParameterKonfirmasiModel parameterKonfirmasiModel = new ParameterKonfirmasiModel();

        parameterKonfirmasiModel.setStringLabelTujuan(mParameterModel.getStringLabelTujuan());
        parameterKonfirmasiModel.setStringButtonSubmit("Bayar");
        parameterKonfirmasiModel.setDefaultIcon(mParameterModel.getDefaultIcon());

        return parameterKonfirmasiModel;
    }


    @Override
    public void onAmountChange(String amount) {
        amountPay.setText(amount);
        setupInputError(edNominal);
    }


    @Override
    public void setAmountListener() {

    }

    public void showCardNominal() {
        if (!isCardShow) {
            onAnimatorShow(layoutInputManual, true, Constant.REQUEST_CLOSE_INQUIRY);
            isCardShow = true;
        }
    }

    @Override
    public void onSelectSumberDana(AccountModel bankModel) {
        super.onSelectSumberDana(bankModel);
        setupInputError(edNominal);
    }

    @Override
    public void onBackPressed() {
        DialogExitCustom dialogExitCustom = new DialogExitCustom(this::onClickYes, "Yakin Untuk Batalkan Pembayaran?", "Kuota individu Anda telah terpotong. Jika membatalkan, detail pembayaran dapat diakses di Riwayat Pembelian","Tidak","Ya, Batalkan");
        FragmentTransaction ft = this.getSupportFragmentManager().beginTransaction();
        ft.add(dialogExitCustom, null);
        ft.commitAllowingStateLoss();
    }


    @Override
    public void onClickYes() {
        Intent i = new Intent();
        setResult(RESULT_OK,i);
        finish();
        super.onBackPressed();
    }

    @Override
    protected void onActivityResult(int requestCode, int resultCode, @Nullable Intent data) {
        super.onActivityResult(requestCode, resultCode, data);
        Intent i = new Intent();
        setResult(RESULT_OK,i);
        finish();
        super.onBackPressed();
    }
}