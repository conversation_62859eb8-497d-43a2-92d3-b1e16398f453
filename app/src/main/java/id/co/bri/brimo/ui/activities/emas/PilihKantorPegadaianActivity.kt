package id.co.bri.brimo.ui.activities.emas

import android.app.Activity
import android.app.SearchManager
import android.content.Intent
import android.os.Bundle
import android.view.View
import androidx.appcompat.widget.SearchView
import androidx.recyclerview.widget.LinearLayoutManager
import com.ethanhua.skeleton.Skeleton
import com.ethanhua.skeleton.SkeletonScreen
import com.google.gson.Gson
import id.co.bri.brimo.R
import id.co.bri.brimo.adapters.PilihPegadaianAdapter
import id.co.bri.brimo.contract.IPresenter.emas.IKantorPegadaianPresenter
import id.co.bri.brimo.contract.IView.emas.IKantorPegadaianView
import id.co.bri.brimo.databinding.ActivityPilihKantorPegadaianBinding
import id.co.bri.brimo.domain.config.Constant
import id.co.bri.brimo.domain.helpers.GeneralHelper
import id.co.bri.brimo.models.apimodel.request.emas.SearchPegadaianRequest
import id.co.bri.brimo.models.apimodel.response.emas.KantorPegadaianResponse
import id.co.bri.brimo.models.apimodel.response.emas.Outlets
import id.co.bri.brimo.ui.activities.base.BaseActivity
import javax.inject.Inject

class PilihKantorPegadaianActivity : BaseActivity(), IKantorPegadaianView,
        PilihPegadaianAdapter.onClickItem {
    lateinit var binding : ActivityPilihKantorPegadaianBinding
    private var outlets: MutableList<Outlets> = mutableListOf()
    lateinit var skeletonScreen: SkeletonScreen
    lateinit var adapter: PilihPegadaianAdapter

    @Inject
    lateinit var presenter: IKantorPegadaianPresenter<IKantorPegadaianView>
    companion object{
        @JvmStatic
        fun launchIntent(caller: Activity) {
            val intent = Intent(caller, PilihKantorPegadaianActivity::class.java)
            caller.startActivityForResult(intent, Constant.REQ_BUKA_REKENING)
        }
    }
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        binding = ActivityPilihKantorPegadaianBinding.inflate(layoutInflater)
        setContentView(binding.root)
        injectDependency()
        searchLocation()

        setUpView()
    }

    fun injectDependency() {
        activityComponent.inject(this)
        //get prresenter from dagger inject
        presenter.view = this
        presenter.setUrlSearch(GeneralHelper.getString(R.string.url_onboard_search_pegadaian))
        presenter.start()
    }

    private fun setUpView(){
        GeneralHelper.setToolbar(this, binding.toolbar.toolbar, "Pilih Kantor Pegadaian")
        binding.recyclerView2.visibility = View.GONE
        binding.lyNotFound.visibility = View.VISIBLE
        binding.lyRedaksional.visibility = View.GONE
        binding.ly01.visibility = View.GONE
        binding.txtRes.visibility = View.GONE

        adapter = PilihPegadaianAdapter(this, outlets, this)
        binding.recyclerView2.layoutManager = LinearLayoutManager(this)
        binding.recyclerView2.adapter = adapter


    }

    override fun onSuccessSearchPegadaian(dataPegadaian: KantorPegadaianResponse) {
        outlets.clear()
        outlets.addAll(dataPegadaian.outlets)
        adapter = PilihPegadaianAdapter(this, outlets, this)
        adapter.notifyDataSetChanged()
        binding.lyRedaksional.visibility = View.VISIBLE
        binding.ly01.visibility = View.GONE
        binding.lyNotFound.visibility = View.GONE
        binding.txtRes.visibility = View.VISIBLE

        binding.txtRes.text = String.format(GeneralHelper.getString(R.string.text_result), dataPegadaian.outlets.size.toString())

        skeletonScreen.hide()
    }

    override fun onFailedGedData() {
        binding.ly01.visibility = View.VISIBLE
        binding.recyclerView2.visibility = View.GONE
        binding.lyNotFound.visibility = View.GONE
        binding.lyRedaksional.visibility = View.GONE
        binding.txtRes.visibility = View.GONE
    }

    private fun searchLocation() {
        val searchManager = this.getSystemService(SEARCH_SERVICE) as SearchManager
        binding.searchviewBukarek.setSearchableInfo(searchManager.getSearchableInfo(this.componentName))
        binding.searchviewBukarek.maxWidth = Int.MAX_VALUE
        binding.searchviewBukarek.setOnQueryTextListener(object : SearchView.OnQueryTextListener {
            override fun onQueryTextSubmit(query: String): Boolean {
                binding.lyNotFound.visibility = View.GONE
                binding.recyclerView2.visibility = View.VISIBLE
                binding.lyRedaksional.visibility = View.GONE
                binding.ly01.visibility = View.GONE
                binding.txtRes.visibility = View.GONE

                skeletonScreen = Skeleton.bind(binding.recyclerView2)
                        .adapter(adapter)
                        .shimmer(true)
                        .angle(10)
                        .frozen(false)
                        .duration(1200)
                        .load(R.layout.item_skeleton_pilih_kantor)
                        .show()

                presenter.searchPegadaian(SearchPegadaianRequest(query))
                return false
            }

            override fun onQueryTextChange(newText: String): Boolean {
                return false
            }
        })
    }

    override fun onClickOutlet(outlets: Outlets) {
        val i = Intent()
        i.putExtra("data_kantor", Gson().toJson(outlets))

        this.setResult(Constant.REQ_UPDATE, i)
        finish()
    }
}