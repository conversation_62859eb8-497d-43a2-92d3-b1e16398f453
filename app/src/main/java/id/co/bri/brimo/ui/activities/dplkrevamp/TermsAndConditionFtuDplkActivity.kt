package id.co.bri.brimo.ui.activities.dplkrevamp

import android.app.Activity
import android.content.Intent
import android.os.Bundle
import id.co.bri.brimo.R
import id.co.bri.brimo.contract.IPresenter.dplkrevamp.ITermsAndConditionFtuDplkPresenter
import id.co.bri.brimo.contract.IView.dplkrevamp.ITermsAndConditionFtuDplkView
import id.co.bri.brimo.databinding.ActivityTermsAndConditionFtuDplkBinding
import id.co.bri.brimo.domain.config.Constant
import id.co.bri.brimo.domain.extension.gone
import id.co.bri.brimo.domain.extension.visible
import id.co.bri.brimo.domain.helpers.GeneralHelper
import id.co.bri.brimo.models.apimodel.request.dplkrevamp.PaymentFtuDplkRequest
import id.co.bri.brimo.models.apimodel.response.dplkrevamp.PaymentFtuDplkResponse
import id.co.bri.brimo.models.apimodel.response.dplkrevamp.StateRegistDplk
import id.co.bri.brimo.ui.activities.LupaPinActivity
import id.co.bri.brimo.ui.activities.base.BaseActivity
import id.co.bri.brimo.ui.fragments.PinFragment
import javax.inject.Inject

class TermsAndConditionFtuDplkActivity : BaseActivity(), PinFragment.SendPin,
    ITermsAndConditionFtuDplkView {

    private var _binding: ActivityTermsAndConditionFtuDplkBinding? = null
    private val binding get() = _binding!!

    @Inject
    lateinit var presenter: ITermsAndConditionFtuDplkPresenter<ITermsAndConditionFtuDplkView>
    private var isArrow = false

    companion object {
        private const val TAG_INTENT_TNC_LINK = "TNC_LINK"
        private const val TAG_INTENT_REF_NUMBER = "REF_NUMBER"
        private const val TAG_INTENT_REGISTERED = "REGISTERED"

        private var mTnCWebView = ""
        private var mReferenceNumber = ""
        private var mIsRegistered = false

        @JvmStatic
        fun launchIntent(
            caller: Activity,
            tnCWebView: String,
            referenceNumber: String,
            isRegistered: Boolean
        ) {
            val intent = Intent(caller, TermsAndConditionFtuDplkActivity::class.java)
            intent.putExtra(TAG_INTENT_TNC_LINK, tnCWebView)
            intent.putExtra(TAG_INTENT_REF_NUMBER, referenceNumber)
            intent.putExtra(TAG_INTENT_REGISTERED, isRegistered)
            caller.startActivityForResult(intent, Constant.REQ_PAYMENT)
        }
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        _binding = ActivityTermsAndConditionFtuDplkBinding.inflate(layoutInflater)
        setContentView(binding.root)
        setupView()
        injectDependency()
    }

    private fun injectDependency() {
        activityComponent.inject(this)
        presenter.view = this
        if (mIsRegistered) {
            presenter.setUrlPaymentFtuDplk(GeneralHelper.getString(R.string.url_dplk_payment_open_account))
        } else {
            presenter.setUrlPaymentFtuDplk(GeneralHelper.getString(R.string.url_dplk_payment_dplk))
        }
        presenter.start()
    }

    private fun setupView() {
        binding.apply {
            intent.getStringExtra(TAG_INTENT_TNC_LINK)?.let {
                mTnCWebView = it
            }
            intent.getStringExtra(TAG_INTENT_REF_NUMBER)?.let {
                mReferenceNumber = it
            }
            mIsRegistered = intent.getBooleanExtra(TAG_INTENT_REGISTERED, false)

            setStatusColor(R.color.primaryBlue80)
            GeneralHelper.setToolbarRevamp(
                this@TermsAndConditionFtuDplkActivity,
                toolbar.toolbar,
                GeneralHelper.getString(R.string.tnc)
            )

            GeneralHelper.setWebView(binding.wvTermsAndCondition, "", mTnCWebView)

            if (!isArrow) {
                scrollview.viewTreeObserver.addOnScrollChangedListener {
                    val scrollY: Int = scrollview.scrollY
                    if (scrollY == 0) imgButtonToDown.visible() else imgButtonToDown.gone()
                }
            } else imgButtonToDown.gone()

            btnApproved.setOnClickListener {
                val pinFragment = PinFragment(
                    this@TermsAndConditionFtuDplkActivity, this@TermsAndConditionFtuDplkActivity
                )
                pinFragment.show()
            }
            imgButtonToDown.setOnClickListener {
                scrollview.scrollTo(0, btnCancel.bottom)
                imgButtonToDown.gone()
            }
        }
    }

    override fun onSendPinComplete(pin: String?) {
        pin?.let {
            presenter.getDataUrlPaymentFtuDplk(PaymentFtuDplkRequest(mReferenceNumber, it))
        }
    }

    override fun onLupaPin() {
        LupaPinActivity.launchIntent(this)
    }

    override fun onSuccessPaymentFtuDplk(response: PaymentFtuDplkResponse) {
        if (response.immediatelyFlag) {
            ReceiptRegistDplkActivity.launchIntent(this@TermsAndConditionFtuDplkActivity, response)
        } else {
            ReceiptPendingRegistDplkActivity.launchIntent(
                this@TermsAndConditionFtuDplkActivity,
                response
            )
        }
    }

    override fun onExceptionTrxExpired(message: String) {
        if (mIsRegistered) {
            DashboardDplkRevampActivity.launchIntentErrorCall(
                this@TermsAndConditionFtuDplkActivity,
                message
            )
        } else {
            FirstTimeDplkActivity.launchIntent(this@TermsAndConditionFtuDplkActivity, message,false,StateRegistDplk.NONE)
        }
    }

    override fun onExceptionTrxFailed(message: String) {
        GeneralHelper.showDialogGagalBackDescBerubah(this, Constant.TRANSAKSI_GAGAL, message)
    }
}