package id.co.bri.brimo.ui.activities.portofolioksei

import android.app.Activity
import android.content.Intent
import android.graphics.Color
import android.os.Bundle
import android.util.Log
import android.view.View
import android.widget.LinearLayout
import androidx.appcompat.app.AppCompatActivity
import androidx.fragment.app.Fragment
import androidx.fragment.app.FragmentManager
import androidx.fragment.app.FragmentPagerAdapter
import androidx.viewpager.widget.ViewPager
import id.co.bri.brimo.R
import id.co.bri.brimo.databinding.ActivityDetailKseiWithTabBinding
import id.co.bri.brimo.domain.config.Constant
import id.co.bri.brimo.domain.helpers.GeneralHelper
import id.co.bri.brimo.domain.helpers.bubbleShowCaseView.BubbleShowCase
import id.co.bri.brimo.domain.helpers.bubbleShowCaseView.BubbleShowCaseBuilder
import id.co.bri.brimo.models.apimodel.response.portofolioksei.DetailAssetResponse
import kotlin.CharSequence
import kotlin.Exception
import kotlin.Float
import kotlin.Int
import kotlin.String


class DetailKseiWithTabActivity : AppCompatActivity(), ViewPager.OnPageChangeListener {
    lateinit var binding: ActivityDetailKseiWithTabBinding
    private var titleList: ArrayList<String> = ArrayList()
    private var fragmentList: ArrayList<Fragment> = ArrayList()
    private var lyTabs: LinearLayout? = null
    private var catatanKeuanganFragmentAdapter: MyPagerAdapter? = null
    private var navTab: BubbleShowCaseBuilder? = null


    companion object{
        var dataMas: DetailAssetResponse? = null
        var mTitle : String? = null
        var mType : String? = null
        @JvmStatic
        fun launchIntent(caller: Activity, data: DetailAssetResponse, title : String, type : String) {
            val intent = Intent( caller, DetailKseiWithTabActivity::class.java)
            dataMas = data
            mTitle = title
            mType = type
            caller.startActivityForResult(intent, Constant.REQ_PAYMENT)
        }
    }
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        binding = ActivityDetailKseiWithTabBinding.inflate(layoutInflater)
        setContentView(binding.root)

        setupView()

    }

    fun setupView(){
        GeneralHelper.setToolbar(this, binding.toolbar.toolbar, mTitle)

        binding.tabCatatanKeuangan.visibility = View.VISIBLE
        binding.vpCatatanKeuangan.visibility = View.VISIBLE

//        setUpTutorial()

        for (i in dataMas!!.instruments!!.indices) {
            titleList.add(dataMas!!.instruments!![i].peName!!)
        }

        if(dataMas!!.instruments!!.size == 1){
            binding.tabCatatanKeuangan.visibility = View.GONE
        }

        catatanKeuanganFragmentAdapter = MyPagerAdapter(this,supportFragmentManager,  titleList, mType!!, dataMas!!)
        binding.vpCatatanKeuangan.adapter = catatanKeuanganFragmentAdapter
        binding.tabCatatanKeuangan.setViewPager(binding.vpCatatanKeuangan)


        lyTabs =  binding.tabCatatanKeuangan.getChildAt(0) as LinearLayout
        binding.tabCatatanKeuangan.setOnPageChangeListener(this)

        GeneralHelper.changeTabsFontSimpleNew(this, lyTabs, binding.vpCatatanKeuangan.currentItem)

    }

    override fun onPageScrolled(position: Int, positionOffset: Float, positionOffsetPixels: Int) {

    }

    override fun onPageSelected(position: Int) {
        GeneralHelper.changeTabsFontSimpleNew(this, lyTabs, position)
    }

    override fun onPageScrollStateChanged(state: Int) {

    }

    class MyPagerAdapter(
            private val mContext : Activity,
            fragmentManager: FragmentManager?, private val mTitle : List<String>,
            private val mType : String,
            private val response: DetailAssetResponse
    ) : FragmentPagerAdapter(fragmentManager!!) {

        // Returns total number of pages
        override fun getCount(): Int {
            return response.instruments!!.size
        }

        // Returns the fragment to display for that page
        override fun getItem(position: Int): Fragment {
            return DetailKseiFragment(mContext,response,position,mType)
        }

        override fun getPageTitle(position: Int): CharSequence? {
            return mTitle[position]
        }
    }

    override fun onBackPressed() {
        val i = Intent()
        setResult(RESULT_CANCELED, i)
        finish()
    }

    fun addMenuKsei(activity: Activity?, list: MutableList<BubbleShowCaseBuilder>) {
        navTab = BubbleShowCaseBuilder(this) //Activity instance
                .title("Geser untuk lihat Broker lainnya") //Any title for the bubble view
                .description(GeneralHelper.getString(R.string.txt_tutorial_ksei_tab))
                .backgroundColor(Color.WHITE)
                .textColor(Color.BLACK)
                .buttonTitle("Berikutnya")
                .targetView(binding.tabCatatanKeuangan)
//                .listener(
//                        object : BubbleShowCaseListener {
//                            override fun onTargetClick(bubbleShowCase: BubbleShowCase) {}
//                            override fun onCloseActionImageClick(bubbleShowCase: BubbleShowCase) {
//
//                                val yourFragment = supportFragmentManager.findFragmentByTag(Constant.FRAGMENT_DETAIL_KSEI) as? DetailKseiFragment
//                                yourFragment?.addBubbleShowCase()
//                            }
//
//                            override fun onBackgroundDimClick(bubbleShowCase: BubbleShowCase) {}
//                            override fun onBubbleClick(bubbleShowCase: BubbleShowCase) {}
//                        })
                    .textViewLewati("Lewati")
                    .enableLewati(true)
                .arrowPosition(BubbleShowCase.ArrowPosition.TOP)
        list.add(0, navTab!!)
    }
    private fun addTutorial(){
        try {

        } catch (e: Exception) {
        }
    }

}