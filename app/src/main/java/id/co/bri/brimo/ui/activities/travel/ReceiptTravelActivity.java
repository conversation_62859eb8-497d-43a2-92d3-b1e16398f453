package id.co.bri.brimo.ui.activities.travel;

import android.app.Activity;
import android.content.ActivityNotFoundException;
import android.content.Intent;
import android.content.pm.PackageManager;
import android.content.pm.ResolveInfo;
import android.graphics.Bitmap;
import android.graphics.Color;
import android.net.Uri;
import android.os.Bundle;
import android.os.Environment;
import android.os.Handler;
import android.view.View;
import android.view.ViewGroup;
import android.view.ViewTreeObserver;
import android.widget.Toast;

import androidx.annotation.NonNull;
import androidx.core.app.ActivityCompat;
import androidx.core.content.FileProvider;
import androidx.recyclerview.widget.GridLayoutManager;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.google.gson.Gson;
import com.wajahatkarim3.easyflipview.EasyFlipView;

import java.io.File;
import java.io.FileOutputStream;
import java.util.ArrayList;
import java.util.List;

import javax.inject.Inject;

import id.co.bri.brimo.BuildConfig;
import id.co.bri.brimo.R;
import id.co.bri.brimo.adapters.BusInquiryDetailAdapter;
import id.co.bri.brimo.adapters.DataTransaksiAdapter;
import id.co.bri.brimo.adapters.HeaderTransaksiAdapter;
import id.co.bri.brimo.adapters.TotalTransaksiAdapter;
import id.co.bri.brimo.adapters.WatermarkAdapter;
import id.co.bri.brimo.contract.IPresenter.general.IGeneralReceiptPresenter;
import id.co.bri.brimo.contract.IView.general.IGeneralReceiptView;
import id.co.bri.brimo.databinding.ActivityReceiptTravelBinding;
import id.co.bri.brimo.domain.config.Constant;
import id.co.bri.brimo.domain.helpers.GeneralHelper;
import id.co.bri.brimo.domain.helpers.calendar.CalendarHelper;
import id.co.bri.brimo.domain.helpers.image.ImageHelper;
import id.co.bri.brimo.models.apimodel.response.DataView;
import id.co.bri.brimo.models.apimodel.response.InquiryReceiptResponse;
import id.co.bri.brimo.models.apimodel.response.InquiryRevampReceiptResponse;
import id.co.bri.brimo.models.apimodel.response.ReceiptTravelResponse;
import id.co.bri.brimo.models.apimodel.response.lifestyle.PatternLifestyleTrackingResponse;
import id.co.bri.brimo.models.apimodel.response.lifestyle.UrlTrackingMobelanjaResponse;
import id.co.bri.brimo.models.apimodel.response.RestResponse;
import id.co.bri.brimo.models.apimodel.response.ValidateResponse;
import id.co.bri.brimo.models.apimodel.response.smartrecom.SmartRecomResponse;
import id.co.bri.brimo.models.apimodel.response.smarttransfer.SmartTransferAccountListConsentResponse;
import id.co.bri.brimo.models.apimodel.response.smarttransfer.SmartTransferConfirmAccBinding;
import id.co.bri.brimo.models.apimodel.response.smarttransfer.SmartTransferGeneralResponse;
import id.co.bri.brimo.models.apimodel.response.voucher.TutorialVoucherResponse;
import id.co.bri.brimo.ui.activities.base.BaseActivity;

public class ReceiptTravelActivity extends BaseActivity implements
        ActivityCompat.OnRequestPermissionsResultCallback,
        EasyFlipView.OnFlipAnimationListener,
        IGeneralReceiptView,
        View.OnClickListener {

    private ActivityReceiptTravelBinding binding;

    private boolean isNoLihatLebih = false;
    ImageHelper imageHelper;

    String footer = "";

    //untuk save instance state
    private static String TAG_PENDING = "pending_data";

    private boolean isShared = false;
    private boolean isReadytoShare = false;
    private Bitmap bm;

    private ArrayList<DataView> dataViews = new ArrayList<DataView>();

    private ReceiptTravelResponse receiptTravelResponse;

    private final List<Integer> watermarkList = new ArrayList<>();

    public static void launchIntent(Activity caller, ReceiptTravelResponse receiptTravelResponse) {
        Intent intent = new Intent(caller, ReceiptTravelActivity.class);
        intent.putExtra(TAG_PENDING, new Gson().toJson(receiptTravelResponse));
        caller.startActivityForResult(intent, Constant.REQ_PAYMENT);
        caller.setResult(RESULT_OK, setResultReceipt(receiptTravelResponse));
        caller.finish();
    }

    @Inject
    IGeneralReceiptPresenter<IGeneralReceiptView> receiptPresenter;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        binding = ActivityReceiptTravelBinding.inflate(getLayoutInflater());
        setContentView(binding.getRoot());

        imageHelper = new ImageHelper(this);

        //parsing data intent
        if (getIntent().getExtras() != null) {
            parseDataIntent(getIntent().getExtras());
        }

        //remove name toolbar
        GeneralHelper.setToolbar(this, binding.tbReceipt.toolbar, "");

        //show menu toolbar
//        toolbar.inflateMenu(R.menu.menu);
//        toolbar.setOnMenuItemClickListener(this);

        //inject presenter
        injectDependency();

        //
        parseDataReceipt();
        onSetLayoutHeight();
        onWatermark();

        binding.btnShare.setOnClickListener(this);
        binding.btnReceipt.setOnClickListener(this);
        binding.flipFooterReceipt.setOnFlipListener(this);
    }

    private void onSetLayoutHeight() {
        ViewTreeObserver viewTreeObserver = binding.layoutTicketView.getViewTreeObserver();
        viewTreeObserver.addOnGlobalLayoutListener(new ViewTreeObserver.OnGlobalLayoutListener() {
            @Override
            public void onGlobalLayout() {
                binding.layoutTicketView.getViewTreeObserver().removeGlobalOnLayoutListener(this);
                int height = binding.layoutTicketView.getMeasuredHeight();

                ViewGroup.LayoutParams params = binding.rvWatermark.getLayoutParams();
                params.height = height;
                binding.rvWatermark.setLayoutParams(params);

            }
        });
    }

    private void onWatermark() {
        for (int i = 0; i < 200; i++) {
            watermarkList.add(i);
        }

        binding.rvWatermark.setHasFixedSize(true);
        binding.rvWatermark.setLayoutManager(new GridLayoutManager(this, 4, RecyclerView.VERTICAL, false) {
            @Override
            public boolean canScrollVertically() {
                return false;
            }
        });
        WatermarkAdapter watermarkAdapter = new WatermarkAdapter(this, watermarkList);
        binding.rvWatermark.setAdapter(watermarkAdapter);
    }

    /**
     * Method digunakan untuk meng-extract data Intent
     *
     * @param extras Bundle savedInstanceState
     */
    protected void parseDataIntent(Bundle extras) {
        if (extras != null) {
            String pendingTemp = extras.getString(TAG_PENDING);
            if (pendingTemp != null) {
                receiptTravelResponse = new Gson().fromJson(pendingTemp, ReceiptTravelResponse.class);
            }

        }
    }

    /**
     * Get return Intent untuk Dashboard activity
     *
     * @return
     */
    public static Intent setResultReceipt(ReceiptTravelResponse response) {
        Intent intentReturn = new Intent();

        try {
            if (response.getTitleImage() != null) {
                if (response.getTitleImage().contains("00")) {
                    intentReturn.putExtra(Constant.REQUEST_RECEIPT, true);
                    return intentReturn;
                } else {
                    intentReturn.putExtra(Constant.REQUEST_RECEIPT, false);
                    return intentReturn;
                }
            } else {
                intentReturn.putExtra(Constant.REQUEST_RECEIPT, true);
                return intentReturn;
            }
        } catch (Exception e) {
            intentReturn.putExtra(Constant.REQUEST_RECEIPT, true);
            return intentReturn;
        }


    }

    protected void injectDependency() {
        getActivityComponent().inject(this);

        if (receiptPresenter != null) {
            receiptPresenter.setView(this);
            receiptPresenter.start();
        }
    }

    protected void parseDataReceipt() {
        if (receiptTravelResponse != null) {

            if (!receiptTravelResponse.getHeaderDataView().isEmpty()) {
                binding.rvDateReceipt.setHasFixedSize(true);
                binding.rvDateReceipt.setLayoutManager(new LinearLayoutManager(getApplicationContext(), RecyclerView.VERTICAL, false));
                DataTransaksiAdapter dataTransaksiAdapter = new DataTransaksiAdapter(receiptTravelResponse.getHeaderDataView(), ReceiptTravelActivity.this);
                binding.rvDateReceipt.setAdapter(dataTransaksiAdapter);
            } else binding.rvDateReceipt.setVisibility(View.GONE);

            //load default detail transaksi
            if (!receiptTravelResponse.getDataViewTransaction().isEmpty()) {
                binding.rvDetailReceipt.setHasFixedSize(true);
                binding.rvDetailReceipt.setLayoutManager(new LinearLayoutManager(getApplicationContext(), RecyclerView.VERTICAL, false));
                HeaderTransaksiAdapter headerTransaksiAdapter1 = new HeaderTransaksiAdapter(receiptTravelResponse.getDataViewTransaction(), ReceiptTravelActivity.this);
                binding.rvDetailReceipt.setAdapter(headerTransaksiAdapter1);
            } else binding.rvDetailReceipt.setVisibility(View.GONE);

            // Detail Bus
            if (receiptTravelResponse.getBus() != null) {
                binding.rvDetailBus.setHasFixedSize(true);
                binding.rvDetailBus.setLayoutManager(new LinearLayoutManager(this, RecyclerView.VERTICAL, false));
                BusInquiryDetailAdapter busInquiryDetailAdapter = new BusInquiryDetailAdapter(receiptTravelResponse.getBus());
                binding.rvDetailBus.setAdapter(busInquiryDetailAdapter);
            } else binding.rvDetailBus.setVisibility(View.GONE);

            // Detail Transaksi
            if (!receiptTravelResponse.getHeaderDataView().isEmpty()) {
                binding.rvNominalReceipt.setHasFixedSize(true);
                binding.rvNominalReceipt.setLayoutManager(new LinearLayoutManager(getApplicationContext(), RecyclerView.VERTICAL, false));
                HeaderTransaksiAdapter headerTransaksiAdapter3 = new HeaderTransaksiAdapter(receiptTravelResponse.getAmountDataView(), ReceiptTravelActivity.this);
                binding.rvNominalReceipt.setAdapter(headerTransaksiAdapter3);
            } else binding.rvNominalReceipt.setVisibility(View.GONE);

            // Total Transaksi
            if (!receiptTravelResponse.getTotalDataView().isEmpty()) {
                binding.rvTotalReceipt.setHasFixedSize(true);
                binding.rvTotalReceipt.setLayoutManager(new LinearLayoutManager(getApplicationContext(), RecyclerView.VERTICAL, false));
                TotalTransaksiAdapter totalTransaksiAdapter = new TotalTransaksiAdapter(receiptTravelResponse.getTotalDataView(), ReceiptTravelActivity.this);
                binding.rvTotalReceipt.setAdapter(totalTransaksiAdapter);
            } else binding.rvTotalReceipt.setVisibility(View.GONE);


            footer = receiptTravelResponse.getFooter();

            if (footer != null) {
                if (footer.equals("")) {
                    footer = receiptTravelResponse.getFooterHtml();
                    if (footer != null) {
                        binding.wvFooter.setVisibility(View.VISIBLE);
                        binding.wvFooter.setBackgroundColor(Color.TRANSPARENT);
                        GeneralHelper.setWebViewReceipt(binding.wvFooter, "", footer);
//                        footerPulsa.setText(Html.fromHtml(footer));
//                        footerPulsa.setVisibility(View.VISIBLE);
                    } else
                        binding.tvFootNote.setVisibility(View.GONE);
                } else {
                    binding.tvFootNote.setVisibility(View.VISIBLE);
//                    footerPulsa.setTextAlignment(View.TEXT_ALIGNMENT_CENTER);
                    binding.tvFootNote.setText(footer);
                }
            } else {
                binding.tvFootNote.setVisibility(View.GONE);
            }

            if (receiptTravelResponse.getTitle() != null) {
                binding.tvTitle.setText(receiptTravelResponse.getTitle());
            }

            if (receiptTravelResponse.getTitleImage() != null) {
                int imageId = this.getResources().getIdentifier(receiptTravelResponse.getTitleImage(), "drawable", this.getPackageName());
                binding.imgCeklist.setImageResource(imageId);
            }

        }

    }

    @Override
    public void onClick(View view) {
        int id = view.getId();
        switch (id) {
            case R.id.btn_receipt:
                this.finish();
                overridePendingTransition(R.anim.nothing, R.anim.bottom_down);
                break;
            case R.id.btn_share:
                binding.flipLogoReceipt.flipTheView();
                binding.flipFooterReceipt.flipTheView();
                onSetLayoutHeight();
                break;
            default:
                break;
        }
    }

    @Override
    public void onViewFlipCompleted(EasyFlipView easyFlipView, EasyFlipView.FlipState newCurrentSide) {
        if (easyFlipView.getId() == R.id.flipFooterReceipt) {
            if (newCurrentSide.equals(EasyFlipView.FlipState.BACK_SIDE)) {
                try {
                    isReadytoShare = true;
                    if (isReadytoShare) {
                        new Handler().postDelayed(new Runnable() {
                            public void run() {
                                requestPermission();
                            }
                        }, 500);
                    }
                } catch (Exception e) {
//                    Log.e(TAG, "onMenuItemClick: ", e);
                }
            }
        }
    }

    /**
     * Generate Share INtent to Another APPS
     *
     * @param file file image receipt
     */
    private void shareImage(File file) {
        Uri uri = FileProvider.getUriForFile(this,
                BuildConfig.APPLICATION_ID + ".fileprovider",
                file);
        Intent intent = new Intent();
        intent.setAction(Intent.ACTION_SEND);
        intent.setType("image/*");

        intent.putExtra(android.content.Intent.EXTRA_SUBJECT, "");
        intent.putExtra(android.content.Intent.EXTRA_TEXT, "");
        intent.putExtra(Intent.EXTRA_STREAM, uri);
        intent.addFlags(Intent.FLAG_GRANT_READ_URI_PERMISSION);

        Intent chooser = Intent.createChooser(intent, "Download and share receipt");

        List<ResolveInfo> resInfoList = this.getPackageManager().queryIntentActivities(chooser, PackageManager.MATCH_DEFAULT_ONLY);

        for (ResolveInfo resolveInfo : resInfoList) {
            String packageName = resolveInfo.activityInfo.packageName;
            this.grantUriPermission(packageName, uri, Intent.FLAG_GRANT_WRITE_URI_PERMISSION | Intent.FLAG_GRANT_READ_URI_PERMISSION);
        }

        try {
            startActivity(chooser);
        } catch (ActivityNotFoundException e) {
            Toast.makeText(this, "Aplikasi tidak tersedia", Toast.LENGTH_SHORT).show();
        }
    }

    protected void requestPermission() {

        if (!hasPermissions(this, PERMISSIONS)) {
            ActivityCompat.requestPermissions(this, PERMISSIONS, PERMISSIONS_ALL);
        } else {
            shareImage(generateImage());
            isShared = true;
            isReadytoShare = false;
        }

    }

    /**
     * Generate File Image
     *
     * @return
     */
    private File generateImage() {
        File file = null;
        if (binding.receiptShare != null)
            bm = imageHelper.getBitmapFromView(binding.receiptShare, binding.receiptShare.getChildAt(0).getHeight(), binding.receiptShare.getChildAt(0).getWidth());
        else
            bm = imageHelper.getBitmapFromView(binding.layoutReceipt, binding.layoutReceipt.getChildAt(0).getHeight(), binding.layoutReceipt.getChildAt(0).getWidth());

        file = saveBitmap(bm, generateNameReceipt());
        return file;
    }

    /**
     * Method untuk digunakan untuk generate Nama File BRImo
     *
     * @return tag name FIle Receipt
     */
    protected String generateNameReceipt() {
        String tag = "";
        String dateTime = CalendarHelper.getCurrentTimeReceipt();
        try {
            tag = Constant.TAG_START_NAME + dateTime + Constant.TAG_END_NAME;
        } catch (Exception e) {
            if (!GeneralHelper.isProd())
//                Log.e(TAG, "generateNameReceipt: ", e);

                tag = Constant.TAG_START_NAME + Constant.TAG_END_NAME;
        }

        return tag;
    }

    private static File saveBitmap(Bitmap bm, String fileName) {
        final String path = Environment.getExternalStorageDirectory().getAbsolutePath() + Constant.URI_DOWNLOAD;
        File dir = new File(path);
        if (!dir.exists())
            dir.mkdirs();
        File file = new File(dir, fileName);

        try {
            FileOutputStream fOut = new FileOutputStream(file);
            bm.compress(Bitmap.CompressFormat.PNG, 90, fOut);
            fOut.flush();
            fOut.close();
        } catch (Exception e) {
        }

        return file;
    }

    @Override
    public void onRequestPermissionsResult(int requestCode, @NonNull String[] permissions, @NonNull int[] grantResults) {
        super.onRequestPermissionsResult(requestCode, permissions, grantResults);
        boolean grantAll = true;
        if (grantResults.length > 0) {
            for (int i = 0; i < grantResults.length; i++) {
                if (grantResults[i] != PackageManager.PERMISSION_GRANTED) {
                    grantAll = false;
                    break;
                }
            }
        }

        if (!grantAll) {
            showAlertFinish(getString(R.string.notes_need_permission_resi));
        } else {
            requestPermission();
        }
    }

    @Override
    public void onResume() {
        super.onResume();
        if (isShared) {
            parseDataReceipt();
            binding.flipLogoReceipt.flipTheView();
            binding.flipFooterReceipt.flipTheView();

            if (!isNoLihatLebih)
//                detailFlip.flipTheView();

                isShared = false;
        }
    }

    @Override
    public void onSuccessGetTutorial(TutorialVoucherResponse tutorialVoucherResponse) {
        //do nothing
    }

    @Override
    public void onSuccessGetUrlTrackingMobelanja(UrlTrackingMobelanjaResponse urlTrackingMobelanjaResponse) {
        //do nothing
    }

    @Override
    protected void onDestroy() {
        super.onDestroy();
        binding = null;
    }

    @Override
    public void onSuccessGetSmartRecom(SmartRecomResponse smartRecomResponse) {
        //do nothing
    }

    @Override
    public void onFailedGetSmartRecom() {
        //do nothing
    }

    @Override
    public void onSuccessGetInquiry(InquiryReceiptResponse inquiryReceiptResponse) {
        //do nothing
    }

    @Override
    public void onSuccessGetInquiryRevamp(InquiryRevampReceiptResponse inquiryRevampReceiptResponse) {
        //do nothing
    }

    @Override
    public void onShowBottomSheetCcAsSof() {
        //do nothing
    }

    @Override
    public void onSuccessChangeSof(String desc) {
        //do nothing
    }

    @Override
    public void onSuccessCheckSimilaritySmartTransfer(SmartTransferGeneralResponse checkSimilarityResponse) {
        //do nothing
    }

    @Override
    public void onSucessGetAccountListConsent(SmartTransferAccountListConsentResponse smartTransferAccountListConsentResponse) {
        //do nothing
    }

    @Override
    public void onSuccessSmartTransferManageUserConsent(SmartTransferConfirmAccBinding smartTransferConfirmAccBinding) {
        //do nothing
    }

    @Override
    public void onSuccessGetTrackingPattern(PatternLifestyleTrackingResponse patternLifestyleTrackingResponse) {
        //do nothing
    }

    @Override
    public void onExceptionEticketNotIssued() {
        //do nothing
    }
}