package id.co.bri.brimo.models.apimodel.request;

import com.google.gson.annotations.Expose;
import com.google.gson.annotations.SerializedName;

public class FastConfirmationRequest extends FastMenuRequest {

    @SerializedName("reference_number")
    @Expose
    private String referenceNumber;
    @SerializedName("account_number")
    @Expose
    private String accountNumber;
    @SerializedName("amount")
    @Expose
    private String amount;
    @SerializedName("save_as")
    @Expose
    private String saveAs;

    public FastConfirmationRequest(String username, String tokenKey) {
        super(username, tokenKey);
    }

    public FastConfirmationRequest(FastMenuRequest request, String refNum, String accountNum, String amount, String save) {
        super(request.getUsername(), request.getTokenKey());
        this.referenceNumber = refNum;
        this.accountNumber = accountNum;
        this.amount = amount;
        this.saveAs = save;
    }

    public String getReferenceNumber() {
        return referenceNumber;
    }

    public void setReferenceNumber(String referenceNumber) {
        this.referenceNumber = referenceNumber;
    }

    public String getAccountNumber() {
        return accountNumber;
    }

    public void setAccountNumber(String accountNumber) {
        this.accountNumber = accountNumber;
    }

    public String getAmount() {
        return amount;
    }

    public void setAmount(String amount) {
        this.amount = amount;
    }

    public String getSaveAs() {
        return saveAs;
    }

    public void setSaveAs(String saveAs) {
        this.saveAs = saveAs;
    }
}
