package id.co.bri.brimo.data.api.observer;

import android.util.Log;

import androidx.databinding.Observable;

import java.net.SocketTimeoutException;

import id.co.bri.brimo.R;
import id.co.bri.brimo.contract.IView.IMvpView;
import id.co.bri.brimo.domain.config.Constant;
import id.co.bri.brimo.domain.converter.MapperHelper;
import id.co.bri.brimo.domain.helpers.GeneralHelper;
import id.co.bri.brimo.models.apimodel.response.ExceptionResponse;
import id.co.bri.brimo.models.apimodel.response.RestResponse;
import io.reactivex.observers.DisposableObserver;
import retrofit2.HttpException;

public abstract class ApiObserverVoiceAssistant extends DisposableObserver implements Observable {

    private static final String TAG = "ApiObserver";
    protected RestResponse restResponse;
    protected String stringResponse;
    protected String responseId;
    protected String sequenceNumber;
    protected IMvpView view;


    public ApiObserverVoiceAssistant(IMvpView view, String seq) {
        this.view = view;
        this.sequenceNumber = seq;
    }

    @Override
    public void onNext(Object o) {

        if (!(o instanceof String)) {
            onApiCallError(new RestResponse("12", "Unidentified Object"));
            return;
        }

        try {
            stringResponse = String.valueOf(o);

            //get ID Integrity
            responseId = MapperHelper.getIdResponse(stringResponse);

            if (responseId.isEmpty()) {
                if (!GeneralHelper.isProd()) {
                    Log.e(TAG, "onNext: ID response not found");
                }
                onFailureHttp("onNext: ID response not found");
                return;
            }

            //Convert String to RestResponse model
            restResponse = MapperHelper.stringToRestResponse(stringResponse, sequenceNumber);
            if (restResponse == null) {
                onError(new Throwable("response not valid"));
                return;
            }

            if (GeneralHelper.isContains(R.array.response_code_success_voice_assistant, restResponse.getCode())) {
                onApiCallSuccess(restResponse);
                return;
            }

            if (!GeneralHelper.isContains(R.array.response_code_error_specific_voice_assistant, restResponse.getCode())) {
                onApiCallError(restResponse.getRestResponse());
                return;
            }

            view.hideProgress();
            switch (restResponse.getCode()) {
                case Constant.RE_SESSION_END:
                    view.onSessionEnd(restResponse.getDesc());
                    break;
                case Constant.RE_HIT_EXCEED:
                    view.onException06(restResponse.getData(ExceptionResponse.class));
                    break;
            }
        } catch (Exception e) {
            if (!GeneralHelper.isProd()) {
                Log.e(TAG, "onNext: ", e);
            }
            onError(e.getCause());
        }

    }

    @Override
    public void onError(Throwable e) {
        if (e instanceof SocketTimeoutException) {
            onFailureHttp(Constant.KONEKSI_TERPUTUS);
        } else if (e instanceof HttpException) {
            HttpException httpException = (HttpException) e;
            onFailureHttp(Constant.SERVER_UNDER_MAINTENANCE);
        } else {
            onFailureHttp(Constant.KONEKSI_TERPUTUS);
        }
    }

    protected abstract void onFailureHttp(String type);

    protected abstract void onApiCallSuccess(RestResponse response);

    protected abstract void onApiCallError(RestResponse restResponse);

    @Override
    public void addOnPropertyChangedCallback(OnPropertyChangedCallback callback) {

    }

    @Override
    public void removeOnPropertyChangedCallback(OnPropertyChangedCallback callback) {

    }

    @Override
    public void onComplete() {

    }

}