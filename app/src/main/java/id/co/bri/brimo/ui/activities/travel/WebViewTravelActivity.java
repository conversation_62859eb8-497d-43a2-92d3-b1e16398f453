package id.co.bri.brimo.ui.activities.travel;

import android.annotation.SuppressLint;
import android.app.Activity;
import android.content.Intent;
import android.graphics.Bitmap;
import android.net.Uri;
import android.net.http.SslError;
import android.os.Bundle;
import android.view.KeyEvent;
import android.view.View;
import android.webkit.CookieManager;
import android.webkit.SslErrorHandler;
import android.webkit.WebSettings;
import android.webkit.WebView;
import android.webkit.WebViewClient;

import androidx.annotation.Nullable;
import androidx.fragment.app.FragmentTransaction;

import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.Locale;
import java.util.Objects;
import java.util.TimeZone;

import javax.inject.Inject;

import id.co.bri.brimo.R;
import id.co.bri.brimo.contract.IPresenter.travel.IWebViewTravelPresenter;
import id.co.bri.brimo.contract.IView.travel.IWebViewTravelView;
import id.co.bri.brimo.databinding.ActivityWebViewTravelBinding;
import id.co.bri.brimo.domain.config.Constant;
import id.co.bri.brimo.domain.config.LifestyleConfig;
import id.co.bri.brimo.domain.helpers.GeneralHelper;
import id.co.bri.brimo.models.ParameterModel;
import id.co.bri.brimo.models.apimodel.response.InquiryBrivaRevampResponse;
import id.co.bri.brimo.models.apimodel.response.InquiryTrainResponse;
import id.co.bri.brimo.ui.activities.base.BaseActivity;
import id.co.bri.brimo.ui.customviews.dialog.DialogExitCustom;
import id.co.bri.brimo.ui.fragments.FragmentBottomDialog;

public class WebViewTravelActivity extends BaseActivity implements
        DialogExitCustom.DialogDefaultListener,
        IWebViewTravelView,
        DialogExitCustom.DialogClickYesNoListener,
        FragmentBottomDialog.onClickItem {

    ActivityWebViewTravelBinding binding;

    private String accNumber = "";

    private String brivaNumber = "";

    private String corpCode = "";

    private String expiredDate = "";

    private String departureDate = "";

    private String qtyTicket = "";

    private String invoiceNumber = "";

    private static String urlBinding;

    private static String mSessionId;

    private static String mType = "";

    private static String mTitle = "";

    private boolean paymentKcic = false;

    private String bookingId = "";

    private boolean isRevokeSession = false;

    private static final String TAG_URL = "url";
    private static final String TAG_SESSION = "sessionId";
    private static final String TAG_TYPE = "type";
    private static final String TAG_TITLE = "title";

    @Inject
    IWebViewTravelPresenter<IWebViewTravelView> webViewTravelPresenter;

    public static void launchIntent(Activity caller, String url, String sessionId, String type,
                                    String title) {
        Intent intent = new Intent(caller, WebViewTravelActivity.class);
        intent.putExtra(TAG_URL, url);
        intent.putExtra(TAG_SESSION, sessionId);
        intent.putExtra(TAG_TYPE, type);
        intent.putExtra(TAG_TITLE, title);
        caller.startActivityForResult(intent, Constant.REQ_PAYMENT);
    }

    @SuppressLint("SetJavaScriptEnabled")
    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        binding = ActivityWebViewTravelBinding.inflate(getLayoutInflater());

        if (getIntent().getExtras() != null) {
            handleIntent(getIntent().getExtras());
        }

        // Enable cookies. This must occur before setContentView() instantiates your WebView.
        CookieManager cookieManager = CookieManager.getInstance();
        binding.wvBinding.clearCache(true);
        cookieManager.removeAllCookies(null);
        cookieManager.acceptCookie();
        cookieManager.setAcceptCookie(true);
        cookieManager.setAcceptThirdPartyCookies(binding.wvBinding, true);

        // Set cookie domain.
        String domain = urlBinding;
        // Set cookie path.
        String path = "/";
        // Set cookie name and value.
        String name = "x-tvlk-brimo-access";
        String value = "LThnkQ39cJEzxx8BaTYpgXQckzTjM9xS";
        // Set cookie expiration date.
        long expirationDateMillis = System.currentTimeMillis() + 31556926L * 1000; // 1 year from now.

        // Format expiration date in the required format.
        SimpleDateFormat sdf = new SimpleDateFormat(
                "EEE, dd MMM yyyy HH:mm:ss 'GMT'",
                Locale.getDefault()
        );
        sdf.setTimeZone(TimeZone.getTimeZone("GMT"));
        String expires = sdf.format(new Date(expirationDateMillis));

        // Construct the cookie string without the 'secure' directive.
        String cookieString = name + "=" + value + "; path=" + path + "; expires=" + expires;

        // Set the cookie.
        cookieManager.setCookie(domain, cookieString);

        // Flush cookies to disk.
        cookieManager.flush();

        setContentView(binding.getRoot());

        injectDependency();

        setToolbar();

        // Enable JavaScript.
        WebSettings webSetting = binding.wvBinding.getSettings();
        webSetting.setJavaScriptEnabled(true);
        webSetting.setJavaScriptCanOpenWindowsAutomatically(true);

        // Set the request's mode to 'no-cors' to disable CORS (Cross-Origin Resource Sharing).
        webSetting.setAllowUniversalAccessFromFileURLs(true);
        webSetting.setAllowFileAccessFromFileURLs(true);
        webSetting.setAllowFileAccess(true);
        webSetting.setAllowContentAccess(true);

        webSetting.setDomStorageEnabled(true);
        webSetting.setUseWideViewPort(true);
        webSetting.setLoadWithOverviewMode(true);

        binding.wvBinding.loadUrl(domain);
        binding.wvBinding.setWebViewClient(new WebViewClient() {
            @Override
            public boolean shouldOverrideUrlLoading(WebView view, String url) {
                if (mType.equalsIgnoreCase(
                        LifestyleConfig.MenuLifestyleCode.MENU_PESAWAT.getMenuCode())) {
                    if (url.contains(GeneralHelper.getString(R.string.flag_timeout))) {
                        binding.wvBinding.stopLoading();
                        onExceptionTimeout();
                        return true;
                    }
                }
                return false;
            }

            @Override
            public void onPageStarted(WebView view, String url, Bitmap favicon) {
                super.onPageStarted(view, url, favicon);
                if (!WebViewTravelActivity.this.isFinishing()) {
                    binding.progressBar1.setVisibility(View.VISIBLE);
                    cookieManager.flush();
                }
            }

            @Override
            public void onPageFinished(WebView view, String url) {
                super.onPageFinished(view, url);
                if (!WebViewTravelActivity.this.isFinishing()) {
                    binding.progressBar1.setVisibility(View.GONE);
                    cookieManager.flush();

                    if (url != null && !url.isEmpty()) {
                        Uri uri = Uri.parse(url);
                        if (mType.equals(
                                LifestyleConfig.MenuLifestyleCode.MENU_KERETA_CEPAT.getMenuCode())) {
                            if (url.contains(GeneralHelper.getString(R.string.flag_url_brimo)) ||
                                    url.contains(GeneralHelper.getString(R.string.flag_briva_number)) ||
                                    url.contains(GeneralHelper.getString(R.string.flag_corp_code)) ||
                                    url.contains(GeneralHelper.getString(R.string.flag_date)) ||
                                    url.contains(GeneralHelper.getString(R.string.flag_departure_date)) ||
                                    url.contains(GeneralHelper.getString(R.string.flag_qty)) ||
                                    url.contains(GeneralHelper.getString(R.string.flag_invoice_number))) {
                                brivaNumber = uri.getQueryParameter(GeneralHelper.getString(R.string.flag_briva_number));
                                corpCode = uri.getQueryParameter(GeneralHelper.getString(R.string.flag_corp_code));
                                expiredDate = uri.getQueryParameter(GeneralHelper.getString(R.string.flag_date));
                                qtyTicket = uri.getQueryParameter(GeneralHelper.getString(R.string.flag_qty));
                                departureDate = uri.getQueryParameter(GeneralHelper.getString(R.string.flag_departure_date));
                                invoiceNumber = uri.getQueryParameter(GeneralHelper.getString(R.string.flag_invoice_number));

                                if (brivaNumber != null && corpCode != null && expiredDate != null &&
                                        qtyTicket != null && departureDate != null &&
                                        invoiceNumber != null) {
                                    binding.wvBinding.setVisibility(View.GONE);
                                    webViewTravelPresenter.getRevokeSession(mSessionId, true);
                                }

                                return;

                            }

                            if (url.contains(getString(R.string.midware)) ||
                                    url.contains(getString(R.string.payment_travel))) {
                                paymentKcic = true;
                            }

                        } else if (mType.equalsIgnoreCase(
                                LifestyleConfig.MenuLifestyleCode.MENU_PESAWAT.getMenuCode())) {
                            if (url.contains(GeneralHelper.getString(R.string.flag_url_brimo)) &&
                                    url.contains(GeneralHelper.getString(R.string.flag_booking_id))) {
                                bookingId = uri.getQueryParameter(GeneralHelper.getString(R.string.flag_booking_id));

                                if (bookingId != null && !bookingId.equals("null") && !isRevokeSession) {
                                    binding.wvBinding.setVisibility(View.GONE);
                                    webViewTravelPresenter.getRevokeSession(mSessionId, true);
                                    isRevokeSession = true;
                                }
                            }

                        } else if (mType.equalsIgnoreCase(Constant.TravelMenu.TRAVEL_KAI)) {
                            if (url.contains(GeneralHelper.getString(R.string.flag_acc_number))) {
                                accNumber = uri.getQueryParameter(GeneralHelper.getString(R.string.flag_acc_number));
                                if (accNumber != null) {
                                    binding.wvBinding.setVisibility(View.GONE);
                                    webViewTravelPresenter.getRevokeSession(mSessionId, true);
                                }
                            }
                        }
                    }
                }
            }

            @SuppressLint("WebViewClientOnReceivedSslError")
            @Override
            public void onReceivedSslError(WebView view, SslErrorHandler handler, SslError error) {
                if (!GeneralHelper.isProd()) handler.proceed();
            }

        });

    }

    private void handleIntent(Bundle extras) {
        if (extras != null) {
            urlBinding = extras.getString(TAG_URL);
            mSessionId = extras.getString(TAG_SESSION);
            mType = extras.getString(TAG_TYPE);
            mTitle = extras.getString(TAG_TITLE);
        }
    }

    private void injectDependency() {
        getActivityComponent().inject(this);
        if (webViewTravelPresenter != null) {
            webViewTravelPresenter.setView(this);
            webViewTravelPresenter.setUrlInquiryKai(GeneralHelper.getString(R.string.url_travel_kai_inquiry));
            webViewTravelPresenter.setUrlRevokeSession(GeneralHelper.getString(R.string.url_brimo_id_revoke_session));
            webViewTravelPresenter.setUrlConfirmationKcic(GeneralHelper.getString(R.string.url_confirmation_kcic));
            webViewTravelPresenter.setUrlPaymentKcic(GeneralHelper.getString(R.string.url_payment_kcic));
            webViewTravelPresenter.setUrlConfirmationFlight(GeneralHelper.getString(R.string.url_confirmation_flight));
            webViewTravelPresenter.setUrlPaymentFlight(GeneralHelper.getString(R.string.url_payment_flight));
            webViewTravelPresenter.start();
        }
    }

    private void setToolbar() {
        if (Objects.equals(mType, Constant.TravelMenu.TRAVEL_KAI)) {
            binding.toolbar.toolbarBackClose.setBackgroundColor(getColor(R.color.toolbar_blue));
        }
        GeneralHelper.setToolbarBackClose(
                this,
                binding.toolbar.toolbarBackClose,
                mTitle,
                false,
                true
        );

        if (mType.equalsIgnoreCase(
                LifestyleConfig.MenuLifestyleCode.MENU_KERETA_CEPAT.getMenuCode())) {
            binding.rlPowered.setVisibility(View.VISIBLE);
        } else {
            binding.rlPowered.setVisibility(View.GONE);
        }
    }

    @Override
    public boolean onKeyDown(int keyCode, KeyEvent event) {
        if (event.getAction() == KeyEvent.ACTION_DOWN && keyCode == KeyEvent.KEYCODE_BACK) {
            if (!paymentKcic && binding.wvBinding.canGoBack()) {
                binding.wvBinding.goBack();
            } else {
                showExitDialog();
            }
            return true;
        }
        return super.onKeyDown(keyCode, event);
    }

    private void showExitDialog() {
        DialogExitCustom dialogExitCustom;
        if (mType.equalsIgnoreCase(
                LifestyleConfig.MenuLifestyleCode.MENU_KERETA_CEPAT.getMenuCode()) ||
                mType.equalsIgnoreCase(
                        LifestyleConfig.MenuLifestyleCode.MENU_PESAWAT.getMenuCode())) {
            dialogExitCustom = new DialogExitCustom(
                    this,
                    GeneralHelper.getString(R.string.title_popup_exit_webview),
                    GeneralHelper.getString(R.string.desc_popup_konfirm_travel),
                    GeneralHelper.getString(R.string.keluar),
                    GeneralHelper.getString(R.string.txt_lanjut_pesan),
                    true);
        } else {
            dialogExitCustom = new DialogExitCustom(
                    this,
                    GeneralHelper.getString(R.string.str_konfirmasi),
                    GeneralHelper.getString(R.string.str_konfirmasi_web_view));
        }
        FragmentTransaction ft = this.getSupportFragmentManager().beginTransaction();
        ft.add(dialogExitCustom, null);
        ft.commitAllowingStateLoss();
    }

    @Deprecated
    @Override
    public void onBackPressed() {
        showExitDialog();
    }

    @Override
    public void onClickYes() {
        webViewTravelPresenter.getRevokeSession(mSessionId, false);
    }

    @Override
    public void hideProgress() {
        if (!WebViewTravelActivity.this.isFinishing()) {
            GeneralHelper.dismissDialog();
        }
    }

    @Override
    public void onSuccessInquiry(InquiryTrainResponse inquiryTrainResponse) {
        TrainInquiryActivity.launchIntentFinish(this, inquiryTrainResponse);
    }

    @Override
    public void onSuccessRevokeSession(boolean isInquiry) {
        if (isInquiry) {
            if (mType.equalsIgnoreCase(
                    LifestyleConfig.MenuLifestyleCode.MENU_KERETA_CEPAT.getMenuCode())) {
                webViewTravelPresenter.getConfirmationTravelKcic(brivaNumber, corpCode, qtyTicket,
                        expiredDate, departureDate, invoiceNumber);
            } else if (mType.equalsIgnoreCase(
                    LifestyleConfig.MenuLifestyleCode.MENU_PESAWAT.getMenuCode())) {
                webViewTravelPresenter.getConfirmationTravelFlight(bookingId);
            } else if (mType.equalsIgnoreCase(Constant.TravelMenu.TRAVEL_KAI)) {
                webViewTravelPresenter.getInquiryTravelKai(accNumber);
            }
        } else {
            Intent returnIntent = new Intent();

            this.setResult(RESULT_CANCELED, returnIntent);
            this.finish();
        }
    }

    @Override
    public void onException12(String message) {
        Intent returnIntent = new Intent();

        returnIntent.putExtra(Constant.TAG_ERROR_MESSAGE, message);

        this.setResult(RESULT_CANCELED, returnIntent);
        this.finish();
    }

    @Override
    public void onException94(String message) {
        Intent returnIntent = new Intent();

        returnIntent.putExtra(Constant.TAG_ERROR_MESSAGE, message);

        this.setResult(RESULT_CANCELED, returnIntent);
        this.finish();
    }

    @Override
    public void onException(String message) {
        if (mType.equalsIgnoreCase(Constant.TravelMenu.TRAVEL_KCIC)) {
            if (GeneralHelper.isContains(Constant.LIST_TYPE_GAGAL, message))
                GeneralHelper.showBottomDialog(this, message);
            else
                showSnackbarErrorMessageRevamp(message, ALERT_ERROR, this, false);
        } else {
            if (GeneralHelper.isContains(Constant.LIST_TYPE_GAGAL, message))
                GeneralHelper.showDialogGagalBack(this, message);
            else
                showSnackbarErrorMessageRevamp(message, ALERT_ERROR, this, false);
        }
    }

    @Override
    public void onSuccessConfirmationkcic(InquiryBrivaRevampResponse inquiryBrivaRevampResponse,
                                          String urlPayment) {
        WebviewKonfirmasiActivity.launchIntent(this,
                inquiryBrivaRevampResponse, urlPayment, false,
                setParameter(), Constant.TravelMenu.TRAVEL_KCIC);
    }

    @Override
    public void onSuccessConfirmationFlight(InquiryBrivaRevampResponse inquiryBrivaRevampResponse,
                                            String urlPayment) {
        WebviewKonfirmasiActivity.launchIntent(this,
                inquiryBrivaRevampResponse, urlPayment, false,
                setParameter(), Constant.TravelMenu.TRAVEL_PESAWAT);
    }

    public ParameterModel setParameter() {
        ParameterModel parameterModel = new ParameterModel();

        parameterModel.setStringLabelTujuan(GeneralHelper.getString(R.string.nomor_tujuan));
        parameterModel.setStringLabelNominal(GeneralHelper.getString(R.string.nominal_pembayaran));
        parameterModel.setStringButtonSubmit(GeneralHelper.getString(R.string.txt_konfirmasi));
        parameterModel.setStringLabelMinimum("");
        return parameterModel;
    }

    @Override
    protected void onActivityResult(int requestCode, int resultCode, @Nullable Intent data) {
        super.onActivityResult(requestCode, resultCode, data);
        if (requestCode == Constant.REQ_PAYMENT) {
            if (resultCode == RESULT_OK) {
                this.setResult(RESULT_OK, data);
            } else {
                this.setResult(RESULT_CANCELED, data);
                if (data != null) {
                    if (data.getStringExtra(Constant.TAG_ERROR_MESSAGE) != null)
                        showSnackbarErrorMessageRevamp(
                                data.getStringExtra(Constant.TAG_ERROR_MESSAGE),
                                ALERT_ERROR,
                                this,
                                false
                        );
                }
            }
            this.finish();
        }
    }

    @Override
    public void onClickBtnYes() {
        // do nothing
    }

    @Override
    public void onClickBtnNo() {
        webViewTravelPresenter.getRevokeSession(mSessionId, false);
    }

    @Override
    public void clickItem() {
        if (mType.equalsIgnoreCase(Constant.TravelMenu.TRAVEL_KCIC)) {
            binding.wvBinding.goBack();
        }
    }

    private void onExceptionTimeout() {
        showSnackbarErrorMessageRevamp(
                GeneralHelper.getString(R.string.txt_timeout_pesawat),
                ALERT_ERROR, this, false);
    }


    @Override
    protected void onDestroy() {
        super.onDestroy();
        binding = null;
    }

    @Override
    protected void onPause() {
        super.onPause();
        binding.wvBinding.onPause();
    }

    @Override
    protected void onResume() {
        super.onResume();
        binding.wvBinding.onResume();
    }
}