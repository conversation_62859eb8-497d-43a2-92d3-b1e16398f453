package id.co.bri.brimo.models.apimodel.request;

import com.google.gson.annotations.Expose;
import com.google.gson.annotations.SerializedName;

public class ListNotificationRequest {
    @SerializedName("limit")
    @Expose
    private String limit;
    @SerializedName("last_id")
    @Expose
    private String lastId;
    @SerializedName("device")
    @Expose
    private String device;
    @SerializedName("filter")
    @Expose
    private String filter;

    public ListNotificationRequest(String limit, String lastId, String device, String filter) {
        this.limit = limit;
        this.lastId = lastId;
        this.device = device;
        this.filter = filter;
    }

    public String getLimit() {
        return limit;
    }

    public void setLimit(String limit) {
        this.limit = limit;
    }

    public String getLastId() {
        return lastId;
    }

    public void setLastId(String lastId) {
        this.lastId = lastId;
    }

    public String getDevice() {
        return device;
    }

    public void setDevice(String device) {
        this.device = device;
    }

    public String getFilter() {
        return filter;
    }

    public void setFilter(String filter) {
        this.filter = filter;
    }
}
