package id.co.bri.brimo.ui.activities.dplk;

import android.app.Activity;
import android.content.Intent;
import android.os.Bundle;
import android.view.View;
import androidx.annotation.Nullable;
import androidx.fragment.app.Fragment;
import androidx.viewpager.widget.ViewPager;
import com.google.gson.Gson;
import id.co.bri.brimo.R;
import id.co.bri.brimo.adapters.baseadapter.ViewPagerAdapter;
import id.co.bri.brimo.databinding.ActivityOnboardingDplkBinding;
import id.co.bri.brimo.domain.config.Constant;
import id.co.bri.brimo.domain.helpers.GeneralHelper;
import id.co.bri.brimo.models.apimodel.response.dplk.DplkBoardingResponse;
import id.co.bri.brimo.ui.activities.base.BaseActivity;
import id.co.bri.brimo.ui.fragments.rdn.RdnOnBoardingFragment;

public class OnboardingDplkActivity extends BaseActivity implements View.OnClickListener, ViewPager.OnPageChangeListener {

    private ActivityOnboardingDplkBinding binding;

    private static final int AUTO_SCROLL_THRESHOLD_IN_MILLI = 3500;
    private ViewPagerAdapter adapterRdn2;

    private static final String TAG_ONBOARDING = "onboarding";

    private static final String TAG_FRAGMENT = "fragment";

    private static final String TAG_IMAGE = "image";

    private DplkBoardingResponse response;

    public static void launchIntent(Activity caller, DplkBoardingResponse response){
        Intent i = new Intent(caller, OnboardingDplkActivity.class);
        i.putExtra(TAG_ONBOARDING,new Gson().toJson(response));
        caller.startActivityForResult(i, Constant.REQ_PAYMENT);
    }

    public static void launchIntent(Activity caller){
        Intent i = new Intent(caller, OnboardingDplkActivity.class);
        caller.startActivityForResult(i, Constant.REQ_PAYMENT);
    }

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        binding = ActivityOnboardingDplkBinding.inflate(getLayoutInflater());
        setContentView(binding.getRoot());

        setupView();

        binding.btnLanjut.setOnClickListener(this);
    }

    private void setupView() {
        GeneralHelper.setToolbar(this, binding.tbOnboardDplk.toolbar, "BRIFINE");
        binding.btnLanjut.setText(GeneralHelper.getString(R.string.dplk_daftar));

        if (getIntent().hasExtra(TAG_ONBOARDING) && getIntent().getStringExtra(TAG_ONBOARDING) !=null){
            response = new Gson().fromJson(getIntent().getStringExtra(TAG_ONBOARDING),DplkBoardingResponse.class);

            adapterRdn2 = new ViewPagerAdapter(getSupportFragmentManager());
            adapterRdn2.addFragment(addFragment(response.getOnboarding().get(0),"dplk_onboarding1"), "");
            adapterRdn2.addFragment(addFragment(response.getOnboarding().get(1),"dplk_onboarding2"), "");
            adapterRdn2.addFragment(addFragment(response.getOnboarding().get(2),"dplk_onboarding3"), "");

            binding.vpRdn.setAdapter(adapterRdn2);
            binding.vpRdn.startAutoScroll();
            binding.vpRdn.setInterval(AUTO_SCROLL_THRESHOLD_IN_MILLI);
            binding.vpRdn.setAutoScrollDurationFactor(6);
            binding.vpRdn.setCycle(true);
            binding.vpRdn.setStopScrollWhenTouch(true);

            binding.dotsIndicator.setViewPager(binding.vpRdn);

        }else {
            RdnOnBoardingFragment fragment = new RdnOnBoardingFragment();
            RdnOnBoardingFragment fragment2 = new RdnOnBoardingFragment();
            RdnOnBoardingFragment fragment3 = new RdnOnBoardingFragment();
            adapterRdn2 = new ViewPagerAdapter(getSupportFragmentManager());
            adapterRdn2.addFragment(fragment, "");
            adapterRdn2.addFragment(fragment2, "");
            adapterRdn2.addFragment(fragment3, "");

            binding.vpRdn.setAdapter(adapterRdn2);
            binding.vpRdn.startAutoScroll();
            binding.vpRdn.setInterval(AUTO_SCROLL_THRESHOLD_IN_MILLI);
            binding.vpRdn.setAutoScrollDurationFactor(6);
            binding.vpRdn.setCycle(true);
            binding.vpRdn.setStopScrollWhenTouch(true);

            binding.dotsIndicator.setViewPager(binding.vpRdn);
        }


    }

    /**
     * Use this method if you want to send data from activity to specific fragment
     *
     * @param onBoarding
     * @param image
     * @return
     */
    private Fragment addFragment(DplkBoardingResponse.Onboarding onBoarding,String image) {
        Bundle bundle = new Bundle();
        bundle.putString(TAG_FRAGMENT, new Gson().toJson(onBoarding));
        bundle.putString(TAG_IMAGE, image);
        RdnOnBoardingFragment fragment = new RdnOnBoardingFragment();
        fragment.setArguments(bundle);
        return fragment;
    }

    @Override
    public void onClick(View view) {
        switch (view.getId()){
            case R.id.btn_lanjut:
                DescriptionDplkProductActivity.launchIntent(this, response);
                break;
        }
    }

    @Override
    public void onPageScrolled(int position, float positionOffset, int positionOffsetPixels) {

    }

    @Override
    public void onPageSelected(int position) {

    }

    @Override
    public void onPageScrollStateChanged(int state) {

    }

    @Override
    protected void onActivityResult(int requestCode, int resultCode, @Nullable Intent data) {
        super.onActivityResult(requestCode, resultCode, data);
        if (requestCode == Constant.REQ_PAYMENT){
            if (resultCode == RESULT_OK) {
                setResult(RESULT_OK, data);
                this.finish();
            }
            else if (resultCode == Activity.RESULT_CANCELED){
                setResult(RESULT_CANCELED,data);
                this.finish();
            }
        }
    }

    @Override
    protected void onDestroy() {
        super.onDestroy();
        binding = null;
    }
}