package id.co.bri.brimo.ui.fragments.emas

import android.app.Activity
import android.content.Context
import android.content.Intent
import android.os.Bundle
import android.os.SystemClock
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.core.widget.NestedScrollView
import androidx.recyclerview.widget.LinearLayoutManager
import id.co.bri.brimo.R
import id.co.bri.brimo.adapters.RiwayatTransaksiBeliAdapter
import id.co.bri.brimo.adapters.RiwayatTransaksiJualAdapter
import id.co.bri.brimo.contract.IPresenter.emas.IRiwayatBeliEmasPresenter
import id.co.bri.brimo.contract.IView.emas.IRiwayatBeliEmasView
import id.co.bri.brimo.databinding.FragmentRiwayatBeliEmasBinding
import id.co.bri.brimo.domain.config.Constant
import id.co.bri.brimo.domain.config.Constant.EMAS_BUY_TYPE
import id.co.bri.brimo.domain.helpers.GeneralHelper
import id.co.bri.brimo.domain.helpers.calendar.CalendarHelper
import id.co.bri.brimo.models.TransactionTypeModel
import id.co.bri.brimo.models.YearModel
import id.co.bri.brimo.models.apimodel.request.emas.RiwayatFilterRequest
import id.co.bri.brimo.models.apimodel.response.onExceptionWH
import id.co.bri.brimo.models.apimodel.response.emas.InquiryOpenEmasResponse
import id.co.bri.brimo.models.apimodel.response.emas.RiwayatTransaksiEmasResponse
import id.co.bri.brimo.models.apimodel.response.emas.SafetyModeDrawerResponse
import id.co.bri.brimo.ui.activities.MutationFilterActivity
import id.co.bri.brimo.ui.activities.emas.InquiryBeliEmasActivity
import id.co.bri.brimo.ui.fragments.BaseFragment
import id.co.bri.brimo.ui.fragments.DialogFragmentRc02
import javax.inject.Inject

class RiwayatBeliEmasFragment(private var mResponse: RiwayatTransaksiEmasResponse, private var mContext :Context) : BaseFragment(),
        IRiwayatBeliEmasView,DialogFragmentRc02.DialogDefaultListener, RiwayatTransaksiBeliAdapter.OnItemClickListener,View.OnClickListener {
    private var _binding: FragmentRiwayatBeliEmasBinding? = null
    private val binding get() = _binding!!
    private var adapter : RiwayatTransaksiBeliAdapter? = null
    var dialog : DialogFragmentRc02? = null
    var mHarga : String? = null

    var rangeFilter:String? = null
    var transactionTypeId:String? = EMAS_BUY_TYPE
    var years:String? = null
    var monthId:String? = null
    var accountNumber:String? = null
    var transactionTypeName:String? = null
    var monthText:String? = null
    protected var mutationDateRangeFilterRequest: RiwayatFilterRequest? = null
    private var endDate: String? = null
    private var startDate: String? = null
    protected var transactionType = ArrayList<TransactionTypeModel>()
    protected var yearList: List<YearModel> = mutableListOf()
    private var isVisible = true
    private var lastScrollY = 0

    @Inject
    lateinit var presenter : IRiwayatBeliEmasPresenter<IRiwayatBeliEmasView>
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)

    }

    override fun onCreateView(inflater: LayoutInflater, container: ViewGroup?,
                              savedInstanceState: Bundle?): View? {
        // Inflate the layout for this fragment
        _binding = FragmentRiwayatBeliEmasBinding.inflate(inflater, container, false)
        val view = binding.root


        return view
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)

        if (mResponse.buyHistory!!.isEmpty()){
            binding.llEmptyTransaksi.visibility = View.VISIBLE
            binding.llFilter.visibility = View.GONE
            binding.wvInfo.visibility = View.VISIBLE
        }else{
            binding.wvInfo.visibility = View.VISIBLE
            GeneralHelper.setWebViewStandart(binding.wvInfo,"",mResponse.webviewInfo)
            binding.nsScrollView.setOnScrollChangeListener(NestedScrollView.OnScrollChangeListener { _, _, scrollY, _, oldScrollY ->
                if (scrollY == 0) {
                    binding.wvInfo.animate().alpha(1f).setDuration(300).withStartAction {
                        binding.wvInfo.visibility = View.VISIBLE
                    }.start()
                } else {
                    binding.wvInfo.animate().alpha(0f).setDuration(300).withEndAction {
                        binding.wvInfo.visibility = View.GONE
                    }.start()
                }
            })


            adapter = RiwayatTransaksiBeliAdapter(mResponse.buyHistory!!, mContext,this)
            val layoutManager = LinearLayoutManager(mContext, LinearLayoutManager.VERTICAL, false)
            binding.rvFragmentTransaksi.layoutManager = layoutManager
            binding.rvFragmentTransaksi.adapter = adapter

        }
        injectDepedency()

        yearList = mResponse.listYear!!
        binding.tvNoRek.text = mResponse.accountNumber
        binding.llTimeFilter.setOnClickListener(this)
    }

    private fun injectDepedency() {
        activityComponent.inject(this)
        presenter.view = this
        presenter.start()
        presenter.setUrlFilter(GeneralHelper.getString(R.string.url_filter_riwayat))
        presenter.setUrlGetFormBeli(GeneralHelper.getString(R.string.url_beli_inqury))
        presenter.setUrlRiwayatTransaksi(GeneralHelper.getString(R.string.url_riwayat_emas))
    }

    override fun onSuccessFormBeliEmas(response: InquiryOpenEmasResponse) {
        InquiryBeliEmasActivity.launchIntentRiwayat(requireActivity(), response,mHarga!!, true)
    }

    override fun exceptionEODEOM(response: onExceptionWH) {
        dialog = DialogFragmentRc02.newInstance(
            this,
            response
        )
        dialog!!.show(childFragmentManager, "")
    }

    override fun onSafetyMode(response: SafetyModeDrawerResponse) {
    }

    override fun onSuccessGetMutation(response: RiwayatTransaksiEmasResponse) {
        hideProgress()
        if (response.buyHistory!!.isEmpty()) {
            binding.llEmptyTransaksi.visibility = View.VISIBLE
            binding.rvFragmentTransaksi.visibility = View.GONE
        }else{
            binding.llEmptyTransaksi.visibility = View.GONE
            binding.rvFragmentTransaksi.visibility = View.VISIBLE
            adapter = RiwayatTransaksiBeliAdapter(response.buyHistory!!, mContext,this)
            val layoutManager = LinearLayoutManager(mContext, LinearLayoutManager.VERTICAL, false)
            binding.rvFragmentTransaksi.layoutManager = layoutManager
            binding.rvFragmentTransaksi.adapter = adapter
        }

    }


    override fun onClickDialogRc02() {
        dialog!!.dismiss()
    }

    override fun onItemClick(harga: String) {
        mHarga = harga
        presenter.getBeliEmas()
    }

    private fun getMutationWithFilter(data: Intent) {
        showProgress()
        if (rangeFilter.equals(Constant.TODAY_FILTER, ignoreCase = true) || rangeFilter.equals(Constant.SEVEN_DAY_FILTER, ignoreCase = true)) {
            startDate = data.getStringExtra(Constant.TAG_START_DATE)
            endDate = data.getStringExtra(Constant.TAG_END_DATE)
            years = data.getStringExtra(Constant.YEAR)
            monthId = data.getStringExtra(Constant.MONTH)
            mutationDateRangeFilterRequest = RiwayatFilterRequest(rangeFilter, startDate, endDate, years, monthId)
            presenter.getMutationRange(mutationDateRangeFilterRequest)
        } else if (rangeFilter.equals(Constant.MONTH_FILTER, ignoreCase = true)) {
            years = data.getStringExtra(Constant.YEAR)
            monthId = data.getStringExtra(Constant.MONTH)
            startDate = data.getStringExtra(Constant.TAG_START_DATE)
            endDate = data.getStringExtra(Constant.TAG_END_DATE)
            monthText = data.getStringExtra(Constant.MONTH_TEXT)
            mutationDateRangeFilterRequest = RiwayatFilterRequest(rangeFilter, startDate, endDate, years, monthId)
            presenter.getMutationRange(mutationDateRangeFilterRequest)
        }
        else if (rangeFilter.equals(Constant.RANGE_FILTER, ignoreCase = true)) {
            startDate = data.getStringExtra(Constant.TAG_START_DATE)
            endDate = data.getStringExtra(Constant.TAG_END_DATE)
            monthId = data.getStringExtra(Constant.MONTH)
            years = data.getStringExtra(Constant.YEAR)
            mutationDateRangeFilterRequest = RiwayatFilterRequest(rangeFilter, startDate, endDate, years, monthId)
            presenter.getMutationRange(mutationDateRangeFilterRequest)
        }

        binding.tvTime.setTextColor(resources.getColor(R.color.whiteColor))
        binding.llTimeFilter.setBackground(resources.getDrawable(R.drawable.rounded_button_blue))
        binding.ivFilter.setVisibility(View.GONE)
        if (rangeFilter.equals(Constant.TODAY_FILTER, ignoreCase = true)) {
            binding.tvTime.setText(Constant.TODAY_TEXT)
        } else if (rangeFilter.equals(Constant.SEVEN_DAY_FILTER, ignoreCase = true)) {
            binding.tvTime.setText(Constant.SEVEN_DAY_TEXT)
        } else if (rangeFilter.equals(Constant.MONTH_FILTER, ignoreCase = true)) {
            binding.tvTime.setText(monthText + " " + years)
        } else {
            binding.tvTime.setText(CalendarHelper.dateFormatyyyyMMdd(startDate) + " - " + CalendarHelper.dateFormatyyyyMMdd(endDate))
        }
    }

    private fun setResetFilter() {
        binding.tvTime.setText(GeneralHelper.getString(R.string.date_range))
        binding.tvTime.setTextColor(resources.getColor(R.color.system10))
        binding.llTimeFilter.setBackground(resources.getDrawable(R.drawable.bg_white_border_accent2))
        binding.ivFilter.setVisibility(View.VISIBLE)
        rangeFilter = null
        transactionTypeId = null
        monthId = null
        years = null
        startDate = null
        endDate = null
        presenter.getDataRiwayatTransaksi()
    }


    override fun onActivityResult(requestCode: Int, resultCode: Int, data: Intent?) {
        super.onActivityResult(requestCode, resultCode, data)
        if (requestCode == Constant.REQ_DURATION) {
            if (resultCode == Activity.RESULT_OK) {
                if (data != null) {
                    if (data.getStringExtra(Constant.TRANSACTION_TYPE_ID).equals(EMAS_BUY_TYPE)) {
                        if (data.getStringExtra(Constant.FILTER) != null) {
                            rangeFilter = data.getStringExtra(Constant.FILTER)
                            getMutationWithFilter(data)
                        } else {
                            setResetFilter()
                        }
                    }
                } else {
                    setResetFilter()
                }
            }
        }
    }

    override fun onException(message: String?) {
        super.onException(message)
        hideProgress()
        GeneralHelper.showSnackBarRevamp(binding.content, message)
    }

    override fun onClick(p0: View?) {
        if (SystemClock.elapsedRealtime() - mLastClickTime < 1000) {
            return
        }


        mLastClickTime = SystemClock.elapsedRealtime()
        val id = p0?.id
        transactionTypeId = EMAS_BUY_TYPE

        when (id) {
            R.id.ll_time_filter -> MutationFilterActivity.launchIntent(requireActivity(), transactionType, yearList, rangeFilter, transactionTypeId, monthId, years, startDate, endDate,true)

        }

    }

}