package id.co.bri.brimo.ui.activities.registrasirevamp

import android.annotation.SuppressLint
import android.app.Activity
import android.content.Intent
import android.os.Bundle
import android.os.CountDownTimer
import android.view.KeyEvent
import android.view.View
import android.view.ViewGroup
import android.view.ViewTreeObserver
import android.view.ViewTreeObserver.OnGlobalLayoutListener
import androidx.activity.OnBackPressedCallback
import androidx.core.text.HtmlCompat
import androidx.recyclerview.widget.GridLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.google.gson.Gson
import id.co.bri.brimo.R
import id.co.bri.brimo.adapters.PinNumberBlueAdapter
import id.co.bri.brimo.adapters.WatermarkAdapter
import id.co.bri.brimo.adapters.baseadapter.base.BasePinAdapter
import id.co.bri.brimo.adapters.pinadapter.OtpRevampAdapter
import id.co.bri.brimo.contract.IPresenter.registrasirevamp.IRegistrasiOtpPrivyPresenter
import id.co.bri.brimo.contract.IView.registrasirevamp.IRegistrasiOtpPrivyView
import id.co.bri.brimo.databinding.ActivityRegistrasiOtpPrivyBinding
import id.co.bri.brimo.domain.config.Constant
import id.co.bri.brimo.domain.helpers.GeneralHelper
import id.co.bri.brimo.models.RegisIdModel
import id.co.bri.brimo.models.apimodel.request.RegisSendOtpReq
import id.co.bri.brimo.models.apimodel.response.registrasi.RegisOtpPrivyRes
import id.co.bri.brimo.models.apimodel.response.registrasi.RegisOtpResponse
import id.co.bri.brimo.ui.activities.AskActivity
import id.co.bri.brimo.ui.activities.DashboardIBActivity
import id.co.bri.brimo.ui.activities.base.BaseActivity
import id.co.bri.brimo.ui.customviews.pin.InsertPinNumbers
import javax.inject.Inject

class RegistrasiOtpPrivyActivity : BaseActivity(),
    IRegistrasiOtpPrivyView,
    View.OnKeyListener,
    PinNumberBlueAdapter.OnPinNumberListener,
    BasePinAdapter.PinAdapterListener {

    @Inject
    lateinit var presenter: IRegistrasiOtpPrivyPresenter<IRegistrasiOtpPrivyView>

    private lateinit var binding: ActivityRegistrasiOtpPrivyBinding

    private var regisOtpResponse: RegisOtpResponse? = null
    private var countDownTimer: CountDownTimer? = null
    private val second = 1000

    private val watermarkList: ArrayList<Int> = ArrayList()

    private lateinit var otpRevampAdapter: OtpRevampAdapter
    private lateinit var pinNumberAdapter: PinNumberBlueAdapter
    private lateinit var pinOtpLayoutManager: GridLayoutManager
    private lateinit var pinPadLayoutManager: GridLayoutManager

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)

        binding = ActivityRegistrasiOtpPrivyBinding.inflate(layoutInflater)
        setContentView(binding.root)

        onBackPressedDispatcher.addCallback(this, onBackPressedCallback)

        intentExtra()
        injectDependency()
        setupView()
        onSetLayoutHeight()
        onWatermark()
    }

    private fun intentExtra() {
        regisOtpResponse =
            Gson().fromJson(intent.getStringExtra(Constant.GENRES), RegisOtpResponse::class.java)
    }

    private fun injectDependency() {
        activityComponent.inject(this)
        presenter.view = this
        presenter.start()
        presenter.setUrlSendOtp(GeneralHelper.getString(R.string.url_registration_validate_otp_privy))
        presenter.setUrlResendOtp(GeneralHelper.getString(R.string.url_registration_resend_otp_privy))
    }

    private fun setupView() {
        GeneralHelper.setToolbarRevamp(
            this,
            binding.toolbarRevamp.toolbar,
            GeneralHelper.getString(R.string.kode_pengesahan)
        )

        binding.tvDescOtp.text = HtmlCompat.fromHtml(
            String.format(
                GeneralHelper.getString(R.string.txt_otp_privy),
                regisOtpResponse?.phone
            ), HtmlCompat.FROM_HTML_MODE_LEGACY
        )

        otpRevampAdapter = OtpRevampAdapter(this, 2)
        pinNumberAdapter =
            PinNumberBlueAdapter(InsertPinNumbers.getPinNumberList(this))
        pinOtpLayoutManager = GridLayoutManager(this, 6)
        pinPadLayoutManager = GridLayoutManager(this, 3)

        pinNumberAdapter.onPinNumberListener = this
        otpRevampAdapter.setListener(this)
        binding.rvBox.layoutManager = pinOtpLayoutManager
        binding.rvBox.adapter = otpRevampAdapter
        binding.rvInputOtp.layoutManager = pinPadLayoutManager
        binding.rvInputOtp.adapter = pinNumberAdapter

        setTextTimer(regisOtpResponse!!.expiredIinSecond)

        binding.tvKrmUlang.setOnClickListener {
            presenter.resendOtp(RegisIdModel(regisOtpResponse!!.registrationId))
        }
    }

    @Suppress("DEPRECATION")
    private fun onSetLayoutHeight() {
        val viewTreeObserver: ViewTreeObserver = binding.layoutPin.viewTreeObserver
        viewTreeObserver.addOnGlobalLayoutListener(object : OnGlobalLayoutListener {
            override fun onGlobalLayout() {
                binding.layoutPin.viewTreeObserver.removeGlobalOnLayoutListener(this)
                val height: Int = binding.layoutPin.measuredHeight
                val params: ViewGroup.LayoutParams = binding.rvWatermark.layoutParams
                params.height = height
                binding.rvWatermark.layoutParams = params
            }
        })
    }

    private fun onWatermark() {
        for (i in 0..199) {
            watermarkList.add(i)
        }
        binding.rvWatermark.setHasFixedSize(true)
        binding.rvWatermark.layoutManager = object :
            GridLayoutManager(this, 4, RecyclerView.VERTICAL, false) {
            override fun canScrollVertically(): Boolean {
                return false
            }
        }
        val watermarkAdapter = WatermarkAdapter(this, watermarkList)
        binding.rvWatermark.adapter = watermarkAdapter
    }

    private fun setTextTimer(timer: Int) {
        val countDown: Int = second * timer
        countDownTimer = object : CountDownTimer(countDown.toLong(), second.toLong()) {
            override fun onTick(millisUntilFinished: Long) {
                val seconds: Int = millisUntilFinished.toInt() / second
                val timeFormat = GeneralHelper.getTimeFormat(seconds)
                binding.tvTimer.text = String.format(
                    resources.getString(R.string.countdown_otp00_00),
                    timeFormat[1], timeFormat[2]
                )

                binding.tvKrmUlang.alpha = 0.3f
                binding.tvKrmUlang.isEnabled = false
            }

            override fun onFinish() {
                binding.tvTimer.text = GeneralHelper.getString(R.string.time00_00)
                binding.tvKrmUlang.alpha = 1f
                binding.tvKrmUlang.isEnabled = true
            }
        }.start()
    }

    override fun onKey(p0: View?, p1: Int, p2: KeyEvent?): Boolean {
        return false
    }

    override fun onPinClicked(pinNumber: Int) {
        otpRevampAdapter.addPin(pinNumber.toString())
    }

    override fun onDeleteClicked() {
        otpRevampAdapter.deletePin()
    }

    @SuppressLint("NotifyDataSetChanged")
    override fun notifyChanges() {
        otpRevampAdapter.notifyDataSetChanged()
    }

    override fun onComplete(string: String) {
        presenter.sendOtp(RegisSendOtpReq(regisOtpResponse!!.registrationId, string))
    }

    override fun deleteAllPin() {
        otpRevampAdapter.deleteAllPin()
    }

    override fun onSuccessDashboard(regisOtpPrivyRes: RegisOtpPrivyRes) {
        setEventAppsFlyer()
        val intent = Intent(this, DashboardIBActivity::class.java)
        intent.addFlags(Intent.FLAG_ACTIVITY_CLEAR_TOP or Intent.FLAG_ACTIVITY_NEW_TASK)
        startActivity(intent)
        setResult(RESULT_OK)
        finish()
    }

    override fun onSuccessLogin() {
        setEventAppsFlyer()
        val intent = Intent(this, AskActivity::class.java)
        intent.flags = Intent.FLAG_ACTIVITY_NEW_TASK or Intent.FLAG_ACTIVITY_CLEAR_TASK
        intent.putExtra(Constant.FEATURE_REGIS, true)
        startActivity(intent)
        finish()
    }

    private fun setEventAppsFlyer() {
        val eventValue: MutableMap<String, Any> = HashMap()
        eventValue[Constant.CUSTOMER_ID] = presenter.persistenceId
        trackAppsFlyerAnalyticEvent("success_register", eventValue)
    }

    override fun onSuccessResend(regisOtpResponse: RegisOtpResponse) {
        this.regisOtpResponse = regisOtpResponse
        setTextTimer(regisOtpResponse.expiredIinSecond)
    }

    private val onBackPressedCallback: OnBackPressedCallback =
        object : OnBackPressedCallback(true) {
            override fun handleOnBackPressed() {
                val intent = Intent()
                intent.putExtra(Constant.NAME, Constant.REGISTRATION_BRIMO)
                intent.putExtra(Constant.CHECK_POINT, 8)
                setResult(Activity.RESULT_CANCELED, intent)
                finish()
            }
        }

    override fun onDestroy() {
        presenter.stop()
        super.onDestroy()
    }

}