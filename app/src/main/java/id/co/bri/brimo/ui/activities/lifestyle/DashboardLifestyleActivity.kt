@file:Suppress("DEPRECATION")

package id.co.bri.brimo.ui.activities.lifestyle

import android.Manifest
import android.annotation.SuppressLint
import android.app.Activity
import android.content.Context
import android.content.Intent
import android.content.pm.PackageManager
import android.graphics.Color
import android.graphics.PorterDuff
import android.location.Geocoder
import android.location.Location
import android.location.LocationListener
import android.net.ConnectivityManager
import android.net.NetworkCapabilities
import android.net.Uri
import android.os.Build
import android.os.Bundle
import android.os.Handler
import android.os.Looper
import android.provider.Settings
import android.util.Log
import android.view.Gravity
import android.view.MotionEvent
import android.view.View
import android.view.WindowManager
import android.widget.LinearLayout
import androidx.annotation.RequiresApi
import androidx.browser.customtabs.CustomTabsIntent
import androidx.core.app.ActivityCompat
import androidx.core.content.ContextCompat
import androidx.core.widget.TextViewCompat
import androidx.recyclerview.widget.GridLayoutManager
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import androidx.swiperefreshlayout.widget.SwipeRefreshLayout
import androidx.viewpager.widget.ViewPager
import com.ethanhua.skeleton.Skeleton
import com.ethanhua.skeleton.SkeletonScreen
import com.google.gson.Gson
import id.co.bri.brimo.R
import id.co.bri.brimo.adapters.dashboardLifestyle.*
import id.co.bri.brimo.contract.IPresenter.lifestyle.IDashboardLifestylePresenter
import id.co.bri.brimo.contract.IView.lifestyle.IDashboardLifestyleView
import id.co.bri.brimo.databinding.ActivityDashboardLifestyleBinding
import id.co.bri.brimo.domain.config.Constant
import id.co.bri.brimo.domain.config.LifestyleConfig
import id.co.bri.brimo.domain.config.LifestyleConfig.MenuLifestyleCode
import id.co.bri.brimo.domain.helpers.AnimationExpand
import id.co.bri.brimo.domain.helpers.GeneralHelper
import id.co.bri.brimo.domain.helpers.GpsTracker
import id.co.bri.brimo.models.apimodel.request.PartnerIdRequest
import id.co.bri.brimo.models.apimodel.response.CityFormResponse
import id.co.bri.brimo.models.apimodel.response.GeneralWebviewResponse
import id.co.bri.brimo.models.apimodel.response.UrlWebViewResponse
import id.co.bri.brimo.models.apimodel.response.lifestyle.*
import id.co.bri.brimo.models.daomodel.lifestyle.MenuLifestyle
import id.co.bri.brimo.ui.activities.BrigunaDigitalActivity
import id.co.bri.brimo.ui.activities.CeriaActivity
import id.co.bri.brimo.ui.activities.base.BaseActivity
import id.co.bri.brimo.ui.activities.lifestyle.ekspedisi.WebviewEkspedisiActivity
import id.co.bri.brimo.ui.activities.travel.FormBusActivity
import id.co.bri.brimo.ui.activities.travel.WebViewTravelActivity
import id.co.bri.brimo.ui.activities.voucher.VoucherActivity
import id.co.bri.brimo.ui.customviews.dialog.DialogExitCustom
import id.co.bri.brimo.ui.customviews.dialog.DialogSetDefaultRevamp
import id.co.bri.brimo.ui.fragments.BaseFragment
import id.co.bri.brimo.ui.fragments.bottomsheet.OpenBottomSheetGeneralFragment
import id.co.bri.brimo.ui.fragments.dashboardLifestyle.SubMenuLifestyleFragment
import id.co.bri.brimo.ui.fragments.lifestyle.DialogEODLifestyle
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import java.util.Locale
import java.util.*
import javax.inject.Inject


class DashboardLifestyleActivity : BaseActivity(), IDashboardLifestyleView, View.OnClickListener,
    LocationListener, SwipeRefreshLayout.OnRefreshListener,
    DialogExitCustom.DialogClickYesNoListener,
    DestinationLifestyleAdapter.OnClickDestinationLifestyle,
    DigitalLoanLifestyleAdapter.OnClickDigitalLoan, CategoryMenuLifestyleAdapter.OnCategoryClick,
    MenuLifestyleAdapter.OnclickMenuLifestyle, DialogEODLifestyle.DialogDefaultListener {


    private val permissionsIndiHome = arrayOf(
        Manifest.permission.WRITE_EXTERNAL_STORAGE,
        Manifest.permission.READ_EXTERNAL_STORAGE,
        Manifest.permission.CAMERA,
        Manifest.permission.ACCESS_FINE_LOCATION,
        Manifest.permission.ACCESS_COARSE_LOCATION
    )

    @RequiresApi(Build.VERSION_CODES.TIRAMISU)
    private val permissionsIndiHome33 = arrayOf(
        Manifest.permission.READ_MEDIA_IMAGES,
        Manifest.permission.CAMERA,
        Manifest.permission.ACCESS_FINE_LOCATION,
        Manifest.permission.ACCESS_COARSE_LOCATION
    )

    private lateinit var binding: ActivityDashboardLifestyleBinding

    private var inLatitude: Double = 0.0
    private var inLongitude: Double = 0.0
    private var gpsTracker: GpsTracker? = null
    private var promoDataViews: List<PromoDataView> = arrayListOf()
    private var menuDataViews: List<MenuDataView> = arrayListOf()
    private var sellingDataViews: List<SellingDataView> = arrayListOf()
    private var linearLayoutPromo: LinearLayout? = null
    private var linearLayoutRepurchase: LinearLayout? = null
    private var skeletonTabPromo: SkeletonScreen? = null
    private var skeletonItemPromo: SkeletonScreen? = null
    private var skeletonHistory: SkeletonScreen? = null
    private var skeletonTabRepurchase: SkeletonScreen? = null
    private var skeletonItemRepurchase: SkeletonScreen? = null
    private var skeletonInjourney: SkeletonScreen? = null
    private var skeletonDigitalLoan: SkeletonScreen? = null
    private var adapterPromoLifestyle: FragmentPromoLifestyleAdapter? = null
    private var adapterRepurchases: FragmentRepurchaseAdapter? = null
    private var colorBgTabPromoDefault: Int = 0
    private var colorBgTabPromoSelected: Int = 0
    private var colorTextTabPromoDefault: Int = 0
    private var colorTextTabPromoSelected: Int = 0
    private var colorTextTabMenuDefault: Int = 0
    private var colorTextTabMenuSelected: Int = 0
    private var colorBgTabRepurchaseDefault: Int = 0
    private var colorBgTabRepurchaseSelected: Int = 0
    private var colorTextTabRepurchaseDefault: Int = 0
    private var colorTextTabRepurchaseSelected: Int = 0
    private var badgeTrx: Int? = 0
    private var networkEnabled = false
    private var subAdminAreaTemp = ""
    private var countryNameTemp = ""
    private var categoryMenuLifestyleAdapter: CategoryMenuLifestyleAdapter? = null
    private var menuLifestyleAdapter: MenuLifestyleAdapter? = null
    private var menuLifestyleMoreAdapter: MenuLifestyleAdapter? = null
    private var mFeatureDatas: MutableList<FeatureDataView> = mutableListOf()
    private var newMenus: MutableList<FeatureDataView> = mutableListOf()
    private var dialog: DialogEODLifestyle? = null
    private var posCategory = 0
    private var menuLifestyleLocals: List<MenuLifestyle>? = arrayListOf()

    @Inject
    lateinit var presenter: IDashboardLifestylePresenter<IDashboardLifestyleView>

    companion object {
        private const val REQUEST_LOCATION: Int = 101
        private const val REQUEST_CODE_LOCATION: Int = 410
        private lateinit var mDashboardMenuResponse: DashboardLifestyleMenuResponse
        private const val TAG_RESPONSE = "response"
        var titleTabPromo: MutableList<String> = mutableListOf()
        var titleTabRepurchase: MutableList<String> = mutableListOf()

        @JvmStatic
        fun launchIntent(
            caller: Activity, dashboardLifestyleMenuResponse: DashboardLifestyleMenuResponse
        ) {
            val intent = Intent(caller, DashboardLifestyleActivity::class.java)
            intent.putExtra(TAG_RESPONSE, Gson().toJson(dashboardLifestyleMenuResponse))

            caller.startActivityForResult(intent, Constant.REQ_PAYMENT)
        }
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)

        //to set status bar transparent and navigation bar still show with background
        val window = window
        window.apply {
            clearFlags(WindowManager.LayoutParams.FLAG_TRANSLUCENT_STATUS)
            addFlags(WindowManager.LayoutParams.FLAG_DRAWS_SYSTEM_BAR_BACKGROUNDS)
            decorView.systemUiVisibility =
                View.SYSTEM_UI_FLAG_LAYOUT_FULLSCREEN or View.SYSTEM_UI_FLAG_LIGHT_STATUS_BAR
            statusBarColor = Color.TRANSPARENT
        }

        binding = ActivityDashboardLifestyleBinding.inflate(layoutInflater)
        setContentView(binding.root)

        if (intent.extras != null) {
            parseIntent()
        }

        injectDependency()
        initListener()
        setupView()
    }

    private fun injectDependency() {
        activityComponent.inject(this)
        presenter.view = this
        presenter.setUrlBagdeTrx(GeneralHelper.getString(R.string.url_dashboard_lifestyle_badge_history))
        presenter.setUrlDashboardInformation(GeneralHelper.getString(R.string.url_dashboard_lifestyle_info))
        presenter.setUrlWebviewTugu(GeneralHelper.getString(R.string.url_webview_new))
        presenter.setUrlDashboardSelling(GeneralHelper.getString(R.string.url_dashboard_lifestyle_selling))
        presenter.setUrlBusShuttle(GeneralHelper.getString(R.string.url_form_bus))
        presenter.setUrlKai(GeneralHelper.getString(R.string.url_travel_kai_webview))
        presenter.getBadgeTrx()
        presenter.getDashboardInformation()
        presenter.getDashboardSelling()
        presenter.start()
    }

    private fun parseIntent() {
        if (intent.hasExtra(TAG_RESPONSE)) {
            mDashboardMenuResponse = Gson().fromJson(
                intent.extras?.getString(TAG_RESPONSE), DashboardLifestyleMenuResponse::class.java
            )
        }
    }

    private fun initListener() {
        binding.srlDashboardLifestyle.setOnRefreshListener(this)
        binding.tvTagLokasi.setOnClickListener(this)
        binding.tvGoTop.setOnClickListener(this)
        binding.tvLihatRekomWisata.setOnClickListener(this)
        binding.rlTrxLifestyle.setOnClickListener(this)
        binding.lyMenuMore.setOnClickListener(this)

    }

    private fun setupView() {
        getLocation()
        setupThematic()
        initSkeleton()
        initMenu()

        //to handle button go to top content if scrolled
        binding.nsView.viewTreeObserver.addOnScrollChangedListener {
            val scrollY = binding.nsView.scrollY
            val scrollViewHeight = binding.nsView.height
            val contentHeight = binding.nsView.getChildAt(0).height

            if (scrollY + scrollViewHeight >= contentHeight * 0.93) {
                binding.tvGoTop.visibility = View.VISIBLE
            } else {
                binding.tvGoTop.visibility = View.GONE
            }
        }

        GeneralHelper.setToolbarRevamp(
            this, binding.toolbarDashboardLifestyle, GeneralHelper.getString(R.string.lifestyle_txt)
        )
    }

    //to show permission location and get latitude and longtitude
    private fun getLocation() {
        gpsTracker = GpsTracker(this)
        networkEnabled = isOnline(this)
        if (gpsTracker?.canGetLocation() == true) {
            if (gpsTracker?.latitude != 0.0 && gpsTracker?.longitude != 0.0) {
                inLatitude = gpsTracker?.latitude ?: 0.0
                inLongitude = gpsTracker?.longitude ?: 0.0
                setupLocation()
            } else {
                setLocationPermDenied(GeneralHelper.getString(R.string.enable_location))
            }
        }
    }

    @SuppressLint("SetTextI18n")
    private fun setupLocation() {
        val geocoder = Geocoder(this, Locale.getDefault())
        var subAdminArea = ""
        var countryName = ""

        CoroutineScope(Dispatchers.IO).launch {
            try {
                // Get address from latitude and longitude with geocoder
                val currentLocation = geocoder.getFromLocation(inLatitude, inLongitude, 1)
                if (currentLocation != null) {
                    // Set address from geocoder
                    subAdminArea = currentLocation.first().subAdminArea
                    countryName = currentLocation.first().countryName
                    if (networkEnabled) {
                        subAdminAreaTemp = currentLocation.first().subAdminArea
                        countryNameTemp = currentLocation.first().countryName
                    }
                }
            } catch (e: Exception) {
                if (subAdminArea == "" && countryName == "") {
                    getLocation()
                }
                if (!GeneralHelper.isProd()) {
                    Log.d("testdebug245", "onErrorGeocoder: $e")
                }
            }

            withContext(Dispatchers.Main) {
                // To set text and icon if permission granted and disable click on tag location text
                if (inLatitude != 0.0 && inLongitude != 0.0) {
                    binding.tvTagLokasi.isClickable = false
                    binding.tvTagLokasi.setCompoundDrawablesRelativeWithIntrinsicBounds(
                        R.drawable.ic_location_fill, 0, 0, 0
                    )
                    if (networkEnabled) {
                        binding.tvTagLokasi.text = "$subAdminArea, $countryName"
                    } else {
                        binding.tvTagLokasi.text = "$subAdminAreaTemp, $countryNameTemp"
                    }
                } else {
                    if (!networkEnabled) {
                        setLocationPermDenied(GeneralHelper.getString(R.string.enable_internet))
                    } else {
                        setLocationPermDenied(GeneralHelper.getString(R.string.enable_location))
                    }
                }
            }
        }
    }

    //to set text and icon tag location permission denied
    private fun setLocationPermDenied(textDenied: String) {
        runOnUiThread {
            binding.tvTagLokasi.setCompoundDrawablesWithIntrinsicBounds(
                R.drawable.ic_location_off, 0, 0, 0
            )
            binding.tvTagLokasi.text = textDenied
        }
    }

    //function used to setting thematic by response from BE
    private fun setupThematic() {
        val flagTheme = mDashboardMenuResponse.thematicDataView?.flagThematic
        val themeCode = mDashboardMenuResponse.thematicDataView?.thematicCode
        val theme = LifestyleConfig.ThematicLifestyle.values().find { it.theme == themeCode }

        if (flagTheme != null && flagTheme == true) {
            when (theme) {
                LifestyleConfig.ThematicLifestyle.NEW_YEAR -> {
                    setTheme(
                        R.drawable.bg_header_new_year_full,
                        R.drawable.bg_header_new_year_crop,
                        R.color.primary_blue80,
                        R.color.primary_blue80_transparent,
                        R.color.primary_blue100
                    )
                }

                LifestyleConfig.ThematicLifestyle.LUNAR_DAY -> {
                    setTheme(
                        R.drawable.bg_header_imlek_full,
                        R.drawable.bg_header_imlek_crop,
                        R.color.error80,
                        R.color.error80_transparent,
                        R.color.error80
                    )
                }

                LifestyleConfig.ThematicLifestyle.EID_MUBARAK -> {
                    setTheme(
                        R.drawable.bg_header_idul_fitri_full,
                        R.drawable.bg_header_idul_fitri_crop,
                        R.color.success80,
                        R.color.success80_transparent,
                        R.color.success80
                    )
                }

                LifestyleConfig.ThematicLifestyle.INDEPENDENCE_DAY -> {
                    setTheme(
                        R.drawable.bg_header_hut_ri_full,
                        R.drawable.bg_header_hut_ri_crop,
                        R.color.primary_blue80,
                        R.color.primary_blue80_transparent,
                        R.color.primary_blue100
                    )
                }

                LifestyleConfig.ThematicLifestyle.BRI_ANNIVERSARY -> {
                    //Set logo HUT BRI if thematic is HUT BRI and image path on thematic not empty
                    if (mDashboardMenuResponse.thematicDataView?.imagePath?.isNotEmpty() == true) {
                        binding.ivHutBri.visibility = View.VISIBLE
                        GeneralHelper.loadImageUrlWithPlaceholder(
                            this,
                            mDashboardMenuResponse.thematicDataView?.imagePath,
                            binding.ivHutBri,
                            0,
                            0,
                            0
                        )
                    }

                    setTheme(
                        R.drawable.bg_header_hut_bri_full,
                        R.drawable.bg_header_hut_bri_crop,
                        R.color.primary_blue80,
                        R.color.primary_blue80_transparent,
                        R.color.primary_blue100
                    )
                }

                LifestyleConfig.ThematicLifestyle.CHRISTMAS_DAY -> {
                    setTheme(
                        R.drawable.bg_header_natal_full,
                        R.drawable.bg_header_natal_crop,
                        R.color.blue_BRI100,
                        R.color.primary_blue80_transparent,
                        R.color.blue_BRI100
                    )
                }

                else -> {
                    setTheme(
                        R.drawable.bg_header_default_full,
                        R.drawable.bg_header_default_crop,
                        R.color.primary_blue80,
                        R.color.primary_blue80_transparent,
                        R.color.primary_blue100
                    )
                }
            }
        } else {
            setTheme(
                R.drawable.bg_header_default_full,
                R.drawable.bg_header_default_crop,
                R.color.primary_blue80,
                R.color.primary_blue80_transparent,
                R.color.primary_blue100
            )
        }
    }

    //function used to set the background header, tab, text, icon, and color by theme
    private fun setTheme(
        bgHeaderTop: Int,
        bgHeaderPromo: Int,
        colorPrimary: Int,
        colorTransparent: Int,
        colorBgTabPromoDef: Int
    ) {
        //to set text and indicator tab color of section Promo
        colorTextTabPromoDefault = R.color.neutral_baseWhite
        colorTextTabPromoSelected = colorPrimary
        colorBgTabPromoDefault = colorBgTabPromoDef
        colorBgTabPromoSelected = R.color.neutral_baseWhite

        //to set text and indicator tab color of section Menu
        colorTextTabMenuDefault = R.color.neutral_light80
        colorTextTabMenuSelected = colorPrimary

        //to set text and indicator tab color of section Repurchase
        colorTextTabRepurchaseDefault = R.color.neutral_baseBlack
        colorTextTabRepurchaseSelected = R.color.neutral_baseWhite
        colorBgTabRepurchaseDefault = R.color.neutral_baseWhite
        colorBgTabRepurchaseSelected = colorPrimary

        //to set header background, text, and color background thematic
        binding.clHeader.setBackgroundResource(bgHeaderTop)
        binding.llPromoLifestyle.setBackgroundResource(bgHeaderPromo)
        binding.ivTrxLifestyle.setColorFilter(
            ContextCompat.getColor(this, colorPrimary), PorterDuff.Mode.SRC_IN
        )
        binding.ivChevronTrxLifestyle.setColorFilter(
            ContextCompat.getColor(this, colorPrimary), PorterDuff.Mode.SRC_IN
        )
        binding.rlBeliLagiLifestyle.setBackgroundResource(colorTransparent)
        binding.tvLihatRekomWisata.setTextColor(GeneralHelper.getColor(colorPrimary))
        TextViewCompat.setCompoundDrawableTintList(
            binding.tvLihatRekomWisata, ContextCompat.getColorStateList(baseContext, colorPrimary)
        )
        binding.tvGoTop.setTextColor(GeneralHelper.getColor(colorPrimary))
        TextViewCompat.setCompoundDrawableTintList(
            binding.tvGoTop, ContextCompat.getColorStateList(baseContext, colorPrimary)
        )
    }

    //to initiate skeleton item to recyclerview and layout
    private fun initSkeleton() {
        //skeleton tab promo and news
        skeletonTabPromo =
            Skeleton.bind(binding.stPromoLifestyle).shimmer(true).angle(20).duration(1200)
                .color(R.color.white).load(R.layout.item_skeleton_tab_rounded_lifestyle).show()

        //skeleton item promo and news
        skeletonItemPromo =
            Skeleton.bind(binding.vpPromoLifestyle).shimmer(true).angle(20).duration(1200)
                .color(R.color.white).load(R.layout.item_skeleton_promo_lifestyle).show()

        //skeleton history
        skeletonHistory =
            Skeleton.bind(binding.rlTrxLifestyle).shimmer(true).angle(20).duration(1200)
                .color(R.color.white).load(R.layout.item_skeleton_history_lifestyle).show()

        //skeleton tab repurchase and best seller
        skeletonTabRepurchase =
            Skeleton.bind(binding.stBeliLagiLifestyle).shimmer(true).angle(20).duration(1200)
                .color(R.color.white).load(R.layout.item_skeleton_tab_rounded_lifestyle).show()

        //skeleton item repurchase and best seller
        skeletonItemRepurchase =
            Skeleton.bind(binding.vpBeliLagiLifestyle).shimmer(true).angle(20).duration(1200)
                .color(R.color.white).load(R.layout.item_skeleton_repurchase_lifestyle).show()

        //skeleton travel recomendation
        skeletonInjourney =
            Skeleton.bind(binding.llRekomWisata).shimmer(true).angle(20).duration(1200)
                .color(R.color.white).load(R.layout.item_skeleton_selling_lifestyle).show()

        //skeleton digital loan
        skeletonDigitalLoan =
            Skeleton.bind(binding.llDigitalLoan).shimmer(true).angle(20).duration(1200)
                .color(R.color.white).load(R.layout.item_skeleton_selling_lifestyle).show()
    }

    //iniate tab and promo item
    @SuppressLint("NotifyDataSetChanged")
    private fun initPromo(dashboardLifestyleInfoResponse: DashboardLifestyleInfoResponse) {
        promoDataViews = dashboardLifestyleInfoResponse.promoDataView

        if (promoDataViews.isNullOrEmpty()) {
            binding.llPromoLifestyle.visibility = View.GONE
        } else {
            //if promo content not empty it will visible
            if (promoDataViews.isNotEmpty()) {
                for (data in promoDataViews.indices) {
                    //if data promo empty will content promo will be hide
                    if (promoDataViews[data].dataView.isNullOrEmpty()) {
                        binding.llPromoLifestyle.visibility = View.GONE
                        //if data promo not empty content promo will be visible
                    } else {
                        binding.llPromoLifestyle.visibility = View.VISIBLE

                        //set title tab
                        titleTabPromo.clear()
                        dashboardLifestyleInfoResponse.promoDataView.forEach {
                            titleTabPromo.add(it.titleTab)
                        }

                        //set content to viewpager
                        adapterPromoLifestyle = FragmentPromoLifestyleAdapter(
                            supportFragmentManager, titleTabPromo, promoDataViews
                        )
                        binding.vpPromoLifestyle.adapter = adapterPromoLifestyle
                        binding.stPromoLifestyle.setViewPager(binding.vpPromoLifestyle)//Add bold effect on selected tab
                        linearLayoutPromo = binding.stPromoLifestyle.getChildAt(0) as LinearLayout

                        //set background indicator tab
                        GeneralHelper.changeTabsFontBoldBackgroundRounded(
                            this,
                            linearLayoutPromo,
                            0,
                            colorTextTabPromoDefault,
                            colorTextTabPromoSelected,
                            R.drawable.bg_blue_round_fill,
                            R.drawable.bg_blue_round_fill,
                            colorBgTabPromoDefault,
                            colorBgTabPromoSelected
                        )

                        binding.stPromoLifestyle.setOnPageChangeListener(object :
                            ViewPager.OnPageChangeListener {
                            override fun onPageScrolled(
                                position: Int, positionOffset: Float, positionOffsetPixels: Int
                            ) {
                                //
                            }

                            override fun onPageSelected(position: Int) {
                                //set background indicator tab
                                GeneralHelper.changeTabsFontBoldBackgroundRounded(
                                    this@DashboardLifestyleActivity,
                                    linearLayoutPromo,
                                    position,
                                    colorTextTabPromoDefault,
                                    colorTextTabPromoSelected,
                                    R.drawable.bg_blue_round_fill,
                                    R.drawable.bg_blue_round_fill,
                                    colorBgTabPromoDefault,
                                    colorBgTabPromoSelected
                                )
                            }

                            override fun onPageScrollStateChanged(state: Int) {
                                //
                            }

                        })
                    }

                }
            } else {
                //if promo content empty will be hide
                binding.llPromoLifestyle.visibility = View.GONE
            }
        }
    }

    //initiate menu
    @SuppressLint("NotifyDataSetChanged")
    private fun initMenu() {
        with(binding) {
            menuDataViews = mDashboardMenuResponse.menuDataView
            presenter.getMenuLifestyleLocal()

            //compare featurecode from BE and DB, and replace isnew in response be from db
            val featureDataLocals: MutableList<FeatureDataView> = ArrayList()
            for (i in mDashboardMenuResponse.menuDataView.indices) {
                featureDataLocals.addAll(mDashboardMenuResponse.menuDataView[i].feature)
            }

            //if menu content not empty it will visible
            if (mDashboardMenuResponse.thematicDataView != null) {
                if (menuDataViews.isNullOrEmpty() || menuDataViews.isEmpty()) {
                    //if menu content empty will be hide
                    llMenuLifestyle.visibility = View.GONE
                } else {
                    llMenuLifestyle.visibility = View.VISIBLE

                    if (menuDataViews.size <= 3) {
                        rvFilter.addOnItemTouchListener(object :
                            RecyclerView.SimpleOnItemTouchListener() {
                            override fun onInterceptTouchEvent(
                                rv: RecyclerView, e: MotionEvent
                            ): Boolean {
                                // Stop only scrolling.
                                return rv.scrollState == RecyclerView.SCROLL_STATE_DRAGGING
                            }
                        });
                    }

                    rvFilter.visibility = if (menuDataViews.size == 1) {
                        View.GONE
                    } else {
                        View.VISIBLE
                    }

                    rvFilter.layoutManager = LinearLayoutManager(
                        this@DashboardLifestyleActivity, RecyclerView.HORIZONTAL, false
                    )

                    categoryMenuLifestyleAdapter = CategoryMenuLifestyleAdapter(
                        menuDataViews.toMutableList(), this@DashboardLifestyleActivity
                    )
                    rvFilter.adapter = categoryMenuLifestyleAdapter
                    menuDataViews[0].isSelected = true
                    categoryMenuLifestyleAdapter?.notifyDataSetChanged()

                    //filter data and set to feature data variable
                    mFeatureDatas =
                        presenter.setFilteredMenu(menuDataViews[0].feature).toMutableList()

                    //send data menu
                    setDataMenuFour(mFeatureDatas)
                    setDataMenuAll(mFeatureDatas)

                    if (mFeatureDatas.size > 4) {
                        lyMenuMore.visibility = View.VISIBLE
                    } else {
                        rvMenuMore.visibility = View.GONE
                        lyMenuMore.visibility = View.GONE
                    }

                }
            }
        }
    }

    //initiate repurchase and best seller
    private fun initRepurchaseBestSeller(dashboardLifestyleSellingResponse: DashboardLifestyleSellingResponse) {
        sellingDataViews = dashboardLifestyleSellingResponse.beliLagiDataView

        //if data selling empty content will be hide
        if (sellingDataViews.isNullOrEmpty()) {
            binding.rlBeliLagiLifestyle.visibility = View.GONE
        } else {
            for (data in sellingDataViews.indices) {
                //if data selling empty content beli lagi paling laris will be hide
                if (sellingDataViews[data].dataSelling.isNullOrEmpty()) {
                    binding.rlBeliLagiLifestyle.visibility = View.GONE
                    //if data selling not empty content beli lagi paling laris will be visible
                } else {
                    binding.rlBeliLagiLifestyle.visibility = View.VISIBLE

                    //set title tab
                    titleTabRepurchase.clear()
                    dashboardLifestyleSellingResponse.beliLagiDataView.forEach {
                        titleTabRepurchase.add(it.titleTabSelling.toString())
                    }

                    //set content to viewpager
                    adapterRepurchases = FragmentRepurchaseAdapter(
                        supportFragmentManager, titleTabRepurchase, sellingDataViews
                    )
                    binding.vpBeliLagiLifestyle.adapter = adapterRepurchases
                    binding.stBeliLagiLifestyle.setViewPager(binding.vpBeliLagiLifestyle)//Add bold effect on selected tab
                    linearLayoutRepurchase =
                        binding.stBeliLagiLifestyle.getChildAt(0) as LinearLayout

                    //set background indicator color tab
                    GeneralHelper.changeTabsFontBoldBackgroundRounded(
                        this,
                        linearLayoutRepurchase,
                        0,
                        colorTextTabRepurchaseDefault,
                        colorTextTabRepurchaseSelected,
                        R.drawable.bg_blue_round_fill,
                        R.drawable.bg_blue_round_fill,
                        colorBgTabRepurchaseDefault,
                        colorBgTabRepurchaseSelected
                    )

                    binding.stBeliLagiLifestyle.setOnPageChangeListener(object :
                        ViewPager.OnPageChangeListener {
                        override fun onPageScrolled(
                            position: Int, positionOffset: Float, positionOffsetPixels: Int
                        ) {
                            //
                        }

                        override fun onPageSelected(position: Int) {
                            //set background indicator color tab
                            GeneralHelper.changeTabsFontBoldBackgroundRounded(
                                this@DashboardLifestyleActivity,
                                linearLayoutRepurchase,
                                position,
                                colorTextTabRepurchaseDefault,
                                colorTextTabRepurchaseSelected,
                                R.drawable.bg_blue_round_fill,
                                R.drawable.bg_blue_round_fill,
                                colorBgTabRepurchaseDefault,
                                colorBgTabRepurchaseSelected
                            )
                        }

                        override fun onPageScrollStateChanged(state: Int) {
                            //
                        }

                    })
                }
            }
        }

    }

    //initiate travel recomendation
    @SuppressLint("NotifyDataSetChanged")
    private fun initDestination(dashboardLifestyleInfoResponse: DashboardLifestyleInfoResponse) {
        //if recom travel not empty it will visible
        if (dashboardLifestyleInfoResponse.destinationDataView.destinationList.isNotEmpty()) {
            binding.tvLihatRekomWisata.visibility = View.VISIBLE
            binding.rvRekomWisata.visibility = View.VISIBLE
            binding.cvInjourney.visibility = View.GONE

            val destinationLists = ArrayList<DestinationList>()

            //set title, subtitle, and button recom travel
            binding.tvTitleRekomWisata.text =
                dashboardLifestyleInfoResponse.destinationDataView.title
            binding.tvSubtitleRekomWisata.text =
                dashboardLifestyleInfoResponse.destinationDataView.subtitle
            binding.tvLihatRekomWisata.text =
                dashboardLifestyleInfoResponse.destinationDataView.description

            //set recyclerview content
            binding.rvRekomWisata.layoutManager = LinearLayoutManager(
                this, LinearLayoutManager.HORIZONTAL, false
            )

            destinationLists.clear()
            destinationLists.addAll(dashboardLifestyleInfoResponse.destinationDataView.destinationList)

            //set content recom travel to adapter
            val destinationLifestyleAdapter = DestinationLifestyleAdapter(
                this, destinationLists, this
            )

            destinationLifestyleAdapter.setItems(destinationLists)
            destinationLifestyleAdapter.notifyDataSetChanged()
            binding.rvRekomWisata.adapter = destinationLifestyleAdapter
        } else {
            //if recom travel empty it will show injourney banner
            binding.tvLihatRekomWisata.visibility = View.GONE
            binding.rvRekomWisata.visibility = View.GONE
            binding.cvInjourney.visibility = View.VISIBLE
        }
    }

    //initiate digital loan
    @SuppressLint("NotifyDataSetChanged")
    private fun initDigitalLoan(dashboardLifestyleInfoResponse: DashboardLifestyleInfoResponse) {
        //if digital loan content not empty it will visible
        if (dashboardLifestyleInfoResponse.launcherDataView.crossellingList.isNotEmpty()) {
            val digitalLoanLists = ArrayList<CrossellingList>()

            //set title, subtitle digital loan content
            binding.tvTitleDigitalLoan.text = dashboardLifestyleInfoResponse.launcherDataView.title
            binding.tvSubtitleDigitalLoan.text =
                dashboardLifestyleInfoResponse.launcherDataView.subtitle

            //set the recylcerview
            binding.rvDigitalLoan.layoutManager = LinearLayoutManager(
                this, LinearLayoutManager.HORIZONTAL, false
            )

            digitalLoanLists.clear()
            digitalLoanLists.addAll(dashboardLifestyleInfoResponse.launcherDataView.crossellingList)

            //set content digital loan to adapter
            val digitalLoanLifestyleAdapter = DigitalLoanLifestyleAdapter(
                this, digitalLoanLists, this
            )

            digitalLoanLifestyleAdapter.setItems(digitalLoanLists)
            digitalLoanLifestyleAdapter.notifyDataSetChanged()
            binding.rvDigitalLoan.adapter = digitalLoanLifestyleAdapter

            //if size of list loan only 2 disable scroll of recycler view
            if (digitalLoanLists.size == 2) {
                binding.rvDigitalLoan.suppressLayout(true)
            } else {
                binding.rvDigitalLoan.suppressLayout(false)
            }
        } else {
            //if content digital loan empty will be hide
            binding.llDigitalLoan.visibility = View.GONE
        }
    }

    //initiate badge trx pending
    @SuppressLint("SetTextI18n")
    override fun onSuccessGetBadgeTrx(dashboardLifestyleBadgeTrxResponse: DashboardLifestyleBadgeTrxResponse) {
        skeletonHistory?.hide()
        binding.srlDashboardLifestyle.isRefreshing = false

        badgeTrx = dashboardLifestyleBadgeTrxResponse.pendingTrx

        //if trx pending not null or 0 will be visible, if trx pending > 99 will be set as 99+
        if (badgeTrx != null && badgeTrx != 0) {
            binding.flBadgeTrx.visibility = View.VISIBLE

            if ((badgeTrx ?: 0) > 99) {
                binding.tvBagdeTrx.text = GeneralHelper.getString(R.string.trx99)
            } else {
                binding.tvBagdeTrx.text = badgeTrx.toString()
                binding.tvBagdeTrx.gravity = Gravity.CENTER_VERTICAL
            }
        } else {
            //if trx pending empty will be hide
            binding.flBadgeTrx.visibility = View.GONE
        }
    }

    override fun onSuccessGetDashboardInfo(dashboardLifestyleInfoResponse: DashboardLifestyleInfoResponse) {
        skeletonTabPromo?.hide()
        skeletonItemPromo?.hide()
        skeletonInjourney?.hide()
        skeletonDigitalLoan?.hide()
        binding.srlDashboardLifestyle.isRefreshing = false

        initPromo(dashboardLifestyleInfoResponse)
        initDestination(dashboardLifestyleInfoResponse)
        initDigitalLoan(dashboardLifestyleInfoResponse)
    }

    override fun onSuccessGetDashboardSelling(dashboardLifestyleSellingResponse: DashboardLifestyleSellingResponse) {
        skeletonTabRepurchase?.hide()
        skeletonItemRepurchase?.hide()
        binding.srlDashboardLifestyle.isRefreshing = false

        initRepurchaseBestSeller(dashboardLifestyleSellingResponse)
    }

    override fun onSuccessGetWebviewTugu(
        generalWebviewResponse: GeneralWebviewResponse, mTitle: String, mCodeMenu: String
    ) {
        when (mCodeMenu) {
            MenuLifestyleCode.MENU_KERETA_API.menuCode, MenuLifestyleCode.MENU_PESAWAT.menuCode -> {
                WebViewTravelActivity.launchIntent(
                    this,
                    generalWebviewResponse.webviewData.url,
                    generalWebviewResponse.sessionId,
                    mCodeMenu,
                    mTitle
                )
            }

            LifestyleConfig.MenuLifestyleCode.MENU_MOKIRIM.menuCode -> {
                WebviewEkspedisiActivity.launchIntent(
                    this,
                    generalWebviewResponse.webviewData.url,
                    GeneralHelper.getString(R.string.txt_kirim_barang),
                    LifestyleConfig.Lifestyle.KIRIM_BARANG,
                    GeneralHelper.getString(R.string.flag_url_brimo),
                    generalWebviewResponse.sessionId
                )
            }

            else -> {
                WebviewLifestyleActivity.launchIntent(
                    this,
                    generalWebviewResponse.webviewData.url,
                    generalWebviewResponse.sessionId,
                    mTitle,
                    "",
                    "",
                    mCodeMenu,
                    generalWebviewResponse.webviewData.postData
                )
            }
        }
    }

    override fun onSuccessGetFormBus(cityFormResponse: CityFormResponse) {
        FormBusActivity.launchIntent(this, cityFormResponse)
    }

    override fun onSuccessGetFormKai(urlWebViewResponse: UrlWebViewResponse, mTitle: String) {
        WebViewTravelActivity.launchIntent(
            this,
            urlWebViewResponse.url,
            urlWebViewResponse.sessionId,
            Constant.TravelMenu.TRAVEL_KAI,
            mTitle
        )
    }

    override fun onClick(p0: View?) {
        when (p0?.id) {
            R.id.tv_go_top -> {
                binding.nsView.smoothScrollTo(0, 0)
            }

            R.id.tv_tag_lokasi -> {
                val dialogExitCustom = DialogExitCustom(
                    this,
                    GeneralHelper.getString(R.string.grant_access_location_title),
                    GeneralHelper.getString(R.string.grant_access_location_subtitle),
                    GeneralHelper.getString(R.string.txt_btn_no),
                    GeneralHelper.getString(R.string.txt_btn_setting),
                    true
                )
                val ft = this.supportFragmentManager.beginTransaction()
                ft.add(dialogExitCustom, null)
                ft.commitAllowingStateLoss()
            }

            R.id.tv_lihat_rekom_wisata -> {
                presenter.getWebViewTugu(
                    PartnerIdRequest(LifestyleConfig.MenuLifestyleId.INJOURNEY_ID),
                    GeneralHelper.getString(R.string.title_injourney),
                    MenuLifestyleCode.MENU_ELSE.menuCode
                )
            }

            R.id.rl_trx_lifestyle -> {
                // intent to transaksi saya ui.
                MyLifestyleTransactionActivity.launchIntent(this, badgeTrx ?: 0, true)
            }

            R.id.ly_menu_more -> {
                toggleExpandMenu()
            }
        }
    }

    override fun onRequestPermissionsResult(
        requestCode: Int, permissions: Array<String>, grantResults: IntArray
    ) {
        super.onRequestPermissionsResult(requestCode, permissions, grantResults)
        if (requestCode == REQUEST_LOCATION) {
            if (grantResults.isNotEmpty() && grantResults[0] == PackageManager.PERMISSION_GRANTED) {
                getLocation()
            } else if (grantResults.isNotEmpty() && grantResults[0] == PackageManager.PERMISSION_DENIED) {
                if (!networkEnabled) {
                    setLocationPermDenied(GeneralHelper.getString(R.string.enable_internet))
                } else {
                    setLocationPermDenied(GeneralHelper.getString(R.string.enable_location))
                }
            }
        }
    }

    @RequiresApi(Build.VERSION_CODES.Q)
    @Deprecated("Deprecated in Java")
    override fun onActivityResult(requestCode: Int, resultCode: Int, data: Intent?) {
        super.onActivityResult(requestCode, resultCode, data)
        if (requestCode == Constant.REQ_PAYMENT) {
            if (resultCode == RESULT_OK) {
                this.setResult(RESULT_OK, data)
                finish()
            } else {
                if (data != null) {
                    if (data.getStringExtra(Constant.TAG_ERROR_MESSAGE) != null) {
                        showSnackbarErrorMessageRevamp(
                            data.getStringExtra(Constant.TAG_ERROR_MESSAGE),
                            ALERT_ERROR,
                            this,
                            false
                        )
                    } else {
                        this.setResult(RESULT_CANCELED, data)
                    }
                } else {
                    this.setResult(RESULT_CANCELED)
                }
            }
        } else if (requestCode == REQUEST_CODE_LOCATION) {
            getLocation()
        }
    }

    override fun onLocationChanged(location: Location) {
        getLocation()
    }

    override fun onDestroy() {
        super.onDestroy()
        gpsTracker?.stopUsingGPS()
        for (i in menuDataViews.indices) {
            menuDataViews[i].isExpand = false
        }
    }

    override fun onClickBtnYes() {
        val builder = CustomTabsIntent.Builder()
        val customTabsIntent = builder.build()
        customTabsIntent.intent.action = Settings.ACTION_APPLICATION_DETAILS_SETTINGS
        customTabsIntent.intent.data = Uri.fromParts("package", packageName, null)
        customTabsIntent.intent.addFlags(Intent.FLAG_ACTIVITY_CLEAR_TOP)
        startActivityForResult(customTabsIntent.intent, REQUEST_CODE_LOCATION)
    }

    override fun onClickBtnNo() {
        //do nothing
    }

    override fun onClickDestinationItem(destinationList: DestinationList) {
        presenter.getWebViewTugu(
            PartnerIdRequest(destinationList.partnerId),
            GeneralHelper.getString(R.string.title_injourney),
            MenuLifestyleCode.MENU_ELSE.menuCode
        )
    }

    override fun onClickDigitalLoanItem(crossellingList: CrossellingList) {
        when (val partnerId = crossellingList.partnerId) {
            LifestyleConfig.MenuLifestyleId.CERIA_ID -> {
                CeriaActivity.launchIntent(this)
            }

            LifestyleConfig.MenuLifestyleId.BRIGUNA_ID -> {
                BrigunaDigitalActivity.launchIntent(this)
            }

            else -> {
                presenter.getWebViewTugu(
                    PartnerIdRequest(partnerId), "", MenuLifestyleCode.MENU_ELSE.menuCode
                )
            }
        }
    }

    override fun onException93(desc: String?) {
        val returnIntent = Intent()
        returnIntent.putExtra(Constant.TAG_ERROR_MESSAGE, desc)
        this.setResult(RESULT_CANCELED, returnIntent)
        finish()
    }

    override fun onExceptionIgnore(desc: String) {
        binding.srlDashboardLifestyle.isRefreshing = false

        //before hide skeleton must be add delay
        Handler(Looper.getMainLooper()).postDelayed({
            skeletonTabPromo?.hide()
            skeletonItemPromo?.hide()
            skeletonHistory?.hide()
            skeletonInjourney?.hide()
            skeletonTabRepurchase?.hide()
            skeletonItemRepurchase?.hide()
            skeletonDigitalLoan?.hide()
        }, 1000)
    }

    override fun onExceptionBadgeTrx(desc: String) {
        binding.srlDashboardLifestyle.isRefreshing = false

        //before hide skeleton must be add delay
        Handler(Looper.getMainLooper()).postDelayed({
            skeletonHistory?.hide()
        }, 1000)
    }

    override fun onExceptionInformation(desc: String) {
        binding.srlDashboardLifestyle.isRefreshing = false
        //hide section promo, digital loan, recyclerview rekom wisata and show card injourney
        binding.llPromoLifestyle.visibility = View.GONE
        binding.rvRekomWisata.visibility = View.GONE
        binding.cvInjourney.visibility = View.VISIBLE
        binding.llDigitalLoan.visibility = View.GONE

        //before hide skeleton must be add delay
        Handler(Looper.getMainLooper()).postDelayed({
            skeletonTabPromo?.hide()
            skeletonItemPromo?.hide()
            skeletonInjourney?.hide()
            skeletonDigitalLoan?.hide()
        }, 1000)
    }

    override fun onExceptionSelling(desc: String) {
        binding.srlDashboardLifestyle.isRefreshing = false
        binding.rlBeliLagiLifestyle.visibility = View.GONE

        //before hide skeleton must be add delay
        Handler(Looper.getMainLooper()).postDelayed({
            skeletonTabRepurchase?.hide()
            skeletonItemRepurchase?.hide()
        }, 1000)
    }

    @SuppressLint("NotifyDataSetChanged")
    override fun onSuccessGetMenuLocals(menuLifestyle: List<MenuLifestyle>) {
        menuLifestyleLocals?.toMutableList()?.clear()
        menuLifestyleLocals = presenter.setFilteredMenuNew(menuLifestyle)

        //set menu local for first time open dashboard and refresh the adapter
        menuLifestyleLocals?.let {
            menuLifestyleAdapter?.setMenuLifestyleLocal(it)
            menuLifestyleMoreAdapter?.setMenuLifestyleLocal(it)
        }
        menuLifestyleAdapter?.notifyDataSetChanged()
        menuLifestyleMoreAdapter?.notifyDataSetChanged()
    }

    override fun onMenuLifestyleEOD(exceptionCase02: EODLifestyleResponse) {
        dialog = DialogEODLifestyle.newInstance(
            this, exceptionCase02, GeneralHelper.getString(R.string.img_time_out)
        )
        dialog?.show(supportFragmentManager, "")
    }

    override fun onClickDialogEOD() {
        dialog?.dismiss()
    }

    override fun onRefresh() {
        refreshDashboard()
    }

    private fun refreshDashboard() {
        getLocation()
        binding.llPromoLifestyle.visibility = View.VISIBLE
        binding.rlBeliLagiLifestyle.visibility = View.VISIBLE
        binding.llDigitalLoan.visibility = View.VISIBLE

        initSkeleton()
        presenter.getBadgeTrx()
        presenter.getDashboardInformation()
        presenter.getDashboardSelling()
    }

    override fun onResume() {
        super.onResume()
        networkEnabled = isOnline(this)
    }

    //to check this device turn on wifi/data to access internet
    private fun isOnline(context: Context): Boolean {
        val connectivityManager =
            context.getSystemService(Context.CONNECTIVITY_SERVICE) as ConnectivityManager
        val network = connectivityManager.activeNetwork ?: return false
        val capabilities = connectivityManager.getNetworkCapabilities(network) ?: return false

        return capabilities.hasCapability(NetworkCapabilities.NET_CAPABILITY_VALIDATED)
    }

    override fun onException(message: String) {
        if (GeneralHelper.isContains(Constant.LIST_TYPE_GAGAL, message)) {
            GeneralHelper.showBottomDialog(this, message)
        } else {
            showSnackbarErrorMessage(message, ALERT_ERROR, this, false)
        }
    }

    @SuppressLint("NotifyDataSetChanged")
    override fun onClickCategory(menuModels: MenuDataView, position: Int) {
        //check if position same, do not refresh
        if (posCategory != position) {
            menuModels.isSelected = true
            mFeatureDatas.clear()
            mFeatureDatas = presenter.setFilteredMenu(menuModels.feature).toMutableList()
            setDataMenuFour(mFeatureDatas)
            setDataMenuAll(mFeatureDatas)
            posCategory = position
            categoryMenuLifestyleAdapter?.notifyDataSetChanged()

            if (mFeatureDatas.size > 4) {
                binding.lyMenuMore.visibility = View.VISIBLE
            } else {
                binding.rvMenuMore.visibility = View.GONE
                binding.lyMenuMore.visibility = View.GONE
            }

            with(binding) {
                if (menuModels.isExpand) {
                    rvMenuMore.visibility = View.VISIBLE
                    AnimationExpand.expand(rvMenuMore)
                    ivMore.rotation = 180f
                    tvMore.text = GeneralHelper.getString(R.string.text_kategori_tutup)
                } else {
                    rvMenuMore.visibility = View.GONE
                    AnimationExpand.collapse(rvMenuMore)
                    ivMore.rotation = 0f
                    tvMore.text = GeneralHelper.getString(R.string.txt_lainnya)
                }
            }
        } else {
            //do nothing
        }
    }

    @SuppressLint("NotifyDataSetChanged")
    private fun setDataMenuFour(dataMenus: List<FeatureDataView>) {
        with(binding) {
            //RV DATA MENU
            rvDataMenu.layoutManager = GridLayoutManager(
                this@DashboardLifestyleActivity, 4, GridLayoutManager.VERTICAL, false
            )

            //check if list menu > 4 will be show only 4 index from total in list
            newMenus = presenter.setFourMenu(dataMenus, 0, 4).toMutableList()
            newMenus.let {
                menuLifestyleAdapter = MenuLifestyleAdapter(
                    this@DashboardLifestyleActivity, it, this@DashboardLifestyleActivity
                )
            }

            rvDataMenu.setHasFixedSize(true)
            rvDataMenu.adapter = menuLifestyleAdapter
            menuLifestyleLocals?.let { menuLifestyleAdapter?.setMenuLifestyleLocal(it) }
            menuLifestyleAdapter?.notifyDataSetChanged()
        }
    }

    @SuppressLint("NotifyDataSetChanged")
    private fun setDataMenuAll(dataMenus: List<FeatureDataView>) {
        with(binding) {
            //RV DATA MENU MORE
            rvMenuMore.layoutManager = GridLayoutManager(
                this@DashboardLifestyleActivity, 4, GridLayoutManager.VERTICAL, false
            )

            //check if list menu > 4 will be show all menu from index 4 to last index in list
            if (dataMenus.size > 4) {
                newMenus = presenter.setFourMenu(dataMenus, 4, dataMenus.size).toMutableList()
                menuLifestyleMoreAdapter = MenuLifestyleAdapter(
                    this@DashboardLifestyleActivity, newMenus, this@DashboardLifestyleActivity
                )

                rvMenuMore.setHasFixedSize(true)
                rvMenuMore.adapter = menuLifestyleMoreAdapter
                menuLifestyleLocals?.let { menuLifestyleMoreAdapter?.setMenuLifestyleLocal(it) }
                menuLifestyleMoreAdapter?.notifyDataSetChanged()
            }
        }
    }

    private fun toggleExpandMenu() {
        with(binding) {
            if (!menuDataViews[posCategory].isExpand) {
                AnimationExpand.expand(rvMenuMore)
                ivMore.animate().rotation(180f).start()
                tvMore.text = GeneralHelper.getString(R.string.text_kategori_tutup)
                menuDataViews[posCategory].isExpand = true
            } else {
                AnimationExpand.collapse(rvMenuMore)
                ivMore.animate().rotation(0f).start()
                tvMore.text = GeneralHelper.getString(R.string.text_kategori_lainnya)
                menuDataViews[posCategory].isExpand = false
            }
        }
    }

    override fun onItemMenuClicked(mMenuDataLists: List<FeatureDataView>, position: Int) {
        val dataMenus = mMenuDataLists[position]
        val status = dataMenus.status
        val statusMenu = LifestyleConfig.StatusMenu.values().find { it.statusMenu == status }
        val codeMenu = dataMenus.featureCode
        val partnerId = dataMenus.partnerId

        when (statusMenu) {
            LifestyleConfig.StatusMenu.MENU_TEMPORARY -> {
                OpenBottomSheetGeneralFragment.showDialogInformation(
                    fragmentManager = supportFragmentManager,
                    imgPath = "",
                    imgName = "img_liveness_general_error",
                    titleTxt = GeneralHelper.getString(R.string.txt_title_temporary_lifestyle),
                    subTitleTxt = GeneralHelper.getString(R.string.txt_desc_temporary_lifestyle),
                    btnFirstFunction = {},
                    isClickableOutside = false,
                    firstBtnTxt = getString(R.string.ok)
                )
            }

            LifestyleConfig.StatusMenu.MENU_ACTIVE -> {
                if (!dataMenus.subFeature.isNullOrEmpty()) {
                    val listFeature = presenter.setFilteredMenu(dataMenus.subFeature)
                    val submenuFragment = SubMenuLifestyleFragment.newInstance(
                        listFeature
                    )
                    val ft = this.supportFragmentManager.beginTransaction()
                    ft.add(submenuFragment, "")
                    ft.commitAllowingStateLoss()
                } else {
                    when (MenuLifestyleCode.values().find {
                        it.menuCode == codeMenu
                    }) {
                        MenuLifestyleCode.MENU_BUS_SHUTTLE -> {
                            presenter.getFormBus()
                        }

                        MenuLifestyleCode.MENU_KERETA_API -> {
                            presenter.getFormKai(
                                dataMenus.featureName
                            )
                        }

                        MenuLifestyleCode.MENU_VOUCHER_GAME -> {
                            VoucherActivity.launchIntent(
                                this, Constant.Voucher.GAME.name
                            )
                        }

                        MenuLifestyleCode.MENU_VOUCHER_STREAMING -> {
                            VoucherActivity.launchIntent(
                                this, Constant.Voucher.STREAMING.name
                            )
                        }

                        MenuLifestyleCode.MENU_INDIHOME -> {
                            val permissions = permissionsCheck()
                            if (!BaseFragment.hasPermissions(this, *permissions)) {
                                ActivityCompat.requestPermissions(this, permissions, 1)
                            } else {
                                presenter.getIndihomeRegistrationData(
                                    selectedMenu = dataMenus
                                )
                            }
                        }

                        else -> {
                            presenter.getWebViewTugu(
                                PartnerIdRequest(partnerId), dataMenus.featureName, codeMenu
                            )
                        }
                    }
                }
            }

            else -> {}
        }
    }

    override fun onUpdateFlagNewMenu(featureCode: String) {
        presenter.getNewMenubyParentCode(featureCode)
    }

    override fun showIndihomeConfirmation(selectedMenu: FeatureDataView) {
        val dialogFragment = DialogSetDefaultRevamp(
            object : DialogSetDefaultRevamp.DialogDefaultListener {
                override fun onClickYesDefault(requestId: Int) {
                    presenter.confirmIndihomeRegistration(selectedMenu)
                }

                override fun onClickNoDefault(requestId: Int) {}
            },
            GeneralHelper.getString(R.string.txt_dialog_title_indihome_registration),
            GeneralHelper.getString(R.string.txt_dialog_description_indihome_registration),
            GeneralHelper.getString(R.string.ya_lanjutkan),
            GeneralHelper.getString(R.string.batal),
            0
        )
        dialogFragment.show(supportFragmentManager, DialogSetDefaultRevamp.TAG)
    }

    private fun permissionsCheck(): Array<String> {
        val permission: Array<String> = if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU) {
            permissionsIndiHome33
        } else {
            permissionsIndiHome
        }
        return permission
    }

}