package id.co.bri.brimo.ui.activities.cc_sof

import android.app.Activity
import android.content.Intent
import android.os.Bundle
import android.os.CountDownTimer
import android.view.KeyEvent
import android.view.View
import androidx.core.content.ContextCompat
import androidx.core.text.HtmlCompat
import androidx.recyclerview.widget.GridLayoutManager
import id.co.bri.brimo.R
import id.co.bri.brimo.adapters.PinNumberAdapter
import id.co.bri.brimo.adapters.baseadapter.base.BasePinAdapter
import id.co.bri.brimo.adapters.pinadapter.OtpRevampAdapter
import id.co.bri.brimo.contract.IPresenter.cc_sof.IVerifikasiOtpBindingCcPresenter
import id.co.bri.brimo.contract.IView.cc_sof.IVerifikasiOtpBindingCcView
import id.co.bri.brimo.databinding.ActivityVerifikasiOtpBindingCcBinding
import id.co.bri.brimo.domain.config.Constant
import id.co.bri.brimo.domain.helpers.GeneralHelper
import id.co.bri.brimo.models.apimodel.request.bindingcc.Code2Request
import id.co.bri.brimo.models.apimodel.request.bindingcc.MethodRequest
import id.co.bri.brimo.models.apimodel.response.bindingcc.CcOtpResponse
import id.co.bri.brimo.ui.activities.base.BaseActivity
import id.co.bri.brimo.ui.customviews.pin.InsertPinNumbers
import id.co.bri.brimo.ui.fragments.registrasi.BottomDialogKirimUlangFragment
import javax.inject.Inject


class VerifikasiOtpBindingCcActivity : BaseActivity(),
    IVerifikasiOtpBindingCcView,
    View.OnKeyListener,
    PinNumberAdapter.OnPinNumberListener,
    BasePinAdapter.PinAdapterListener,
    BottomDialogKirimUlangFragment.OnBackPressUlangi {

    @Inject
    lateinit var presenter: IVerifikasiOtpBindingCcPresenter<IVerifikasiOtpBindingCcView>

    private lateinit var binding: ActivityVerifikasiOtpBindingCcBinding
    private var countDownTimer: CountDownTimer? = null
    private val second = 1000
    private var methodCheck: String? = null
    private lateinit var otpRevampAdapter: OtpRevampAdapter
    private lateinit var pinNumberAdapter: PinNumberAdapter
    private lateinit var pinOtpLayoutManager: GridLayoutManager
    private lateinit var pinPadLayoutManager: GridLayoutManager

    companion object {
        private lateinit var otpResponse: CcOtpResponse
        fun launchIntent(caller: Activity, response: CcOtpResponse) {
            val intent = Intent(caller, VerifikasiOtpBindingCcActivity::class.java)
            otpResponse = response
            caller.startActivityForResult(intent, Constant.REQ_UPDATE)
        }
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)

        binding = ActivityVerifikasiOtpBindingCcBinding.inflate(layoutInflater)
        setContentView(binding.root)

        injectDependency()
        setupView()
    }

    private fun injectDependency() {
        activityComponent.inject(this)
        presenter.view = this
        presenter.start()
        presenter.setUrlResend(GeneralHelper.getString(R.string.url_cc_binding_resend_otp))
        presenter.setUrlSend(GeneralHelper.getString(R.string.url_cc_binding_validate_otp))
    }

    private fun setupView() {
        GeneralHelper.setToolbarRevamp(
            this,
            binding.toolbarRevamp.toolbar,
            GeneralHelper.getString(R.string.konfirmasi_otp)
        )

        checkWaOrSms(otpResponse.method)

        otpRevampAdapter = OtpRevampAdapter(this, 1)
        pinNumberAdapter =
            PinNumberAdapter(InsertPinNumbers.getPinNumberList(this))
        pinOtpLayoutManager = GridLayoutManager(this, 6)
        pinPadLayoutManager = GridLayoutManager(this, 3)

        pinNumberAdapter.onPinNumberListener = this
        otpRevampAdapter.setListener(this)
        binding.rvBox.layoutManager = pinOtpLayoutManager
        binding.rvBox.adapter = otpRevampAdapter
        binding.rvInputOtp.layoutManager = pinPadLayoutManager
        binding.rvInputOtp.adapter = pinNumberAdapter

        setTextTimer(otpResponse.expiredInSecond)

        binding.tvUlangi.setOnClickListener {
            bottomSheetResending()
        }
    }

    private fun bottomSheetResending() {
        val dialogFragment = BottomDialogKirimUlangFragment(this)
        dialogFragment.show(supportFragmentManager, "")
        dialogFragment.isCancelable = true
    }

    private fun setTextTimer(timer: Int) {
        val countDown: Int = second * timer
        countDownTimer = object : CountDownTimer(countDown.toLong(), second.toLong()) {
            override fun onTick(millisUntilFinished: Long) {
                val seconds: Int = millisUntilFinished.toInt() / second
                val timeFormat = GeneralHelper.getTimeFormat(seconds)
                binding.tvTimer.text = String.format(
                    resources.getString(R.string.countdown_otp00_00),
                    timeFormat[1], timeFormat[2]
                )

                binding.tvUlangi.setTextColor(
                    ContextCompat.getColor(
                        applicationContext,
                        R.color.neutral_light50
                    )
                )
                binding.tvUlangi.isEnabled = false
            }

            override fun onFinish() {
                binding.tvTimer.text = GeneralHelper.getString(R.string.time00_00)
                binding.tvUlangi.setTextColor(
                    ContextCompat.getColor(
                        applicationContext,
                        R.color.neutral_light10
                    )
                )
                binding.tvUlangi.isEnabled = true
            }
        }.start()
    }


    private fun checkWaOrSms(method: String) {
        if (method == "WA") {
            binding.tvDescOtp.text = HtmlCompat.fromHtml(
                String.format(
                    GeneralHelper.getString(R.string.desc_regis_otp_wa),
                    otpResponse.cellphoneNumber
                ), HtmlCompat.FROM_HTML_MODE_LEGACY
            )
        } else {
            binding.tvDescOtp.text = HtmlCompat.fromHtml(
                String.format(
                    GeneralHelper.getString(R.string.desc_regis_otp_sms),
                    otpResponse.cellphoneNumber
                ), HtmlCompat.FROM_HTML_MODE_LEGACY
            )
        }
    }

    override fun onKey(v: View?, keyCode: Int, event: KeyEvent?): Boolean {
        return false
    }

    override fun onPinClicked(pinNumber: Int) {
        otpRevampAdapter.addPin(pinNumber.toString())
    }

    override fun onDeleteClicked() {
        otpRevampAdapter.deletePin()
    }

    override fun notifyChanges() {
        otpRevampAdapter.notifyDataSetChanged()
    }

    override fun onComplete(string: String) {
        presenter.sendSendOtp(Code2Request(string))
    }

    override fun itemResending(method: String) {
        methodCheck = method
        presenter.sendResendOtp(MethodRequest(method))
    }

    override fun deletePin() {
        otpRevampAdapter.deleteAllPin()
    }

    override fun onSuccessGetResend(response: CcOtpResponse) {
        if (countDownTimer != null)
            countDownTimer!!.cancel()
        otpResponse = response
        checkWaOrSms(otpResponse.method)
        setTextTimer(otpResponse.expiredInSecond)
    }

    override fun onSuccessSend() {
        val intent = Intent()
        setResult(RESULT_OK, intent)
        finish()
    }

    override fun onDestroy() {
        presenter.stop()
        super.onDestroy()
    }
}