package id.co.bri.brimo.ui.activities.asuransirevamp

import android.app.Activity
import android.content.Intent
import android.os.Bundle
import android.view.View
import id.co.bri.brimo.databinding.ActivityPanduanClaimBinding
import id.co.bri.brimo.domain.config.Constant
import id.co.bri.brimo.domain.helpers.GeneralHelper
import id.co.bri.brimo.ui.activities.PilihAtmActivity
import id.co.bri.brimo.ui.activities.PilihanKantorTerdekatActivity
import id.co.bri.brimo.ui.activities.base.BaseActivity

class PanduanClaimAsuransiActivity : BaseActivity(), View.OnClickListener {
    lateinit var binding : ActivityPanduanClaimBinding

    companion object{
        lateinit var mHtml : String
        fun launchIntent(caller: Activity, html: String) {
            val intent = Intent(caller, PanduanClaimAsuransiActivity::class.java)
            mHtml = html
            caller.startActivityFor<PERSON><PERSON>ult(intent, Constant.REQ_PAYMENT)
        }
    }
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        binding = ActivityPanduanClaimBinding.inflate(layoutInflater)
        setContentView(binding.root)
        GeneralHelper.setToolbarRevamp(this, binding.toolbar.toolbar,"Panduan Claim")
        GeneralHelper.setWebViewStandart(binding.wvSyarat, "", mHtml)
        binding.rlPanduan.setOnClickListener(this)
    }

    override fun onClick(p0: View?) {
        PilihanKantorTerdekatActivity.launchIntent(this)
    }
}