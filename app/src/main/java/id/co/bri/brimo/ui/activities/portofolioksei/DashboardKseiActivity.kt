package id.co.bri.brimo.ui.activities.portofolioksei

import android.annotation.SuppressLint
import android.app.Activity
import android.content.Intent
import android.os.Bundle
import android.view.View
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.swiperefreshlayout.widget.SwipeRefreshLayout.OnRefreshListener
import com.ethanhua.skeleton.Skeleton
import com.ethanhua.skeleton.SkeletonScreen
import id.co.bri.brimo.R
import id.co.bri.brimo.adapters.portofolioksei.ItemAssetAdapter
import id.co.bri.brimo.contract.IPresenter.portoksei.IPortofolioKseiPresenter
import id.co.bri.brimo.contract.IView.portoksei.IPortofolioKseiView
import id.co.bri.brimo.data.preference.BRImoPrefRepository
import id.co.bri.brimo.databinding.ActivityDaashboardKseiBinding
import id.co.bri.brimo.domain.config.Constant
import id.co.bri.brimo.domain.helpers.GeneralHelper
import id.co.bri.brimo.models.apimodel.request.portofolioksei.GetDetailKseiRequest
import id.co.bri.brimo.models.apimodel.response.MessageResponse
import id.co.bri.brimo.models.apimodel.response.portofolioksei.Asset
import id.co.bri.brimo.models.apimodel.response.portofolioksei.DetailAssetResponse
import id.co.bri.brimo.models.apimodel.response.portofolioksei.ItemAssetResponse
import id.co.bri.brimo.models.apimodel.response.portofolioksei.RegistrasiResponse
import id.co.bri.brimo.ui.activities.base.BaseActivity
import id.co.bri.brimo.ui.customviews.dialog.DialogWithImageCustom
import id.co.bri.brimo.ui.fragments.BottomFragmentNoImage
import id.co.bri.brimo.ui.fragments.BottomFragmentNoImageRevamp
import id.co.bri.brimo.ui.fragments.FragmentBottomDialog
import id.co.bri.brimo.ui.fragments.FragmentDialogNoImageRevamp
import id.co.bri.brimo.ui.fragments.rdn.CustomBottomDialogFragment
import javax.inject.Inject


class DashboardKseiActivity : BaseActivity(), ItemAssetAdapter.OnItemCheckListener,
IPortofolioKseiView, View.OnClickListener, FragmentDialogNoImageRevamp.DialogDefaultListener,DialogWithImageCustom.DialogDefaultListener, CustomBottomDialogFragment.DialogDefaultListener, OnRefreshListener,BottomFragmentNoImageRevamp.OnCallBackBottomInfoImage {

    lateinit var binding : ActivityDaashboardKseiBinding
    private var skeletonScreen: SkeletonScreen? = null
    private var skeletonScreen1: SkeletonScreen? = null
    private var skeletonScreen2: SkeletonScreen? = null
    private var skeletonScreen3: SkeletonScreen? = null
    private var dataMaster: ItemAssetResponse? = null
    private var adapter: ItemAssetAdapter? = null
    private var title=""
    private var mType: String? = null

    var list: ArrayList<Asset> = ArrayList()
    var customBottomDialogFragment: CustomBottomDialogFragment? = null
    private var brImoPrefRepository: BRImoPrefRepository? = BRImoPrefRepository(this)

    @Inject
    lateinit var presenter : IPortofolioKseiPresenter<IPortofolioKseiView>

    companion object{
        lateinit var mResponse : RegistrasiResponse
        var mFromBoolean: Boolean? = false
        @JvmStatic
        fun launchIntent(caller : Activity,fromBoolean: Boolean ){
            val intent = Intent(caller, DashboardKseiActivity::class.java)
            mFromBoolean = fromBoolean
            caller.startActivityForResult(intent, Constant.REQ_PAYMENT)
        }

        @JvmStatic
        fun launchIntentRegis(caller : Activity, response: RegistrasiResponse, fromBoolean: Boolean){
            val intent = Intent(caller, DashboardKseiActivity::class.java)
            mResponse = response
            mFromBoolean = fromBoolean
            caller.startActivityForResult(intent, Constant.REQ_PAYMENT)
        }
    }
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        binding = ActivityDaashboardKseiBinding.inflate(layoutInflater)
        setContentView(binding.root)

        if (mFromBoolean!!){
            val dialogWithImageCustom = DialogWithImageCustom(this, mResponse.onboarding!!.title, mResponse.onboarding!!.desc, mResponse.onboarding!!.imgPath, "Jelajahi", true)
            val ft = supportFragmentManager.beginTransaction()
            ft.add(dialogWithImageCustom, null)
            ft.commitAllowingStateLoss()
        }else{
            injectDependecy()

            setupView()
        }


    }

    private fun injectDependecy() {
        activityComponent.inject(this)
        presenter.view = this
        presenter.setUrlPortofolio(GeneralHelper.getString(R.string.url_porto_ksei))
        presenter.setUrlDetail(GeneralHelper.getString(R.string.url_detail_asset))
        presenter.setUrlRegisKsei(GeneralHelper.getString(R.string.url_regis_ksei))
        presenter.start()
        presenter.getRegisKsei()

    }

    fun setupView(){
        GeneralHelper.setToolbar(this, binding.toolbar.toolbar, "Portofolio KSEI")
        binding.rcAsset.layoutManager = LinearLayoutManager(this)
        adapter = ItemAssetAdapter(this, list, this)

        binding.lySid.setOnClickListener(this)
        binding.swipeRefresh.setOnRefreshListener(this)
        setSkeletonView()
    }

    fun setSkeletonView() {
        binding.lySid.isEnabled = false
        skeletonScreen = Skeleton.bind(binding.tvSid)
                .shimmer(true)
                .angle(20)
                .duration(1200)
                .load(R.layout.item_skeleton_single)
                .show()
        skeletonScreen1 = Skeleton.bind(binding.ptSumber)
                .shimmer(true)
                .angle(20)
                .duration(1200)
                .load(R.layout.item_skeleton_single)
                .show()
        skeletonScreen2 = Skeleton.bind(binding.rcAsset)
                .adapter(adapter)
                .shimmer(true)
                .count(5)
                .angle(5)
                .frozen(false)
                .duration(1200)
                .load(R.layout.item_skeleton_porto_ksei)
                .show()

    }

    fun hideSkeleton(){
        skeletonScreen!!.hide()
        skeletonScreen1!!.hide()
        skeletonScreen2!!.hide()
    }

    override fun onItemCheck(position: Int) {
        title = dataMaster!!.asset[position].nameAset!!
        mType = dataMaster!!.asset[position].type
        presenter.getDataDetail(GetDetailKseiRequest(dataMaster!!.asset[position].type))
    }

    @SuppressLint("NotifyDataSetChanged")
    override fun onSuccessGetData(response: ItemAssetResponse) {
        hideSkeleton()
        binding.swipeRefresh.setRefreshing(false)
        dataMaster = response
        adapter!!.updateData(dataMaster!!.asset)
        adapter!!.notifyDataSetChanged()

        binding.tvSid.text = dataMaster!!.sid
        binding.ptSumber.text = dataMaster!!.goldBalance!!.kustodianInfo

        binding.rcAsset.adapter = adapter
        binding.lySid.isEnabled = true
    }

    override fun onSuccessGetDetail(response: DetailAssetResponse) {
        DetailKseiWithTabActivity.launchIntent(this, response, title, mType!!)
    }

    override fun onSuccessRegisKsei(response: RegistrasiResponse, idString: String) {
        RegistrasiKseiActivity.launchIntent(this, response, false,idString)
    }

    override fun onSuccessBottomDrawer(response: RegistrasiResponse, idString : String) {
        RegistrasiKseiActivity.launchIntent(this, response,true, idString)
    }

    override fun onSucessDashboard() {
        presenter.getDataPortofolio()
    }

    override fun onException93(message: String) {
        val returnIntent = Intent()

        returnIntent.putExtra(Constant.TAG_ERROR_MESSAGE, message)

        this.setResult(RESULT_CANCELED, returnIntent)
        finish()
    }

    override fun onException21() {
        binding.swipeRefresh.setRefreshing(false)
        hideSkeleton()
        binding.rlRefreshPortofolio.visibility = View.VISIBLE
        binding.tvSid.text = "-"
        binding.rlRefreshPortofolio.setOnClickListener(View.OnClickListener {
            setSkeletonView()
            binding.rlRefreshPortofolio.visibility = View.GONE
            presenter.getDataPortofolio()

        })
    }

    override fun onException12(message: String) {
        showSnackbarErrorMessage(message, ALERT_ERROR, this, false)
    }

    override fun onException01(message: MessageResponse) {
        val fragmentBottomDialog = FragmentBottomDialog(this, "",
                message.title,
                message.desc,
                "ic_transaksi_gagal", false, false, true
        )
        fragmentBottomDialog.isCancelable = true
        fragmentBottomDialog.show(supportFragmentManager, "")
    }

    override fun onClick(p0: View?) {
        when(p0!!.id){
            R.id.ly_sid-> {
                val appropriateDialog = BottomFragmentNoImageRevamp(this,
                        dataMaster!!.goldBalance!!.sidInfo!!.title!!,
                        dataMaster!!.goldBalance!!.sidInfo!!.desc!!,
                        GeneralHelper.getString(R.string.ok)
                )
                appropriateDialog.show(supportFragmentManager, "")
            }
        }
    }

    override fun onClickYes(type: String?) {

    }

    override fun onClickToSafety() {

    }

    override fun onActivityResult(requestCode: Int, resultCode: Int, data: Intent?) {
        super.onActivityResult(requestCode, resultCode, data)
        if (requestCode == Constant.REQ_PAYMENT){
            if (resultCode == RESULT_OK) {
                setResult(RESULT_OK)
                finish()
            }
            else if (resultCode == RESULT_CANCELED && data != null) {
                setResult(RESULT_CANCELED, data)
            }
            else {
                setResult(RESULT_CANCELED)
                finish()
            }
        }
    }

    override fun onClickDialogWithImageCustom() {
        injectDependecy()

        setupView()
    }

    override fun onNantiSaja() {

    }

    override fun onClickDialog() {
        finish()
    }

    override fun onRefresh() {
        setSkeletonView()
        binding.rlRefreshPortofolio.visibility = View.GONE
        presenter.getDataPortofolio()
    }

    override fun onCallInfoImage() {
    }
}