package id.co.bri.brimo.ui.activities.bukarekening

import android.Manifest
import android.annotation.SuppressLint
import android.app.Activity
import android.app.SearchManager
import android.content.Intent
import android.content.pm.PackageManager
import android.net.Uri
import android.os.Bundle
import android.provider.Settings
import android.util.TypedValue
import android.view.View
import android.widget.Button
import android.widget.EditText
import android.widget.LinearLayout
import android.widget.TextView
import androidx.appcompat.widget.SearchView
import androidx.appcompat.widget.Toolbar
import androidx.core.app.ActivityCompat
import androidx.core.content.res.ResourcesCompat
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import butterknife.Bind
import butterknife.ButterKnife
import com.ethanhua.skeleton.Skeleton
import com.ethanhua.skeleton.SkeletonScreen
import com.google.gson.Gson
import id.co.bri.brimo.R
import id.co.bri.brimo.adapters.ListLokasiKantorAdapter
import id.co.bri.brimo.contract.IPresenter.general.IGeneralPilihKantorPresenter
import id.co.bri.brimo.contract.IView.general.IGeneralPilihKantorView
import id.co.bri.brimo.domain.config.Constant
import id.co.bri.brimo.domain.helpers.GeneralHelper
import id.co.bri.brimo.domain.helpers.GpsTracker
import id.co.bri.brimo.models.ParameterPilihKantorModel
import id.co.bri.brimo.models.apimodel.request.LocationRequest
import id.co.bri.brimo.models.apimodel.request.SearchLocationRequest
import id.co.bri.brimo.models.apimodel.response.InquiryGeneralOpenAccountResponse
import id.co.bri.brimo.models.apimodel.response.InquiryOpenRencanaResponse
import id.co.bri.brimo.models.apimodel.response.ListKantorResponse
import id.co.bri.brimo.models.apimodel.response.junio.FormOpenJunioResponse
import id.co.bri.brimo.ui.activities.PilihKantorGeneralActivity
import id.co.bri.brimo.ui.activities.base.BaseActivity
import id.co.bri.brimo.ui.activities.britamajunio.FormBritamaJunioActivity
import id.co.bri.brimo.ui.activities.openaccount.InquiryGeneralOpenAccountActivity
import id.co.bri.brimo.ui.activities.simpedes.InquirySimpedesOpenActivity
import id.co.bri.brimo.ui.customviews.dialog.DialogOpenAccount
import javax.inject.Inject

class PilihanKantorActivity : BaseActivity(), ListLokasiKantorAdapter.onClickItem,
    IGeneralPilihKantorView, DialogOpenAccount.DialogDefaultListener, View.OnClickListener {

    @Bind(R.id.toolbar)
    lateinit var toolbar: Toolbar

    @Bind(R.id.searchview_bukarek)
    lateinit var searchviewBukarekening: SearchView

    @Bind(R.id.recycler_view)
    lateinit var recyclerView: RecyclerView

    @Bind(R.id.tv_data_tidak_ditemukan)
    lateinit var tvDataTidakDitemukan: TextView

    @Bind(R.id.tv_desc_not_found)
    lateinit var tvDescNotFound: TextView

    @Bind(R.id.ly_not_found)
    lateinit var lyNotFound: LinearLayout

    @Bind(R.id.ly_search_result)
    lateinit var lySearchResult: LinearLayout

    @Bind(R.id.btn_search)
    lateinit var btnSearch: Button

    lateinit var skeletonScreen: SkeletonScreen
    lateinit var lokasiKantorAdapter: ListLokasiKantorAdapter
    var officeList: List<ListKantorResponse.Office> = ArrayList()

    lateinit var inLatitude: String
    lateinit var inLongitude: String
    lateinit var address: String

    lateinit var tipe: String
    lateinit var tipeProduk: String
    lateinit var title: String
    lateinit var urlLokasiSendiri: String
    lateinit var urlLokasiPencarian: String
    lateinit var urlInquiry: String

    lateinit var dialog: DialogOpenAccount

    lateinit var parameterModel: ParameterPilihKantorModel

    @Inject
    lateinit var presenter: IGeneralPilihKantorPresenter<IGeneralPilihKantorView>

    companion object {

        private lateinit var parameterModel: ParameterPilihKantorModel

        fun launchIntent(caller: Activity, parameterPilihKantorModel: ParameterPilihKantorModel?) {
            val intent = Intent(caller, PilihKantorGeneralActivity::class.java)
            if (parameterPilihKantorModel != null) {
                parameterModel = parameterPilihKantorModel
            }
            caller.startActivityForResult(intent, Constant.REQ_PAYMENT)
        }
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContentView(R.layout.activity_pilihan_kantor)

        ButterKnife.bind(this)

        urlLokasiSendiri = parameterModel.urlOwnLocation
        urlLokasiPencarian = parameterModel.urlSearchLocation
        urlInquiry = parameterModel.urlInquiry
        tipe = parameterModel.type
        title = parameterModel.name
        tipeProduk = parameterModel.productType

        GeneralHelper.setToolbarRevamp(this, toolbar, getTitleBar())

        injectDependency()
        getLocation()
        searchLocation()

        val editText: EditText =
            searchviewBukarekening.findViewById(androidx.appcompat.R.id.search_src_text)
        editText.setHintTextColor(resources.getColor(R.color.black3))
        editText.typeface = ResourcesCompat.getFont(this, R.font.bri_digital_text_medium)
        editText.setTextSize(TypedValue.COMPLEX_UNIT_SP, 14f)

        lokasiKantorAdapter = ListLokasiKantorAdapter(officeList, applicationContext, this)
        val layoutManager = LinearLayoutManager(this)
        recyclerView.setHasFixedSize(true)
        recyclerView.layoutManager = layoutManager
        recyclerView.adapter = lokasiKantorAdapter

        skeletonScreen = Skeleton.bind(recyclerView)
            .adapter(lokasiKantorAdapter)
            .shimmer(true)
            .angle(10)
            .frozen(false)
            .duration(1200)
            .load(R.layout.item_skeleton_pilih_kantor)
            .show() //default count is 10

        btnSearch.setOnClickListener(this)
    }

    private fun getTitleBar(): String? {
        return "Pilih Kantor"
    }

    /**
     * method untuk  lokasi
     */
    private fun searchLocation() {
        val searchManager = this.getSystemService(SEARCH_SERVICE) as SearchManager
        searchviewBukarekening.setSearchableInfo(searchManager.getSearchableInfo(this.componentName))
        searchviewBukarekening.maxWidth = Int.MAX_VALUE
        searchviewBukarekening.setOnQueryTextListener(object : SearchView.OnQueryTextListener {
            override fun onQueryTextSubmit(query: String): Boolean {
                //skeletonScreen.show()
                presenter!!.sendSearchLocation(createSearchLocationRequest())
                return false
            }

            override fun onQueryTextChange(newText: String): Boolean {
                address = newText
                return false
            }
        })
    }

    /**
     * Method untuk mendapatkan lokasi hp
     */
    private fun getLocation() {
        val gpsTracker = GpsTracker(this)
        if (gpsTracker.canGetLocation()) {
            inLatitude = gpsTracker.latitude.toString()
            inLongitude = gpsTracker.longitude.toString()
            presenter!!.sendLokasiSendiri(createLocationRequest())
        } else {
            showDialogEnableLocation()
        }
    }

    private fun injectDependency() {
        activityComponent.inject(this)
        if (presenter != null) {
            presenter.view = this
            presenter.setUrl(urlLokasiSendiri)
            presenter.setSearchUrl(urlLokasiPencarian)
            presenter.setUrlInquiry(urlInquiry)
            presenter.start()
        }
    }


    /**
     * method untuk mengalihkan aplikasi ke google map
     *
     * @param clickMap
     */
    override fun onClickMaps(clickMap: Class<*>?, office: ListKantorResponse.Office) {
        val uri = "https://www.google.com/maps/place/" + office.latitude + "," + office.longitude
        val intent = Intent(Intent.ACTION_VIEW, Uri.parse(uri))
        intent.setClassName("com.google.android.apps.maps", "com.google.android.maps.MapsActivity")
        startActivity(intent)
    }

    override fun onClickKantor(clickKantor: Class<*>?, office: ListKantorResponse.Office) {
        getPresenter(office)
    }

    //mapping product
    private fun getPresenter(office: ListKantorResponse.Office) {
        if (tipe == Constant.OPEN_ACCOUNT_S3f) {
            presenter!!.getDataInquiryS3f(office.branchCode, office.name, office.address)
        } else if (tipe == Constant.OPEN_ACCOUNT_JUNIO) {
            presenter!!.getDataFormJunio(tipeProduk, office.branchCode)
        } else {
            presenter!!.getDataInquiryGeneral(tipeProduk, office.branchCode, office.name)
        }
    }

    @SuppressLint("NotifyDataSetChanged")
    override fun onSuccessLocationDefault(kantorResponse: ListKantorResponse) {
        //skeletonScreen.hide()
        lokasiKantorAdapter!!.setItems(kantorResponse.officeList)
        lokasiKantorAdapter!!.notifyDataSetChanged()
        tvDataTidakDitemukan.visibility = View.GONE
        lySearchResult.visibility = View.VISIBLE
        lyNotFound.visibility = View.GONE
    }

    override fun onFailedLocation(msg: String) {
        dataTidakDitemukan(msg)
    }

    /**
     * method data tidak ditemukan
     */
    private fun dataTidakDitemukan(msg: String) {
        tvDataTidakDitemukan.visibility = View.VISIBLE
        lySearchResult.visibility = View.GONE
        lyNotFound.visibility = View.VISIBLE
    }

    override fun createLocationRequest(): LocationRequest {
        return LocationRequest(inLatitude, inLongitude)
    }

    override fun createSearchLocationRequest(): SearchLocationRequest {
        val searchLocationRequest = SearchLocationRequest(inLatitude, inLongitude, address)
        lySearchResult.visibility = View.VISIBLE
        lyNotFound.visibility = View.GONE
        return searchLocationRequest
    }

    override fun onException12(msg: String?) {
        showDialog()
    }

    private fun showDialog() {
        dialog = DialogOpenAccount(
            this,
            "Pembukaan Rekening\nSedang Diproses",
            "Pantau status pembukaan rekening kamu pada menu aktivitas atau hubungi Call Center BRI untuk informasi lebih lanjut",
            "14017"
        )
        val ft = supportFragmentManager.beginTransaction()
        ft.add(dialog, null)
        ft.commitAllowingStateLoss()
    }


    override fun onSuccessInquiryS3F(generalInquiryResponse: InquiryOpenRencanaResponse?) {
        InquirySimpedesOpenActivity.launchIntent(this, Gson().toJson(generalInquiryResponse))
    }

    override fun onSuccesGetDataForm(formOpenJunioResponse: FormOpenJunioResponse?, branchCode: String?) {
        FormBritamaJunioActivity.launchIntent(this, formOpenJunioResponse, tipeProduk, branchCode)
    }

    override fun onSuccessInquiryGeneralOpen(generalInquiryResponse: InquiryGeneralOpenAccountResponse?) {
        InquiryGeneralOpenAccountActivity.launchIntent(
            this,
            Gson().toJson(generalInquiryResponse),
            parameterModel.name
        )
    }

    override fun onActivityResult(requestCode: Int, resultCode: Int, data: Intent?) {
        super.onActivityResult(requestCode, resultCode, data)
        if (requestCode == Constant.REQ_PAYMENT) {
            if (resultCode == RESULT_OK && data != null) {
                setResult(RESULT_OK, data)
                finish()
            } else if (resultCode == RESULT_OK && data == null) {
                finish()
            } else if (resultCode == RESULT_CANCELED && data != null) {
                if (data.getStringExtra(Constant.TAG_ERROR_MESSAGE) != null && data.getStringExtra(
                        Constant.TAG_ERROR_MESSAGE
                    )!!.isNotEmpty()
                ) {
                    onException(data.getStringExtra(Constant.TAG_ERROR_MESSAGE))
                }
            }
        }
    }

    override fun onDestroy() {
        presenter!!.stop()
        super.onDestroy()
    }

    override fun onClick(view: View) {
        when (view.id) {
            R.id.btn_search -> if (checkSelfPermission(Manifest.permission.ACCESS_COARSE_LOCATION) == PackageManager.PERMISSION_GRANTED) {
                doSearchLocation()
            } else {
                ActivityCompat.requestPermissions(
                    this,
                    arrayOf(Manifest.permission.ACCESS_COARSE_LOCATION),
                    1
                )
            }
        }
    }

    private fun doSearchLocation() {
        lyNotFound.visibility = View.GONE
        lySearchResult.visibility = View.VISIBLE

        skeletonScreen = Skeleton.bind(recyclerView)
            .adapter(lokasiKantorAdapter)
            .shimmer(true)
            .angle(10)
            .frozen(false)
            .duration(1200)
            .load(R.layout.item_skeleton_pilih_kantor)
            .show()

        if (inLatitude.isEmpty() && inLongitude.isEmpty()) {
            getLocation()
        } else {
            if (address == null || address.isEmpty()) {
                getLocation()
            } else {
                presenter!!.sendSearchLocation(createSearchLocationRequest())
            }
        } //default count is 10
    }

    override fun onRequestPermissionsResult(
        requestCode: Int,
        permissions: Array<out String>,
        grantResults: IntArray
    ) {
        super.onRequestPermissionsResult(requestCode, permissions, grantResults)
        when (requestCode) {
            1 -> {
                if (grantResults.isNotEmpty()
                    && grantResults[0] == PackageManager.PERMISSION_GRANTED
                ) {
                    doSearchLocation()
                }
            }
        }
    }

    override fun onClickYes() {}
}