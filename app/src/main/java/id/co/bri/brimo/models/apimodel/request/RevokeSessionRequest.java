package id.co.bri.brimo.models.apimodel.request;

import com.google.gson.annotations.Expose;
import com.google.gson.annotations.SerializedName;

public class RevokeSessionRequest {
    @SerializedName("username")
    @Expose
    private String username;
    @SerializedName("token_key")
    @Expose
    private String tokenKey;
    @SerializedName("session_id")
    @Expose
    private String sessionId;

    public RevokeSessionRequest(String username, String tokenKey, String sessionId) {
        this.username = username;
        this.tokenKey = tokenKey;
        this.sessionId = sessionId;
    }

    public String getUsername() {
        return username;
    }

    public void setUsername(String username) {
        this.username = username;
    }

    public String getTokenKey() {
        return tokenKey;
    }

    public void setTokenKey(String tokenKey) {
        this.tokenKey = tokenKey;
    }

    public String getSessionId() {
        return sessionId;
    }

    public void setSessionId(String sessionId) {
        this.sessionId = sessionId;
    }
}
