package id.co.bri.brimo.data.repository.category;

import id.co.bri.brimo.models.daomodel.Category;

import java.util.List;

import io.reactivex.Completable;
import io.reactivex.Maybe;

public interface CategoryPfmSource {

    Maybe<List<Category>> getCategoryTransaksi();

    Maybe<List<Category>> getCategoryPembayaran();

    Maybe<List<Category>> getCategoryIncome();

    Completable saveCategory(List<Category> catgeories);


}
