package id.co.bri.brimo.data.repository.menudashall;

import android.os.Build;

import java.util.List;

import androidx.annotation.RequiresApi;
import id.co.bri.brimo.data.repository.menudashall.local.MenuDashAllLocalSource;
import id.co.bri.brimo.models.daomodel.DashboardMenu.MenuDashAll;
import id.co.bri.brimo.models.daomodel.DashboardMenu.MenuFeatureModel;
import id.co.bri.brimo.models.daomodel.DashboardMenu.SubFeatureModel;
import io.reactivex.Completable;
import io.reactivex.Maybe;

public class MenuDashAllRepository implements MenuDashAllSource {

    MenuDashAllLocalSource menuDashAllLocalSource;

    public MenuDashAllRepository(MenuDashAllLocalSource menuDashAllLocalSource) {
        this.menuDashAllLocalSource = menuDashAllLocalSource;
    }

    @Override
    public Completable insertMenuDashAll(List<MenuDashAll> dashMenuAll) {
        return menuDashAllLocalSource.insertMenuDashAll(dashMenuAll);
    }

    @Override
    public Maybe<List<SubFeatureModel>> getDashMenuAll() {
        return menuDashAllLocalSource.getDashMenuAll();
    }

    @RequiresApi(api = Build.VERSION_CODES.N)
    @Override
    public Maybe<List<MenuFeatureModel>> getMenuFeatureList() {
        return menuDashAllLocalSource.getMenuFeatureList();
    }

    @Override
    public Completable deleteDashMenuAll() {
        return menuDashAllLocalSource.deleteDashMenuAll();
    }

    @Override
    public Completable deleteDashMenuAllByName(String name) {
        return menuDashAllLocalSource.deleteDashMenuAllByName(name);
    }
}
