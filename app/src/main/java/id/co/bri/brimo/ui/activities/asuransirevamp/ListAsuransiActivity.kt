package id.co.bri.brimo.ui.activities.asuransirevamp

import android.app.Activity
import android.content.Intent
import android.os.Bundle
import android.view.View
import android.widget.LinearLayout
import androidx.fragment.app.Fragment
import androidx.fragment.app.FragmentManager
import androidx.fragment.app.FragmentStatePagerAdapter
import androidx.viewpager.widget.ViewPager
import id.co.bri.brimo.R
import id.co.bri.brimo.contract.IPresenter.asuransiRevamp.IListAsuransiPresenter
import id.co.bri.brimo.contract.IView.asuransiRevamp.IListAsuransiView
import id.co.bri.brimo.databinding.ActivityListAsuransiBinding
import id.co.bri.brimo.domain.config.Constant
import id.co.bri.brimo.domain.helpers.GeneralHelper
import id.co.bri.brimo.models.apimodel.request.asuransirevamp.DetailAsuransiRequest
import id.co.bri.brimo.models.apimodel.response.asuransi.DetailAsuransiResponse
import id.co.bri.brimo.models.apimodel.response.asuransi.ListAsuransiResponse
import id.co.bri.brimo.ui.activities.base.BaseActivity
import id.co.bri.brimo.ui.fragments.asuransiRevamp.ListAsuransiFragment
import javax.inject.Inject

class ListAsuransiActivity : BaseActivity(),ListAsuransiFragment.DialogDefaultListener, ViewPager.OnPageChangeListener, IListAsuransiView {
    private lateinit var binding : ActivityListAsuransiBinding
    private var titleList: ArrayList<String> = ArrayList()

    private var lyTabs : LinearLayout? = null

    @Inject
    lateinit var presenter : IListAsuransiPresenter<IListAsuransiView>

    companion object {
        var mResponse : ListAsuransiResponse? = null
        var mFlag01 : Boolean = false
        fun launchIntent(caller: Activity,response: ListAsuransiResponse, fromFastMenu: Boolean, flag01 : Boolean) {
            val intent = Intent(caller, ListAsuransiActivity::class.java)
            mResponse = response
            isFromFastMenu = fromFastMenu
            mFlag01 = flag01
            caller.startActivityForResult(intent, Constant.REQ_PAYMENT)
        }
        fun launchIntent(caller: Activity, fromFastMenu: Boolean, flag01 : Boolean) {
            val intent = Intent(caller, ListAsuransiActivity::class.java)
            isFromFastMenu = fromFastMenu
            mFlag01 = flag01
            caller.startActivityForResult(intent, Constant.REQ_PAYMENT)
        }
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        binding = ActivityListAsuransiBinding.inflate(layoutInflater)
        injectDependency()
        setContentView(binding.root)

        setupView()
    }

    private fun injectDependency() {
        activityComponent.inject(this)
        presenter.view = this
        presenter.start()
        presenter.setUrlDetail(GeneralHelper.getString(R.string.url_detail_asuransi))
    }

    private fun setupView() {
        if (mFlag01){
            GeneralHelper.setToolbarRevamp(this, binding.tbListAsuransi.toolbar, GeneralHelper.getString(R.string.tb_list_asuransi_tidak_aktif))
            binding.rlNoDataFastMutasi.visibility = View.VISIBLE
            binding.vpCatatanKeuangan.visibility = View.GONE
            binding.flListAsuransi.visibility = View.GONE
        }else{
            GeneralHelper.setToolbarRevamp(this, binding.tbListAsuransi.toolbar, GeneralHelper.getString(R.string.tb_list_asuransi))
            binding.rlNoDataFastMutasi.visibility = View.GONE
            binding.flListAsuransi.visibility = View.VISIBLE
            binding.vpCatatanKeuangan.visibility = View.VISIBLE

            for (i in mResponse!!.listUserInsurance!!.indices) {
                titleList.add(mResponse!!.listUserInsurance!![i].insuranceType!!)
            }

            val catatanKeuanganFragmentAdapter = MyPagerAdapter(supportFragmentManager, mResponse!!.listUserInsurance!!, this, titleList, mResponse!!)
            binding.vpCatatanKeuangan.adapter = catatanKeuanganFragmentAdapter
            binding.tabCatatanKeuangan.setViewPager(binding.vpCatatanKeuangan)

            //Add bold effect on selected tab
            binding.tabCatatanKeuangan.setOnPageChangeListener(this)
            lyTabs =  binding.tabCatatanKeuangan.getChildAt(0) as LinearLayout

            //change default style toolbar font
            GeneralHelper.changeTabsFontBoldForAsuransi(this, lyTabs, 0)
        }


    }
    override fun onPageScrolled(position: Int, positionOffset: Float, positionOffsetPixels: Int) {

    }

    override fun onPageSelected(position: Int) {
        //change default style toolbar font
        GeneralHelper.changeTabsFontBoldForAsuransi(this, lyTabs, position)
    }

    override fun onPageScrollStateChanged(state: Int) {

    }

//    override fun onSuccessDetail(response: DetailAsuransiResponse) {
//        DetailAsuransiRevampActivity.launchIntent(this,false,response)
//    }
//
//    override fun showProgress1() {
//        GeneralHelper.showDialog(this)
//    }
//
//    override fun hideProgress1() {
//        GeneralHelper.dismissDialog()
//    }

    override fun onType(position: Int, productCode: String) {
       presenter.getDataDetail(DetailAsuransiRequest(productCode))
    }


    class MyPagerAdapter(
            fragmentManager: FragmentManager?,
            private val product1: List<ListAsuransiResponse.ListUserInsurance>, private val onListener : ListAsuransiFragment.DialogDefaultListener, private val mTitle : List<String>, private val response: ListAsuransiResponse
    ) : FragmentStatePagerAdapter(fragmentManager!!) {

        // Returns total number of pages
        override fun getCount(): Int {
            return product1.size
        }

        // Returns the fragment to display for that page
        override fun getItem(position: Int): Fragment {
            return ListAsuransiFragment(product1,position,onListener,response)
        }

        override fun getPageTitle(position: Int): CharSequence? {
            return mTitle[position]
        }
    }

    override fun onSuccessDetail(response: DetailAsuransiResponse) {
        DetailAsuransiRevampActivity.launchIntent(this,false,response)
    }

    override fun onException12(message: String) {
        showSnackbarErrorMessage(message, ALERT_ERROR, this, false)
    }

}