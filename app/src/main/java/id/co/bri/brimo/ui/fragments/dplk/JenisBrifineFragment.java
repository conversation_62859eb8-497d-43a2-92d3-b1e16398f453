package id.co.bri.brimo.ui.fragments.dplk;

import android.os.Build;
import android.os.Bundle;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;

import androidx.fragment.app.Fragment;
import androidx.recyclerview.widget.LinearLayoutManager;

import java.util.ArrayList;
import java.util.List;

import id.co.bri.brimo.adapters.PilihJenisBrifineAdapter;
import id.co.bri.brimo.databinding.FragmentJenisBrifineBinding;
import id.co.bri.brimo.models.apimodel.response.dplk.DplkFormResponse;
import id.co.bri.brimo.ui.activities.dplk.BrifineKombinasiActivity;

/**
 * A simple {@link Fragment} subclass.
 * Use the {@link JenisBrifineFragment#newInstance} factory method to
 * create an instance of this fragment.
 */
public class JenisBrifineFragment extends Fragment implements PilihJenisBrifineAdapter.onClickItem {

    private FragmentJenisBrifineBinding binding;

    private List<DplkFormResponse.JenisBrifine.Product> list = new ArrayList<>();
    PilihJenisBrifineAdapter adapter;
    onClickProduct onClickProduct;

    // TODO: Rename parameter arguments, choose names that match
    // the fragment initialization parameters, e.g. ARG_ITEM_NUMBER
    private static final String ARG_PARAM1 = "param1";
    private static final String ARG_PARAM2 = "param2";

    // TODO: Rename and change types of parameters
    private String mParam1;
    private String mParam2;

    public JenisBrifineFragment(List<DplkFormResponse.JenisBrifine.Product> list, onClickProduct onClickProduct, PilihJenisBrifineAdapter adapter) {
        // Required empty public constructor
        this.list = list;
        this.onClickProduct = onClickProduct;
        this.adapter = adapter;
    }

    public void setAdapter(PilihJenisBrifineAdapter adapter) {
        this.adapter = adapter;
    }

    public void setList(List<DplkFormResponse.JenisBrifine.Product> list) {
        this.list = list;
    }

    public void setOnClickProduct(JenisBrifineFragment.onClickProduct onClickProduct) {
        this.onClickProduct = onClickProduct;
    }

    public JenisBrifineFragment() {
        // Required empty public constructor
    }

    public interface onClickProduct {
        void onClickProduct(DplkFormResponse.JenisBrifine.Product code, int p);
    }

    /**
     * Use this factory method to create a new instance of
     * this fragment using the provided parameters.
     *
     * @param param1 Parameter 1.
     * @param param2 Parameter 2.
     * @return A new instance of fragment JenisBrifineFragment.
     */
    // TODO: Rename and change types and number of parameters
    public static JenisBrifineFragment newInstance(String param1, String param2) {
        JenisBrifineFragment fragment = new JenisBrifineFragment();
        Bundle args = new Bundle();
        args.putString(ARG_PARAM1, param1);
        args.putString(ARG_PARAM2, param2);
        fragment.setArguments(args);
        return fragment;
    }

    @Override
    public void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        if (getArguments() != null) {
            mParam1 = getArguments().getString(ARG_PARAM1);
            mParam2 = getArguments().getString(ARG_PARAM2);
        }
    }

    @Override
    public View onCreateView(LayoutInflater inflater, ViewGroup container,
                             Bundle savedInstanceState) {
        binding = FragmentJenisBrifineBinding.inflate(inflater, container, false);
        if (adapter != null) {
            adapter.setClickItem(this);
            binding.rcBrifine.setLayoutManager(new LinearLayoutManager(getActivity()));
            binding.rcBrifine.setAdapter(adapter);
        }

        return binding.getRoot();
    }

    public void clearSelected() {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.N) {
            list.forEach((p) -> p.setSelected(false));
        } else {
            for (DplkFormResponse.JenisBrifine.Product p : list) {
                p.setSelected(false);
            }
        }

        adapter.updateAdapter(this, getActivity(), list);
    }

    @Override
    public void onClickBrifine(DplkFormResponse.JenisBrifine.Product code, int p) {
        clearSelected();
        list.get(p).setSelected(true);
        adapter.notifyDataSetChanged();
        if (Integer.parseInt(code.getCode()) == 21) {
            BrifineKombinasiActivity.launchIntent(getActivity(), code.getTitle(), code.getCode(), false);
        } else {
            onClickProduct.onClickProduct(code, p);
        }
    }


    @Override
    public void onResume() {

        clearSelected();
        super.onResume();
    }
}