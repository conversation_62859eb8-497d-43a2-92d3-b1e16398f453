package id.co.bri.brimo.data.repository.menudashboard;

import java.util.List;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import id.co.bri.brimo.data.dao.menudashboard.MenuDao;
import id.co.bri.brimo.models.daomodel.DashboardMenu.MenuDashboard;
import io.reactivex.Completable;
import io.reactivex.Maybe;

public class MenuDashboardRepository implements MenuDashboardSource{

    MenuDao menuDashboardDao;

    public MenuDashboardRepository(MenuDao menuDao) {
        this.menuDashboardDao = menuDao;
    }

    @NonNull
    @Override
    public Completable insertMenuDashboard(@Nullable List<MenuDashboard> listMenuKategori) {
        return menuDashboardDao.insertListMenu(listMenuKategori);
    }

    @NonNull
    @Override
    public Maybe<List<MenuDashboard>> getAllMenuDashboard() {
        return menuDashboardDao.getAllMenu();
    }

    @NonNull
    @Override
    public Completable updateMenuDashboard(@NonNull MenuDashboard itemMenu, int menuId) {
        return menuDashboardDao.insertMenu(itemMenu);
    }

    @NonNull
    @Override
    public Maybe<List<MenuDashboard>> getMenuByID(int kategoriId) {
        return menuDashboardDao.getMenuByKategori(kategoriId);
    }

    @NonNull
    @Override
    public Completable updateIsNew(int menuId, int menuStatus) {
        return menuDashboardDao.updateIsNewMenu(menuId, menuStatus);
    }

    @NonNull
    @Override
    public Completable updateIsEvent(@NonNull String eventName) {
        return menuDashboardDao.updateEventName(eventName);
    }

    @NonNull
    @Override
    public Completable deleteMenuDashboard(int menuId) {
        return menuDashboardDao.deleteMenu(menuId);
    }

    @NonNull
    @Override
    public Completable updateLanguageMenuName(String menuId, @NonNull String menuName) {
        return menuDashboardDao.updateLanguageMenuName(menuId, menuName);
    }

    @Override
    public Completable deleteAllMenuDashboard() {
         return menuDashboardDao.deleteAll();
    }
}
