package id.co.bri.brimo.models.apimodel.request;

import com.google.gson.annotations.Expose;
import com.google.gson.annotations.SerializedName;

public class KonfimasiBukaValasRequest {

    @SerializedName("reference_number")
    @Expose
    private String reference_number;
    @SerializedName("account_number")
    @Expose
    private String account_number;
    @SerializedName("debit_amount")
    @Expose
    private String debit_amount;
    @SerializedName("credit_amount")
    @Expose
    private String credit_amount;
    @SerializedName("credit_currency")
    @Expose
    private String credit_currency;
    @SerializedName("open_purpose")
    @Expose
    private String open_purpose;
    @SerializedName("branch_code")
    @Expose
    private String branch_code;
    @SerializedName("branch_name")
    @Expose
    private String branch_name;

    public KonfimasiBukaValasRequest(String account_number,String credit_currency,String branch_name, String debit_amount,String reference_number, String credit_amount, String open_purpose, String branch_code) {
        this.reference_number = reference_number;
        this.account_number = account_number;
        this.debit_amount = debit_amount;
        this.credit_amount = credit_amount;
        this.credit_currency = credit_currency;
        this.open_purpose = open_purpose;
        this.branch_code = branch_code;
        this.branch_name= branch_name;
    }

    public String getReference_number() {
        return reference_number;
    }

    public void setReference_number(String reference_number) {
        this.reference_number = reference_number;
    }

    public String getAccount_number() {
        return account_number;
    }

    public void setAccount_number(String account_number) {
        this.account_number = account_number;
    }

    public String getDebit_amount() {
        return debit_amount;
    }

    public void setDebit_amount(String debit_amount) {
        this.debit_amount = debit_amount;
    }

    public String getCredit_amount() {
        return credit_amount;
    }

    public void setCredit_amount(String credit_amount) {
        this.credit_amount = credit_amount;
    }

    public String getDebit_currency() {
        return credit_currency;
    }

    public void setDebit_currency(String debit_currency) {
        this.credit_currency = debit_currency;
    }

    public String getOpen_purpose() {
        return open_purpose;
    }

    public void setOpen_purpose(String open_purpose) {
        this.open_purpose = open_purpose;
    }

    public String getBranch_code() {
        return branch_code;
    }

    public void setBranch_code(String branch_code) {
        this.branch_code = branch_code;
    }

    public String getBranch_name() {
        return branch_name;
    }

    public void setBranch_name(String branch_name) {
        this.branch_name = branch_name;
    }
}
