package id.co.bri.brimo.domain.helpers.bubbleShowCaseView

/**
 * Created by jcampos on 10/09/2018.
 */
interface OnBubbleMessageViewListener {
    /**
     * It is called when a user clicks the close action image in the BubbleMessageView
     */
    fun onCloseActionImageClick()


    /**
     * It is called when a user clicks the BubbleMessageView
     */
    fun onBubbleClick()

    /**
     * It is called when a user clicks the BubbleMessageView
     */
    fun onSkipActionClick()

    /**
     * It is function for close buble
     */
    fun onCloseBuble()
}