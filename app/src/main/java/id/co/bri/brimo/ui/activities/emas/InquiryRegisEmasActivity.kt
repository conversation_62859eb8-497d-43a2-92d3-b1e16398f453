package id.co.bri.brimo.ui.activities.emas

import android.annotation.SuppressLint
import android.app.Activity
import android.content.Intent
import android.os.Build
import android.os.Bundle
import android.os.SystemClock
import android.text.Editable
import android.text.InputFilter
import android.text.InputFilter.LengthFilter
import android.text.Selection
import android.text.TextWatcher
import android.view.View
import android.widget.EditText
import androidx.recyclerview.widget.LinearLayoutManager
import com.google.gson.Gson
import id.co.bri.brimo.R
import id.co.bri.brimo.adapters.RekomendasiAdapter
import id.co.bri.brimo.contract.IPresenter.emas.IInquiryEmasPresenter
import id.co.bri.brimo.contract.IView.emas.IInquiryEmasView
import id.co.bri.brimo.databinding.ActivityInquiryRegisEmasBinding
import id.co.bri.brimo.domain.config.Constant
import id.co.bri.brimo.domain.helpers.GeneralHelper
import id.co.bri.brimo.domain.helpers.ValidationHelper
import id.co.bri.brimo.domain.helpers.amount.DecimalFormatHelper
import id.co.bri.brimo.domain.helpers.calendar.CalendarHelper
import id.co.bri.brimo.models.AccountModel
import id.co.bri.brimo.models.DurasiModel
import id.co.bri.brimo.models.apimodel.request.emas.ConfirmationOpenEmasRequest
import id.co.bri.brimo.models.apimodel.request.emas.GrafikEmasRequest
import id.co.bri.brimo.models.apimodel.response.GeneralConfirmationResponse
import id.co.bri.brimo.models.apimodel.response.bukarekening.RecomInitialDepositItem
import id.co.bri.brimo.models.apimodel.response.emas.*
import id.co.bri.brimo.ui.activities.base.BaseActivity
import id.co.bri.brimo.ui.activities.bukarekening.KonfirmasiTabunganRevActivity
import id.co.bri.brimo.ui.fragments.SetCalenderAFTFragment
import id.co.bri.brimo.ui.fragments.SumberDanaFragmentRevamp
import java.math.RoundingMode
import java.text.DecimalFormat
import java.text.SimpleDateFormat
import java.util.*
import java.util.function.Consumer
import javax.inject.Inject


class InquiryRegisEmasActivity : BaseActivity(), RekomendasiAdapter.OnItemClickListener,
        DecimalFormatHelper.OnEnteredGrams, View.OnClickListener, SetCalenderAFTFragment.OnSelectDate, IInquiryEmasView,
        SumberDanaFragmentRevamp.SelectSumberDanaInterface{
    lateinit var binding : ActivityInquiryRegisEmasBinding
    private var adapter : RekomendasiAdapter? = null
    private var saldoNominal : Double = 0.0
    private var saldoNominalGram : Double = 0.0
    private var maxGram : Double = 0.0
    private var maxNominal : Double = 0.0
    private var minGram : Double = 0.0
    private var minNominal : Double = 0.0
    private var isCheck = false
    private var durasiModel: DurasiModel? = null
    private var stringDate : String = CalendarHelper.getCurrentDateString()
    lateinit var model: AccountModel
    lateinit var mSaldoString : String
    private var mSaldo : Double = 0.0
    lateinit var mDefaultAkun : String
    private var saldo = 0.0
    private var counter = 0
    lateinit var mListAccountModel: List<AccountModel>
    private var mListFailed: List<Int>? = mutableListOf()
    private var idBranch = ""
    private var isRecom = false
    lateinit var outlet: Outlets
    private var isActive = false
    lateinit var calendarFragment: SetCalenderAFTFragment
    private var formatdate : String =CalendarHelper.getCurrentDateString()
    private var mSaldoHold : Boolean = false
    private var errorMessage: String? = null



    @Inject
    lateinit var presenter: IInquiryEmasPresenter<IInquiryEmasView>

    companion object{
        var dataMaster : InquiryOpenEmasResponse? = null

        @JvmStatic
        fun launchIntent(caller: Activity, data: InquiryOpenEmasResponse) {
            val intent = Intent(caller, InquiryRegisEmasActivity::class.java)
            dataMaster = data
            caller.startActivityForResult(intent, Constant.REQ_BUKA_REKENING)
        }
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        binding = ActivityInquiryRegisEmasBinding.inflate(layoutInflater)
        setContentView(binding.root)
        injectDependency()

        setupView()
        validationButton()
    }

    fun injectDependency() {
        activityComponent.inject(this)
        //get prresenter from dagger inject
        presenter.view = this
        presenter.setUrlInquiry(GeneralHelper.getString(R.string.url_onboard_confirmation))
        presenter.setUrlGrafikJual(GeneralHelper.getString(R.string.url_grafik_emas))
        presenter.start()
    }

    private fun setupView(){
        binding.tvDebet.visibility = View.GONE
        binding.etSetoranAwal.setText("0")
        binding.etSetoranAwal2.setText("0")
        maxGram = dataMaster!!.maximum!!.gramMax!!.maximumFloat!!
        minGram = dataMaster!!.minimum!!.gramMin!!.minimumFloat!!
        maxNominal = dataMaster!!.maximum!!.depositMax!!.maximumFloat!!
        minNominal = dataMaster!!.minimum!!.depositMin!!.minimumFloat!!

        GeneralHelper.setToolbar(this, binding.tbInquiryRegisEmas.toolbar, GeneralHelper.getString(R.string.tb_txt_setoran_emas))
        adapter = RekomendasiAdapter(this, dataMaster!!.recomInitialDeposit, this)

        binding.tvBuyRate.text = dataMaster!!.buyRate!!.buyRateString
        binding.tvBuyRateGram.text = GeneralHelper.getString(R.string.txt_minimal_gram)
        GeneralHelper.loadImageUrl(this, dataMaster!!.productIconUrl, binding.ivImage,R.drawable.bri, 0)
        GeneralHelper.loadImageUrl(this, dataMaster!!.productImageUrl, binding.ivLogo,R.drawable.bri, 0)
        binding.txtMinimal.text = String.format(GeneralHelper.getString(R.string.text_minimal), dataMaster!!.minimum!!.depositMin!!.minimumString)

        binding.rvRecomendation.layoutManager = LinearLayoutManager(this, LinearLayoutManager.HORIZONTAL, false)
        binding.rvRecomendation.adapter = adapter

        setListenerEdittext()
        binding.etSetoranAwal2.addTextChangedListener(
                DecimalFormatHelper(binding.etSetoranAwal2, 4, this, dataMaster!!.maximum!!.gramMax!!.maximumFloat!!)
        )
        binding.etSetoranAwal2.background = null
        binding.etSetoranAwal2.clearComposingText()

        binding.etSetoranAwal2.setOnFocusChangeListener { view, b ->
            isActive = b
            setBackgorundLayout()
            if (!b){
                if (binding.etSetoranAwal2.text.isNullOrEmpty()){
                    binding.etSetoranAwal2.setText("0")
                }
            }else{
                isRecom = false
            }
        }

        binding.etSetoranAwal.setOnFocusChangeListener { view, b ->
            isActive = b
            setBackgorundLayout()
            if (b){
                isRecom = false
            }
        }

        binding.lyCheck.setOnClickListener(this)
        binding.etPendebetan.setOnClickListener(this)
        binding.btnSubmit.setOnClickListener(this)
        binding.lyKantorPegadaian.setOnClickListener(this)
        binding.btnBiru.setOnClickListener(this)
        binding.lyInc.llSumberDana.setOnClickListener {
            counter++
            val fragmentSumberDanaNew = SumberDanaFragmentRevamp(mListAccountModel, counter, GeneralHelper.clearingAmountSigned("Rp"+binding.etSetoranAwal.text.toString()).toLong(), this,mListFailed, false)
            fragmentSumberDanaNew.show(supportFragmentManager, Constant.TAG_PICK_ACCOUNT)
        }
    }

    @SuppressLint("NotifyDataSetChanged")
    override fun onItemClick(position: Int) {
        isRecom = true
        binding.etSetoranAwal.setText(String.format("%.0f", dataMaster!!.recomInitialDeposit[position].recomFloat.toFloat()))

        clearSelected()
        dataMaster!!.recomInitialDeposit[position].isSelected = true
        adapter!!.notifyDataSetChanged()
    }

    @SuppressLint("NotifyDataSetChanged")
    private fun clearSelected() {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.N) {
            dataMaster!!.recomInitialDeposit.forEach(Consumer { p: RecomInitialDepositItem ->
                p.isSelected = false
            })
        } else {
            for (p in dataMaster!!.recomInitialDeposit) {
                p.isSelected = false
            }
        }
        adapter!!.notifyDataSetChanged()
    }

    private fun setBackgorundLayout(){
        if (isActive){
            binding.lyEdleft.setBackgroundResource(R.drawable.bg_input_left_blue)
            binding.lyEdright.setBackgroundResource(R.drawable.bg_input_right_blue)
        }else {
            binding.lyEdleft.setBackgroundResource(R.drawable.bg_input_left)
            binding.lyEdright.setBackgroundResource(R.drawable.bg_input_right)
        }
    }

    open fun setListenerEdittext(){
        binding.etSetoranAwal.filters = arrayOf<InputFilter>(LengthFilter(11))
        binding.etSetoranAwal.addTextChangedListener(object: TextWatcher {
            override fun afterTextChanged(s: Editable?) {
                clearSelected()
                if (binding.etSetoranAwal.text.toString() == ""){
                    binding.etSetoranAwal.setText("0")
                }
                saldoNominal = GeneralHelper.clearingAmountSigned("Rp"+binding.etSetoranAwal.text.toString()).toDouble()
                try {
                    binding.etSetoranAwal.removeTextChangedListener(this)
                    var value: String = binding.etSetoranAwal.text.toString()
                    if (value == ""){
                        value = "0"
                    }
                    var str = value.replace(Constant.CURRENCY.toRegex(), "")
                            .replace("\\.".toRegex(), "")
                    str = getDecimalFormattedString(str)
                    value = str
                    binding.etSetoranAwal.setText(value)
                    Selection.setSelection(binding.etSetoranAwal.text, value.length)
                    if (saldoNominal >= minNominal && !binding.etSetoranAwal2.hasFocus()){
                        if (saldoNominal >= saldo) {
                            convertToGold(binding.etSetoranAwal2, saldoNominal, dataMaster!!.buyRate!!.buyRateFloat!!)
                            binding.txtMinimal.text = GeneralHelper.getString(R.string.txt_saldo_tidak_cukup)
                            binding.txtMinimal.setTextColor(GeneralHelper.getColor(R.color.error80))
                        }else if (saldoNominal <= maxNominal){
                            convertToGold(binding.etSetoranAwal2, saldoNominal, dataMaster!!.buyRate!!.buyRateFloat!!)
                            binding.txtMinimal.text = String.format(GeneralHelper.getString(R.string.text_minimal), "Rp"+GeneralHelper.formatNominal(minNominal))
                            binding.txtMinimal.setTextColor(GeneralHelper.getColor(R.color.neutral_light50))
                        }else{
                            convertToGold(binding.etSetoranAwal2, saldoNominal , dataMaster!!.buyRate!!.buyRateFloat!!)
                            binding.txtMinimal.text = String.format(GeneralHelper.getString(R.string.text_maximal), "Rp"+GeneralHelper.formatNominal(maxNominal))
                            binding.txtMinimal.setTextColor(GeneralHelper.getColor(R.color.error80))
                        }
                    }else {
                        if (!binding.etSetoranAwal2.hasFocus()) {
                            convertToGold(binding.etSetoranAwal2, 0.0, dataMaster!!.buyRate!!.buyRateFloat!!)
                            binding.txtMinimal.text = String.format(GeneralHelper.getString(R.string.text_minimal), "Rp" + GeneralHelper.formatNominal(minNominal))
                            binding.txtMinimal.setTextColor(GeneralHelper.getColor(R.color.error80))
                        }
                    }
                    binding.etSetoranAwal.addTextChangedListener(this)
                } catch (ex: Exception) {
                    binding.etSetoranAwal.addTextChangedListener(this)
                }


                validationButton()
            }

            override fun beforeTextChanged(s: CharSequence?, start: Int, count: Int, after: Int) { }

            override fun onTextChanged(s: CharSequence?, start: Int, before: Int, count: Int) { }
        })

        ValidationHelper.disableCopyPaste(binding.etSetoranAwal)
        ValidationHelper.disableCopyPaste( binding.etSetoranAwal2)
    }

    private fun convertToGold(edGold : EditText, nominal : Double, priceGold : Double){
        val rateGold : Double = priceGold*100

        if (nominal == 0.0){
            edGold.setText("0")
        }
        else if(nominal == maxNominal){
            edGold.setText(maxGram.toInt().toString())
        }
        else{
            val df = DecimalFormat("#.####")
            df.roundingMode = RoundingMode.CEILING

            val res : Double = nominal.div(rateGold)
            val filterUserPrice = df.format(res)
            var data = ""
            if (filterUserPrice.contains(",")){
                val strs = filterUserPrice.split(",").toTypedArray()
                if (strs[1] == "0"){
                    data = strs[0]
                }else{
                    data = filterUserPrice
                }
            }else if (filterUserPrice.contains(".")){
                val strs = filterUserPrice.split(".").toTypedArray()
                if (strs[1] == "0"){
                    data = strs[0]
                }else{
                    data = filterUserPrice
                }
            }else{
                data = filterUserPrice
            }
            if (data.contains(".")){
                edGold.setText(data.replace(".", ","))
            }else{
                edGold.setText(data)
            }
        }
    }

    private fun getDecimalFormattedString(value: String): String {
        return GeneralHelper.formatNominal(value)
    }

    override fun onEntered(gram: Double) {
        saldoNominalGram = gram

        if (binding.etSetoranAwal2.text.isNullOrEmpty()){
            saldoNominalGram = 0.0
        }else{
            if (binding.etSetoranAwal2.text.toString().contains(",")){
                saldoNominalGram = binding.etSetoranAwal2.text.toString().replace(",",".").toDouble()
            }else{
                saldoNominalGram = binding.etSetoranAwal2.text.toString().toDouble()
            }
        }

        val digitNom = (saldoNominalGram* (dataMaster!!.buyRate!!.buyRateFloat!!*100))
        val roundedDown = digitNom.toBigDecimal().setScale(0, RoundingMode.DOWN).toDouble().toInt()
        if (!isRecom && !binding.etSetoranAwal.hasFocus()){
            binding.etSetoranAwal.setText(GeneralHelper.formatNominal(roundedDown))
        }

        if (saldoNominalGram < minGram){
            binding.txtMinimal.text = String.format(GeneralHelper.getString(R.string.text_minimal), dataMaster!!.minimum!!.gramMin!!.minimumString)
            binding.txtMinimal.setTextColor(GeneralHelper.getColor(R.color.error80))
        }else if (saldoNominalGram > maxGram){
            binding.txtMinimal.text = String.format(GeneralHelper.getString(R.string.text_maximal), dataMaster!!.maximum!!.gramMax!!.maximumString)
            binding.txtMinimal.setTextColor(GeneralHelper.getColor(R.color.error80))
        }else if (roundedDown >= saldo){
            binding.txtMinimal.text = GeneralHelper.getString(R.string.txt_saldo_tidak_cukup)
            binding.txtMinimal.setTextColor(GeneralHelper.getColor(R.color.error80))
        }else{
            binding.txtMinimal.text = String.format(GeneralHelper.getString(R.string.text_minimal), dataMaster!!.minimum!!.gramMin!!.minimumString)
            binding.txtMinimal.setTextColor(GeneralHelper.getColor(R.color.neutral_light50))
        }
    }

    override fun onClick(p0: View?) {
        if (SystemClock.elapsedRealtime() - mLastClickTime < 1000){
            return
        }
        mLastClickTime = SystemClock.elapsedRealtime()

        when(p0!!.id){
            R.id.ly_check->{
                if (isCheck){
                    isCheck = false
                    validationButton()
                }else{
                    isCheck = true
                    binding.tvDebet.visibility = View.VISIBLE
                    validationButton()
                }
                setImageCheck(isCheck)
                showCalender(isCheck)
            }

            R.id.et_pendebetan->{
                selectDate()
            }

            R.id.btnBiru ->{
                presenter.getDataGrafikJual(GrafikEmasRequest("BUY"))
            }

            R.id.btnSubmit->{
                presenter.getDataConfirmationOpen(
                        ConfirmationOpenEmasRequest(
                                GeneralHelper.clearingAmountSigned("Rp"+binding.etSetoranAwal.text.toString()).toLong(),
                                saldoNominalGram,
                                dataMaster!!.buyRate!!.buyRateFloat!!.toLong(),
                                idBranch,
                                model.acoount,
                                isCheck,
                                formatdate

                        )
                )
            }

            R.id.ly_kantor_pegadaian->{
                PilihKantorPegadaianActivity.launchIntent(this)
            }
        }
    }

    private fun selectDate(){
        calendarFragment = SetCalenderAFTFragment(this)
        val args = Bundle()
        args.putBoolean(Constant.TAG_DAY_OF_DATE, true)
        calendarFragment.arguments = args
        calendarFragment.isCancelable = true
        calendarFragment.show(supportFragmentManager,"")
    }

    override fun onSelect(dateSelect: org.threeten.bp.LocalDate) {

        durasiModel = DurasiModel(
                dateSelect.dayOfMonth,
                dateSelect.monthValue,
                dateSelect.year
        )
        setDurasi(durasiModel)
        binding.tvDebet.text = String.format(GeneralHelper.getString(R.string.text_debet),dateSelect.dayOfMonth)
        binding.tvDebet.visibility = View.VISIBLE
    }

    fun setDurasi(durasi: DurasiModel?) {
        durasiModel = durasi
        val tanggalString = "Tanggal " + durasi!!.startDateSiklus
        binding.etPendebetan.setText(tanggalString)
        val hariIni = durasi.startDateSiklus
        val bulanIni = CalendarHelper.getCurrentMonth()
        val tahunIni = CalendarHelper.getCurrentYear()
        stringDate = tahunIni + "-" + bulanIni + "-" + hariIni
        val format = SimpleDateFormat("yyyy-MM-dd")
        val date = format.parse(stringDate)
        val outputDateFormat = SimpleDateFormat("yyyy-MM-dd")
        formatdate = outputDateFormat.format(date!!)
        validationButton()
    }

    private fun setImageCheck(bool : Boolean){
        if (bool)
            binding.imgCheck.setImageResource(R.drawable.check_blue)
        else
            binding.imgCheck.setImageResource(R.drawable.check_abu)
    }

    private fun showCalender(bool: Boolean){
        if (bool){
            binding.lyTglAutodebet.visibility = View.VISIBLE
        }else{
            binding.lyTglAutodebet.visibility = View.GONE
        }
    }

    override fun onSuccessGetDataConfirmation(response: GeneralConfirmationChecklistResponse) {
        KonfirmasiTabunganRevActivity.launchIntent(this, response, model, mSaldoString, GeneralHelper.getString(R.string.url_onboard_payment), true)
    }

    override fun onSuccessGetBeliConfirmation(response: GeneralConfirmationResponse) {

    }

    override fun setDefaultSaldo(saldo: Double, saldoString: String?, account: String?, saldoHold : Boolean?) {
        mSaldoString = saldoString!!
        mSaldo = saldo
        mDefaultAkun = account!!
        mSaldoHold = saldoHold!!
        setupAccount(mSaldo)
    }

    override fun onSuccessDetailEmas(response: DetailEmasResponse) {

    }

    override fun onSuccessGrafikEmas(response: GrafikEmasResponse) {
        GrafikBeliEmasActivity.launchIntent(this, response)
    }

    override fun onException01(message: String) {
        GeneralHelper.showDialogGagalBackDescBerubah(this, Constant.TRANSAKSI_GAGAL, message)
        setResult(RESULT_OK)
    }

    override fun onExceptionTrxExpired(message: String) {
        val i = Intent()
        i.putExtra(Constant.TAG_ERROR_MESSAGE, message)
        setResult(RESULT_CANCELED, i)
        finish()
    }

    fun setupAccount(saldoDoubled : Double) {
        //List Account
        if (dataMaster!!.accountList.isNotEmpty()) {
            mListAccountModel = dataMaster!!.accountList
        }

        //get account default
        for (accountModel in mListAccountModel) {
            if (accountModel.isDefault == 1) {
                model = accountModel
                break
            } else {
                model = mListAccountModel[0]

            }
        }

        if (model.imagePath != null) {
            if (!model.imagePath.equals("", ignoreCase = true)) {
                GeneralHelper.loadImageUrl(
                        this,
                        model.imagePath,
                        binding.lyInc.ivIconRek,
                        R.drawable.bri,
                        0
                )
            } else {
                binding.lyInc.ivIconRek.setImageResource(R.drawable.bri)
            }
        } else {
            binding.lyInc.ivIconRek.setImageResource(R.drawable.bri)
        }

        if (model.acoountString != null) {
            binding.lyInc.tvNoRek.text = model.acoountString
        }
        if (model.alias != null && !model.alias.equals("")) {
            binding.lyInc.tvAliasRek.text = model.alias
        }else{
            binding.lyInc.tvAliasRek.text = "Belum ada alias"
        }

        if (model.acoount != null) {
            val saldoText: String = saldoDoubled.toString()
            if (saldoText != "") {
                saldo = saldoDoubled
                binding.lyInc.tvSaldoRek.text = "Rp."+GeneralHelper.formatNominal(saldoDoubled)
            }
            if (model.acoount == mDefaultAkun) {
                binding.lyInc.tvSaldoRek.text = GeneralHelper.formatNominalIDR(model.currency, saldo)
            }else {
                binding.lyInc.tvSaldoRek.text = "-"
                binding.lyInc.tvAliasRek.text = "- "
                saldo = 0.0
                model.acoount = null
            }
        }

        if (!isFromFastMenu) {
            if (mSaldoHold) binding.lyInc.ivAlertSaldo.visibility = View.VISIBLE
            else binding.lyInc.ivAlertSaldo.visibility = View.GONE
        }

        validationButton()
    }

    override fun onSelectSumberDana(bankModel: AccountModel) {
        model = bankModel

        if (bankModel.saldoReponse != null) {
            binding.lyInc.tvSaldoRek.text = GeneralHelper.formatNominalIDR(
                    model.currency,
                    model.saldoReponse.balanceString
            )
            saldo = bankModel.saldoReponse.balance
        } else {
            binding.lyInc.tvSaldoRek.text = String.format("%s%s", bankModel.currency, "-")
            saldo = 0.0
        }

        binding.lyInc.tvNoRek.text = model.acoountString
        if (bankModel.alias != null){
            if (bankModel.alias.equals("")){
                binding.lyInc.tvAliasRek.text = "Belum ada alias"
            }else{
                binding.lyInc.tvAliasRek.text = bankModel.alias
            }
        }else{
            binding.lyInc.tvAliasRek.text = "Belum ada alias"
        }

        if (bankModel.imagePath != null) {
            if (!bankModel.imagePath.equals("", ignoreCase = true)) {
                GeneralHelper.loadImageUrl(
                        this,
                        bankModel.imagePath,
                        binding.lyInc.ivIconRek,
                        R.drawable.bri,
                        0
                )
            } else {
                binding.lyInc.ivIconRek.setImageResource(R.drawable.bri)
            }
        } else {
            binding.lyInc.ivIconRek.setImageResource(R.drawable.bri)
        }

        if (!isFromFastMenu) {
            if (bankModel.saldoReponse.isOnHold) binding.lyInc.ivAlertSaldo.visibility = View.VISIBLE
            else binding.lyInc.ivAlertSaldo.visibility = View.GONE
        }
        cekSaldoNominal()
        validationButton()

    }

    private fun cekSaldoNominal(){
        if (saldoNominal >= saldo){
            binding.txtMinimal.text = GeneralHelper.getString(R.string.txt_saldo_tidak_cukup)
            binding.txtMinimal.setTextColor(GeneralHelper.getColor(R.color.error80))
        }else{
            binding.txtMinimal.text = String.format(GeneralHelper.getString(R.string.text_minimal), "Rp"+GeneralHelper.formatNominal(minNominal))
            binding.txtMinimal.setTextColor(GeneralHelper.getColor(R.color.neutral_light50))
        }
    }

    override fun onSendFailedList(list: MutableList<Int>?) {
        mListFailed = list
    }

    private fun validationButton() {
        if (saldoNominal >= dataMaster!!.minimum!!.depositMin!!.minimumFloat!!
                && saldoNominal <= dataMaster!!.maximum!!.depositMax!!.maximumFloat!!
                && !model.acoount.isNullOrEmpty()
                && !idBranch.isNullOrEmpty()
                && saldoNominal < saldo
                && saldoNominal < (saldo-model.minimumBalance)) {
            if (isCheck){
                if (!binding.etPendebetan.text.isNullOrEmpty()){
                    binding.btnSubmit.isEnabled = true
                    binding.btnSubmit.setTextColor(GeneralHelper.getColor(R.color.whiteColor))
                }else{
                    binding.btnSubmit.isEnabled = false
                    binding.btnSubmit.setTextColor(GeneralHelper.getColor(R.color.neutral_light60))
                }
            }else{
                binding.btnSubmit.isEnabled = true
                binding.btnSubmit.setTextColor(GeneralHelper.getColor(R.color.whiteColor))
            }
        } else {
            binding.btnSubmit.isEnabled = false
            binding.btnSubmit.setTextColor(GeneralHelper.getColor(R.color.neutral_light60))
        }
    }

    override fun onActivityResult(requestCode: Int, resultCode: Int, data: Intent?) {
        super.onActivityResult(requestCode, resultCode, data)
        if (requestCode == Constant.REQ_BUKA_REKENING && data != null){
            if (resultCode == Constant.REQ_UPDATE) {
                val gson = Gson()
                outlet = gson.fromJson(data.getStringExtra("data_kantor"), Outlets::class.java)
                binding.txtOutlet.text = outlet.namaOutlet
                binding.txtAddressOutlet.text = outlet.alamat
                idBranch = outlet.kodeOutlet!!
                validationButton()
            } else if (resultCode == RESULT_CANCELED && data.getStringExtra(Constant.TAG_ERROR_MESSAGE) != null) {
                setResult(RESULT_CANCELED,data)
                //membalikan data error message agar muncul snackbar
                showSnackbarErrorMessage(
                        data.getStringExtra(Constant.TAG_ERROR_MESSAGE),
                        ALERT_ERROR,
                        this,
                        false
                )
            }
            else if(resultCode == RESULT_FIRST_USER){
                this.setResult(RESULT_FIRST_USER, data)
                finish()
            }
            else if (resultCode == RESULT_OK) {
                setResult(RESULT_OK, data)
                finish()
            }
        }
    }

    override fun onBackPressed() {
        setResult(RESULT_CANCELED)
        finish()
    }
}