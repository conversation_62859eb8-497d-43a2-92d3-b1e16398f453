package id.co.bri.brimo.models.apimodel.request;

import com.google.gson.annotations.Expose;
import com.google.gson.annotations.SerializedName;

public class OtpRegisNdsRequest {
    @SerializedName("username")
    @Expose
    private String username;
    @SerializedName("password")
    @Expose
    private String kunciPas;
    @SerializedName("otp")
    @Expose
    private String otp;
    @SerializedName("registration_code")
    @Expose
    private String code;

    public OtpRegisNdsRequest(String username, String password, String otp, String code) {
        this.username = username;
        this.kunciPas = password;
        this.otp = otp;
        this.code = code;
    }

    public String getUsername() {
        return username;
    }

    public void setUsername(String username) {
        this.username = username;
    }

    public String getKunciPas() {
        return kunciPas;
    }

    public void setKunciPas(String kunciPas) {
        this.kunciPas = kunciPas;
    }

    public String getOtp() {
        return otp;
    }

    public void setOtp(String otp) {
        this.otp = otp;
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }
}
