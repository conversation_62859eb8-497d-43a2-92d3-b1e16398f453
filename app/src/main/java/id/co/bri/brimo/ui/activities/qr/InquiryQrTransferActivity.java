package id.co.bri.brimo.ui.activities.qr;


import android.app.Activity;
import android.content.Intent;
import android.util.Log;

import com.google.gson.Gson;


import id.co.bri.brimo.domain.config.Constant;
import id.co.bri.brimo.domain.helpers.GeneralHelper;
import id.co.bri.brimo.models.ParameterModel;
import id.co.bri.brimo.models.apimodel.response.GeneralConfirmationResponse;
import id.co.bri.brimo.models.apimodel.response.GeneralInquiryResponse;
import id.co.bri.brimo.ui.activities.InquiryGeneralOpenActivity;

public class InquiryQrTransferActivity extends InquiryGeneralOpenActivity {

    private static final String TAG = "InquiryQrTransferActivi";

    public static void launchIntent(Activity caller, GeneralInquiryResponse inquiryResponse, String urlKonfirmasi, String urlPayment, String title, ParameterModel parameterModel, boolean fromfastMenu) {
        Intent intent = new Intent(caller, InquiryQrTransferActivity.class);

        isFromFastMenu = fromfastMenu;

        //put data to Intent
        if (inquiryResponse != null) {
            try {
                intent.putExtra(TAG_INQUIRY_DATA, new Gson().toJson(inquiryResponse));
            } catch (Exception e) {
                if (!GeneralHelper.isProd())
                    Log.e(TAG_INQUIRY_DATA, "launchIntent: ", e);
            }
        }

        if (parameterModel != null) {
            try {
                intent.putExtra(TAG_PARAMS_DATA, new Gson().toJson(parameterModel));
            } catch (Exception e) {
                if (!GeneralHelper.isProd())
                    Log.e(TAG_PARAMS_DATA, "launchIntent: ", e);
            }
        }

        //set URL service
        try {
            intent.putExtra(TAG_URL_KONFIRM, urlKonfirmasi);
            intent.putExtra(TAG_URL_PAYMENT, urlPayment);
            intent.putExtra(TAG_TITLE, title);
        } catch (Exception e) {
            if (!GeneralHelper.isProd())
                Log.e(TAG, "launchIntent: ", e);
        }

        caller.startActivityForResult(intent, Constant.REQ_PAYMENT);
    }

    @Override
    public void onSuccessGetConfirmation(GeneralConfirmationResponse brivaConfirmationResponse) {
        isLoading = false;
        KonfirmasiQrActivity.launchIntent(this, brivaConfirmationResponse, mUrlPayment, getTitleBar(), mParameterKonfirmasiModel, isFromFastMenu);
    }
}