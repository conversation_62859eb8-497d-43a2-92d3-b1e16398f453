package id.co.bri.brimo.ui.activities.registrasirevamp

import android.app.Activity
import android.content.ActivityNotFoundException
import android.content.Intent
import android.content.IntentFilter
import android.os.Bundle
import android.os.CountDownTimer
import android.os.Handler
import android.os.Looper
import android.util.Log
import androidx.activity.OnBackPressedCallback
import androidx.activity.result.ActivityResultLauncher
import androidx.activity.result.contract.ActivityResultContracts
import androidx.core.content.ContextCompat
import androidx.core.text.HtmlCompat
import androidx.localbroadcastmanager.content.LocalBroadcastManager
import com.google.gson.Gson
import id.co.bri.brimo.R
import id.co.bri.brimo.contract.IPresenter.registrasirevamp.IRegistrasiVerifyEmailPresenter
import id.co.bri.brimo.contract.IView.registrasirevamp.IRegistrasiVerifyEmailView
import id.co.bri.brimo.databinding.ActivityRegistrasiVerifyEmailBinding
import id.co.bri.brimo.domain.config.Constant
import id.co.bri.brimo.domain.helpers.GeneralHelper
import id.co.bri.brimo.models.NotifikasiModel
import id.co.bri.brimo.models.apimodel.request.registrasi.RegisEmailRequest
import id.co.bri.brimo.models.apimodel.response.registrasi.RegisEmailResponse
import id.co.bri.brimo.ui.activities.base.BaseActivity
import id.co.bri.brimo.ui.fragments.FragmentBottomDialog
import id.co.bri.brimo.ui.fragments.registrasi.BottomDialogEmailFragment
import javax.inject.Inject


class RegistrasiVerifyEmailActivity : BaseActivity(), IRegistrasiVerifyEmailView,
    BottomDialogEmailFragment.OnCallbackEmail {

    @Inject
    lateinit var presenter: IRegistrasiVerifyEmailPresenter<IRegistrasiVerifyEmailView>

    private lateinit var binding: ActivityRegistrasiVerifyEmailBinding

    private var regisEmailResponse: RegisEmailResponse? = null

    private lateinit var regisId: String

    private var isResume = false

    private var countDownTimer: CountDownTimer? = null
    private val second = 1000
    private val handler = Handler(Looper.getMainLooper())
    private val delayMillis = 15000L // 30 detik

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)

        binding = ActivityRegistrasiVerifyEmailBinding.inflate(layoutInflater)
        setContentView(binding.root)

        onBackPressedDispatcher.addCallback(this, onBackPressedCallback)

        LocalBroadcastManager.getInstance(this)
            .registerReceiver(updates_receiver, IntentFilter(Constant.TAG_NOTIF))

        intentExtra()
        injectDependency()
        setupView()
    }

    private fun intentExtra() {
        if (intent.hasExtra(Constant.TAG_NOTIF))
            parseDataNotifForeground(intent)

        if (intent.hasExtra(Constant.GENRES)) {
            regisEmailResponse = Gson().fromJson(
                intent.getStringExtra(Constant.GENRES), RegisEmailResponse::class.java
            )
        }
    }

    private fun injectDependency() {
        activityComponent.inject(this)
        presenter.view = this
        presenter.setUrlResend(GeneralHelper.getString(R.string.url_registration_resend_email))
        presenter.setUrlCheckProgress(GeneralHelper.getString(R.string.url_registration_check_progress))
        presenter.start()
    }

    private fun setupView() {
        GeneralHelper.setToolbarRevamp(
            this, binding.toolbarRevamp.toolbar, GeneralHelper.getString(R.string.verifikasi_email)
        )

        binding.layoutStep.imgStep1.setImageResource(R.drawable.bg_step_orange)
        binding.layoutStep.imgStep2.setImageResource(R.drawable.bg_step_orange)

        regisId = regisEmailResponse!!.registrationId
        binding.tvDescOtp.text = HtmlCompat.fromHtml(
            String.format(
                GeneralHelper.getString(R.string.desc_verify_email_regis), regisEmailResponse?.email
            ), HtmlCompat.FROM_HTML_MODE_LEGACY
        )

        setTextTimer(regisEmailResponse!!.expiredInSecond)

        binding.btnOpen.setOnClickListener { openEmail() }
        binding.btnEdit.setOnClickListener { showDialogChangeEmail() }
        binding.tvUlangi.setOnClickListener {
            presenter.sendResend(
                RegisEmailRequest(
                    regisId, regisEmailResponse!!.email
                )
            )
        }

        sendRequestPeriodically()
    }

    private fun sendRequestPeriodically() {
        handler.postDelayed({
            presenter.sendCheckProgress()
            sendRequestPeriodically()
        }, delayMillis)
    }

    private fun openEmail() {
        try {
            val intent =
                Intent(Intent.ACTION_MAIN)
            intent.addCategory(Intent.CATEGORY_APP_EMAIL)
            intent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK)
            startActivity(intent)
        } catch (e: ActivityNotFoundException) {
            val intent = Intent(Intent.ACTION_MAIN)
            intent.addCategory(Intent.CATEGORY_APP_EMAIL)
            this.startActivity(intent)
        }

    }

    private fun showDialogChangeEmail() {
        val dialogFragment = BottomDialogEmailFragment(regisEmailResponse!!.email, this)
        dialogFragment.show(supportFragmentManager, "")
        dialogFragment.isCancelable = true
    }

    override fun onExceptionNoBackAction(message: String?) {
        if (GeneralHelper.isContains(Constant.LIST_TYPE_GAGAL, message))
            showDialogConnectionError()
    }

    private fun showDialogConnectionError() {
        val fragmentBottomDialog = FragmentBottomDialog(
            this,
            Constant.KONEKSI_TERPUTUS,
            GeneralHelper.getString(R.string.title_koneksi_terputus),
            GeneralHelper.getString(R.string.desc_koneksi_terputus),
            Constant.IMAGE_KONEKSI_TERPUTUS, false
        ) { onExceptionStatusNotMatch() }
        fragmentBottomDialog.isCancelable = false
        fragmentBottomDialog.show(supportFragmentManager, "")
    }

    private fun setTextTimer(timer: Int) {
        val countDown: Int = second * timer
        countDownTimer = object : CountDownTimer(countDown.toLong(), second.toLong()) {
            override fun onTick(millisUntilFinished: Long) {
                val seconds: Int = millisUntilFinished.toInt() / second
                val timeFormat = GeneralHelper.getTimeFormat(seconds)
                binding.tvTimer.text = String.format(
                    resources.getString(R.string.countdown_otp00_00), timeFormat[1], timeFormat[2]
                )

                binding.tvUlangi.setTextColor(
                    ContextCompat.getColor(
                        applicationContext,
                        R.color.neutral_light50
                    )
                )
                binding.tvUlangi.isEnabled = false

            }

            override fun onFinish() {
                handler.removeCallbacksAndMessages(null)
                binding.tvTimer.text = GeneralHelper.getString(R.string.time00_00)
                binding.tvUlangi.setTextColor(
                    ContextCompat.getColor(
                        applicationContext,
                        R.color.neutral_light10
                    )
                )
                binding.tvUlangi.isEnabled = true
            }
        }.start()
    }

    override fun onSuccessGetResend(regisEmailResponse: RegisEmailResponse) {
        handler.removeCallbacksAndMessages(null)
        sendRequestPeriodically()

        if (countDownTimer != null)
            countDownTimer!!.cancel()

        this.regisEmailResponse = regisEmailResponse
        binding.tvDescOtp.text = HtmlCompat.fromHtml(
            String.format(
                GeneralHelper.getString(R.string.desc_verify_email_regis), regisEmailResponse.email
            ), HtmlCompat.FROM_HTML_MODE_LEGACY
        )
        setTextTimer(regisEmailResponse.expiredInSecond)
    }

    override fun onSuccessProgress(data: String) {
        handler.removeCallbacksAndMessages(null)
        val intent = Intent(this, RegistrasiVerifyWajahActivity::class.java)
        intent.putExtra(Constant.GENRES, data)
        startActivityIntent.launch(intent)
    }

    override fun itemCallbackEmail(email: String) {
        presenter.sendResend(
            RegisEmailRequest(
                regisId, email
            )
        )
    }

    override fun parseDataNotifForeground(intent: Intent?) {
        if (intent != null) {
            try {
                // Get data from notifikasi
                val extras = intent.extras
                if (extras != null) {
                    try {
                        val notifikasiString = extras.getString(Constant.TAG_NOTIF)
                        val gson = Gson()
                        val notifikasiModel = gson.fromJson(
                            notifikasiString, NotifikasiModel::class.java
                        )
                        if (notifikasiModel != null) {
                            if (notifikasiModel.type.equals("RegistrationBrimo")) {
                                presenter.sendCheckProgress()
                            }
                        }
                    } catch (e: Exception) {
                        if (!GeneralHelper.isProd()) Log.e(
                            "TestNotif", "parseDataNotif: ", e
                        )
                    }
                }
            } catch (e: Exception) {
                if (!GeneralHelper.isProd()) Log.e(
                    "TestNotif", "parseDataNotif: ", e
                )
            }
        }
    }

    override fun onPause() {
        handler.removeCallbacksAndMessages(null)
        LocalBroadcastManager.getInstance(this).unregisterReceiver(updates_receiver)
        super.onPause()
    }

    private var startActivityIntent: ActivityResultLauncher<Intent> = registerForActivityResult(
        ActivityResultContracts.StartActivityForResult()
    ) {
        // Add same code that you want to add in onActivityResult method
            result ->
        if (result.resultCode == Activity.RESULT_OK) {
            // There are no request codes
            // val data: Intent? = result.data
        } else {
            if (result.data != null) {
                if (result.data!!.hasExtra(Constant.NAME)) {
                    setResult(RESULT_CANCELED, result.data)
                    finish()
                }
            }
        }
    }

    private val onBackPressedCallback: OnBackPressedCallback =
        object : OnBackPressedCallback(true) {
            override fun handleOnBackPressed() {
                val intent = Intent()
                intent.putExtra(Constant.NAME, Constant.REGISTRATION_BRIMO)
                intent.putExtra(Constant.CHECK_POINT, 3)
                setResult(Activity.RESULT_CANCELED, intent)
                finish()
            }
        }

    override fun onResume() {
        if (isResume) {
            presenter.sendCheckProgress()
        } else isResume = true
        super.onResume()
    }

    override fun onDestroy() {
        handler.removeCallbacksAndMessages(null)
        presenter.stop()
        super.onDestroy()
    }
}