package id.co.bri.brimo.ui.fragments.esbn;

import android.annotation.SuppressLint;
import android.os.Bundle;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;

import androidx.fragment.app.DialogFragment;

import com.google.android.material.bottomsheet.BottomSheetDialogFragment;

import id.co.bri.brimo.R;
import id.co.bri.brimo.databinding.FragmentBottomVerifikasiESBNBinding;
import id.co.bri.brimo.models.BottomDialogModel;
import id.co.bri.brimo.models.apimodel.response.esbn.DashboardDataSbnResponse;


public class BottomFragmentVerifikasiESBN extends BottomSheetDialogFragment {

    private FragmentBottomVerifikasiESBNBinding binding;

    private BottomDialogDefaultListener dialogDefaulListener;
    private DashboardDataSbnResponse.Header header;


    private String stButton;
    BottomDialogModel model;
    private Boolean overLimit;

    @SuppressLint("ValidFragment")
    public BottomFragmentVerifikasiESBN() {

    }

    public BottomFragmentVerifikasiESBN(BottomDialogDefaultListener dialogDefaulListener2, BottomDialogModel model, DashboardDataSbnResponse.Header header, Boolean bool, String stButton) {
        this.model = model;
        this.overLimit = bool;
        this.dialogDefaulListener = dialogDefaulListener2;
        this.stButton = stButton;
        this.header = header;
    }

    @Override
    public void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setStyle(DialogFragment.STYLE_NORMAL, R.style.CustomBottomSheetDialogTheme);
    }

    @Override
    public View onCreateView(LayoutInflater inflater, ViewGroup container,
                             Bundle savedInstanceState) {
        binding = FragmentBottomVerifikasiESBNBinding.inflate(inflater, container, false);

        binding.tvCek1.setText(header.getStep().get(0));
        binding.tvCek2.setText(header.getStep().get(1));
        binding.tvCek3.setText(header.getStep().get(2));
        binding.tvTitle.setText(model.getTitle());
        binding.tvDesc.setText(model.getDesc());
        binding.btnClose.setText(stButton);

        if (overLimit) {
            binding.btnBatalkan.setVisibility(View.VISIBLE);
        } else {
            binding.btnBatalkan.setVisibility(View.GONE);
        }

        binding.btnClose.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View view) {
                if (dialogDefaulListener != null) {
                    dialogDefaulListener.onClickBiruVerifikasi();
                }
                dismiss();
            }
        });
        return binding.getRoot();
    }

    public interface BottomDialogDefaultListener {
        void onClickBiruVerifikasi();
    }
}