package id.co.bri.brimo.ui.fragments.dashboard

import android.app.Activity
import android.content.DialogInterface
import android.content.Intent
import android.graphics.Typeface
import android.os.Bundle
import android.os.SystemClock
import android.util.Log
import android.view.*
import android.widget.FrameLayout
import android.widget.ImageView
import androidx.core.view.isVisible
import com.airbnb.lottie.FontAssetDelegate
import com.airbnb.lottie.LottieAnimationView
import com.airbnb.lottie.LottieComposition
import com.airbnb.lottie.LottieCompositionFactory
import com.airbnb.lottie.LottieOnCompositionLoadedListener
import com.airbnb.lottie.LottieTask
import com.airbnb.lottie.TextDelegate
import com.google.android.exoplayer2.ExoPlayer
import com.google.android.exoplayer2.MediaItem
import com.google.android.exoplayer2.source.DefaultMediaSourceFactory
import com.google.android.exoplayer2.source.ProgressiveMediaSource
import com.google.android.exoplayer2.upstream.DataSource
import com.google.android.exoplayer2.upstream.DefaultDataSource
import com.google.android.exoplayer2.upstream.RawResourceDataSource
import com.google.android.material.bottomsheet.BottomSheetBehavior
import com.google.android.material.bottomsheet.BottomSheetDialog
import com.google.android.material.bottomsheet.BottomSheetDialogFragment
import com.teresaholfeld.stories.StoriesProgressView
import id.co.bri.brimo.R
import id.co.bri.brimo.databinding.FragmentInvestmentRecapBinding
import id.co.bri.brimo.domain.config.Constant
import id.co.bri.brimo.domain.config.InvestasiConfig
import id.co.bri.brimo.ui.activities.bukarekening.TabunganActivity
import id.co.bri.brimo.ui.activities.deposito.DepositoActivity
import id.co.bri.brimo.ui.activities.dplk.InfoDplkActivity
import id.co.bri.brimo.ui.activities.emas.DashboardEmasActivity
import id.co.bri.brimo.ui.activities.portofolioksei.DashboardKseiActivity
import id.co.bri.brimo.ui.activities.sbn.DashboardESBNActivity
import id.co.bri.brimo.ui.activities.sbnrevamp.DashboardSbnRevampActivity
import java.io.BufferedInputStream
import java.io.FileInputStream

class BottomFragmentInvestmentRecap(
    private var caller: Activity,
    private var mListContent: List<LottieTask<LottieComposition>>,
    private var mMappingRoute: String,
    private var animationMapList: List<List<Map<String, String>>> = listOf(),
    private val onClickShare: (view : LottieAnimationView) -> Unit,
) : BottomSheetDialogFragment(), StoriesProgressView.StoriesListener {

    private var _binding: FragmentInvestmentRecapBinding? = null
    private val binding get() = _binding!!
    private lateinit var simpleExoPlayer: ExoPlayer

    private var storiesProgressView: StoriesProgressView? = null
    private var counter = 0

    private var pressTime = 0L
    private var limit = 500L
    var lastClickTime = 0L
    val debounceTime = 1000L

    override fun onCreateView(
        inflater: LayoutInflater, container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View? {
        _binding = FragmentInvestmentRecapBinding.inflate(inflater, container, false)
        return binding.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        dialog?.setOnShowListener {
            val bottomSheetDialog = it as BottomSheetDialog
            setupFullHeight(bottomSheetDialog)

        }

        prepareMediaPlayer()

        this.mListContent = mListContent
        this.animationMapList = animationMapList

        storiesProgressView = binding.stories
        if (mListContent.isNotEmpty() && animationMapList.isNotEmpty()) { // Check both lists
            storiesProgressView?.setStoriesCount(mListContent.size)
            storiesProgressView?.setStoryDuration(9000L)
            storiesProgressView?.setStoriesListener(this)

            // Load the first story
            storiesProgressView?.startStories()
            loadAnimationStory(counter)

            binding.reverse.setOnClickListener { storiesProgressView?.reverse() }
            binding.reverse.setOnTouchListener(onTouchListener)

            binding.viewButton.isVisible = counter == mListContent.size - 1


            binding.viewButton.setOnClickListener{
                binding.viewButton.isEnabled = false
                onClickShare(binding.animationView)
                storiesProgressView?.pause()
            }


            binding.skip.setOnClickListener {if (counter == mListContent.size - 1) {
                onLastStoryClicked()
            } else {
                storiesProgressView?.skip()
            }
            }
            binding.skip.setOnTouchListener(onTouchListener)
        } else {
            dismiss() // Dismiss if either list is empty
        }

    }

    private fun prepareMediaPlayer() {
        // Create the URI to the raw resource file
        val rawResourceUri = RawResourceDataSource.buildRawResourceUri(R.raw.invesment_recap_backsong)

        // Create a data source factory that uses RawResourceDataSource
        val dataSourceFactory = DataSource.Factory { RawResourceDataSource(caller) }

        // Create the media source using the raw resource URI
        val mediaSource = ProgressiveMediaSource.Factory(dataSourceFactory)
            .createMediaSource(MediaItem.fromUri(rawResourceUri))

        val mediaSourceFactory = DefaultMediaSourceFactory(dataSourceFactory)

        simpleExoPlayer = ExoPlayer.Builder(caller)
            .setMediaSourceFactory(mediaSourceFactory)
            .build()

        simpleExoPlayer.addMediaSource(mediaSource)
        simpleExoPlayer.prepare()
    }

    private fun setupFullHeight(bottomSheetDialog: BottomSheetDialog) {
        val bottomSheet = bottomSheetDialog.findViewById<FrameLayout>(R.id.design_bottom_sheet)
        val behavior = BottomSheetBehavior.from(bottomSheet!!)
        behavior.peekHeight = 0 // Set peek height to 0

        val layoutParams = bottomSheet.layoutParams
        layoutParams?.height = WindowManager.LayoutParams.MATCH_PARENT
        bottomSheet.layoutParams = layoutParams
        behavior.state = BottomSheetBehavior.STATE_EXPANDED

        behavior.addBottomSheetCallback(object : BottomSheetBehavior.BottomSheetCallback() {
            override fun onStateChanged(bottomSheet: View, newState: Int) {
                if (newState == BottomSheetBehavior.STATE_HIDDEN) {
                    simpleExoPlayer.stop()
                }else if (newState == BottomSheetBehavior.STATE_COLLAPSED){
                    simpleExoPlayer.pause()
                    storiesProgressView?.destroy()
                    dismiss()
                }
            }

            override fun onSlide(bottomSheet: View, slideOffset: Float) {
                // Handle slide progress if necessary
            }
        })
    }


    private fun onLastStoryClicked() {
        dismiss()
        simpleExoPlayer.stop()
        when (mMappingRoute) {
             InvestasiConfig.menuInvestasi.DEPOSITO_DASHBOARD.menu -> {
                val newIntent = Intent(caller, DepositoActivity::class.java)
                startActivityForResult(newIntent, Constant.REQ_PAYMENT)
            }
            InvestasiConfig.menuInvestasi.EMAS_DASHBOARD.menu,  InvestasiConfig.menuInvestasi.EMAS_OPEN.menu ->
                DashboardEmasActivity.launchIntent(caller, false)
            InvestasiConfig.menuInvestasi.SBN_DASHBOARD.menu,  InvestasiConfig.menuInvestasi.SBN_OPEN.menu ->
                DashboardSbnRevampActivity.launchIntent(caller, false, false)
            InvestasiConfig.menuInvestasi.DPLK_DASHBOARD.menu ->
                InfoDplkActivity.LaunchIntent(caller)
            InvestasiConfig.menuInvestasi.DPLK_OPEN.menu -> {
                // Handle "dplk_open" case if necessary
            }
            InvestasiConfig.menuInvestasi.DEPOSITO_OPEN.menu -> {
                // Handle "deposito_open" case if necessary
            }
            InvestasiConfig.menuInvestasi.KSEI_DASHBOARD.menu ->
                DashboardKseiActivity.launchIntent(caller, false)
            InvestasiConfig.menuInvestasi.RENCANA_DASHBOARD.menu ->
                TabunganActivity.launchIntent(caller)
            null -> dismiss() // Default case
        }
    }

    private val onTouchListener = View.OnTouchListener { v, event ->
        when (event.action) {
            MotionEvent.ACTION_DOWN -> {
                pressTime = System.currentTimeMillis()
                storiesProgressView?.pause()
                return@OnTouchListener false
            }
            MotionEvent.ACTION_UP -> {
                val now = System.currentTimeMillis()
                storiesProgressView?.resume()
                return@OnTouchListener limit < now - pressTime
            }
        }
        false
    }

    override fun onNext() {
        if (counter + 1 < mListContent.size) {
            loadAnimationStory(++counter)
            binding.viewButton.isVisible = counter == mListContent.size - 1
        }
    }

    override fun onPrev() {
        if (counter - 1 >= 0) {
            loadAnimationStory(--counter)
        }
    }

    override fun onComplete() {
        onLastStoryClicked()
    }


    private fun loadAnimationStory(animationIndex: Int) {
        if (animationIndex in animationMapList.indices) {
            val textMaps = animationMapList.getOrNull(animationIndex) ?: return
            val animationView = binding.animationView ?: return

            mListContent[animationIndex].addListener { composition ->
                composition?.let {
                    val textDelegate = TextDelegate(animationView)
                    textMaps.forEach { textMap ->
                        textMap.forEach { (key, value) ->
                            textDelegate.setText(key, value)
                        }
                    }
                    animationView.setTextDelegate(textDelegate)
                    animationView.setComposition(it)
                    animationView.playAnimation()
                }
                simpleExoPlayer.playWhenReady = true
                simpleExoPlayer.repeatMode = ExoPlayer.REPEAT_MODE_ALL
            }.addFailureListener {
                // Handle failure
            }
        }
    }


    override fun onPause() {
        super.onPause()
        simpleExoPlayer.pause()
    }

    override fun onDestroyView() {
        super.onDestroyView()
        _binding = null
    }

    override fun onDestroy() {
        super.onDestroy()
        simpleExoPlayer.stop()
    }

    override fun onResume() {
        super.onResume()
        binding.viewButton.isEnabled = true
        simpleExoPlayer.playWhenReady = true
        storiesProgressView?.resume()
    }
    override fun onStart() {
        super.onStart()

        dialog?.window?.clearFlags(WindowManager.LayoutParams.FLAG_DIM_BEHIND)
    }

}