package id.co.bri.brimo.ui.activities.rdn;

import android.app.Activity;
import android.content.Intent;
import android.os.Bundle;
import android.view.View;
import android.widget.LinearLayout;
import androidx.annotation.Nullable;
import androidx.fragment.app.Fragment;
import androidx.viewpager.widget.ViewPager;
import java.util.ArrayList;
import java.util.List;
import javax.inject.Inject;
import id.co.bri.brimo.R;
import id.co.bri.brimo.adapters.CatatanKeuanganAdapter;
import id.co.bri.brimo.contract.IPresenter.rdn.IDescriptionRdnProductPresenter;
import id.co.bri.brimo.contract.IView.rdn.IDescriptionRdnProductView;
import id.co.bri.brimo.databinding.ActivityDescriptionProductBinding;
import id.co.bri.brimo.domain.config.Constant;
import id.co.bri.brimo.domain.helpers.GeneralHelper;
import id.co.bri.brimo.models.apimodel.response.rdn.RdnCheckpointResponse;
import id.co.bri.brimo.models.apimodel.response.rdn.RdnOnBoardingResponse;
import id.co.bri.brimo.ui.activities.base.BaseActivity;
import id.co.bri.brimo.ui.fragments.DetailTabTabunganFragment;
import id.co.bri.brimo.ui.fragments.rdn.RdnConsentBottomFragment;

public class DescriptionRdnProductActivity extends BaseActivity implements ViewPager.OnPageChangeListener, View.OnClickListener, RdnConsentBottomFragment.OnFragmentInteractionListener, IDescriptionRdnProductView {

    private ActivityDescriptionProductBinding binding;

    LinearLayout linearLayout;
    public static RdnOnBoardingResponse rdnOnBoardingResponse;
    private static List<String> titleTab = new ArrayList<>();
    private static List<String> webTab = new ArrayList<>();
    
    private static final String TAG_ONBOARDING = "onboarding";

    RdnConsentBottomFragment consentBottomFragment;

    @Inject
    IDescriptionRdnProductPresenter<IDescriptionRdnProductView> presenter;

    public static void launchIntent(Activity caller){
        Intent i = new Intent(caller,DescriptionRdnProductActivity.class);
        caller.startActivityForResult(i,Constant.REQ_PAYMENT);
    }

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        binding = ActivityDescriptionProductBinding.inflate(getLayoutInflater());
        setContentView(binding.getRoot());

        injectDependency();

        setupView();

        binding.btnSubmit.setOnClickListener(this);
    }

    private void injectDependency() {
        getActivityComponent().inject(this);
        if(presenter!=null){
            presenter.setView(this);
            presenter.setUrl(GeneralHelper.getString(R.string.url_rdn_start_reg));
            presenter.start();
        }
    }

    private void setupView() {
        GeneralHelper.setToolbar(this, binding.toolbar.toolbar, "Buka Rekening Efek - RDN");

        GeneralHelper.loadImageUrl(getApplicationContext(), "", binding.imageRekening, R.drawable.brights_logo, 0);

        titleTab.clear();
        webTab.clear();

        binding.tvDescription.setText(rdnOnBoardingResponse.getDetailProduct().getFullDescription());

        for (int i = 0; i < rdnOnBoardingResponse.getDetailProduct().getTab().size(); i++) {
            titleTab.add(rdnOnBoardingResponse.getDetailProduct().getTab().get(i).getTitle());
            webTab.add(rdnOnBoardingResponse.getDetailProduct().getTab().get(i).getContent());
        }


        List<Fragment> fragmentList = new ArrayList<>();
        for (int i = 0; i < webTab.size(); i++) {
            fragmentList.add(new DetailTabTabunganFragment(webTab.get(i)));
        }

        CatatanKeuanganAdapter viewAdapter = new CatatanKeuanganAdapter(getSupportFragmentManager(), this, fragmentList, titleTab);
        binding.viewpager.setAdapter(viewAdapter);

        binding.tabDetail.setViewPager(binding.viewpager);

        binding.tabDetail.setOnPageChangeListener(this);
        linearLayout = (LinearLayout) binding.tabDetail.getChildAt(0);

        GeneralHelper.changeTabsFontSimple(this, linearLayout, 0);
    }

    @Override
    public void onPageScrolled(int position, float positionOffset, int positionOffsetPixels) {

    }

    @Override
    public void onPageSelected(int position) {
        GeneralHelper.changeTabsFontSimple(this, linearLayout, position);
    }

    @Override
    public void onPageScrollStateChanged(int state) {

    }

    @Override
    public void onClick(View view) {
        switch (view.getId()){
            case R.id.btnSubmit:
                 consentBottomFragment = new RdnConsentBottomFragment(rdnOnBoardingResponse.getConsent().getTitle(),rdnOnBoardingResponse.getConsent().getDescription(),this);
                consentBottomFragment.show(getSupportFragmentManager(),"");
                break;
        }
    }

    @Override
    public void onClickConsent() {
        presenter.onSendDescription();
        consentBottomFragment.dismiss();
    }

    @Override
    public void onSuccessSendDescription(RdnCheckpointResponse response) {
        Step1RdnDocumentActivity.launchIntent(this,response.getCheckpointId());
    }

    @Override
    protected void onActivityResult(int requestCode, int resultCode, @Nullable Intent data) {
        super.onActivityResult(requestCode, resultCode, data);
        if (resultCode == RESULT_OK && data != null) {
            setResult(RESULT_OK, data);
            finish();
        } else {
            if (data != null) {
                setResult(RESULT_CANCELED, data);
                finish();
            }
        }

        if (requestCode == Constant.REQ_PAYMENT && data == null) {
            if (resultCode == RESULT_OK) {
                setResult(RESULT_OK);
                this.finish();
            }
        }

        if (requestCode == Constant.REQ_REGIS){
            if (resultCode == Activity.RESULT_OK){
                setResult(RESULT_OK);
                this.finish();
            }
            else if (resultCode == Activity.RESULT_CANCELED && data != null) {
                setResult(RESULT_CANCELED,data);
                this.finish();
            }
            else {
                setResult(RESULT_CANCELED);
                this.finish();
            }
        }

    }

    @Override
    protected void onDestroy() {
        super.onDestroy();
        binding = null;
    }
}