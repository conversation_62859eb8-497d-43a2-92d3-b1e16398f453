package id.co.bri.brimo.ui.activities.pengelolaankartu

import android.content.Context
import android.content.Intent
import android.os.Bundle
import androidx.fragment.app.commit
import id.co.bri.brimo.R
import id.co.bri.brimo.databinding.ActivityChangePindebitBinding
import id.co.bri.brimo.domain.helpers.GeneralHelper
import id.co.bri.brimo.domain.providers.CardType
import id.co.bri.brimo.models.activationdebit.response.ActivationDebitResponse
import id.co.bri.brimo.ui.activities.base.BaseActivity
import id.co.bri.brimo.ui.activities.activationdebit.ActivationDebitOtpActivity
import id.co.bri.brimo.ui.fragments.pengelolaankartu.InputNewPinFragment

class ChangePINDebitActivity : BaseActivity() {

    private val binding by lazy(LazyThreadSafetyMode.NONE) { ActivityChangePindebitBinding.inflate(layoutInflater) }
    private var cardNumberExtra: String? = null
    private var cardToken: String? = null
    private var cardType: CardType = CardType.DEBIT
    private var isByPassOldPin: Boolean = false
    private var activationDebitResponse: ActivationDebitResponse? = null

    companion object {
        const val CARD_NUMBER_EXTRA = "card-number"
        const val CHANGE_PIN_MESSAGE = "change_pin_message"
        const val REF_NUM_EXTRA = "ref-num"
        const val CARD_TOKEN = "card-token"
        const val CARD_TYPE_EXTRA = "card-type"
        const val EXTRA_VALIDATE_DEBIT = "extra_validate_debit"
        const val EXTRA_PIN = "extra_pin"
        const val BY_PASS_OLD_PIN = "by-pass-old-pin"

        fun launch(
            context: Context,
            cardNumber: String,
            refNum: String,
            cardToken: String,
            cardType: CardType
        ): Intent = Intent(context, ChangePINDebitActivity::class.java).also {
            it.putExtra(CARD_NUMBER_EXTRA, cardNumber)
            it.putExtra(REF_NUM_EXTRA, refNum)
            it.putExtra(CARD_TOKEN, cardToken)
            it.putExtra(CARD_TYPE_EXTRA, cardType.name)
        }

        fun launch(
            context: Context,
            cardNumber: String,
            refNum: String,
            isByPassOldPin: Boolean,
            cardType: CardType
        ): Intent = Intent(context, ChangePINDebitActivity::class.java).also {
            it.putExtra(CARD_NUMBER_EXTRA, cardNumber)
            it.putExtra(REF_NUM_EXTRA, refNum)
            it.putExtra(BY_PASS_OLD_PIN, isByPassOldPin)
            it.putExtra(CARD_TYPE_EXTRA, cardType.name)
        }

        fun launch(
            context: Context,
            activationDebitResponse: ActivationDebitResponse?,
            refNum: String,
            cardType: CardType,
        ): Intent = Intent(context, ChangePINDebitActivity::class.java).also {
            it.putExtra(EXTRA_VALIDATE_DEBIT, activationDebitResponse)
            it.putExtra(CARD_TYPE_EXTRA, cardType.name)
            it.putExtra(REF_NUM_EXTRA, refNum)
        }
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContentView(binding.root)

        getBundleData()
        setToolbar()
        setViews()
    }

    private fun getBundleData() {
        cardNumberExtra = intent?.getStringExtra(CARD_NUMBER_EXTRA)
        cardToken = intent?.getStringExtra(CARD_TOKEN)
        isByPassOldPin = intent?.getBooleanExtra(BY_PASS_OLD_PIN, false) ?: false
        cardType = intent?.getStringExtra(CARD_TYPE_EXTRA)?.let { CardType.valueOf(it) } ?: CardType.DEBIT
        activationDebitResponse = intent?.getParcelableExtra(EXTRA_VALIDATE_DEBIT)
    }

    private fun setToolbar() {
        when {
            cardType != CardType.DEBIT_ACTIVATION -> {
                GeneralHelper.setToolbar(
                    this,
                    binding.toolbar.toolbar,
                    GeneralHelper.handleStrCardType(
                        R.string.change_pin_credit_debit_card_label,
                        cardType
                    )
                )
            }

            else -> {
                GeneralHelper.setToolbar(
                    this,
                    binding.toolbar.toolbar,
                    GeneralHelper.getString(R.string.txt_create_new_pin)
                )
            }
        }
        setStatusColor(R.color.primaryBlue80)
    }

    private fun setViews() {
        val args = Bundle().apply {
            putString(CARD_NUMBER_EXTRA, cardNumberExtra)
            putString(CARD_TOKEN, cardToken)
            putBoolean(BY_PASS_OLD_PIN, isByPassOldPin)
            putString(CARD_TYPE_EXTRA, cardType.name)
            putParcelable(EXTRA_VALIDATE_DEBIT, activationDebitResponse)
        }
        supportFragmentManager.commit(allowStateLoss = true) {
            add(
                R.id.fragment_container,
                InputNewPinFragment::class.java,
                args,
                InputNewPinFragment.TAG,
            )
        }
    }
}