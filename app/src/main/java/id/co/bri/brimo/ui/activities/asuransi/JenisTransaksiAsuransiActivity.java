package id.co.bri.brimo.ui.activities.asuransi;

import android.app.Activity;
import android.content.Intent;
import android.os.Bundle;

import androidx.annotation.Nullable;
import androidx.recyclerview.widget.LinearLayoutManager;

import com.ethanhua.skeleton.Skeleton;
import com.ethanhua.skeleton.SkeletonScreen;

import javax.inject.Inject;

import id.co.bri.brimo.R;
import id.co.bri.brimo.adapters.ListJenisTransaksiAsuransiAdapter;
import id.co.bri.brimo.contract.IPresenter.asuransi.IJenisTransaksiAsuransiPresenter;
import id.co.bri.brimo.contract.IView.asuransi.IJenisTransaksiAsuransiView;
import id.co.bri.brimo.databinding.ActivityJenisTransaksiAsuransiBinding;
import id.co.bri.brimo.domain.config.Constant;
import id.co.bri.brimo.domain.helpers.GeneralHelper;
import id.co.bri.brimo.models.apimodel.response.JenisTransaksiAsuransiResponse;
import id.co.bri.brimo.models.apimodel.response.ProdukAsuransiResponse;
import id.co.bri.brimo.ui.activities.FormAsuransiActivity;
import id.co.bri.brimo.ui.activities.base.BaseActivity;

public class JenisTransaksiAsuransiActivity extends BaseActivity implements ListJenisTransaksiAsuransiAdapter.onClickItem, IJenisTransaksiAsuransiView {

    SkeletonScreen skeletonScreen;
    ListJenisTransaksiAsuransiAdapter adapter;
    @Inject
    IJenisTransaksiAsuransiPresenter<IJenisTransaksiAsuransiView> presenter;
    private ActivityJenisTransaksiAsuransiBinding binding;

    public static void launchIntent(Activity caller) {
        Intent intent = new Intent(caller, JenisTransaksiAsuransiActivity.class);
        caller.startActivityForResult(intent, Constant.REQ_PAYMENT);
    }

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        binding = ActivityJenisTransaksiAsuransiBinding.inflate(getLayoutInflater());
        setContentView(binding.getRoot());

        GeneralHelper.setToolbar(this, binding.toolbar.toolbar, getString(R.string.asuransi));
        injectDependency();

        setupLayout();
    }

    private void setupLayout() {

        LinearLayoutManager linearLayoutManager = new LinearLayoutManager(this);
        binding.rvJenisTransaksiAsuransi.setLayoutManager(linearLayoutManager);
        binding.rvJenisTransaksiAsuransi.setHasFixedSize(true);

        skeletonScreen = Skeleton.bind(binding.rvJenisTransaksiAsuransi)
                .shimmer(true)
                .angle(20)
                .count(2)
                .duration(1200)
                .load(R.layout.skeleton_item_jenis_transaksi_asuransi)
                .color(R.color.white)
                .show();
    }

    @Override
    protected void onResume() {
        super.onResume();
        if (presenter != null) {
            presenter.setView(this);
            presenter.setUrl(GeneralHelper.getString(R.string.url_jenis_transaksi_brilife));
//            presenter.setUrlProduk(GeneralHelper.getString(R.string.url_produk_asuransi_brilife));
            presenter.setUrlProduk(GeneralHelper.getString(R.string.url_produk_asuransi_new));
            presenter.start();
        }

    }

    private void injectDependency() {
        getActivityComponent().inject(this);
        if (presenter != null) {
            presenter.setView(this);
            presenter.setUrl(GeneralHelper.getString(R.string.url_jenis_transaksi_brilife));
//            presenter.setUrlProduk(GeneralHelper.getString(R.string.url_produk_asuransi_brilife));
            presenter.setUrlProduk(GeneralHelper.getString(R.string.url_produk_asuransi_new));
            presenter.start();
        }
    }

    /**
     * @param position mapping activity by position
     */
    @Override
    public void onClickDetail(int position) {
        switch (position) {
            case 0:
                FormAsuransiActivity.launchIntent(this, false);
                break;
            case 1:
                showProgress();
                presenter.getProduk();
                break;
        }
    }

    @Override
    public void onSuccess(JenisTransaksiAsuransiResponse response) {
        LinearLayoutManager linearLayoutManager = new LinearLayoutManager(this);
        binding.rvJenisTransaksiAsuransi.setLayoutManager(linearLayoutManager);
        binding.rvJenisTransaksiAsuransi.setHasFixedSize(true);
        adapter = new ListJenisTransaksiAsuransiAdapter(response.getListOfMenu(), this, this);
        binding.rvJenisTransaksiAsuransi.setAdapter(adapter);
        adapter.notifyDataSetChanged();
    }

    @Override
    public void onSuccessGetProduk(ProdukAsuransiResponse response) {
        ListProdukAsuransiActivity.launchIntent(this, response);
    }

    @Override
    protected void onActivityResult(int requestCode, int resultCode, @Nullable Intent data) {
        super.onActivityResult(requestCode, resultCode, data);
        if (requestCode == Constant.REQ_PAYMENT && data != null) {
            if (resultCode == Activity.RESULT_OK) {
                this.setResult(RESULT_OK, data);
                this.finish();
            }
            if (resultCode == Activity.RESULT_CANCELED) {
                if (data.hasExtra(Constant.TAG_ERROR_MESSAGE)) {
                    onException(data.getStringExtra(Constant.TAG_ERROR_MESSAGE));
                }
            }
        } else if (requestCode == Constant.REQ_FORGET_PIN) {
            if (resultCode == RESULT_OK) {
                this.setResult(RESULT_OK);
                this.finish();
            }
        }
    }

    @Override
    public void onException(String message) {
        showSnackbarErrorMessage(message, ALERT_ERROR, this, false);
    }

    @Override
    protected void onDestroy() {
        presenter.stop();
        binding = null;
        super.onDestroy();
    }
}