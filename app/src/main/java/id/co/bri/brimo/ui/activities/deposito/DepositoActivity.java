package id.co.bri.brimo.ui.activities.deposito;

import android.app.Activity;
import android.content.Intent;
import android.os.Bundle;
import android.os.SystemClock;
import android.view.View;
import android.widget.Button;
import android.widget.LinearLayout;

import androidx.annotation.Nullable;
import androidx.fragment.app.FragmentTransaction;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.swiperefreshlayout.widget.SwipeRefreshLayout;
import com.ethanhua.skeleton.Skeleton;
import com.ethanhua.skeleton.SkeletonScreen;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import javax.inject.Inject;
import id.co.bri.brimo.R;
import id.co.bri.brimo.adapters.ListDepositoAdapter;
import id.co.bri.brimo.contract.IPresenter.saldo.IDepositoPresenter;
import id.co.bri.brimo.contract.IView.saldo.IDepositoView;
import id.co.bri.brimo.databinding.ActivityDepositoBinding;
import id.co.bri.brimo.domain.config.Constant;
import id.co.bri.brimo.domain.helpers.GeneralHelper;
import id.co.bri.brimo.models.apimodel.response.DepositoResponse;
import id.co.bri.brimo.models.apimodel.response.DetailDepositoResponse;
import id.co.bri.brimo.models.apimodel.response.TermConditionTabRes;
import id.co.bri.brimo.ui.activities.base.BaseActivity;
import id.co.bri.brimo.ui.customviews.dialog.DialogInformation;

public class DepositoActivity extends BaseActivity implements
        IDepositoView,
        SwipeRefreshLayout.OnRefreshListener,
        ListDepositoAdapter.ClickItem,
        DialogInformation.OnActionClick {

    private ActivityDepositoBinding binding;

    private SkeletonScreen skeletonScreen;
    ListDepositoAdapter listDepositoAdapter;
    List<DepositoResponse.Account> listitems = new ArrayList<>();
    protected static String errorMessage = null;

    private boolean isLoading = false;

    private boolean isFromResutlOke = false;

    private Intent mData;

    @Inject
    IDepositoPresenter<IDepositoView> activityPresenter;

    public static void launchIntent(Activity caller) {
        Intent intent = new Intent(caller, DepositoActivity.class);
        caller.startActivityForResult(intent, Constant.REQ_PAYMENT);
    }
    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        binding = ActivityDepositoBinding.inflate(getLayoutInflater());
        setContentView(binding.getRoot());

        injectDependency();

        GeneralHelper.setToolbar(this, binding.tbDeposito.toolbar, GeneralHelper.getString(R.string.toolbar_deposito));

        LinearLayoutManager linearLayoutManager = new LinearLayoutManager(this);
        binding.rvRekening.setLayoutManager(linearLayoutManager);
        binding.rvRekening.setHasFixedSize(true);
        listDepositoAdapter = new ListDepositoAdapter(getApplicationContext(), listitems, this);

        skeletonScreen = Skeleton.bind(binding.rvRekening)
                .adapter(listDepositoAdapter)
                .shimmer(true)
                .angle(20)
                .frozen(false)
                .duration(1200)
                .load(R.layout.item_skeleton_list_rekening)
                .show(); //default count is 10

        binding.swipeRefreshRekening.setOnRefreshListener(this);

        binding.btnRekeningBaru.setOnClickListener(v -> {
            if (SystemClock.elapsedRealtime() - mLastClickTime < 1500) {
                return;
            }
            mLastClickTime = SystemClock.elapsedRealtime();
            if (activityPresenter != null)
                activityPresenter.getDataTerm();
        });
    }


    protected void injectDependency() {
        getActivityComponent().inject(this);

        if (activityPresenter != null) {
            activityPresenter.setView(this);
            activityPresenter.setFormUrl(GeneralHelper.getString(R.string.url_form_deposito));
            activityPresenter.setDetailUrl(GeneralHelper.getString(R.string.url_detail_deposito));
            activityPresenter.setTermUrl(GeneralHelper.getString(R.string.url_deposito_open_term_condition));
            activityPresenter.start();
//            activityPresenter.getDataDeposito();
        }
    }


    @Override
    public void onSuccessGetData(DepositoResponse depositoResponse) {
        skeletonScreen.hide();
        listitems = depositoResponse.getAccount();
        if (listitems.isEmpty()) {
            binding.defaultDeposito.setVisibility(View.VISIBLE);
            binding.rvRekening.setVisibility(View.GONE);
        } else {
            binding.rvRekening.setVisibility(View.VISIBLE);
            binding.defaultDeposito.setVisibility(View.GONE);
        }

        listDepositoAdapter.setItems(listitems);
        listDepositoAdapter.notifyDataSetChanged();
    }

    @Override
    public void onGetSaldo(List<DepositoResponse.Account> accountList, boolean isRefreshed) {

        for (DepositoResponse.Account account : accountList) {

            if (account.getDetailDepositoResponse() != null && account.getDetailDepositoResponse().getBalance() != null)
                account.getDetailDepositoResponse().getBalance();
        }

        listitems = accountList;
        listDepositoAdapter.notifyDataSetChanged();
    }

    @Override
    public void onSuccessGetDetail(DetailDepositoResponse detailDepositoResponse) {
        DetailDepositoActivity.launchIntent(this, detailDepositoResponse);
    }

    @Override
    public void showProgress() {
        isLoading = true;
        binding.swipeRefreshRekening.setEnabled(false);
    }

    @Override
    public void hideProgress() {
        binding.swipeRefreshRekening.setRefreshing(false);
    }

    @Override
    public void onException(String message) {
        if (GeneralHelper.isContains(Constant.LIST_TYPE_GAGAL, message))
            GeneralHelper.showDialogGagalBack(this, message);
        else
            showSnackbarErrorMessage(message, ALERT_ERROR, this, false);
    }

    @Override
    public void onException12(String message) {
        Intent intentReturn = new Intent();
        intentReturn.putExtra(Constant.TAG_ERROR_MESSAGE, message);
        setResult(RESULT_CANCELED, intentReturn);
        finish();
    }

    @Override
    public void onGetSaldoComplete() {
        isLoading = false;
        binding.swipeRefreshRekening.setEnabled(true);
    }

    @Override
    public void showProgressTerm() {
        GeneralHelper.showDialog(this);
    }

    @Override
    public void hideProgressTerm() {
        GeneralHelper.dismissDialog();
    }

    @Override
    public void onGetDataTerm(TermConditionTabRes termConditionTabRes) {
        InfoBukaDepositoActivity.launchIntent(this, termConditionTabRes);
    }

    @Override
    public void onGetDataProses() {
        DialogInformation dialog = new DialogInformation(this, GeneralHelper.getString(R.string.mohon_tunggu), GeneralHelper.getString(R.string.desc_tunggu), this, "maaf_maaf");
        FragmentTransaction ft = getSupportFragmentManager().beginTransaction();
        ft.add(dialog, null);
        ft.commitAllowingStateLoss();
    }

    @Override
    public void onRefresh() {
        if (!isLoading) {
            if (listitems == null) {
                skeletonScreen = Skeleton.bind(binding.rvRekening)
                        .adapter(listDepositoAdapter)
                        .shimmer(true)
                        .angle(20)
                        .frozen(false)
                        .duration(1200)
                        .load(R.layout.item_skeleton_list_rekening)
                        .show(); //default count is 10
                activityPresenter.getDataDeposito();
            } else {
                for (DepositoResponse.Account account : listitems)
                    account.setDetailDepositoResponse(null);
                listDepositoAdapter.notifyDataSetChanged();
                activityPresenter.getSaldoRefresh(listitems, true);
            }
        }
    }

    @Override
    public void onClick(DetailDepositoResponse detailDepositoResponse) {
        if (SystemClock.elapsedRealtime() - mLastClickTime < 1000) {
            return;
        }
        DetailDepositoActivity.launchIntent(this, detailDepositoResponse);
    }

    @Override
    protected void onDestroy() {
        activityPresenter.stop();
        binding = null;
        super.onDestroy();
    }


    @Override
    public void onBackPressed() {
       if (isFromResutlOke){
           setResult(RESULT_OK, mData);
           finish();
       }else{
           setResult(RESULT_CANCELED, mData);
           finish();
       }
    }

    @Override
    protected void onActivityResult(int requestCode, int resultCode, @Nullable Intent data) {
        super.onActivityResult(requestCode, resultCode, data);
        if (requestCode == Constant.REQ_PAYMENT) {
            if (resultCode == RESULT_OK) {
                setResult(RESULT_OK, data);
                mData=data;
                isFromResutlOke = true;
                if (data != null && data.hasExtra(Constant.TAG_MESSAGE)) {
                    GeneralHelper.showSnackBarGreen(Objects.requireNonNull(this).findViewById(R.id.content), data.getStringExtra(Constant.TAG_MESSAGE));
                }
                activityPresenter.stop();
                activityPresenter.start();
                skeletonScreen.show();

            } else {
                isFromResutlOke =false;
                mData = data;
                setResult(RESULT_CANCELED, data);
            }
        }
    }

    @Override
    public void onClickAction() {

    }
}