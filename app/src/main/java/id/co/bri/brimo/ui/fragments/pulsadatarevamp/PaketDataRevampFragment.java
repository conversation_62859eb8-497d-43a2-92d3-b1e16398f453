package id.co.bri.brimo.ui.fragments.pulsadatarevamp;

import android.annotation.SuppressLint;
import android.os.Build;
import android.os.Bundle;
import android.util.Log;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.LinearLayout;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.coordinatorlayout.widget.CoordinatorLayout;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.google.gson.Gson;
import com.tbuonomo.viewpagerdotsindicator.DotsIndicator;

import java.util.ArrayList;
import java.util.List;

import id.co.bri.brimo.R;
import id.co.bri.brimo.adapters.ViewPagerAdapterPaketData;
import id.co.bri.brimo.adapters.option.OptionPaketDataRevampAdapter;
import id.co.bri.brimo.databinding.FragmentPaketDataBinding;
import id.co.bri.brimo.domain.helpers.GeneralHelper;
import id.co.bri.brimo.models.apimodel.response.DataListRevamp;
import id.co.bri.brimo.models.apimodel.response.pulsarevamp.ProviderItem;
import id.co.bri.brimo.ui.activities.base.BaseActivity;
import id.co.bri.brimo.ui.activities.pulsadata.InquiryPulsaRevampRevActivity;
import id.co.bri.brimo.ui.customviews.EnhancedWrapContentViewPager;
import id.co.bri.brimo.ui.fragments.BaseFragment;
import io.rmiri.skeleton.utils.ConverterUnitUtil;

public class PaketDataRevampFragment extends BaseFragment implements OptionPaketDataRevampAdapter.onClickPaketListener, PaketDataCustomFragment.onClickPaketListenerCustom {

    private FragmentPaketDataBinding binding;

    private List<DataListRevamp> paketModelList = new ArrayList<>();
    private List<DataListRevamp> paketCustomModelList = new ArrayList<>();
    private OptionPaketDataRevampAdapter paketAdapter;
    private selectedPaketListener selectedPaketListener = null;
    private ProviderItem provider = null;
    private String dataId = "";
    private final String TAG_FRAGMENT_PAKET_CUSTOM = "FragmentPaketDataCustom";
    private final String TAG_FRAGMENT_PAKET_CUSTOM_POSITION = "FragmentPaketDataCustomPosition";

    private ViewPagerAdapterPaketData viewPagerAdapterPaketCustom;
    private static final String TAG = "PaketDataRevampFragment";

    public PaketDataRevampFragment() {
    }

    // Inflate the view for the fragment based on layout XML
    @Override
    public View onCreateView(LayoutInflater inflater, ViewGroup container,
                             Bundle savedInstanceState) {
        binding = FragmentPaketDataBinding.inflate(inflater, container, false);
        return binding.getRoot();
    }


    private void setupView(){
        if (getActivity() != null){
            ((InquiryPulsaRevampRevActivity)getActivity()).setFragmentRefreshListener(new InquiryPulsaRevampRevActivity.FragmentRefreshListener() {
                @Override
                public void notifyRefresh() {
                    try {
                        if (viewPagerAdapterPaketCustom != null){
                            viewPagerAdapterPaketCustom.notifyDataSetChanged();
                            setPaketCustom();
                        }
                    }catch (Exception e){
                        if (!GeneralHelper.isProd()) Log.e(
                                TAG,
                                "notify refresh",
                                e
                        );
                    }

                }

                @Override
                public void onRefresh(@Nullable ArrayList<DataListRevamp> dataList) {
                    try {
                        provider.setDataListCustom(dataList);
                        setPaketCustom();
                    }catch (Exception e){
                        if (!GeneralHelper.isProd()) Log.e(
                                TAG,
                                "on refresh",
                                e
                        );
                    }
                }

                @Override
                public void hideCustomData() {
                    try {
                        binding.llRecomendedPaket.setVisibility(View.GONE);
                        binding.clRecomendedPaket.setVisibility(View.GONE);
                    }catch (Exception e){
                        if (!GeneralHelper.isProd()) Log.e(
                                TAG,
                                "hide custom data",
                                e
                        );
                    }
                }
            });
        }
    }
    @Override
    public void onViewCreated(@NonNull View view, @Nullable Bundle savedInstanceState) {
        super.onViewCreated(view, savedInstanceState);
        if (provider != null)
            changeListPaketAdapter(provider);
        setupView();
        setPaketCustom();
    }

    @SuppressLint("NotifyDataSetChanged")
    public void changeListPaketAdapter(ProviderItem newProvider) {
        provider = newProvider;

        if(!provider.getDataCode().isEmpty()){
            paketModelList = provider.getDataList();

            if (!dataId.isEmpty()){
                if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.N) {
                    paketModelList.forEach((p) ->
                            {
                                if (String.valueOf(p.getId()).equalsIgnoreCase(dataId)) {
                                    p.setSelected(true);
                                    selectedPaketListener.onPaketSelected(p, false);
                                }
                            }
                    );
                } else {
                    for (DataListRevamp p : paketModelList) {
                        {
                            if (String.valueOf(p.getId()).equalsIgnoreCase(dataId)) {
                                p.setSelected(true);
                                selectedPaketListener.onPaketSelected(p, false);
                            }
                        }
                    }
                }
                dataId = "";
            }

            if(paketModelList.size() > 0) {
                paketAdapter = new OptionPaketDataRevampAdapter(paketModelList, (BaseActivity) getActivity(), this);

                //initiate RV paket
                binding.rvButtonPaket.setLayoutManager(new LinearLayoutManager(getActivity(), RecyclerView.VERTICAL, false));
                binding.rvButtonPaket.setHasFixedSize(true);
                binding.rvButtonPaket.setAdapter(paketAdapter);
                paketAdapter.notifyDataSetChanged();
                binding.llPaketEmpty.setVisibility(View.GONE);
            }
        } else {
            paketModelList = null;
            binding.llPaketEmpty.setVisibility(View.VISIBLE);
            binding.llRecomendedPaket.setVisibility(View.GONE);
            binding.clRecomendedPaket.setVisibility(View.GONE);
        }

    }

    @Override
    public void onDestroyView() {
        binding = null;
        super.onDestroyView();
    }


    public void setSelectedPaketListener(selectedPaketListener selectedPaketListener) {
        this.selectedPaketListener = selectedPaketListener;
    }

    @SuppressLint("NotifyDataSetChanged")
    @Override
    public void onClickPaket(DataListRevamp model, int position) {
        clearSelected();

        paketModelList.get(position).setSelected(true);
        paketAdapter.notifyDataSetChanged();
        if(selectedPaketListener!= null)
            selectedPaketListener.onPaketSelected(model, false);

        if (viewPagerAdapterPaketCustom != null){
            viewPagerAdapterPaketCustom.notifyDataSetChanged();
        }
    }

    @SuppressLint("NotifyDataSetChanged")
    @Override
    public void onClickPaketCustom(@Nullable DataListRevamp model, int position) {
        clearSelected();


        if (paketAdapter != null){
            paketAdapter.notifyDataSetChanged();
        }
        if(selectedPaketListener!= null) {
            selectedPaketListener.onPaketSelected(model, true);
        }
        for (int i = 0; i < paketCustomModelList.size(); i++) {
            if (paketCustomModelList.get(i) != null){
                paketCustomModelList.get(i).setSelected(false);
            }
        }
        viewPagerAdapterPaketCustom.notifyDataSetChanged();
        viewPagerAdapterPaketCustom.getItem(position).changeLayout();
    }

    @Override
    public void onBuyPaket(DataListRevamp model) {
        if(selectedPaketListener!= null)
            selectedPaketListener.onBuyPaket(model);
    }

    @SuppressLint("NotifyDataSetChanged")
    public void clearSelected(){
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.N) {
            paketModelList.forEach((p) -> p.setSelected(false));
        } else {
            for (DataListRevamp p : paketModelList) {
                p.setSelected(false);
            }
        }

        paketAdapter.notifyDataSetChanged();
        if (viewPagerAdapterPaketCustom != null){
            viewPagerAdapterPaketCustom.notifyDataSetChanged();
        }
    }

    public ProviderItem getProvider() {
        return provider;
    }

    public void setProvider(ProviderItem provider) {
        this.provider = provider;
    }

    public void setPaketCustom(){
        if (provider == null){
            return;
        }

        if (provider.getDataListCustom() == null){
            return;
        }

        paketCustomModelList = provider.getDataListCustom();
        if (paketCustomModelList.size() > 0){
            if (getActivity() == null) return;
            viewPagerAdapterPaketCustom = new ViewPagerAdapterPaketData(getActivity().getSupportFragmentManager());

            for (int i = 0; i < paketCustomModelList.size(); i++) {
                if (paketCustomModelList.get(i) != null) {
                    viewPagerAdapterPaketCustom.addFragment(addFragment(paketCustomModelList.get(i), i));
                }
            }
            binding.vpPaketCustom.setAdapter(viewPagerAdapterPaketCustom);
            binding.vpPaketCustom.setPageMargin(ConverterUnitUtil.dpToPx(getActivity(), 16));
            binding.vpPaketCustom.setClipToPadding(false);
            binding.vpPaketCustom.setPadding(ConverterUnitUtil.dpToPx(getActivity(), 16), 0, ConverterUnitUtil.dpToPx(getActivity(), 16), 0);

            binding.dotsIndicator.setViewPager(binding.vpPaketCustom);
            
            binding.llRecomendedPaket.setVisibility(View.VISIBLE);
            binding.clRecomendedPaket.setVisibility(View.VISIBLE);
            
            binding.llPaketEmpty.setVisibility(View.GONE);
        }else{
            binding.llRecomendedPaket.setVisibility(View.GONE);
            binding.clRecomendedPaket.setVisibility(View.GONE);
        }
    }

    public interface selectedPaketListener{
        void onPaketSelected(DataListRevamp paketDataModel, boolean isCustomIndosat);

        void onBuyPaket(DataListRevamp paketDataModel);
    }

    public String getDataId() {
        return dataId;
    }

    public void setDataId(String dataId) {
        this.dataId = dataId;
    }

    private PaketDataCustomFragment addFragment(DataListRevamp dataListRevamp, int position) {
        Bundle bundle = new Bundle();
        bundle.putString(TAG_FRAGMENT_PAKET_CUSTOM, new Gson().toJson(dataListRevamp));
        bundle.putInt(TAG_FRAGMENT_PAKET_CUSTOM_POSITION, position);
        PaketDataCustomFragment fragment = PaketDataCustomFragment.newInstance(this);
        fragment.setArguments(bundle);
        return fragment;
    }
}
