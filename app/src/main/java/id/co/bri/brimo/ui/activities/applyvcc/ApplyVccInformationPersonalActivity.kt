package id.co.bri.brimo.ui.activities.applyvcc

import android.content.Intent
import android.os.Bundle
import androidx.appcompat.app.AppCompatActivity
import androidx.core.widget.addTextChangedListener
import id.co.bri.brimo.R
import id.co.bri.brimo.contract.IPresenter.applyvcc.IApplyVccLocalStoragePresenter
import id.co.bri.brimo.contract.IView.applyvcc.IApplyVccLocalStorageView
import id.co.bri.brimo.databinding.ActivityApplyVccInformationPersonalBinding
import id.co.bri.brimo.models.AccountModel
import id.co.bri.brimo.models.apimodel.request.applyccrevamp.ApplyCcSubmitDataLocalRequest
import id.co.bri.brimo.ui.activities.base.BaseActivity
import id.co.bri.brimo.ui.fragments.SumberDanaFmFragmentRevamp
import id.co.bri.brimo.ui.fragments.applyccrevamp.ApplyVccBottomFragment
import id.co.bri.brimo.util.extension.toArrayList
import id.co.bri.brimo.util.extension.visibleView
import javax.inject.Inject

class ApplyVccInformationPersonalActivity : BaseActivity(),
        IApplyVccLocalStorageView,
        SumberDanaFmFragmentRevamp.SelectSumberDanaInterface
{

    private var hasInitial: Boolean = false
    private var isEdit: Boolean = false
    private var applyCcSubmitDataRequest = ApplyCcSubmitDataLocalRequest()
    private var counter = 0

    private lateinit var binding: ActivityApplyVccInformationPersonalBinding

    @Inject
    lateinit var iApplyVccLocalStoragePresenter: IApplyVccLocalStoragePresenter<IApplyVccLocalStorageView>

    companion object {
        private const val IS_EDIT = "IS_EDIT"

        fun launchIntent(caller: AppCompatActivity, isEdit: Boolean = false) {
            val intent = Intent(caller, ApplyVccInformationPersonalActivity::class.java).apply {
                putExtra(IS_EDIT, isEdit)
            }
            caller.startActivity(intent)
        }
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        binding = ActivityApplyVccInformationPersonalBinding.inflate(layoutInflater)
        setContentView(binding.root)
        injectDependency()
        getData()
        setContent()
    }

    override fun onPause() {
        super.onPause()
        iApplyVccLocalStoragePresenter.setApplyVccRequestLocal(applyCcSubmitDataRequest)
    }

    override fun onRestart() {
        super.onRestart()
        getData()
        updateUiValidationAndSetLocal()
    }

    override fun onResume() {
        super.onResume()
        getData()
        updateUiValidationAndSetLocal()
    }

    private fun setContent() = with(binding) {
        toolbar.setSupportActionBar(this@ApplyVccInformationPersonalActivity)
        content
        layoutStep.root.visibleView(!isEdit)
        layoutStep.setStep(0)
        tvStepInfo.visibleView(!isEdit)

        etEducation.setOnClickListener { showEducationListDialog() }
        etMartialStatus.setOnClickListener { showMaritalStatusDialog() }

        etDependents.addTextChangedListener {
            applyCcSubmitDataRequest.child = it.toString().toIntOrNull() ?: 0
            applyCcSubmitDataRequest.childFilled = it.toString().isNotEmpty()
            updateUiValidationAndSetLocal(false)
        }
        etNameKerabat.addTextChangedListener {
            if (it.toString().length in 1..2) {
                if (hasInitial) {
                    tilNameKerabat.error = getString(R.string.txt_panjang_karakter_tidak_mencukupi)
                    tilNameKerabat.isErrorEnabled = true
                }
                applyCcSubmitDataRequest.emergencyContactName = ""
            } else {
                tilNameKerabat.error = ""
                tilNameKerabat.isErrorEnabled = false
                applyCcSubmitDataRequest.emergencyContactName = it.toString()
            }
            updateUiValidationAndSetLocal(false)
        }
        etHandphoneKerabat.addTextChangedListener {
            if (it.toString().length in 1..8) {
                if (hasInitial) {
                    tilHandphoneKerabat.error = getString(R.string.txt_panjang_karakter_tidak_mencukupi)
                    tilHandphoneKerabat.isErrorEnabled = true
                }
                applyCcSubmitDataRequest.emergencyContactPhone = ""
            } else {
                tilHandphoneKerabat.error = ""
                tilHandphoneKerabat.isErrorEnabled = false
                applyCcSubmitDataRequest.emergencyContactPhone = it.toString()
            }
            updateUiValidationAndSetLocal(false)
        }
        etKodeReferral.addTextChangedListener {
            applyCcSubmitDataRequest.referalCode = it.toString()
            updateUiValidationAndSetLocal(false, false)
        }
        etRekeningAutoDebit.setOnClickListener { showMaritalRekeningAutoDebitDialog() }

        layoutButton
        btnNext.setOnClickListener {
            iApplyVccLocalStoragePresenter.setApplyVccRequestLocal(applyCcSubmitDataRequest)
            gotoNextPageOrFinish()
        }
        if (isEdit) btnNext.text = getString(R.string.simpan)

        updateUiValidationAndSetLocal()
        hasInitial = true
    }

    private fun injectDependency() {
        activityComponent.inject(this)
        iApplyVccLocalStoragePresenter.view = this
        iApplyVccLocalStoragePresenter.start()
    }

    private fun getData() {
        applyCcSubmitDataRequest = iApplyVccLocalStoragePresenter.getApplyVccRequestLocal()
        intent?.apply {
            isEdit = getBooleanExtra(IS_EDIT, isEdit)
        }
    }

    private fun validationButtonNext() = with(binding) {
        val isValid = when {
            applyCcSubmitDataRequest.educationId == 0 -> false
            applyCcSubmitDataRequest.maritalStatusId == 0 -> false
            binding.etDependents.text.toString().isEmpty() -> false
            applyCcSubmitDataRequest.emergencyContactName.isEmpty() -> false
            applyCcSubmitDataRequest.emergencyContactPhone.isEmpty() -> false
            //applyVccRequestModel.referalCode.isEmpty() -> false
            applyCcSubmitDataRequest.accountNo.isEmpty() -> false
            else -> true
        }

        btnNext.isEnabled = isValid
    }

    private fun setUiFormApplyVccRequestModel() = with(binding) {
        etEducation.setText(applyCcSubmitDataRequest.educationText)
        etMartialStatus.setText(applyCcSubmitDataRequest.maritalStatusText)
        if (applyCcSubmitDataRequest.childFilled) etDependents.setText(applyCcSubmitDataRequest.child.toString())
        etNameKerabat.setText(applyCcSubmitDataRequest.emergencyContactName)
        etHandphoneKerabat.setText(applyCcSubmitDataRequest.emergencyContactPhone)
        etKodeReferral.setText(applyCcSubmitDataRequest.referalCode)
        etRekeningAutoDebit.setText(applyCcSubmitDataRequest.accountNo)
    }

    private fun showEducationListDialog() {
        ApplyVccBottomFragment(
            getString(R.string.pendidikan),
            iApplyVccLocalStoragePresenter.getApplyVccDataFormLocal().educationList.toArrayList()
        ) {
            applyCcSubmitDataRequest.educationText = it.value
            applyCcSubmitDataRequest.educationId = it.codeInt
            updateUiValidationAndSetLocal()
        }.show(this)
    }

    private fun showMaritalStatusDialog() {
        ApplyVccBottomFragment(
            getString(R.string.status_perkawinan),
            iApplyVccLocalStoragePresenter.getApplyVccDataFormLocal().maritalStatusList.toArrayList(),
        ) {
            applyCcSubmitDataRequest.maritalStatusText = it.value
            applyCcSubmitDataRequest.maritalStatusId = it.codeInt
            updateUiValidationAndSetLocal()
        }.show(this)
    }

    private fun showMaritalRekeningAutoDebitDialog() {
        counter++
        val fragmentSumberDanaFm = SumberDanaFmFragmentRevamp(iApplyVccLocalStoragePresenter.getApplyVccDataFormLocal().accountList, counter, 0, this, true)
        fragmentSumberDanaFm.show(supportFragmentManager, "")
    }

    private fun gotoNextPageOrFinish() {
        if (isEdit) finish()
        else ApplyVccInformationAddressActivity.launchIntent(this@ApplyVccInformationPersonalActivity)
    }

    private fun updateUiValidationAndSetLocal(isWithSetUiForm: Boolean = true, isWithButtonValidation: Boolean = true) {
        if (isWithSetUiForm) setUiFormApplyVccRequestModel()
        if (isWithButtonValidation) validationButtonNext()
    }

    override fun onSelectSumberDana(bankModel: AccountModel?) {
        applyCcSubmitDataRequest.accountNo = bankModel?.acoountString ?: ""
        updateUiValidationAndSetLocal()
    }

    override fun onSendFailedList(list: MutableList<Int>?) {
        // do nothing
    }
}