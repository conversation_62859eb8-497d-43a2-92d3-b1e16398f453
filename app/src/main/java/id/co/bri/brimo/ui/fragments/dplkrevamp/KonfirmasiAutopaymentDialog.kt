package id.co.bri.brimo.ui.fragments.dplkrevamp

import android.content.res.Resources
import android.graphics.Rect
import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.fragment.app.DialogFragment
import id.co.bri.brimo.databinding.FragmentKonfirmasiAutopaymentDialogBinding

class KonfirmasiAutopaymentDialog(
    private val title: String,
    private val subTitle: String,
    private val btnNoText: String,
    private val btnYesText: String,
    private val showBtnNo: Boolean,
    private val showBtnYes: <PERSON>olean,
    private val listener: DialogClickYesNoListener
) : DialogFragment() {

    private var _binding: FragmentKonfirmasiAutopaymentDialogBinding? = null
    private val binding get() = _binding!!

    override fun onCreateView(
        inflater: LayoutInflater, container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        _binding = FragmentKonfirmasiAutopaymentDialogBinding.inflate(inflater, container, false)
        return binding.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        setupView()
        setupListeners()
        setWidthPercent(100)

    }

    private fun setupView() {
        binding.apply {
            tvTitle.text = title
            tvSubtitle.text = subTitle
            btnNo.visibility = if (showBtnNo) View.VISIBLE else View.GONE
            btnYes.visibility = if (showBtnYes) View.VISIBLE else View.GONE
            btnNo.text = btnNoText
            btnYes.text = btnYesText
        }
    }

    private fun setupListeners() {
        binding.apply {
            btnNo.setOnClickListener {
                listener.onClickBtnNo()
                dismiss()
            }
            btnYes.setOnClickListener {
                listener.onClickBtnYes()
                dismiss()
            }
        }
    }

    override fun onDestroyView() {
        super.onDestroyView()
        _binding = null
    }

    interface DialogClickYesNoListener {
        fun onClickBtnYes()
        fun onClickBtnNo()
    }

    fun DialogFragment.setWidthPercent(percentage: Int) {
        val percent = percentage.toFloat() / 100
        val dm = Resources.getSystem().displayMetrics
        val rect = dm.run { Rect(0, 0, widthPixels, heightPixels) }
        val percentWidth = rect.width() * percent
        dialog?.window?.setLayout(percentWidth.toInt(), ViewGroup.LayoutParams.WRAP_CONTENT)
    }
}
