package id.co.bri.brimo.di.components.fragment

import dagger.Component
import id.co.bri.brimo.di.components.ActivityComponent
import id.co.bri.brimo.di.scopes.PerFragment
import id.co.bri.brimo.ui.fragments.pengelolaankartu.InputExistingPinFragment
import id.co.bri.brimo.ui.fragments.pengelolaankartu.InputNewPinFragment

@PerFragment
@Component(
    dependencies = [
        ActivityComponent::class
    ],
)
interface FragmentComponent {

    fun inject(fragment: InputNewPinFragment)

    fun inject(fragment: InputExistingPinFragment)

}