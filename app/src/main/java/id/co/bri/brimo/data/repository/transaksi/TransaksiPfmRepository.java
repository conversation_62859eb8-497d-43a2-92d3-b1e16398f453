package id.co.bri.brimo.data.repository.transaksi;


import id.co.bri.brimo.models.CatatanAnggaranModel;
import id.co.bri.brimo.models.CatatanPengeluaranModel;
import id.co.bri.brimo.models.TransaksiModel;
import id.co.bri.brimo.models.daomodel.AmountCategory;
import id.co.bri.brimo.models.daomodel.Transaksi;

import java.util.Date;
import java.util.List;

import io.reactivex.Completable;
import io.reactivex.Maybe;
import io.reactivex.Single;

public class TransaksiPfmRepository implements TransaksiPfmSource {

    private static final String TAG = "TransaksiPfmRepository";

    TransaksiPfmSource transaksiLocalSource;

    public TransaksiPfmRepository(TransaksiPfmSource transaksiLocalSource) {
        this.transaksiLocalSource = transaksiLocalSource;
    }


    @Override
    public Single<Transaksi> getFindId(long idTransaksi) {
        return transaksiLocalSource.getFindId(idTransaksi);
    }

    @Override
    public Maybe<List<CatatanAnggaranModel>> getTotalTransaksiCategoryByMonth(String type, String bulan, String tahun) {
        return transaksiLocalSource.getTotalTransaksiCategoryByMonth(type, bulan, tahun);
    }

    @Override
    public Maybe<List<CatatanPengeluaranModel>> getTotalPengeluaranCategory(String type,String starDate,String endDate, String startMonth, String endMonth, String year) {
        return transaksiLocalSource.getTotalPengeluaranCategory(type,starDate,endDate, startMonth, endMonth, year);
    }

    @Override
    public Maybe<List<TransaksiModel>> getTransaksiPfmByDate(String type) {
        return transaksiLocalSource.getTransaksiPfmByDate(type);
    }

    @Override
    public Single<Long> getTransaksiPfmByCategory(long idCategory, Date startDate, Date endDate) {
        return transaksiLocalSource.getTransaksiPfmByCategory(idCategory, startDate, endDate);
    }

    @Override
    public Single<Long> saveTransaksiPfm(Transaksi transaksi) {
        return transaksiLocalSource.saveTransaksiPfm(transaksi);
    }

    @Override
    public Maybe<Long> getTotalAmount(String type, String startMonth, String endMonth, String year) {
        return transaksiLocalSource.getTotalAmount(type, startMonth, endMonth, year);
    }

    @Override
    public Maybe<Long> getTotalAmountPFM(String type,String starDay,String endDay, String startMonth, String endMonth, String year) {
        return transaksiLocalSource.getTotalAmountPFM(type,starDay,endDay, startMonth, endMonth, year);
    }


    @Override
    public Single<AmountCategory> getTotalTransaksiByMonth(String type, String bulan) {
        return transaksiLocalSource.getTotalTransaksiByMonth(type, bulan);
    }

    @Override
    public Completable deleteTransaksiById(long id) {
        return transaksiLocalSource.deleteTransaksiById(id);
    }

    @Override
    public Single<Integer> updateTransaksiPfm(long categoryId, long anggaranId, String trxName, String phone, String type,
                                              String username, long amount, Date date, long refNum, long paymentType, long transaksiId) {
        return transaksiLocalSource.updateTransaksiPfm(categoryId, anggaranId, trxName, phone, type, username, amount, date, refNum, paymentType, transaksiId);
    }

//    @Override
//    public Maybe<List<TransaksiModel>> getListPFM(String type) {
//        return transaksiLocalSource.getListPFM(type);
//    }

}