package id.co.bri.brimo.ui.fragments.dplkrevamp

import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.core.content.ContextCompat
import com.google.android.material.bottomsheet.BottomSheetDialogFragment
import id.co.bri.brimo.R
import id.co.bri.brimo.databinding.FragmentFilterRentangWaktuBinding
import id.co.bri.brimo.domain.config.Constant
import id.co.bri.brimo.domain.extension.gone
import id.co.bri.brimo.domain.extension.visible
import id.co.bri.brimo.domain.helpers.calendar.CalendarHelper
import id.co.bri.brimo.models.YearModel
import org.threeten.bp.YearMonth


class FilterRentangWaktuFragment(
    private val callback: OnCallback,
    private val listYear: List<YearModel>?,
    private val isHasSubmit: Boolean = false,
    private var selectedFilterIs: String = "",
    private var selectedMonthMonthly: String = "",
    private var selectedYearMonthly: String = "",
    private var selectedStartDate: String = "",
    private var selectedEndDate: String = "",
) : BottomSheetDialogFragment(), FilterPeriodeFragment.OnCallback {

    lateinit var binding: FragmentFilterRentangWaktuBinding
    lateinit var filterPeriod: FilterPeriodeFragment
    private var isMonthly = false
    private var selectedMonthFrom = ""
    private var selectedYearFrom = ""
    private var selectedMonthEnd = ""
    private var selectedYearEnd = ""

    interface OnCallback {
        fun onMonthFilter(month: String, year: String)
        fun onPeriodFilter(dateStart: String, dateEnd: String)
        fun onResetFilter()
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setStyle(STYLE_NORMAL, R.style.CustomBottomSheetDialogThemeInput)

    }

    override fun onCreateView(
        inflater: LayoutInflater, container: ViewGroup?, savedInstanceState: Bundle?
    ): View {
        binding = FragmentFilterRentangWaktuBinding.inflate(inflater, container, false)
        return binding.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        setupView()
        initListener()
    }

    private fun setupView() {
        when (selectedFilterIs) {
            Constant.MONTH_FILTER -> {
                binding.rbPilihBulan.isChecked = true
                binding.lyPilihBulan.visible()
                binding.tvMonth.text = selectedMonthMonthly
                binding.tvYear.text = selectedYearMonthly
            }

            Constant.RANGE_FILTER -> {
                binding.rbPilihPeriode.isChecked = true
                binding.lyPilihPeriode.visible()
                binding.tvStartDate.text = selectedStartDate
                binding.tvEndDate.text = selectedEndDate
                selectedMonthFrom =
                    if (selectedStartDate.isNotEmpty()) selectedStartDate.split(" ")[0] else ""
                selectedYearFrom =
                    if (selectedStartDate.isNotEmpty()) selectedStartDate.split(" ")[1] else ""
                selectedMonthEnd =
                    if (selectedEndDate.isNotEmpty()) selectedEndDate.split(" ")[0] else ""
                selectedYearEnd =
                    if (selectedEndDate.isNotEmpty()) selectedEndDate.split(" ")[1] else ""
            }
        }
    }

    private fun initListener() {
        with(binding) {
            rlPilihBulan.setOnClickListener {
                rbPilihBulan.isChecked = true
                rbPilihPeriode.isChecked = false
            }
            rlPilihPeriode.setOnClickListener {
                rbPilihBulan.isChecked = false
                rbPilihPeriode.isChecked = true
            }

            if (isHasSubmit) {
                binding.btnCancel.setTextColor(
                    ContextCompat.getColor(
                        requireContext(), R.color.primaryBlue80
                    )
                )
                binding.btnCancel.isEnabled = true
            }

            rbPilihBulan.setOnCheckedChangeListener { _, isChecked ->
                if (isChecked) {
                    isMonthly = true
                    rbPilihBulan.isChecked = true
                    rbPilihPeriode.isChecked = false

                    lyPilihBulan.visibility = View.VISIBLE
                    lyMonthMonthly.isEnabled = true
                    lyYearMonthly.isEnabled = true

                    lyStartDatePeriod.isEnabled = false
                    lyEndDatePeriod.isEnabled = false
                    lyPilihPeriode.visibility = View.GONE
                    validateButton()
                }
            }

            rbPilihPeriode.setOnCheckedChangeListener { _, isChecked ->
                if (isChecked) {
                    rbPilihBulan.isChecked = false
                    rbPilihPeriode.isChecked = true
                    isMonthly = false

                    lyPilihBulan.visibility = View.GONE
                    lyMonthMonthly.isEnabled = false
                    lyYearMonthly.isEnabled = false

                    lyStartDatePeriod.isEnabled = true
                    lyEndDatePeriod.isEnabled = true
                    lyPilihPeriode.visibility = View.VISIBLE
                    validateButton()
                }
            }

            lyPilihBulan.setOnClickListener {
                filterPeriod = FilterPeriodeFragment(
                    CalendarHelper.TypeFilterPeriode.MONTH_MONTHLY,
                    this@FilterRentangWaktuFragment,
                    listYear,
                    selectedMonthMonthly,
                    selectedYearMonthly
                )
                filterPeriod.show(parentFragmentManager, "")
            }

            lyYearMonthly.setOnClickListener {
                filterPeriod = FilterPeriodeFragment(
                    CalendarHelper.TypeFilterPeriode.YEAR_MONTHLY,
                    this@FilterRentangWaktuFragment,
                    listYear,
                    selectedMonthMonthly,
                    selectedYearMonthly
                )
                filterPeriod.show(parentFragmentManager, "")
            }

            lyStartDatePeriod.setOnClickListener {
                filterPeriod = FilterPeriodeFragment(
                    CalendarHelper.TypeFilterPeriode.START_DATE,
                    this@FilterRentangWaktuFragment,
                    listYear,
                    selectedMonthFrom,
                    selectedYearFrom
                )
                filterPeriod.show(parentFragmentManager, "")
            }

            lyEndDatePeriod.setOnClickListener {
                filterPeriod = FilterPeriodeFragment(
                    CalendarHelper.TypeFilterPeriode.END_DATE,
                    this@FilterRentangWaktuFragment,
                    listYear,
                    selectedMonthEnd,
                    selectedYearEnd
                )
                filterPeriod.show(parentFragmentManager, "")
            }

            btnCancel.setOnClickListener {
                resetButton()
                if (isHasSubmit) {
                    callback.onResetFilter()
                    dismiss()
                }
            }

            btnSubmit.setOnClickListener {
                if (isMonthly) {
                    val monthId =
                        listYear?.find { it.year == selectedYearMonthly }?.listMonth?.find {
                            it.monthString.equals(
                                selectedMonthMonthly, ignoreCase = true
                            )
                        }?.month?.toIntOrNull()

                    callback.onMonthFilter(monthId.toString(), selectedYearMonthly)
                    dismiss()
                } else {
                    val monthIdDateStart =
                        listYear?.find { it.year == selectedYearFrom }?.listMonth?.find {
                            it.monthString.equals(
                                selectedMonthFrom, ignoreCase = true
                            )
                        }?.month?.toIntOrNull()

                    val monthIdDateEnd =
                        listYear?.find { it.year == selectedYearEnd }?.listMonth?.find {
                            it.monthString.equals(
                                selectedMonthEnd, ignoreCase = true
                            )
                        }?.month?.toIntOrNull()

                    val monthIdDateStartStr = if (monthIdDateStart.toString().length < 2) {
                        "0" + monthIdDateStart.toString()
                    } else {
                        monthIdDateStart.toString()
                    }

                    val monthIdDateEndStr = if (monthIdDateEnd.toString().length < 2) {
                        "0" + monthIdDateEnd.toString()
                    } else {
                        monthIdDateEnd.toString()
                    }

                    val dateStart = selectedYearFrom + monthIdDateStartStr + "01"
                    val dateEnd = selectedYearEnd + monthIdDateEndStr + "01"
                    callback.onPeriodFilter(dateStart, dateEnd)
                    dismiss()
                }
            }
        }
    }

    override fun onMonthFilter(month: String, year: String, navigation: String) {
        when (navigation) {
            CalendarHelper.TypeFilterPeriode.MONTH_MONTHLY -> {
                selectedMonthMonthly = month
                selectedYearMonthly = year
                binding.tvYear.text = year
                binding.tvMonth.text = month
            }

            CalendarHelper.TypeFilterPeriode.YEAR_MONTHLY -> {
                selectedMonthMonthly = month
                selectedYearMonthly = year
                binding.tvMonth.text = month
                binding.tvYear.text = year
            }
        }
        validateButton()
        if (isHasSubmit) {
            binding.btnSubmit.isEnabled = true
            binding.btnSubmit.setTextColor(
                ContextCompat.getColor(
                    requireContext(), R.color.whiteColor
                )
            )
            isMonthly = true
        }
    }


    override fun onPeriodFilter(selectedMonth: String, selectedYear: String, navigation: String) {
        when (navigation) {
            CalendarHelper.TypeFilterPeriode.START_DATE -> {
                selectedMonthFrom = selectedMonth
                selectedYearFrom = selectedYear
                binding.tvStartDate.text = selectedMonth + " " + selectedYear
            }

            CalendarHelper.TypeFilterPeriode.END_DATE -> {
                selectedMonthEnd = selectedMonth
                selectedYearEnd = selectedYear
                binding.tvEndDate.text = selectedMonth + " " + selectedYear
            }
        }
        validateButton()
    }

    fun validateButton() {
        binding.apply {
            binding.btnSubmit.isEnabled = false
            if (isMonthly) {
                if (selectedMonthMonthly.isNotEmpty() || selectedYearMonthly.isNotEmpty()) {
                    binding.btnSubmit.isEnabled = true
                    binding.btnCancel.isEnabled = true
                    btnSubmit.setTextColor(
                        ContextCompat.getColor(
                            requireContext(), R.color.whiteColor
                        )
                    )
                    btnCancel.setTextColor(
                        ContextCompat.getColor(
                            requireContext(), R.color.primaryBlue80
                        )
                    )
                }
            } else {
                if (!(selectedMonthFrom.isEmpty() || selectedYearFrom.isEmpty() || selectedMonthEnd.isEmpty() || selectedYearEnd.isEmpty())) {
                    binding.btnSubmit.isEnabled = true
                    binding.btnCancel.isEnabled = true
                    btnSubmit.setTextColor(
                        ContextCompat.getColor(
                            requireContext(), R.color.whiteColor
                        )
                    )
                    btnCancel.setTextColor(
                        ContextCompat.getColor(
                            requireContext(), R.color.primaryBlue80
                        )
                    )
                    val monthFromInt = CalendarHelper.convertMonthInt(selectedMonthFrom)
                    val monthEndInt = CalendarHelper.convertMonthInt(selectedMonthEnd)
                    val startDate = YearMonth.of(selectedYearFrom.toInt(), monthFromInt.toInt())
                    val endDate = YearMonth.of(selectedYearEnd.toInt(), monthEndInt.toInt())
                    if (startDate > endDate) {
                        binding.btnSubmit.isEnabled = false
                    }
                }
            }
        }
    }

    private fun resetButton() {
        binding.apply {
            lyPilihPeriode.gone()
            lyPilihBulan.gone()

            rbPilihBulan.isChecked = false
            rbPilihPeriode.isChecked = false
            btnCancel.isEnabled = false
            btnSubmit.isEnabled = false

            btnSubmit.setTextColor(
                ContextCompat.getColor(
                    requireContext(), R.color.neutralLight60
                )
            )

            btnSubmit.background = ContextCompat.getDrawable(
                requireContext(), R.drawable.rounded_button_disabled_revamp
            )

            btnCancel.setTextColor(
                ContextCompat.getColor(
                    requireContext(), R.color.neutralLight60
                )
            )

            tvMonth.text = getString(R.string.txt_filter_riwayat_pilih_bulan)
            tvYear.text = getString(R.string.txt_filter_riwayat_pilih_bulan)
            tvStartDate.text = getString(R.string.txt_filter_riwayat_pilih_bulan)
            tvEndDate.text = getString(R.string.txt_filter_riwayat_pilih_bulan)
        }
    }
}