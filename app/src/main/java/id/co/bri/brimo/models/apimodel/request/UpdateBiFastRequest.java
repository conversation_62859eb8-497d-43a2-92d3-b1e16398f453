package id.co.bri.brimo.models.apimodel.request;

import com.google.gson.annotations.Expose;
import com.google.gson.annotations.SerializedName;

public class UpdateBiFastRequest {
    @SerializedName("proxy_value")
    @Expose
    private String proxyValue;
    @SerializedName("account")
    @Expose
    private String account;
    @SerializedName("type")
    @Expose
    private String type;

    public UpdateBiFastRequest(String proxyValue, String account, String type) {
        this.proxyValue = proxyValue;
        this.account = account;
        this.type = type;
    }

    public String getProxyValue() {
        return proxyValue;
    }

    public void setProxyValue(String proxyValue) {
        this.proxyValue = proxyValue;
    }

    public String getAccount() {
        return account;
    }

    public void setAccount(String account) {
        this.account = account;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }
}
