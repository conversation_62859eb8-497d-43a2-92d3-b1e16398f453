package id.co.bri.brimo.ui.fragments.esbn;

import android.annotation.SuppressLint;
import android.os.Bundle;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;

import androidx.fragment.app.DialogFragment;

import com.google.android.material.bottomsheet.BottomSheetDialogFragment;

import id.co.bri.brimo.R;
import id.co.bri.brimo.databinding.FragmentBottomRegKemenkeuBinding;
import id.co.bri.brimo.databinding.FragmentBottomKomposisiBinding;
import id.co.bri.brimo.models.BottomDialogModel;
import id.co.bri.brimo.models.apimodel.request.esbn.RegisKemenkeuRequest;
import id.co.bri.brimo.models.apimodel.response.esbn.DashboardDataSbnResponse;

public class BottomFragmentRegKemenkeu extends BottomSheetDialogFragment {

    private FragmentBottomRegKemenkeuBinding _binding;
    private BottomDialogDefaultListener dialogDefaulListener;
    private String mAccount;
    private String mSre;
    private String mSid;


    private String stButton;
    BottomDialogModel model;
    private Boolean overLimit;
    public RegisKemenkeuRequest regisKemenkeuRequest;

    private DashboardDataSbnResponse dataSbnResponse;

    private FragmentBottomRegKemenkeuBinding getBinding() {
        if (_binding == null) {
            throw new IllegalStateException("Attempt to access binding when it is null. Make sure binding is initialized before accessing.");
        }
        return _binding;
    }
    @SuppressLint("ValidFragment")
    public BottomFragmentRegKemenkeu() {
        // Required empty public constructor
    }

    public BottomFragmentRegKemenkeu(BottomDialogDefaultListener dialogDefaulListener2, String stButton) {
        this.dialogDefaulListener = dialogDefaulListener2;
        this.stButton = stButton;
    }

    @Override
    public void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
    }

    @Override
    public View onCreateView(LayoutInflater inflater, ViewGroup container,
                             Bundle savedInstanceState) {
        // Inflate the layout for this fragment
        _binding = FragmentBottomRegKemenkeuBinding.inflate(inflater,container,false);

        getBinding().btnBatalkan.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                if (dialogDefaulListener != null){
                    dialogDefaulListener.onCLickNanti();
                }
                dismiss();
            }
        });

        getBinding().btnClose.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View view) {
                if (dialogDefaulListener != null){
                    dialogDefaulListener.onClickOk(mAccount,mSre,mSid);
                }
                dismiss();
            }
        });
        return getBinding().getRoot();
    }

    public interface BottomDialogDefaultListener {
        void onClickOk(String account, String sre,String sid);

        void onCLickNanti();

    }
}