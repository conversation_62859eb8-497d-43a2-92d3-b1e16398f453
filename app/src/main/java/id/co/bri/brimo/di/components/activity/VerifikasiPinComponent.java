package id.co.bri.brimo.di.components.activity;

import id.co.bri.brimo.di.components.ActivityComponent;
import id.co.bri.brimo.di.modules.activity.VerifikasiPinModule;
import id.co.bri.brimo.di.scopes.PerForm;
import id.co.bri.brimo.ui.activities.VerifikasiPinActivity;

import dagger.Component;

@PerForm
@Component(dependencies = ActivityComponent.class,modules = {VerifikasiPinModule.class})
public interface VerifikasiPinComponent {

    void inject(VerifikasiPinActivity verifikasiPinActivity);
}
