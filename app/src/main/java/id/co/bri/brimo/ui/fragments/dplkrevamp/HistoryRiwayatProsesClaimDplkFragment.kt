package id.co.bri.brimo.ui.fragments.dplkrevamp

import android.content.Context
import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.core.view.isVisible
import androidx.recyclerview.widget.LinearLayoutManager
import com.ethanhua.skeleton.Skeleton
import com.ethanhua.skeleton.SkeletonScreen
import id.co.bri.brimo.R
import id.co.bri.brimo.adapters.dplkrevamp.ItemHistoryKlaimDplkAdapter
import id.co.bri.brimo.contract.IPresenter.dplkrevamp.IHistoryRiwayatProsesClaimDplkPresenter
import id.co.bri.brimo.contract.IView.dplkrevamp.IHistoryTransaksiProsesClaimView
import id.co.bri.brimo.databinding.FragmentHistoryRiwayatProsesKlaimDplkBinding
import id.co.bri.brimo.domain.config.Constant
import id.co.bri.brimo.domain.helpers.GeneralHelper
import id.co.bri.brimo.models.apimodel.request.dplkrevamp.DetailKlaimDplkRequest
import id.co.bri.brimo.models.apimodel.response.PendingResponse
import id.co.bri.brimo.models.apimodel.response.ReceiptRevampResponse
import id.co.bri.brimo.models.apimodel.response.dplkrevamp.RiwayatClaimDplkResponse
import id.co.bri.brimo.ui.activities.general.ReceiptStatusActivity
import id.co.bri.brimo.ui.fragments.BaseFragment
import javax.inject.Inject

class HistoryRiwayatProsesClaimDplkFragment(
    private val mContext: Context,
    private var mData: RiwayatClaimDplkResponse
) : BaseFragment(), IHistoryTransaksiProsesClaimView {

    lateinit var binding: FragmentHistoryRiwayatProsesKlaimDplkBinding
    private lateinit var mAdapterHistory: ItemHistoryKlaimDplkAdapter
    private var skeletonHistoryTrx : SkeletonScreen? = null

    @Inject
    lateinit var presenter : IHistoryRiwayatProsesClaimDplkPresenter<IHistoryTransaksiProsesClaimView>


    override fun onCreateView(
        inflater: LayoutInflater, container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        binding = FragmentHistoryRiwayatProsesKlaimDplkBinding.inflate(inflater, container, false)
        return binding.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        injectDependency()
        setupRecyclerView()
        initListener()
    }
    private fun injectDependency() {
        activityComponent.inject(this)
        presenter.view = this
        presenter.seturlListClaimBrifine(getString(R.string.url_list_klaim_brifine))
        presenter.setUrlDetailClaimBrifine(getString(R.string.url_detail_klaim_brifine))
    }

    private fun initListener() {
        with(binding){
            swipeRefresh.setOnRefreshListener {
                setSkeletonView()
                presenter.getListClaimBrifine()
            }
            binding.swipeRefreshEmpty.setOnRefreshListener {
                setSkeletonView()
                presenter.getListClaimBrifine()
            }
        }
    }

    private fun setSkeletonView() {
        skeletonHistoryTrx = Skeleton.bind(binding.rvHistory)
            .shimmer(true)
            .angle(20)
            .duration(1200)
            .load(R.layout.skeleton_riwayat_klaim_dplk)
            .show()
    }

    private fun setupRecyclerView() {
        setState()
        mAdapterHistory = ItemHistoryKlaimDplkAdapter(
            mContext,
            items = mData.pending.flatMap { it.list },
            onItemClick = { item ->
                presenter.getDetailClaimBrifine(DetailKlaimDplkRequest(item.trxId,item.brifineAccount))
            }
        )

        binding.rvHistory.apply {
            layoutManager = LinearLayoutManager(context)
            adapter = mAdapterHistory
        }
    }

    private fun setState(){
        if (mData.pending.isEmpty()){
            binding.lyRiwayatTransaksiKosong.isVisible = true
            binding.rvHistory.isVisible = false
            binding.swipeRefresh.isVisible = false
            binding.rvHistory.isVisible = false
        } else {
            binding.lyRiwayatTransaksiKosong.isVisible = false
            binding.swipeRefreshEmpty.isVisible = false
            binding.swipeRefresh.isVisible = true
            binding.rvHistory.isVisible = true
        }
    }

    override fun onSuccessGetHistoryDetailClaimDplk(data: ReceiptRevampResponse) {
        ReceiptStatusActivity.launchIntent(requireActivity(),data.referenceNumber, data,false,
            Constant.TRX_TYPE_KLAIM_DPLK_DETAIL)

        // Handle the response for detail claim history
    }

    override fun onSuccessGetHistoryListClaimDplk(data: RiwayatClaimDplkResponse) {
        skeletonHistoryTrx?.hide()
        binding.swipeRefresh.isRefreshing = false
        binding.swipeRefreshEmpty.isRefreshing = false
        mData = data
        setupRecyclerView()
    }

    override fun onException(message: String?) {
        super.onException(message)
        GeneralHelper.showSnackBarRevamp(binding.content, message)
    }

}
