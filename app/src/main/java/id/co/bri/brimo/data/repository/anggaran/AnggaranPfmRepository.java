package id.co.bri.brimo.data.repository.anggaran;

import id.co.bri.brimo.models.daomodel.Anggaran;
import id.co.bri.brimo.models.daomodel.AnggaranCategory;

import java.util.List;

import io.reactivex.Observable;
import io.reactivex.Single;

public class AnggaranPfmRepository implements AnggaranPfmSource {

    AnggaranPfmSource anggaranLocalSource;

    public AnggaranPfmRepository (AnggaranPfmSource anggaranLocalSource){
        this.anggaranLocalSource = anggaranLocalSource;
    }

    @Override
    public Observable<List<AnggaranCategory>> getAnggaranPfm(String month, String year) {
        return anggaranLocalSource.getAnggaranPfm(month, year);
    }

    @Override
    public Single<Long> saveAnggaranPfm(Anggaran anggaran) {
        return anggaranLocalSource.saveAnggaranPfm(anggaran);
    }

    @Override
    public Single<Long> getAnggaranLimit() {
        return anggaranLocalSource.getAnggaranLimit();
    }
}
