package id.co.bri.brimo.data.repository.rate;

import java.util.Date;

import id.co.bri.brimo.data.repository.rate.local.RateLocalSource;
import id.co.bri.brimo.models.daomodel.RateUs;
import io.reactivex.Single;

public class RateRepository implements RateSource {

    RateLocalSource rateLocalSource;

    public RateRepository(RateLocalSource rateLocalSource) {
        this.rateLocalSource = rateLocalSource;
    }

    @Override
    public Single<Long> saveRateUs(RateUs rate) {
        return rateLocalSource.saveRateUs(rate);
    }

    @Override
    public Single<Integer> updateRateUs(long id, long rateStat, Date rateDate, long rateCounter) {
        return rateLocalSource.updateRateUs(id, rateStat, rateDate, rateCounter);
    }

    @Override
    public Single<Integer> updateRateCounter(long rateCounter, long id) {
        return rateLocalSource.updateRateCounter(rateCounter, id);
    }

    @Override
    public Single<Integer> updateDateRate(Date date, long rateCounter, long id) {
        return rateLocalSource.updateDateRate(date, rateCounter, id);
    }

    @Override
    public Single<RateUs> getRateAll() {
        return rateLocalSource.getRateAll();
    }
}