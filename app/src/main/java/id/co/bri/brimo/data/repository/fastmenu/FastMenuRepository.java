package id.co.bri.brimo.data.repository.fastmenu;

import android.util.Log;

import java.util.ArrayList;
import java.util.List;


import id.co.bri.brimo.domain.config.MenuConfig;
import id.co.bri.brimo.models.daomodel.FastMenu;
import io.reactivex.Completable;
import io.reactivex.Maybe;
import io.reactivex.Observable;
import io.reactivex.Single;
import io.reactivex.android.schedulers.AndroidSchedulers;
import io.reactivex.disposables.Disposable;
import io.reactivex.observers.DisposableCompletableObserver;
import io.reactivex.schedulers.Schedulers;

/**
 * Created by user on 16/03/2021
 */
public class FastMenuRepository implements FastMenuSource {

    private static final String TAG = "FastMenuRepository";

    private DisposableCompletableObserver categoryDisposable;
    private FastMenuSource fastMenuSource;

    public FastMenuRepository(FastMenuSource categoryLocalSource) {
        this.fastMenuSource = categoryLocalSource;
    }

    @Override
    public Observable<List<FastMenu>> getFastMenu() {
        return fastMenuSource.getFastMenu()
                .switchIfEmpty(fastMenuSource.getFastMenu());
    }

    @Override
    public Completable saveFastMenu(List<FastMenu> fastMenus) {
        return fastMenuSource.saveFastMenu(fastMenus);
    }

    @Override
    public Completable deleteAll() {
        return fastMenuSource.deleteAll();
    }

    @Override
    public Completable deleteById(String id){
        return fastMenuSource.deleteById(id);
    }

    @Override
    public Single<Integer> updateFastMenu(int id,String kode, String menuName, String gambarMenu, String menu, String tag, boolean flagNew) {
        return fastMenuSource.updateFastMenu(id,kode,menuName,gambarMenu,menu,tag,flagNew);
    }

//    protected Maybe<List<FastMenu>> saveSampleFastMenu(){
//        List<FastMenu> fastMenuModel = new ArrayList<>();
//        fastMenuModel.addAll(MenuConfig.fetchFastMenu());
//        categoryDisposable = new DisposableCompletableObserver() {
//            @Override
//            public void onComplete() {
//                getFastMenu();
//                Log.d(TAG, "onComplete: insert sample done");
//                disposeObserver();
//            }
//
//            @Override
//            public void onError(Throwable e) {
//                /*
//                if (!GeneralHelper.isProd())
//                e.printStackTrace();
//
//                 */
//            }
//        };
//
//        fastMenuSource.saveFastMenu(fastMenuModel).subscribeOn(Schedulers.io())
//                .observeOn(AndroidSchedulers.mainThread())
//                .subscribe(categoryDisposable);
//
//
//
//        return Maybe.just(fastMenuModel);
//
//    }

    public void disposeObserver() {
        if (categoryDisposable != null && !categoryDisposable.isDisposed()) {
            categoryDisposable.dispose();
        }
    }
}
