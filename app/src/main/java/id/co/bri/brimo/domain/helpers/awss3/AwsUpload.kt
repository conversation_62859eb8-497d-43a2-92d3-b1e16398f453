package id.co.bri.brimo.domain.helpers.awss3

import android.content.Context
import android.text.TextUtils
import android.util.Log
import com.amazonaws.ClientConfiguration
import com.amazonaws.Protocol
import com.amazonaws.auth.BasicAWSCredentials
import com.amazonaws.mobileconnectors.s3.transferutility.TransferListener
import com.amazonaws.mobileconnectors.s3.transferutility.TransferState
import com.amazonaws.mobileconnectors.s3.transferutility.TransferUtility
import com.amazonaws.regions.Region
import com.amazonaws.services.s3.AmazonS3Client
import com.amazonaws.services.s3.S3ClientOptions
import com.amazonaws.services.s3.model.CannedAccessControlList
import com.amazonaws.services.s3.model.ObjectMetadata
import id.co.bri.brimo.domain.config.AppConfig
import id.co.bri.brimo.domain.helpers.GeneralHelper
import java.io.File

class AwsUpload(
    private val context: Context,
    private val bucket: String,
    private val filePath: String,
    val onAwsImageUploadListener: OnAwsImageUploadListener,
    val filePathKey: String,
    private val contentType: String
) {
    interface OnAwsImageUploadListener {
        fun onSuccess(imgUrl: String)
        fun onError(errorMsg: String)
    }

    private var image: File? = null
    private var mTransferUtility: TransferUtility? = null

    fun beginUpload() {

        if (TextUtils.isEmpty(filePath)) {
            onAwsImageUploadListener.onError("Could not find the filepath of the selected file")
            return
        }

        val file = File(filePath)
        image = file

        try {
            val metadata = ObjectMetadata()
            metadata.contentType = contentType

            val observer = getTransferUtility(context)?.upload(
                bucket, filePathKey + file.name, image,
                metadata, CannedAccessControlList.PublicReadWrite
            )
            observer?.setTransferListener(UploadListener())
        } catch (e: Exception) {
            if (!GeneralHelper.isProd()) {
                Log.d("TestAWS3", "beginUpload: " + e.printStackTrace())
            }
        }
    }

    private inner class UploadListener : TransferListener {

        override fun onError(id: Int, e: Exception) {
            if (!GeneralHelper.isProd()) {
                Log.d("TESTAWS3", "onError: " + e.printStackTrace())
            }
        }

        override fun onProgressChanged(id: Int, bytesCurrent: Long, bytesTotal: Long) {
        }

        override fun onStateChanged(id: Int, newState: TransferState) {
            if (newState == TransferState.COMPLETED) {
                val finalImageUrl =
                    AppConfig.getURLMinio() + "/" + bucket + "/" + filePathKey + image!!.name
                onAwsImageUploadListener.onSuccess((finalImageUrl))
            } else if (newState == TransferState.CANCELED || newState == TransferState.FAILED) {
                onAwsImageUploadListener.onError("Error in uploading file.")
            }
        }
    }

    @Suppress("DEPRECATION")
    private fun getTransferUtility(context: Context): TransferUtility? {
        val configuration = ClientConfiguration()
        configuration.maxErrorRetry = 3
        configuration.connectionTimeout = 501000
        configuration.socketTimeout = 501000
        configuration.protocol = Protocol.HTTP
        val options = S3ClientOptions()
        options.isPathStyleAccess = true
        val credentials = BasicAWSCredentials(
            AppConfig.getAccessKeyMinio(), AppConfig.getSecretKeyMinio()
        )
        val s3 = AmazonS3Client(credentials, Region.getRegion(AppConfig.REGION))
        s3.endpoint = AppConfig.getURLMinio()
        s3.setS3ClientOptions(options)

        if (mTransferUtility == null) {
            mTransferUtility = TransferUtility.builder()
                .context(context)
                .s3Client(s3)
                .build()
        }
        return mTransferUtility
    }
}