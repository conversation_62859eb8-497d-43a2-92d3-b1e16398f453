package id.co.bri.brimo.ui.activities.bukaValas;


import android.app.Activity;
import android.content.Intent;
import android.os.Build;
import android.os.Bundle;
import android.view.View;
import android.view.Window;
import android.view.WindowManager;
import androidx.annotation.Nullable;
import androidx.fragment.app.FragmentTransaction;
import java.util.regex.Pattern;
import javax.inject.Inject;
import id.co.bri.brimo.R;
import id.co.bri.brimo.adapters.DetailTransaksiAdapter;
import id.co.bri.brimo.adapters.TotalKonfirmasiAdapter;
import id.co.bri.brimo.contract.IPresenter.bukaValas.IKonfirmasiBukaValasPresenter;
import id.co.bri.brimo.contract.IView.bukaValas.IKonfirmasiBukaValasView;
import id.co.bri.brimo.databinding.ActivityKonfirmasiSetoranAwalValasBinding;
import id.co.bri.brimo.domain.config.Constant;
import id.co.bri.brimo.domain.helpers.GeneralHelper;
import id.co.bri.brimo.models.AccountModel;
import id.co.bri.brimo.models.ParameterKonfirmasiModel;
import id.co.bri.brimo.models.apimodel.response.DetailListType;
import id.co.bri.brimo.models.apimodel.response.KonfirmasiBukaValasResponse;
import id.co.bri.brimo.models.apimodel.response.PendingResponse;
import id.co.bri.brimo.ui.activities.DashboardIBActivity;
import id.co.bri.brimo.ui.activities.GeneralSyaratActivity;
import id.co.bri.brimo.ui.activities.LupaPinActivity;
import id.co.bri.brimo.ui.activities.LupaPinFastActivity;
import id.co.bri.brimo.ui.activities.base.BaseActivity;
import id.co.bri.brimo.ui.customviews.PatternEditableBuilder;
import id.co.bri.brimo.ui.customviews.dialog.DialogExitCustom;
import id.co.bri.brimo.ui.fragments.FragmentBottomDialog;
import id.co.bri.brimo.ui.fragments.PinFragment;

public class KonfirmasiBukaValasActivity extends BaseActivity implements View.OnClickListener,IKonfirmasiBukaValasView , PinFragment.SendPin, DialogExitCustom.DialogDefaultListener{

    private ActivityKonfirmasiSetoranAwalValasBinding binding;

    @Inject
    IKonfirmasiBukaValasPresenter<IKonfirmasiBukaValasView> presenter;

    DetailListType sourceAccountDataViews;
    DetailListType billingDetails;
    DetailListType detailDataView;

    private static final String TAG = "KonfirmasiBukaValasActivity";

    protected boolean isCheckedSave = false;
    protected boolean isSyarat = false;

    protected static String TAG_KONFIRMASI_DATA = "konfirmasi_data";
    protected static String TAG_PARAMS_KONFIRM_DATA = "params_konfirm_data";
    protected static String TAG_URL_PAYMENT = "url_payment";
    protected static String TAG_TITLE = "url_title";

    protected static KonfirmasiBukaValasResponse mConfirmResponse;
    protected static String mUrlPayment;
    protected static String mTitle;
    protected static ParameterKonfirmasiModel mParameterKonfirmasiModel;
    protected static boolean mIsFromTopUpOnline;
    protected static AccountModel accountModel;

    public static void launchIntent(Activity caller, KonfirmasiBukaValasResponse generalConfirmationResponse, String urlPayment, String title, ParameterKonfirmasiModel parameterKonfirmasiModel, boolean isFromFastmenu, AccountModel model) {
        Intent intent = new Intent(caller, KonfirmasiBukaValasActivity.class);

        isFromFastMenu = isFromFastmenu;
        accountModel = model;
        mConfirmResponse = generalConfirmationResponse;
        mUrlPayment = urlPayment;
        mTitle = title;
        mParameterKonfirmasiModel = parameterKonfirmasiModel;

        caller.startActivityForResult(intent, Constant.REQ_PAYMENT);
    }

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        binding = ActivityKonfirmasiSetoranAwalValasBinding.inflate(getLayoutInflater());
        setContentView(binding.getRoot());

//      inject Presenter dgn Dagger
        injectDependency();
        if (binding.tbKonfirmasi.toolbar != null) {
            GeneralHelper.setToolbar(this, binding.tbKonfirmasi.toolbar, "Konfirmasi");
        }

        binding.btnSubmit.setOnClickListener(this);
        binding.tvSave2.setOnClickListener(this);
        // Membuat span dengan tampilan berbeda dan dapat diklik
        new PatternEditableBuilder().
                addPattern(Pattern.compile("Syarat dan Ketentuan"), GeneralHelper.getColor(R.color.colorTextBlueBri),
                        text -> {
                        }).into(binding.tvSave);
        //setup view
        setupView();

//      set blue bar
        if (Build.VERSION.SDK_INT >= 21) {
            Window window = getWindow();
            window.addFlags(WindowManager.LayoutParams.FLAG_DRAWS_SYSTEM_BAR_BACKGROUNDS);
            window.clearFlags(WindowManager.LayoutParams.FLAG_TRANSLUCENT_STATUS);
            window.setStatusBarColor(getResources().getColor(R.color.toolbar_blue));
        }
    }

    protected void injectDependency() {
        getActivityComponent().inject(this);

        if (presenter != null) {
            presenter.setView(this);
            presenter.setUrlPayment(mUrlPayment);
            presenter.start();

        }
    }

    protected void setupView() {

        sourceAccountDataViews = mConfirmResponse.getSourceAccountDataView();

        //Detail Rekening Asal
        binding.tvNama.setText(sourceAccountDataViews.getTitle());
        binding.tvNorek.setText(sourceAccountDataViews.getSubtitle());
        binding.tvInisial.setText(GeneralHelper.formatInitialName(sourceAccountDataViews.getTitle()));


        //Detail Nama dan Billing
        if (mConfirmResponse.getBillingDetail() != null) {
            parseDetailTagihan();
        } else {
            binding.llDetailBilling.setVisibility(View.GONE);
        }

        binding.lblTujuan.setText(mParameterKonfirmasiModel.getStringLabelTujuan());
        binding.btnSubmit.setText(mParameterKonfirmasiModel.getStringButtonSubmit());

        GeneralHelper.loadIconTransaction(
                this,
                accountModel.getIcon_path(),
                accountModel.getIcon_name(),
                binding.ivInisial,
                mParameterKonfirmasiModel.getDefaultIcon());

        // detail keterangan
        binding.llKeterangan.setVisibility(View.VISIBLE);


        binding.checkboxSave.setOnClickListener(this);
        binding.checkboxSave2.setOnClickListener(this);
        binding.tvSave.setOnClickListener(this);

        setvalidasiButton();


        binding.tvValasTo.setText(mConfirmResponse.getInfo_kurs_1());
        binding.tvValasFrom.setText(mConfirmResponse.getInfo_kurs_2());

    }

    @Override
    public void onSuccessGetPayment(PendingResponse paymentResponse) {
        PendingBukaValasActivity.launchIntentPending(this, paymentResponse, isFromFastMenu, mConfirmResponse);
    }

    protected void parseDetailTagihan() {
        billingDetails = mConfirmResponse.getBillingDetail();
        detailDataView = mConfirmResponse.getDetailDataView1();
        //Set Image Circle
        if (billingDetails.getListType().equals("image")) {
            binding.llLogo.setVisibility(View.VISIBLE);
            binding.rlInisial.setVisibility(View.GONE);
            //load icon transaction
            GeneralHelper.loadIconTransaction(
                    this,
                    billingDetails.getIconPath(),
                    billingDetails.getIconName(),
                    binding.ivIcon,
                    mParameterKonfirmasiModel.getDefaultIcon());
        } else {
            binding.llLogo.setVisibility(View.GONE);
            binding.rlInisial.setVisibility(View.VISIBLE);
            //Set Initial
            String title = billingDetails.getTitle();
            binding.tvInisialTujuan.setText(GeneralHelper.formatInitialName(title));
        }

        //View
        binding.tvTujuan.setText(billingDetails.getTitle());
        binding.tvNomorPayment.setText(billingDetails.getSubtitle());
        binding.tvTujuanMenabung.setText(detailDataView.getTitle());
    }

    @Override
    public void onException93(String message) {
        Intent returnIntent = new Intent();

        returnIntent.putExtra(Constant.TAG_ERROR_MESSAGE, message);

        this.setResult(RESULT_CANCELED, returnIntent);
        this.finish();
    }

    @Override
    public void onException(String message) {
        if (GeneralHelper.isContains(Constant.LIST_TYPE_GAGAL, message))
            GeneralHelper.showDialogGagalBack(this, message);
        else
            showSnackbarErrorMessage(message, ALERT_ERROR, this, false);
    }

    @Override
    public void onSendPinComplete(String pin) {
        if (presenter != null) {
            presenter.getDataPayment(
                    pin,
                    billingDetails.getTitle(),
                    mConfirmResponse,
                    isFromFastMenu
            );
        }

    }

    @Override
    public void onLupaPin() {
        //TO DO routing
        if (isFromFastMenu) LupaPinFastActivity.launchIntent(this);
        else LupaPinActivity.launchIntent(this);
    }

    @Override
    public void onBackPressed() {
        DialogExitCustom dialogExitCustom = new DialogExitCustom(this::onClickYes, GeneralHelper.getString(R.string.title_dialog_exit_konfirmasi), GeneralHelper.getString(R.string.content_dialog_exit_konfirmasi));
        FragmentTransaction ft = this.getSupportFragmentManager().beginTransaction();
        ft.add(dialogExitCustom, null);
        ft.commitAllowingStateLoss();
    }

    @Override
    public void onClick(View view) {
        switch (view.getId()) {
            case R.id.btnSubmit:
                PinFragment pinFragment = new PinFragment(this, this);
                pinFragment.show();
                break;
            case R.id.checkbox_save:
            case R.id.tv_save:
                GeneralSyaratActivity.launchIntent(this, mConfirmResponse.getTerm());
                break;
            case R.id.checkbox_save_2:
                setvalidasiButton();
                break;
            case R.id.tv_save_2:
                if (!isCheckedSave) {
                    binding.checkboxSave2.setChecked(true);
                    isCheckedSave = true;
                } else {
                    binding.checkboxSave2.setChecked(false);
                    isCheckedSave = false;
                }
                setvalidasiButton();
                break;
            default:
                break;
        }

    }

    public void setvalidasiButton(){
        if (binding.checkboxSave.isChecked() && binding.checkboxSave2.isChecked()) {
            binding.btnSubmit.setEnabled(true);
            binding.btnSubmit.setAlpha(1);
        } else {
            binding.btnSubmit.setEnabled(false);
            binding.btnSubmit.setAlpha((float) 0.3);
        }
    }

    @Override
    public void onException01(String message) {

        FragmentBottomDialog fragmentBottomDialog = new FragmentBottomDialog(this,
                Constant.TRANSAKSI_GAGAL,
                GeneralHelper.getString(R.string.title_transaki_gagal),
                message,
                Constant.IMAGE_TRANSAKSI_GAGAL, false, () -> DashboardIBActivity.launchIntent(this));
        fragmentBottomDialog.setCancelable(false);
        fragmentBottomDialog.show(getSupportFragmentManager(), "");

    }

    @Override
    protected void onActivityResult(int requestCode, int resultCode, @Nullable Intent data) {
        super.onActivityResult(requestCode, resultCode, data);

        if (requestCode == Constant.REQ_PETTUNJUK1 && data != null) {
            if (resultCode == RESULT_OK) {
                isSyarat = Boolean.parseBoolean(data.getStringExtra("checkbox"));

                if (isSyarat) {
                    binding.checkboxSave.setChecked(true);
                    isCheckedSave = true;
                } else {
                    binding.checkboxSave.setChecked(false);
                    isCheckedSave = false;
                }
                setvalidasiButton();
            }
        }
        if (requestCode == Constant.REQ_PAYMENT) {
            if (resultCode == RESULT_OK) {
                this.setResult(RESULT_OK, data);
                this.finish();
            } else {
                this.setResult(RESULT_CANCELED, data);
                this.finish();
            }
        } else if (requestCode == Constant.REQ_FORGET_PIN) {
            if (resultCode == RESULT_OK) {
                this.setResult(RESULT_OK);
                this.finish();
            }
        }
    }


    @Override
    public void onClickYes() {
        this.setResult(RESULT_CANCELED);
        this.finish();
    }

    @Override
    protected void onDestroy() {
        presenter.stop();
        super.onDestroy();
        binding = null;
    }

    @Override
    protected void onResume() {
        super.onResume();
        injectDependency();
    }
}