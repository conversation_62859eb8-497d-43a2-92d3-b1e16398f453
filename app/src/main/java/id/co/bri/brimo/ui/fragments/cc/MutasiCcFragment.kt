package id.co.bri.brimo.ui.fragments.cc

import android.annotation.SuppressLint
import android.content.Intent
import android.os.Bundle
import android.os.SystemClock
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.activity.result.ActivityResultLauncher
import androidx.activity.result.contract.ActivityResultContracts
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.ethanhua.skeleton.Skeleton
import com.ethanhua.skeleton.SkeletonScreen
import com.google.gson.Gson
import id.co.bri.brimo.R
import id.co.bri.brimo.adapters.cc.ListBulanMutasiCcAdapter
import id.co.bri.brimo.contract.IPresenter.cc_sof.IMutasiCcPresenter
import id.co.bri.brimo.contract.IView.cc_sof.IMutasiCcView
import id.co.bri.brimo.databinding.FragmentMutasiCcBinding
import id.co.bri.brimo.domain.config.Constant
import id.co.bri.brimo.domain.helpers.GeneralHelper
import id.co.bri.brimo.models.YearModel
import id.co.bri.brimo.models.apimodel.request.DetailCcSofRequest
import id.co.bri.brimo.models.apimodel.response.cc.Detail
import id.co.bri.brimo.models.apimodel.response.cc.MutationCcResponse
import id.co.bri.brimo.models.apimodel.response.cc.Statement
import id.co.bri.brimo.ui.activities.StickHeaderItemDecoration
import id.co.bri.brimo.ui.activities.cc_sof.EstatementCcActivity
import id.co.bri.brimo.ui.fragments.BaseFragment
import javax.inject.Inject

class MutasiCcFragment(private val cardToken: String) : BaseFragment(),
    IMutasiCcView,
    ListBulanMutasiCcAdapter.ItemMutationClick {

    @Inject
    lateinit var presenter: IMutasiCcPresenter<IMutasiCcView>

    private var _binding: FragmentMutasiCcBinding? = null
    private val binding get() = _binding!!

    private lateinit var listMonthMutationAdapter: ListBulanMutasiCcAdapter
    private lateinit var skeletonScreenMutation: SkeletonScreen
    private lateinit var recyclerItemDecoration: StickHeaderItemDecoration
    private var mutasiResponses: ArrayList<Statement>? = ArrayList()
    private var yearModels: ArrayList<YearModel>? = ArrayList()

    @SuppressLint("ValidFragment")
    fun MutasiCcFragment() {
        // do nothing
    }

    override fun onCreateView(
        inflater: LayoutInflater, container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        _binding = FragmentMutasiCcBinding.inflate(layoutInflater, container, false)
        return binding.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)

        injectDependency()
        initiateMonthAdapter()
        setupViews()
    }

    override fun onDestroyView() {
        _binding = null
        super.onDestroyView()
    }

    private fun injectDependency() {
        activityComponent.inject(this)
        presenter.view = this
        presenter.start()
        presenter.setUrlMutationCc(GeneralHelper.getString(R.string.url_cc_sof_transaction))
        presenter.getMutationCc(DetailCcSofRequest(cardToken))
    }

    private fun setupViews() {
        initiateMonthAdapter()

        skeletonScreenMutation = Skeleton.bind(binding.rvMutasi)
            .adapter(listMonthMutationAdapter)
            .shimmer(true)
            .angle(20)
            .frozen(false)
            .duration(1200)
            .count(1)
            .load(R.layout.item_skeleton_mutasi)
            .show()

        binding.btStatement.setOnClickListener {
            val intent = Intent(requireActivity(), EstatementCcActivity::class.java)
            intent.putExtra(Constant.GENRES, Gson().toJson(yearModels))
            intent.putExtra(Constant.CARD_TOKEN, cardToken)
            startActivityIntent.launch(intent)
        }
    }

    private fun initiateMonthAdapter() {
        if (SystemClock.elapsedRealtime() - mLastClickTime < 1000) {
            return
        }
        binding.rvMutasi.layoutManager =
            LinearLayoutManager(context, RecyclerView.VERTICAL, false)
        binding.rvMutasi.smoothScrollToPosition(0)
        listMonthMutationAdapter =
            ListBulanMutasiCcAdapter(requireActivity(), mutasiResponses, this)
        binding.rvMutasi.adapter = listMonthMutationAdapter
        recyclerItemDecoration = StickHeaderItemDecoration(
            activity,
            requireActivity().resources.getDimensionPixelSize(R.dimen.recycler_section_header_height),
            true,
            getSectionCallback(mutasiResponses)
        )
    }

    private fun getSectionCallback(mutasi: List<Statement>? = null): StickHeaderItemDecoration.SectionCallback {
        return object : StickHeaderItemDecoration.SectionCallback {
            override fun isSection(position: Int): Boolean {
                return position == 0 || mutasi!![position].date !== mutasi!![position - 1].date
            }

            override fun getSectionHeaderName(pos: Int): String {
                return mutasi!![pos].date
            }
        }
    }

    override fun isHideSkeleton(isHide: Boolean) {
        if (isHide) {
            skeletonScreenMutation.hide()
            binding.rvMutasi.addItemDecoration(recyclerItemDecoration)
        } else {
            skeletonScreenMutation.show()
            binding.rvMutasi.removeItemDecoration(recyclerItemDecoration)
        }
    }

    @SuppressLint("NotifyDataSetChanged")
    override fun onSuccessMutationCc(response: MutationCcResponse) {
        if (response.statements.isNotEmpty()) {
            binding.layoutMutasi.visibility = View.VISIBLE
            binding.layoutNoData.visibility = View.GONE

            mutasiResponses?.addAll(response.statements)
            listMonthMutationAdapter.notifyDataSetChanged()
        } else {
            binding.layoutMutasi.visibility = View.GONE
            binding.layoutNoData.visibility = View.VISIBLE
            binding.imgNoData.setImageResource(R.drawable.ic_empty_state)
            binding.titleNoData.text =
                GeneralHelper.getString(R.string.data_mutasi_kosong)
            binding.descNoData.text = GeneralHelper.getString(R.string.desc_data_mutasi_kosong)
        }

        if (response.dateParam.isNotEmpty()) {
            yearModels?.addAll(response.dateParam)
            enableButton(true)
        }
    }

    private fun enableButton(isEnable: Boolean) {
        if (isEnable) {
            binding.btStatement.alpha = 1f
            binding.btStatement.isEnabled = true
        } else {
            binding.btStatement.alpha = 0.3f
            binding.btStatement.isEnabled = false
        }
    }

    override fun onException(message: String?) {
        if (GeneralHelper.isContains(
                Constant.LIST_TYPE_GAGAL,
                message
            )
        ) GeneralHelper.showDialogGagalBack(activity, message)
        else {
            enableButton(false)
            binding.layoutMutasi.visibility = View.GONE
            binding.layoutNoData.visibility = View.VISIBLE
            binding.imgNoData.setImageResource(R.drawable.mutation_time_out)
            binding.titleNoData.text =
                GeneralHelper.getString(R.string.title_exception_cc)
            binding.descNoData.text = GeneralHelper.getString(R.string.desc_exception_cc)
        }
    }

    override fun clickItem(details: List<Detail>) {
        val dialogFragment = BottomDetailStatementFragment(details)
        dialogFragment.show(requireActivity().supportFragmentManager, "")
        dialogFragment.isCancelable = true
    }

    private var startActivityIntent: ActivityResultLauncher<Intent> = registerForActivityResult(
        ActivityResultContracts.StartActivityForResult()
    ) {}
}