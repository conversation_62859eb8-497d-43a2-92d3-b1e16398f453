package id.co.bri.brimo.ui.activities.base;

import android.content.Intent;
import android.os.Bundle;
import android.os.SystemClock;
import android.text.Editable;
import android.util.Log;
import android.view.View;
import android.widget.Button;
import android.widget.CheckBox;
import android.widget.EditText;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.TextView;

import androidx.annotation.Nullable;
import androidx.appcompat.widget.Toolbar;

import com.google.gson.Gson;

import androidx.fragment.app.FragmentTransaction;

import id.co.bri.brimo.R;
import id.co.bri.brimo.contract.IView.base.IBaseInquiryView;
import id.co.bri.brimo.domain.config.Constant;
import id.co.bri.brimo.domain.helpers.GeneralHelper;
import id.co.bri.brimo.domain.helpers.ValidationHelper;
import id.co.bri.brimo.models.AccountModel;
import id.co.bri.brimo.models.Amount;
import id.co.bri.brimo.models.ParameterKonfirmasiModel;
import id.co.bri.brimo.models.ParameterModel;
import id.co.bri.brimo.models.apimodel.response.cc.DetailCcSofResponse;
import id.co.bri.brimo.models.apimodel.response.GeneralConfirmationResponse;
import id.co.bri.brimo.models.apimodel.response.GeneralInquiryResponse;
import id.co.bri.brimo.models.apimodel.response.SaldoReponse;
import id.co.bri.brimo.ui.activities.KonfirmasiGeneralActivity;
import id.co.bri.brimo.ui.customviews.dialog.DialogInformation;
import id.co.bri.brimo.ui.fragments.ListRekeningFragment;
import id.co.bri.brimo.ui.fragments.SumberDanaFragment;

import java.math.BigInteger;
import java.util.List;


public abstract class BaseInquiryActivity extends BaseActivity implements
        SumberDanaFragment.SelectSumberDanaInterface,
        IBaseInquiryView,
        View.OnClickListener,
        DialogInformation.OnActionClick {

    private static final String TAG = "BaseInquiryActivity";

    protected List<Integer> mListFailed;
    protected List<AccountModel> mListAccountModel;
    protected AccountModel model;
    protected Double saldo = 0.0;
    protected int counter = 0;
    protected Long minTrx = Long.valueOf(0);
    protected String minTrxString;
    protected boolean isLoading, isCheckedSave = false;
    protected String saldoString = "";
    protected String defaultAkun;
    protected Amount amountPay = new Amount();

    protected boolean isSaldoHold = false;
    protected static boolean mIsFromTopUpOnline;
    protected static String errorMessage = null;
    protected GeneralInquiryResponse mInquiryResponse;
    protected String mUrlKonfirmasi;
    protected String mUrlPayment;
    protected String mUrlPending = "";
    protected String mTitle;
    protected ParameterModel mParameterModel;
    protected ParameterKonfirmasiModel mParameterKonfirmasiModel;

    protected static String TAG_INQUIRY_DATA = "inquiry_data";
    protected static String TAG_PARAMS_DATA = "params_data";
    protected static String TAG_URL_KONFIRM = "url_konfirm";
    protected static String TAG_URL_PAYMENT = "url_payment";
    protected static String TAG_URL_PENDING = "url_pending";
    protected static String TAG_TITLE = "url_title";

    protected EditText etSaved, edNominal;
    protected TextView tvError, tvSaldo, tvNorek, tvInisial, tvSave;
    protected ImageView ivAlertSaldo;
    protected LinearLayout llSave, viewSaved, layoutError,
            layout;
    protected CheckBox cbSave;
    protected Button btnSubmit;

    protected Toolbar toolbar;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(getLayoutResource());
        etSaved = findViewById(R.id.et_saved_name);
        edNominal = findViewById(R.id.edNominal);
        tvError = findViewById(R.id.tv_error);
        tvSaldo = findViewById(R.id.tv_saldo);
        tvNorek = findViewById(R.id.tv_norek);
        tvInisial = findViewById(R.id.tv_inisial);
        ivAlertSaldo = findViewById(R.id.iv_alert_saldo);
        llSave = findViewById(R.id.save_content);
        cbSave = findViewById(R.id.checkbox_save);
        tvSave = findViewById(R.id.tv_save);
        viewSaved = findViewById(R.id.view_saved);
        layoutError = findViewById(R.id.layout_error);
        btnSubmit = findViewById(R.id.btnSubmit);
        toolbar = findViewById(R.id.tb_inquiry_toolbar);
        layout = findViewById(R.id.item_layout_background);

        //parsing data intent
        if (getIntent().getExtras() != null) {
            parseDataIntentInquiry(getIntent().getExtras());
        }

        GeneralHelper.setToolbar(this, toolbar, getTitleBar());

        mParameterKonfirmasiModel = setParameterKonfirmasi();

        //Error Snackbar
        if (errorMessage != null) {
            showSnackbarErrorMessage(errorMessage, ALERT_ERROR, this, false);
            errorMessage = null;
        }

        layout.setOnClickListener(this);
        btnSubmit.setOnClickListener(this);

        //set listener untuk field inputan nama
        if (etSaved != null) {
            etSaved.addTextChangedListener(activityTextListener);
        }
    }

    /**
     * Method digunakan untuk meng-extract data Intent dari Form Activity
     *
     * @param savedInstanceState Bundle savedInstanceState
     */
    protected void parseDataIntentInquiry(Bundle savedInstanceState) {
        if (savedInstanceState != null) {
            try {
                String tempInquiry = savedInstanceState.getString(TAG_INQUIRY_DATA);
                if (tempInquiry != null) {
                    mInquiryResponse = new Gson().fromJson(tempInquiry, GeneralInquiryResponse.class);
                }

                String tempParams = savedInstanceState.getString(TAG_PARAMS_DATA);
                if (tempParams != null) {
                    mParameterModel = new Gson().fromJson(tempParams, ParameterModel.class);
                }

                mUrlKonfirmasi = savedInstanceState.getString(TAG_URL_KONFIRM);
                mUrlPayment = savedInstanceState.getString(TAG_URL_PAYMENT);
                mUrlPending = savedInstanceState.getString(TAG_URL_PENDING);
                mTitle = savedInstanceState.getString(TAG_TITLE);

            } catch (Exception e) {
                if (!GeneralHelper.isProd()) {
                    Log.e(TAG, "parseDataIntentInquiry: ", e);
                }
            }
        }
    }

    @Override
    protected void afterText(Editable editable) {
        //balikan listener field inputan nama
        try {
            String savedText = etSaved.getText().toString();
            if (savedText.equalsIgnoreCase("") || !ValidationHelper.validateAmountString(amountPay.getText())
                    || isSaldoHold) {
                disableButtonSubmit(true);
                setupInputError(edNominal);
            } else if (!savedText.equalsIgnoreCase("") && saldo <= 0 || isSaldoHold) {
                disableButtonSubmit(true);
                setupInputError(edNominal);
            } else {
                disableButtonSubmit(false);
                setupInputError(edNominal);
            }
        } catch (Exception e) {
            if (!GeneralHelper.isProd()) {
                Log.e(TAG, "afterText: ", e);
            }
        }
    }


    /**
     * Callback dari get saldo default dari BRImoPref
     *
     * @param saldoDefault    saldo dalam double
     * @param saldoStringPref saldo dalam bentuk string
     * @param defaultAcc      rekening default
     * @param saldoHold       saldo tertahan
     */
    @Override
    public void setDefaultSaldo(double saldoDefault, String saldoStringPref, String defaultAcc, boolean saldoHold) {
        // load default saldo dari preference
        saldoString = saldoStringPref;
        saldo = saldoDefault;
        defaultAkun = defaultAcc;
        isSaldoHold = saldoHold;

        if (edNominal != null) {
            setupInputError(edNominal);
        }

        //set layout
        setupAccount(saldoDefault);
        setupSaveName();
        setupView();
    }

    /**
     * Dipanggil ketika callback dari
     * 1. presenter.start
     * 2. presenter.getSaldoDefault
     * 3. this.setDefaultSaldo()
     * setup account digunakan utk mengisi list account pada SumberDanaFragment
     * method ini mempengaruhi method onSelectSumberDana()
     *
     * @param saldoDefault
     */
    protected void setupAccount(double saldoDefault) {
        if (mInquiryResponse == null)
            finish();

        //List Account
        model = new AccountModel();
        if (mInquiryResponse.getAccountList().size() > 0)
            mListAccountModel = mInquiryResponse.getAccountList();

        for (AccountModel accountModel : mListAccountModel) {
            if (accountModel.getIsDefault() == 1) {
                model = accountModel;
                break;
            } else {
                model = mListAccountModel.get(0);
            }
        }

        //jika get minimum tidak null
        if (model.getMinimumBalance() != null) {
            saldo = saldoDefault - model.getMinimumBalance();
        } else {
            saldo = saldoDefault;
        }

        if (isFromFastMenu) {
            tvSaldo.setVisibility(View.GONE);
        } else {
            tvSaldo.setVisibility(View.VISIBLE);

            if (model.getAcoount() != null) {
                if (model.getAcoount().equals(defaultAkun) && !saldoString.equalsIgnoreCase("")) {
                    //tvSaldo.setText(String.format("%s%s", model.getCurrency(), saldoString));
                    tvSaldo.setText(GeneralHelper.formatNominalIDR(model.getCurrency(), saldoString));
                } else {
                    tvSaldo.setText("-");
                }
            }

        }

        //jika string rekening tidak kosong
        if (model.getAcoountString() != null) {
            tvNorek.setText(model.getAcoountString());
        } else {
            tvNorek.setText("-");
        }

        //jika nama kosong
        if (model.getName() != null) {
            tvInisial.setText(GeneralHelper.formatInitialName(model.getName()));
        } else {
            tvInisial.setText("I");
        }

        if (model.getSaldoReponse() != null) {
            isSaldoHold = model.getSaldoReponse().isOnHold();
        }

        if (!isFromFastMenu) {
            if (isSaldoHold)
                ivAlertSaldo.setVisibility(View.VISIBLE);
            else ivAlertSaldo.setVisibility(View.GONE);
        }

    }

    protected void setupSaveName() {
        if (mInquiryResponse == null)
            finish();

        //All About Save name
        if (mInquiryResponse.getSaved().equals("")) {
            if (llSave!=null){
                llSave.setVisibility(View.GONE);
                if (cbSave!=null){
                    cbSave.setOnClickListener(view -> {
                        if (cbSave.isChecked()) {
                            onAnimatorShow(llSave, true, Constant.REQUEST_INQUIRY);
                            isCheckedSave = true;
                            setupInputError(edNominal);
                        } else {
                            onAnimatorFade(llSave, false, Constant.REQUEST_INQUIRY);
                            // disableButtonSubmit(false);
                            isCheckedSave = false;
                            setupInputError(edNominal);
                        }
                    });
                }
            }

            if (tvSave!=null){
                tvSave.setOnClickListener(view -> {
                    if (cbSave!=null){
                        if (!isCheckedSave) {
                            cbSave.setChecked(true);
                            onAnimatorShow(llSave, true, Constant.REQUEST_INQUIRY);
                            isCheckedSave = true;
                            setupInputError(edNominal);
                        } else {
                            cbSave.setChecked(false);
                            onAnimatorFade(llSave, false, Constant.REQUEST_INQUIRY);
                            // disableButtonSubmit(false);
                            isCheckedSave = false;
                            setupInputError(edNominal);
                        }
                    }
                });
            }
        } else {
            if (viewSaved!=null){
                viewSaved.setVisibility(View.GONE);
            }
        }
    }


    @Override
    protected void onAnimatorFadeEnd(String tagIdFade) {
        super.onAnimatorFadeEnd(tagIdFade);

        if (tagIdFade.equals(Constant.REQUEST_INQUIRY)) {
            llSave.setVisibility(View.GONE);
        }
    }

    @Override
    protected void onAnimatorShowEnd(String tagId) {
        super.onAnimatorShowEnd(tagId);
        if (tagId.equals(Constant.REQUEST_INQUIRY)) {
            llSave.setVisibility(View.VISIBLE);
        }
    }

    /*
     * Method ini digunakan untuk validasi saldo cukup.
     *
     */
    protected void setupInputError(EditText edNominal) {
        if (edNominal==null) return;
        if (edNominal.getText().toString().isEmpty()) {
            disableButtonSubmit(true);
            onAnimator(layoutError, true, ANIMATE_SHOW, Constant.REQUEST_BASE_INQUIRY);
            showAmountMinimal(mParameterModel);
            return;
        }

        amountPay.setText(GeneralHelper.clearingAmount(edNominal.getText().toString()));

        //jika bisa menyimpan atau ceklist save true
        if (mInquiryResponse.getSaved().equals("") && isCheckedSave) {
            if (ValidationHelper.validateAmountString(amountPay.getText()) && !etSaved.getText().toString().equals("")) {
                if (BigInteger.valueOf(minTrx).compareTo(BigInteger.valueOf(Long.valueOf(amountPay.getText()))) <= 0) {
                    if (BigInteger.valueOf(Long.valueOf(amountPay.getText())).compareTo(BigInteger.valueOf(saldo.longValue())) <= 0 || isFromFastMenu) {
                        onAnimator(layoutError, false, ANIMATE_GONE, Constant.REQUEST_BASE_INQUIRY);
                        disableButtonSubmit(false);
                    } else {
                        onAnimator(layoutError, true, ANIMATE_SHOW, Constant.REQUEST_BASE_INQUIRY);
                        disableButtonSubmit(true);
                        if (isSaldoHold)
                            showSaldoHold();
                        else showAmountInsuficient();
                    }
                } else {
                    onAnimator(layoutError, true, ANIMATE_SHOW, Constant.REQUEST_BASE_INQUIRY);
                    showAmountMinimal(mParameterModel);
                    disableButtonSubmit(true);
                }
            } else if (!ValidationHelper.validateAmountString(amountPay.getText())) {
                onAnimator(layoutError, true, ANIMATE_SHOW, Constant.REQUEST_BASE_INQUIRY);
                showAmountMinimal(mParameterModel);
                disableButtonSubmit(true);
            } else if (etSaved.getText().toString().equals("") && ValidationHelper.validateAmountString(amountPay.getText())) {
                disableButtonSubmit(true);
            } else {
                onAnimator(layoutError, false, ANIMATE_GONE, Constant.REQUEST_BASE_INQUIRY);
                disableButtonSubmit(false);
            }


        } else {
            if (ValidationHelper.validateAmountString(amountPay.getText())) {
                if (BigInteger.valueOf(minTrx).compareTo(BigInteger.valueOf(Long.valueOf(amountPay.getText()))) <= 0) {
                    if (BigInteger.valueOf(Long.valueOf(amountPay.getText())).compareTo(BigInteger.valueOf(saldo.longValue())) <= 0 || isFromFastMenu) {
                        onAnimator(layoutError, false, ANIMATE_GONE, Constant.REQUEST_BASE_INQUIRY);
                        disableButtonSubmit(false);
                    } else {
                        onAnimator(layoutError, true, ANIMATE_SHOW, Constant.REQUEST_BASE_INQUIRY);
                        disableButtonSubmit(true);

                        if (isSaldoHold)
                            showSaldoHold();
                        else showAmountInsuficient();
                    }
                } else {
                    onAnimator(layoutError, true, ANIMATE_SHOW, Constant.REQUEST_BASE_INQUIRY);
                    showAmountMinimal(mParameterModel);
                    disableButtonSubmit(true);
                }
            } else if (!ValidationHelper.validateAmountString(amountPay.getText())) {
                onAnimator(layoutError, true, ANIMATE_SHOW, Constant.REQUEST_BASE_INQUIRY);
                showAmountMinimal(mParameterModel);
                disableButtonSubmit(true);
            } else {
                onAnimator(layoutError, false, ANIMATE_GONE, Constant.REQUEST_BASE_INQUIRY);
                disableButtonSubmit(false);
            }
        }
    }

    /**
     * Show parameter minimal di field
     *
     * @param params Parameter Model
     */
    protected void showAmountMinimal(ParameterModel params) {
        if (params.getStringLabelMinimum() != null) {
            if (tvError==null) return;
            tvError.setText(String.format("Minimal %s %s", params.getStringLabelMinimum(), minTrxString));
            tvError.setTextColor(getResources().getColor(R.color.colorErrorMinTrx));
        }
    }

    /**
     * Show parameter saldo tidak cukup
     */
    protected void showAmountInsuficient() {
        if (tvError==null) return;
        tvError.setText("Saldo Anda tidak cukup");
        tvError.setTextColor(getResources().getColor(R.color.red));
    }

    /**
     * Show parameter saldo hold
     */
    protected void showSaldoHold() {
        if (tvError==null) return;
        tvError.setText(GeneralHelper.getString(R.string.saldo_tertahan));
        tvError.setTextColor(getResources().getColor(R.color.red));
    }

    @Override
    public void onClick(View view) {
        if (SystemClock.elapsedRealtime() - mLastClickTime < 1000) {
            return;
        }
        mLastClickTime = SystemClock.elapsedRealtime();
        int id = view.getId();
        switch (id) {
            case R.id.item_layout_background:
                counter++;
                if (mListAccountModel == null) {
                    GeneralHelper.showToast(this, GeneralHelper.getString(R.string.you_dont_have_any_accounts_yet));
                } else {
                    if (isFromFastMenu) {
                        //list rekening untuk fastmenu
                        ListRekeningFragment fragmentSumberDana = new ListRekeningFragment(mListAccountModel, this, counter, mListFailed);
                        fragmentSumberDana.show(getSupportFragmentManager(), Constant.TAG_PICK_ACCOUNT);
                    } else {
                        //list rekening untuk inquiry general
                        SumberDanaFragment fragmentSumberDana = new SumberDanaFragment(mListAccountModel, this, counter, mListFailed);
                        fragmentSumberDana.show(getSupportFragmentManager(), Constant.TAG_PICK_ACCOUNT);
                    }

                }
                break;
            case R.id.btnSubmit:
                onSubmit();
                break;
        }
    }

    @Override
    public void onException93(String message) {
        isLoading = false;
        Intent returnIntent = new Intent();

        returnIntent.putExtra(Constant.TAG_ERROR_MESSAGE, message);

        this.setResult(RESULT_CANCELED, returnIntent);
        this.finish();
    }

    @Override
    public void onException(String message) {
        isLoading = false;
        if (GeneralHelper.isContains(Constant.LIST_TYPE_GAGAL, message)) {
            GeneralHelper.showDialogGagalBack(this, message);
        } else {
            showSnackbarErrorMessage(message, ALERT_ERROR, this, false);
        }
    }

    @Override
    public void onSelectSumberDana(AccountModel bankModel) {
        model = bankModel;
        onChangeSourcePayment(bankModel);
    }

    protected void onChangeSourcePayment(AccountModel bankModel) {
        if (bankModel.getAccountType().equalsIgnoreCase("CC")) {
            double limit = bankModel.getDetailCcSofResponse().getLimit();
            if (bankModel.getDetailCcSofResponse().getLimit() == null) {
               limit = bankModel.getLimit();
            }

            if (bankModel.getDetailCcSofResponse() != null) {
                if (limit >= bankModel.getDetailCcSofResponse().getBalance()) {
                    tvSaldo.setText(GeneralHelper.formatNominalIDR(model.getCurrency(), model.getDetailCcSofResponse().getBalanceString()));
                    saldo = bankModel.getDetailCcSofResponse().getBalance();
                } else {
                    tvSaldo.setText(GeneralHelper.formatNominalIDR(model.getCurrency(), model.getLimitString()));
                    saldo = Double.valueOf(bankModel.getLimit());
                }
            } else {
                tvSaldo.setText(String.format("%s%s", bankModel.getCurrency(), "-"));
                saldo = 0.0;
            }
        } else {
            if (bankModel.getSaldoReponse() != null) {
                tvSaldo.setText(GeneralHelper.formatNominalIDR(model.getCurrency(), model.getSaldoReponse().getBalanceString()));
                saldo = bankModel.getSaldoReponse().getBalance() - bankModel.getMinimumBalance();
                isSaldoHold = bankModel.getSaldoReponse().isOnHold();
            } else {
                tvSaldo.setText(String.format("%s%s", bankModel.getCurrency(), "-"));
                saldo = 0.0;
                isSaldoHold = false;
            }
        }

        // saldo tertahan
        if (!isFromFastMenu) {
            if (bankModel.getSaldoReponse().isOnHold()) {
                ivAlertSaldo.setVisibility(View.VISIBLE);
                dialogInfoSaldoHold();
            } else ivAlertSaldo.setVisibility(View.GONE);
        }

        tvNorek.setText(model.getAcoountString());
        tvInisial.setText(GeneralHelper.formatInitialName(bankModel.getName()));
    }

    protected void dialogInfoSaldoHold() {
        DialogInformation dialog = new DialogInformation(this, "maaf_maaf",
                GeneralHelper.getString(R.string.title_popup_saldo_tertahan),
                GeneralHelper.getString(R.string.desc_popup_saldo_tertahan),
                GeneralHelper.getString(R.string.ok), this, true, false);
        FragmentTransaction ft = getSupportFragmentManager().beginTransaction();
        ft.add(dialog, null);
        ft.commitAllowingStateLoss();
    }

    /*
     * Menambahkan List Rekening
     */
    @Override
    public void onSendFailedList(List<Integer> list) {
        this.mListFailed = list;
    }


    @Override
    protected void onActivityResult(int requestCode, int resultCode, @Nullable Intent data) {
        super.onActivityResult(requestCode, resultCode, data);
        if (requestCode == Constant.REQ_PAYMENT) {
            if (resultCode == RESULT_OK) {
                this.setResult(RESULT_OK, data);
                this.finish();
            } else {
                this.setResult(RESULT_CANCELED, data);
                this.finish();
            }
        }
    }

    @Override
    public void onSuccessGetConfirmation(GeneralConfirmationResponse brivaConfirmationResponse) {
        isLoading = false;
        KonfirmasiGeneralActivity.launchIntent(this, brivaConfirmationResponse, mUrlPayment, mUrlPending, getTitleBar(), mParameterKonfirmasiModel, isFromFastMenu, true, true);
        //isCASHBACK sudah tidak digunakan dihapus di versioning berikut nya
    }

    protected void disableButtonSubmit(boolean disable) {
        if (disable) {
            btnSubmit.setEnabled(false);
            btnSubmit.setAlpha((float) 0.3);
        } else {
            btnSubmit.setEnabled(true);
            btnSubmit.setAlpha(1);
        }
    }

    @Override
    public void onClickAction() {
        // do nothing
    }

    protected abstract void setupView();

    @Override
    public abstract void onSubmit();

    @Override
    public abstract int getAmount();

    protected abstract int getLayoutResource();

    protected abstract String getTitleBar();

    protected void injectDependency() {
        getActivityComponent().inject(this);
    }

    @Override
    public void onSucessSofCcSaldo(DetailCcSofResponse detailCcSofResponse, SaldoReponse saldoReponse) {
        //do nothing
    }

    @Override
    public void onShowDialogCcAsSof() {
        //do nothing
    }

    @Override
    public void onSuccessChangeSof(String desc) {
        //do  nothing
    }
}