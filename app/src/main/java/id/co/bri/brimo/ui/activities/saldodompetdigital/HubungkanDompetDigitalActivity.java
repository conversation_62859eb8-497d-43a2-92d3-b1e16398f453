package id.co.bri.brimo.ui.activities.saldodompetdigital;

import android.app.Activity;
import android.content.Intent;
import android.content.res.Resources;
import android.os.Build;
import android.os.Bundle;
import android.view.View;
import android.view.Window;
import android.view.WindowManager;

import androidx.annotation.Nullable;
import androidx.fragment.app.FragmentTransaction;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.google.gson.Gson;

import java.util.List;
import java.util.Objects;
import java.util.regex.Pattern;

import javax.inject.Inject;

import id.co.bri.brimo.R;
import id.co.bri.brimo.adapters.ListFiturDompetDigitalAdapter;
import id.co.bri.brimo.contract.IPresenter.saldodompetdigital.IHubungkanDompetDigitalPresenter;
import id.co.bri.brimo.contract.IView.saldodompetdigital.IHubungkanDompetDigitalView;
import id.co.bri.brimo.databinding.ActivityHubungkanDompetDigitalBinding;
import id.co.bri.brimo.domain.config.Constant;
import id.co.bri.brimo.domain.helpers.GeneralHelper;
import id.co.bri.brimo.models.apimodel.response.EwalletBindingResponse;
import id.co.bri.brimo.models.apimodel.response.EwalletFeatureResponse;
import id.co.bri.brimo.models.apimodel.response.EwalletProductResponse;
import id.co.bri.brimo.models.apimodel.response.ExtrasResponse;
import id.co.bri.brimo.models.apimodel.response.SyaratKetentuanResponse;
import id.co.bri.brimo.ui.activities.GeneralSyaratActivity;
import id.co.bri.brimo.ui.activities.base.BaseActivity;
import id.co.bri.brimo.ui.activities.topuprevamp.TopUpRevampActivity;
import id.co.bri.brimo.ui.customviews.PatternEditableBuilder;
import id.co.bri.brimo.ui.customviews.dialog.DialogSetDefault;

public class HubungkanDompetDigitalActivity extends BaseActivity implements
        IHubungkanDompetDigitalView,
        View.OnClickListener {

    private ActivityHubungkanDompetDigitalBinding binding;

    @Inject
    IHubungkanDompetDigitalPresenter<IHubungkanDompetDigitalView> presenter;

    protected static final String TAG_RESPONSE = "response";
    protected EwalletProductResponse mEwalletProductModel;
    protected ListFiturDompetDigitalAdapter listFiturDompetDigitalAdapter;
    protected boolean isSyarat = false;

    public static void launchIntent(Activity caller, String response) {
        Intent intent = new Intent(caller, HubungkanDompetDigitalActivity.class);
        intent.putExtra(TAG_RESPONSE, response);
        caller.startActivityForResult(intent, Constant.REQ_PAYMENT);
    }

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        binding = ActivityHubungkanDompetDigitalBinding.inflate(getLayoutInflater());
        setContentView(binding.getRoot());

        if (getIntent().getExtras().getString(TAG_RESPONSE) != null)
            mEwalletProductModel = new Gson().fromJson(getIntent().getExtras().getString(TAG_RESPONSE), EwalletProductResponse.class);

        String toolbarTitle = "Hubungkan " + mEwalletProductModel.getTitle();
        GeneralHelper.setToolbar(this, binding.toolbar, toolbarTitle);

        injectDependency();

        //Detail Rekening Asal
        GeneralHelper.loadIconTransaction(
                this,
                mEwalletProductModel.getIconPath(),
                mEwalletProductModel.getIconName(),
                binding.ivLogoDompet,
                GeneralHelper.getImageId(this, "ic_menu_qna_dompet_digital"));
        Resources res = getResources();
        binding.tvDescSyarat.setText(String.format(res.getString(R.string.syarat_hubungkan_dompet_digital), mEwalletProductModel.getTitle()));
        binding.cvSnk.setOnClickListener(this);
        binding.tvDescSyarat.setOnClickListener(this);
        binding.btnSubmit.setOnClickListener(this);

        setupView();

        if (Build.VERSION.SDK_INT >= 21) {
            Window window = getWindow();
            window.addFlags(WindowManager.LayoutParams.FLAG_DRAWS_SYSTEM_BAR_BACKGROUNDS);
            window.clearFlags(WindowManager.LayoutParams.FLAG_TRANSLUCENT_STATUS);
            window.setStatusBarColor(getResources().getColor(R.color.headerColor));
        }
        new PatternEditableBuilder().
                addPattern(Pattern.compile("Syarat dan Ketentuan"), GeneralHelper.getColor(R.color.notif_color),
                        text -> {
                        }).into(binding.tvDescSyarat);

    }

    private void setupView() {
        String title = mEwalletProductModel.getFeatureDesc().getTitle();
        binding.tvTitle.setText(title);
        if (mEwalletProductModel.getFeatureDesc().getFeatures() != null) {
            List<EwalletFeatureResponse> ewalletFeatureResponses = mEwalletProductModel.getFeatureDesc().getFeatures();
            //Adapter fitur
            binding.rvFiturDompetDigital.setHasFixedSize(true);
            binding.rvFiturDompetDigital.setLayoutManager(new LinearLayoutManager(getApplicationContext(), RecyclerView.VERTICAL, false));
            listFiturDompetDigitalAdapter = new ListFiturDompetDigitalAdapter(this, ewalletFeatureResponses);
            binding.rvFiturDompetDigital.setAdapter(listFiturDompetDigitalAdapter);
        }


    }

    private void injectDependency() {
        getActivityComponent().inject(this);
        if (presenter != null) {
            presenter.setView(this);
            presenter.setUrlEwalletTnc(GeneralHelper.getString(R.string.url_ewallet_tnc));
            presenter.setUrlEwalletBinding(GeneralHelper.getString(R.string.url_ewallet_binding));
            presenter.start();

        }
    }

    @Override
    public void onClick(View v) {
        switch (v.getId()) {
            case R.id.btn_submit:
                if (isSyarat) {
                    DialogSetDefault dialogSetDefault = new DialogSetDefault(
                            new DialogSetDefault.DialogDefaultListener() {
                                @Override
                                public void onClickYesDefault(int requestId) {
                                    presenter.bindingEwallet(mEwalletProductModel.getCode(), mEwalletProductModel.getCountry_code());
                                }

                                @Override
                                public void onClickNoDefault(int requestId) {
                                    // Do nothing because default
                                }
                            },
                            "Konfirmasi Nomor Handphone",
                            mEwalletProductModel.getConfirmation(), "Konfirmasi", "Batal", Constant.REQ_AKTIF_ALIAS);
                    FragmentTransaction ft = getSupportFragmentManager().beginTransaction();
                    ft.add(dialogSetDefault, null);
                    ft.commitAllowingStateLoss();
                }
                break;
            case R.id.cv_snk:
                presenter.getEwalletTnc(mEwalletProductModel.getType());
                break;
            case R.id.tv_desc_syarat:
                presenter.getEwalletTnc(mEwalletProductModel.getType());
                break;
            default:
                break;
        }

    }

    @Override
    protected void onActivityResult(int requestCode, int resultCode, @Nullable Intent data) {
        super.onActivityResult(requestCode, resultCode, data);
        if (requestCode == Constant.REQ_PETTUNJUK1 && data != null) {
            if (resultCode == RESULT_OK) {
                isSyarat = Boolean.parseBoolean(data.getStringExtra("checkbox"));

                if (isSyarat) {
                    binding.cbSyarat.setBackgroundResource(R.drawable.checkbox_on);
                } else {
                    binding.cbSyarat.setBackgroundResource(R.drawable.checkbox_off);
                }
                setvalidasiButton();
            }
        }

        if (requestCode == Constant.REQ_PAYMENT) {
            if (resultCode == RESULT_OK) {
                this.setResult(RESULT_OK, data);
                this.finish();
            } else {
                this.setResult(RESULT_CANCELED, data);
                if (data != null) {
                    if (data.getStringExtra(Constant.TAG_ERROR_MESSAGE) != null) {
                        String errorMessage = data.getStringExtra(Constant.TAG_ERROR_MESSAGE);
                        GeneralHelper.showSnackBar(Objects.requireNonNull(this).findViewById(R.id.content), errorMessage);
                    }
                }
            }
        }
    }

    protected void setvalidasiButton() {
        if (isSyarat) {
            binding.btnSubmit.setAlpha(1);
            binding.btnSubmit.setEnabled(true);
        } else {
            binding.btnSubmit.setAlpha(0.3f);
            binding.btnSubmit.setEnabled(false);
        }
    }

    @Override
    public void onSuccessGetTnc(SyaratKetentuanResponse syaratKetentuanResponse) {
        hideProgress();
        GeneralSyaratActivity.launchIntent(this, syaratKetentuanResponse.getTerm());
    }

    @Override
    public void onException12(String message) {
        Intent returnIntent = new Intent();

        returnIntent.putExtra(Constant.TAG_ERROR_MESSAGE, message);

        this.setResult(RESULT_CANCELED, returnIntent);
        this.finish();
    }

    @Override
    public void onSuccessBinding(EwalletBindingResponse ewalletBindingResponse) {
        if (ewalletBindingResponse.getStatusCode().equals("200")) {
            String txtSuksesBindingTitle = (ewalletBindingResponse.getExtrasResponse().getTitle() != null) ? ewalletBindingResponse.getExtrasResponse().getTitle() : GeneralHelper.getString(R.string.txt_sukses_binding_title);
            String txtSuksesBindingDesc = (ewalletBindingResponse.getExtrasResponse().getDesc() != null) ? ewalletBindingResponse.getExtrasResponse().getDesc() : GeneralHelper.getString(R.string.txt_sukses_binding_description);
            TopUpRevampActivity.Companion.launchIntentDialog(this, GeneralHelper.getString(R.string.drawable_src_ilustration), txtSuksesBindingTitle, txtSuksesBindingDesc);
        } else {
            WebViewBindingActivity.launchIntent(this, ewalletBindingResponse);
        }
    }

    @Override
    public void onException01(EwalletBindingResponse ewalletBindingResponse) {
        if (ewalletBindingResponse.getExtrasResponse() != null) {
            ExtrasResponse extrasResponse = ewalletBindingResponse.getExtrasResponse();
            TopUpRevampActivity.Companion.launchIntentDialog(this, GeneralHelper.getString(R.string.drawable_src_maaf), extrasResponse.getTitle(), extrasResponse.getDesc());
        }
    }

    @Override
    protected void onDestroy() {
        super.onDestroy();
        binding = null;
    }
}