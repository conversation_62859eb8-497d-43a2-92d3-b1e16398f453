package id.co.bri.brimo.ui.activities.virtualdebitcard

import android.app.Activity
import android.content.Intent
import android.os.Bundle
import androidx.activity.result.ActivityResultLauncher
import androidx.activity.result.contract.ActivityResultContracts
import androidx.recyclerview.widget.LinearLayoutManager
import com.google.gson.Gson
import id.co.bri.brimo.R
import id.co.bri.brimo.adapters.virtualdebitcard.DetailDataVDCAdapter
import id.co.bri.brimo.databinding.ActivityProcessPageVirtualCardBinding
import id.co.bri.brimo.domain.config.Constant
import id.co.bri.brimo.models.apimodel.response.virtualdebitcard.CreateVDCResponse
import id.co.bri.brimo.models.apimodel.response.virtualdebitcard.DetailData
import id.co.bri.brimo.models.apimodel.response.virtualdebitcard.HeaderData
import id.co.bri.brimo.ui.activities.base.BaseActivity
import id.co.bri.brimo.ui.activities.ssc.SelfServiceActivity

class ProcessPageVDCActivity : BaseActivity() {

    private lateinit var binding: ActivityProcessPageVirtualCardBinding

    private val gson = Gson()
    private lateinit var responseCreateVDC: CreateVDCResponse
    private lateinit var headerContent: HeaderData
    private lateinit var accountData: List<DetailData>
    private lateinit var detailData: List<DetailData>
    private val detailDataVDCAdapter = DetailDataVDCAdapter()
    private val accountDataVDCAdapter = DetailDataVDCAdapter()

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)

        binding = ActivityProcessPageVirtualCardBinding.inflate(layoutInflater)
        setContentView(binding.root)

        responseCreateVDC =
            gson.fromJson(
                intent.getStringExtra(Constant.TAG_CONTENT),
                CreateVDCResponse::class.java
            )

        headerContent = responseCreateVDC.headerData
        accountData = responseCreateVDC.accountData
        detailData = responseCreateVDC.detailData

        binding.animationView.playAnimation()

        injectDependency()
        setupView()
    }

    private fun injectDependency() {
        activityComponent.inject(this)
    }

    private fun setupView() {
        binding.toolbar.setNavigationIcon(R.drawable.ic_arrow_left_revamp)
        binding.toolbar.setNavigationOnClickListener { onBackPressed() }

        setupHeader()
        setupAccount()
        setupDataVDC()

        binding.ivCallCenter.setOnClickListener {
            SelfServiceActivity.launchIntent(this)
        }
        binding.btnSubmit.setOnClickListener {
            handleGoToListVDC()
        }
    }

    override fun onBackPressed() {
        super.onBackPressed()
        handleGoToListVDC()
    }

    private fun handleGoToListVDC() {
        val intent = Intent(this, OnBoardingVDCActivity::class.java)
        intent.addFlags(Intent.FLAG_ACTIVITY_CLEAR_TOP)
        startActivityIntent.launch(intent)
        finish()
    }

    private fun setupHeader() {
        binding.titleTxt.text = headerContent.title
        binding.subTitleTxt.text = headerContent.subTitle
    }

    private fun setupAccount() {
        binding.rvDataAccount.apply {
            layoutManager =
                LinearLayoutManager(context, LinearLayoutManager.VERTICAL, false)
            adapter = accountDataVDCAdapter
        }
        accountDataVDCAdapter.detailData = accountData.toMutableList()
    }

    private fun setupDataVDC() {
        binding.rvDataVirtualCard.apply {
            layoutManager =
                LinearLayoutManager(context, LinearLayoutManager.VERTICAL, false)
            adapter = detailDataVDCAdapter
        }
        detailDataVDCAdapter.detailData = detailData.toMutableList()
    }

    private var startActivityIntent: ActivityResultLauncher<Intent> =
        registerForActivityResult(ActivityResultContracts.StartActivityForResult()) { result ->
            if (result.resultCode == Activity.RESULT_OK) {
                // There are no request codes
                // val data: Intent? = result.data
            } else {
                if (result.data != null) {
                    setResult(RESULT_CANCELED, result.data)
                    finish()
                }
            }
        }
}