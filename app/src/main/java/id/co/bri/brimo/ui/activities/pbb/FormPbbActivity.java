package id.co.bri.brimo.ui.activities.pbb;

import android.app.Activity;
import android.content.Intent;
import android.os.Bundle;
import android.util.Log;
import android.view.View;

import com.ethanhua.skeleton.Skeleton;
import com.google.gson.Gson;

import androidx.annotation.Nullable;

import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;
import id.co.bri.brimo.R;
import id.co.bri.brimo.adapters.HistoryThreeRowAdapter;
import id.co.bri.brimo.adapters.SavedThreeRowAdapter;
import id.co.bri.brimo.contract.IPresenter.pbb.IFormPbbPresenter;
import id.co.bri.brimo.contract.IView.pbb.IFormPbbView;
import id.co.bri.brimo.domain.config.Constant;
import id.co.bri.brimo.domain.helpers.GeneralHelper;
import id.co.bri.brimo.models.ParameterModel;
import id.co.bri.brimo.models.apimodel.request.InquiryPbbRequest;
import id.co.bri.brimo.models.apimodel.request.ReInquiryPbbRequest;
import id.co.bri.brimo.models.apimodel.response.DataPbbResponse;
import id.co.bri.brimo.models.apimodel.response.ExtraResponse;
import id.co.bri.brimo.models.apimodel.response.GeneralInquiryResponse;
import id.co.bri.brimo.models.apimodel.response.HistoryResponse;
import id.co.bri.brimo.models.apimodel.response.PbbListResponse;
import id.co.bri.brimo.models.apimodel.response.ReInquiryPbbResponse;
import id.co.bri.brimo.models.apimodel.response.RestResponse;
import id.co.bri.brimo.models.apimodel.response.SavedResponse;
import id.co.bri.brimo.models.apimodel.response.WilayahDataPbbResponse;
import id.co.bri.brimo.models.optionmodel.OptionGeneralModel;
import id.co.bri.brimo.ui.activities.FormEditSavedActivity;
import id.co.bri.brimo.ui.activities.base.BaseActivity;
import id.co.bri.brimo.ui.activities.base.BaseFormActivity;
import id.co.bri.brimo.ui.fragments.PilihanGeneralFragment;

import java.util.ArrayList;
import java.util.List;

import javax.inject.Inject;

public class FormPbbActivity extends BaseFormActivity implements IFormPbbView, HistoryThreeRowAdapter.ClickItem, SavedThreeRowAdapter.ClickItem {

    private static final String TAG = "FormPbbActivity";

    @Inject
    IFormPbbPresenter<IFormPbbView> pbbPresenter;

    protected DataPbbResponse mDataPbbResponse;
    protected List<ExtraResponse> extras;
    ParameterModel parameterModel;
    protected HistoryThreeRowAdapter historyAdapter;
    protected SavedThreeRowAdapter savedAdapter;
    protected String mCodeWilayah = "";
    protected String mNoPembayaran = "";

    public static void launchIntent(Activity caller, boolean fromFast) {
        Intent intent = new Intent(caller, FormPbbActivity.class);
        isFromFastMenu = fromFast;
        caller.startActivityForResult(intent, Constant.REQ_PAYMENT);
    }

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        initiateSavedAdapterThreeRow();
        isShowButtonAddSavedList(false);
    }

    public static void launchIntentNotice(Activity caller, boolean fromFast) {
        Intent intent = new Intent(caller, FormPbbActivity.class);

        isFromFastMenu = fromFast;

        Intent intentReturn = new Intent();
        intentReturn.putExtra(Constant.REQUEST_RECEIPT, true);

        caller.setResult(RESULT_OK, intentReturn);
        caller.startActivityForResult(intent, Constant.REQ_PAYMENT);
        caller.finish();
    }

    @Override
    public int getLayoutResource() {
        //load layout
        return R.layout.activity_form_briva;
    }

    @Override
    public int getDefaultIconResource() {
        return R.drawable.ic_menu_qna_pbb;
    }

    @Override
    public void setTextForm() {
        btnSubmitTambah.setText(GeneralHelper.getString(R.string.tx_pembayaran_baru));
        tvTerakhir.setText(GeneralHelper.getString(R.string.last_payment));
        tvDaftarTersimpan.setText(GeneralHelper.getString(R.string.txt_list_payment_pbb));
    }

    @Override
    public String getTitleBar() {
        return GeneralHelper.getString(R.string.toolbar_pembayaran_pbb);
    }

    @Override
    public ParameterModel setParameter() {
        ParameterModel parameterModel = new ParameterModel();

        parameterModel.setStringLabelTujuan("Nomor Tujuan");
        parameterModel.setStringLabelNominal("Nominal");
        parameterModel.setStringButtonSubmit("Top Up");
        parameterModel.setStringLabelMinimum("Top Up");
        parameterModel.setDefaultIcon(getDefaultIconResource());
        parameterModel.setStringButtonSubmit(GeneralHelper.getString(R.string.pay));

        return parameterModel;
    }


    @Override
    protected void injectDependency() {
        super.injectDependency();
        getActivityComponent().inject(this);

        if (pbbPresenter != null) {
            //setting URL for presenter
            initiatePresenter();
            pbbPresenter.getDataForm();
        }
    }


    @Override
    protected void onResume() {
        super.onResume();

        if (pbbPresenter != null) {
            //setting URL for presenter
            initiatePresenter();
        }
    }

    private void initiatePresenter() {
        pbbPresenter.setView(this);

        //setting URL for presenter

        pbbPresenter.setFormUrl(GeneralHelper.getString(R.string.url_form_pbb));
        pbbPresenter.setInquiryUrl(GeneralHelper.getString(R.string.url_inquiry_pbb));
        pbbPresenter.setReInquiryUrl(GeneralHelper.getString(R.string.url_re_inquiry_pbb));
        pbbPresenter.setKonfirmasiUrl(GeneralHelper.getString(R.string.url_confirmation_pbb));
        pbbPresenter.setPaymentUrl(GeneralHelper.getString(R.string.url_payment_pbb));

        pbbPresenter.start();
    }

    @Override
    public void onSuccessUpdate(SavedResponse savedResponse, int item, int type) {
        String message = GeneralHelper.getString(R.array.type_option_desc, type);
        showSnackbarErrorMessage(message, BaseActivity.ALERT_CONFIRM, this, false);
        pbbPresenter.getDataForm();
    }


    @Override
    public void onSuccessGetRestResponse(RestResponse restResponse) {
        mDataPbbResponse = restResponse.getData(DataPbbResponse.class);
    }


    @Override
    protected void onDestroy() {
        pbbPresenter.stop();
        super.onDestroy();
    }

    @Override
    public void onClickSavedItem(SavedResponse savedResponse) {
        String s = savedResponse.getValue();
        str1 = s.split("\\|");
        if (str1.length == 4) {
            mCodeWilayah = str1[1];
            mNoPembayaran = str1[2];
            pbbPresenter.getDataReInquiry(new ReInquiryPbbRequest(mCodeWilayah, mNoPembayaran), isFromFastMenu);
        }
    }


    @Override
    public void onClickHistoryItem(HistoryResponse historyResponse) {
        String s = historyResponse.getValue();
        str1 = s.split("\\|");
        if (str1.length == 2) {
            mCodeWilayah = str1[0];
            mNoPembayaran = str1[1];
            pbbPresenter.getDataReInquiry(new ReInquiryPbbRequest(mCodeWilayah, mNoPembayaran), isFromFastMenu);
        }
    }


    @Override
    public void onAddNewClick() {

        TambahDaftarPbbActivity.launchIntent(this, getTitleBar(),
                GeneralHelper.getString(R.string.url_inquiry_pbb),
                GeneralHelper.getString(R.string.url_confirmation_pbb),
                GeneralHelper.getString(R.string.url_payment_pbb),
                mDataPbbResponse, extras, setParameter(), false, ""
        );


    }

    //Update date saved number
    @Override
    public void onUpdateItem(SavedResponse savedResponseItem, int type, int position) {
        if (type == Constant.EditOption.FAV) {
            pbbPresenter.setUpdateItem(GeneralHelper.getString(R.string.url_favorit_pbb), savedResponseItem, position, type);
        }

        if (type == Constant.EditOption.HAPUS) {
            pbbPresenter.setUpdateItem(GeneralHelper.getString(R.string.url_delete_pbb), savedResponseItem, position, type);
        }

        if (type == Constant.EditOption.EDIT) {
            FormEditSavedActivity.launchIntent(this, savedResponseItem, position, getDefaultIconResource(), GeneralHelper.getString(R.string.url_update_pbb), "Nomor Tujuan");
        }

        if (type == Constant.EditOption.NON_FAV)
            pbbPresenter.setUpdateItem(GeneralHelper.getString(R.string.url_unfavorit_pbb), savedResponseItem, position, Constant.EditOption.NON_FAV);

    }

    public void initiateHistoryAdapter() {
        rvBrivaTerakhir.setLayoutManager(new LinearLayoutManager(this, RecyclerView.HORIZONTAL, false));
        historyAdapter = new HistoryThreeRowAdapter(this, historyResponses, this, getDefaultIconResource());
        skeletonScreenHistory = Skeleton.bind(rvBrivaTerakhir)
                .adapter(historyAdapter)
                .shimmer(true)
                .angle(20)
                .frozen(false)
                .duration(1200)
                .count(5)

                .load(R.layout.item_skeleton_history_trx_three)
                .show();
    }

    public void initiateSavedAdapterThreeRow(){
        rvDaftarBriva.setLayoutManager(new LinearLayoutManager(this, RecyclerView.VERTICAL, false));
        savedAdapter = new SavedThreeRowAdapter(this, savedResponses, this, getDefaultIconResource(), isFromFastMenu);
        skeletonScreenSaved = Skeleton.bind(rvDaftarBriva)
                .adapter(savedAdapter)
                .shimmer(true)
                .angle(20)
                .frozen(false)
                .duration(1200)
                .count(5)
                .load(R.layout.item_skeleton_saved_trx_three)
                .show();
    }

    @Override
    public void onSuccessGetHistoryForm(List<HistoryResponse> historyResponse) {
        historyResponses.clear();
        historyResponses.addAll(historyResponse);
        historyAdapter.setHistoryResponses(historyResponses);
        historyAdapter.notifyDataSetChanged();

        if (historyResponses.size() > 0)
            layoutNoData.setVisibility(View.GONE);
        else
            layoutNoData.setVisibility(View.VISIBLE);
    }

    @Override
    public void onSuccessGetSavedForm(List<SavedResponse> savedResponse) {
        savedResponses.clear();
        savedResponses.addAll(savedResponse);
        savedAdapter.setSavedResponses(savedResponses);
        savedAdapter.notifyDataSetChanged();

        if (savedResponses.size() > 0) {
            rvDaftarBriva.setVisibility(View.VISIBLE);
            llNoDataSaved.setVisibility(View.GONE);
        } else {
            rvDaftarBriva.setVisibility(View.GONE);
            llNoDataSaved.setVisibility(View.VISIBLE);
        }
    }

    @Override
    protected void onActivityResult(int requestCode, int resultCode, @Nullable Intent data) {
        super.onActivityResult(requestCode, resultCode, data);

        if (requestCode == Constant.REQ_EDIT_SAVED && data != null) {
            if (resultCode == RESULT_OK) {
                String nama = data.getStringExtra(Constant.TAG_TITLE);
                int position = data.getIntExtra(Constant.TAG_POSITION, 0);
                String message = GeneralHelper.getString(R.array.type_option_desc, Constant.EditOption.EDIT);
                showSnackbarErrorMessage(message, BaseActivity.ALERT_CONFIRM, this, false);
                pbbPresenter.getDataForm();
            }
        }
    }

    @Override
    public void onBackPressed() {
        pbbPresenter.stop();
        super.onBackPressed();
    }

    @Override
    public void onSuccessGetReInquiry(ReInquiryPbbResponse reInquiryPbbResponse, String urlKonfirmasi, String urlPayment, boolean fromFastMenu) {
        DataPbbResponse dataPbbResponse = new DataPbbResponse();

        dataPbbResponse.setYearList(reInquiryPbbResponse.getYearList());
        List<PbbListResponse> pbbListResponses = new ArrayList<>();
        List<WilayahDataPbbResponse> wilayahDataPbbResponseList = new ArrayList<>();
        wilayahDataPbbResponseList.add(new WilayahDataPbbResponse(reInquiryPbbResponse.getCode_wilayah(),reInquiryPbbResponse.getCode_wilayah(),reInquiryPbbResponse.getWilayahName()));
        pbbListResponses.add(new PbbListResponse(reInquiryPbbResponse.getCode_wilayah(), reInquiryPbbResponse.getClusterName(), wilayahDataPbbResponseList));

        dataPbbResponse.setPbbListResponses(pbbListResponses);

        TambahDaftarPbbActivity.launchIntent(this, getTitleBar(),
                GeneralHelper.getString(R.string.url_inquiry_pbb),
                GeneralHelper.getString(R.string.url_confirmation_pbb),
                GeneralHelper.getString(R.string.url_payment_pbb),
                dataPbbResponse, extras, setParameter(), true, reInquiryPbbResponse.getNoPembayaran()
        );
    }

    @Override
    public void checkDataHistorySavedList() {
        if (historyResponses.isEmpty() && savedResponses.isEmpty() && !isFromFastMenu){
            onAddNewClick();
        }
    }
}
