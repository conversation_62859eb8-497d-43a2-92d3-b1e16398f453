package id.co.bri.brimo.data.dao.lifestyle

import androidx.room.Dao
import androidx.room.Insert
import androidx.room.OnConflictStrategy
import androidx.room.Query
import id.co.bri.brimo.models.daomodel.lifestyle.MenuLifestyle
import io.reactivex.Completable
import io.reactivex.Maybe

@Dao
interface MenuLifestyleDao {

    //insert
    @Insert(onConflict = OnConflictStrategy.REPLACE)
    fun insertListMenuLifestyle(listMenuLifestyle: List<MenuLifestyle>): Completable

    @Query("SELECT * FROM tbl_menu_lifestyle")
    fun getAllMenuLifestyle(): Maybe<List<MenuLifestyle>>

    @Query("SELECT * FROM tbl_menu_lifestyle WHERE is_new = 1")
    fun getAllMenuLifestyleNew(): Maybe<List<MenuLifestyle>>

    @Query("UPDATE tbl_menu_lifestyle SET is_new = :isNewMenu WHERE feature_code = :featureCode")
    fun updateMenuNew(featureCode: String, isNewMenu :Int) : Completable

    @Query("SELECT * FROM tbl_menu_lifestyle WHERE feature_code LIKE feature_code = :featureCode AND is_new = 1")
    fun getMenuNewByParentCode(featureCode: String): Maybe<List<MenuLifestyle>>
}