package id.co.bri.brimo.ui.fragments.voucher;

import android.annotation.SuppressLint;
import android.app.Activity;
import android.app.Dialog;
import android.content.DialogInterface;
import android.os.Bundle;
import android.text.Html;
import android.view.View;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.fragment.app.DialogFragment;
import androidx.fragment.app.FragmentActivity;
import androidx.fragment.app.FragmentManager;
import androidx.fragment.app.FragmentTransaction;

import com.google.android.material.bottomsheet.BottomSheetDialogFragment;

import id.co.bri.brimo.R;
import id.co.bri.brimo.databinding.FragmentRincianVoucherBinding;
import id.co.bri.brimo.domain.config.Constant;
import id.co.bri.brimo.domain.helpers.GeneralHelper;

public class DetailVoucherFragment extends BottomSheetDialogFragment {

    private FragmentRincianVoucherBinding binding;

    private String mVoucherType;

    String mLabel;

    String mExpirationDate;

    String mRincian;

    String mTimeLimit;

    String mNominal;

    String mBiayaAdmin;

    FragmentActivity fragmentActivity;

    onClickItem onClickItem;

    int intNominal;

    public DetailVoucherFragment() {}

    public DetailVoucherFragment(FragmentActivity activity, String voucherType, String label,
                                 String expirationDate, String rincian, String timeLimit,
                                 String nominal, String biayaAdmin, onClickItem onClick, int nominalInt) {
        fragmentActivity = activity;
        mVoucherType = voucherType;
        mLabel = label;
        mExpirationDate = expirationDate;
        mRincian = rincian;
        mTimeLimit = timeLimit;
        mNominal = nominal;
        mBiayaAdmin = biayaAdmin;
        onClickItem = onClick;
        intNominal = nominalInt;
    }

    @Override
    public void onViewCreated(@NonNull View view, @Nullable Bundle savedInstanceState) {
        super.onViewCreated(view, savedInstanceState);

    }

    @Override
    public void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setStyle(DialogFragment.STYLE_NORMAL, R.style.CustomBottomSheetDialogThemeInput);
    }

    @SuppressLint("RestrictedApi")
    @Override
    public void setupDialog(@NonNull Dialog dialog, int style) {
        super.setupDialog(dialog, style);
        binding = FragmentRincianVoucherBinding.inflate(getLayoutInflater());
        dialog.setContentView(binding.getRoot());

        if (mVoucherType.equals(Constant.Voucher.GAME.name())) {
            binding.tvTitle.setText(GeneralHelper.getString(R.string.rincian_voucher_game));
            binding.view.setVisibility(View.GONE);
            binding.llTextview1.setVisibility(View.GONE);
            binding.llTimeLimit.setVisibility(View.GONE);
        } else if (mVoucherType.equals(Constant.Voucher.STREAMING.name())) {
            binding.tvTitle.setText(GeneralHelper.getString(R.string.rincian_voucher_streaming));
            binding.view.setVisibility(View.VISIBLE);
            binding.llTextview1.setVisibility(View.VISIBLE);
            binding.llTimeLimit.setVisibility(View.VISIBLE);

            binding.tvSubtitle.setText(mLabel);
            binding.tvExpirationDate.setText(mExpirationDate);
            binding.tvTimeLimit.setText(mTimeLimit);
        }

        binding.tvRincian.setText(Html.fromHtml(mRincian));
        binding.tvNominal.setText(mNominal);
        binding.tvBiayaAdmin.setText(mBiayaAdmin);

        if (intNominal != 0) {
            binding.btnSubmit.setEnabled(true);
        } else {
            binding.btnSubmit.setEnabled(false);
            binding.btnSubmit.setTextColor(GeneralHelper.getColor(R.color.neutral_light60));
        }

        binding.btnSubmit.setOnClickListener(v -> onClickItem.onClickBeli());
    }

    @Override
    public void onDismiss(@NonNull DialogInterface dialog) {
        fragmentActivity.setResult(Activity.RESULT_CANCELED);

        super.onDismiss(dialog);
    }

    @Override
    public void show(@NonNull FragmentManager manager, @Nullable String tag) {
        try {
            FragmentTransaction ft = manager.beginTransaction();
            ft.add(this, tag);
            ft.commitAllowingStateLoss();
        } catch (IllegalStateException e) {

        }
    }

    public interface onClickItem {
        void onClickBeli();
    }
}
