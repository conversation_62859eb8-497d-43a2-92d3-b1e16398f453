package id.co.bri.brimo.ui.activities.dplkrevamp

import android.app.Activity
import android.content.Intent
import android.os.Bundle
import androidx.recyclerview.widget.LinearLayoutManager
import id.co.bri.brimo.R
import id.co.bri.brimo.adapters.dplkrevamp.PersonalDataDplkAdapter
import id.co.bri.brimo.contract.IPresenter.dplkrevamp.IRegistrationStepPersonalDataPresenter
import id.co.bri.brimo.contract.IView.dplkrevamp.IRegistrationStepPersonalDataView
import id.co.bri.brimo.databinding.ActivityStepPersonalDataDplkBinding
import id.co.bri.brimo.domain.helpers.GeneralHelper
import id.co.bri.brimo.models.apimodel.response.CheckPengkinianResponse
import id.co.bri.brimo.models.apimodel.response.dplkrevamp.PersonalDataDplkResponse
import id.co.bri.brimo.models.apimodel.response.dplkrevamp.PickProfileRiskResponse
import id.co.bri.brimo.models.apimodel.response.emas.Body
import id.co.bri.brimo.models.apimodel.response.emas.Header
import id.co.bri.brimo.models.apimodel.response.emas.UpdateDataContent
import id.co.bri.brimo.ui.activities.base.BaseActivity
import id.co.bri.brimo.ui.activities.pengkiniandata.PengkinianDataActivity
import id.co.bri.brimo.ui.customviews.dialog.DialogInformation
import id.co.bri.brimo.ui.fragments.BottomDialogListFragment
import id.co.bri.brimo.ui.fragments.dplkrevamp.KonfirmasiAutopaymentDialog
import id.co.bri.brimo.util.extension.gone
import javax.inject.Inject

class StepPersonalDataDplkActivity : BaseActivity(), IRegistrationStepPersonalDataView,
    BottomDialogListFragment.DialogBottomListener,
    KonfirmasiAutopaymentDialog.DialogClickYesNoListener, DialogInformation.OnActionClick {

    private var _binding: ActivityStepPersonalDataDplkBinding? = null
    private val binding get() = _binding!!

    @Inject
    lateinit var presenter: IRegistrationStepPersonalDataPresenter<IRegistrationStepPersonalDataView>
    private lateinit var bottomSheetDialog: BottomDialogListFragment

    companion object {
        private var mPersonalDataDplkResponse: PersonalDataDplkResponse = PersonalDataDplkResponse()
        private const val TAG_INTENT_PERSONAL_DATA = "TAG_INTENT_PERSONAL_DATA"

        @JvmStatic
        fun launchIntent(caller: Activity, response: PersonalDataDplkResponse) {
            val intent = Intent(caller, StepPersonalDataDplkActivity::class.java)
            intent.apply {
                putExtra(TAG_INTENT_PERSONAL_DATA, response)
            }
            caller.startActivity(intent)
        }
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        _binding = ActivityStepPersonalDataDplkBinding.inflate(layoutInflater)
        setContentView(binding.root)
        setupView()
        initListener()
        injectDependency()
    }

    override fun onDestroy() {
        super.onDestroy()
        _binding = null
    }

    private fun injectDependency() {
        activityComponent.inject(this)
        presenter.view = this
        presenter.setUrlProfileRisk(GeneralHelper.getString(R.string.url_dplk_pick_profile_risk))
        presenter.setUrlPengkinianData(GeneralHelper.getString(R.string.url_pdn_check))
        presenter.start()
    }

    private fun setupView() {
        binding.apply {
            val responseIntent: PersonalDataDplkResponse? =
                intent.getParcelableExtra(TAG_INTENT_PERSONAL_DATA)
            responseIntent?.let {
                mPersonalDataDplkResponse = it
            }
            GeneralHelper.setToolbarRevamp(
                this@StepPersonalDataDplkActivity,
                tbRegistrationDplkProfileRisk.toolbar,
                GeneralHelper.getString(R.string.txt_title_toolbar_personal_data)
            )
            if (!mPersonalDataDplkResponse.validData) {
                btnDiffData.gone()
                btnSubmit.text = GeneralHelper.getString(R.string.txt_btn_update_data)
            }
        }
        setupAdapter()
    }

    private fun setupAdapter() {
        binding.apply {
            val personalDataDplkAdapter = PersonalDataDplkAdapter()
            personalDataDplkAdapter.differ.submitList(mPersonalDataDplkResponse.userProfile)
            rvPersonalData.adapter = personalDataDplkAdapter
            rvPersonalData.layoutManager = LinearLayoutManager(this@StepPersonalDataDplkActivity)
        }
    }

    private fun initListener() {
        binding.apply {
            btnSubmit.setOnClickListener {
                handleBtnSubmitClick()
            }
            btnDiffData.setOnClickListener {
                showBottomSheetDiffData()
            }
        }
    }

    private fun handleBtnSubmitClick() {
        if (mPersonalDataDplkResponse.validData) {
            val dialogNotice = KonfirmasiAutopaymentDialog(
                GeneralHelper.getString(R.string.txt_titlte_dialog_approval_data),
                GeneralHelper.getString(R.string.txt_sub_titlte_dialog_approval_data),
                GeneralHelper.getString(R.string.cek_kembali),
                GeneralHelper.getString(R.string.txt_dialog_btn_already_appropriate),
                showBtnNo = true, showBtnYes = true,
                listener = this@StepPersonalDataDplkActivity,
            )
            dialogNotice.show(supportFragmentManager, "")
        } else {
            showBottomSheetDiffData()
        }
    }

    private fun showBottomSheetDiffData() {
        bottomSheetDialog = BottomDialogListFragment.newInstance(
            this@StepPersonalDataDplkActivity,
            updateDataContent()
        )
        bottomSheetDialog.show(supportFragmentManager, "")
    }

    private fun updateDataContent(): UpdateDataContent {
        return UpdateDataContent(
            Header(
                mPersonalDataDplkResponse.updateDrawer.title,
                mPersonalDataDplkResponse.updateDrawer.desc
            ), Body(
                mPersonalDataDplkResponse.updateDrawer.updateIconUrl, "",
                mPersonalDataDplkResponse.updateDrawer.updateTitle,
                mPersonalDataDplkResponse.updateDrawer.updateDesc
            )
        )
    }

    override fun onSuccessGetProfileRisk(response: PickProfileRiskResponse) {
        StepPickProfileRiskDplkActivity.launchIntent(this, response)
    }

    override fun onSuccessDataUpdating(response: CheckPengkinianResponse) {
        PengkinianDataActivity.launchIntent(this, response, false)
        bottomSheetDialog.dismiss()
    }

    override fun onFailedDataUpdating(response: CheckPengkinianResponse) {
        val dialogInformation = DialogInformation(
            this, "ic_update_pengkinian",
            response.title, response.desc,
            GeneralHelper.getString(R.string.ok), this, true, true
        )
        val ft = supportFragmentManager.beginTransaction()
        ft.add(dialogInformation, null)
        ft.commitAllowingStateLoss()

    }

    override fun onClickDirect() {
        presenter.onCheckPengkinianData()
    }

    override fun onClickButton() {
        bottomSheetDialog.dismiss()
    }

    override fun onClickBtnYes() {
        presenter.getkProfileRisk()
    }

    override fun onClickBtnNo() {
    }

    override fun onClickAction() {
    }
}